import { SecurityConfig, ItemExchangeConfig } from './types.js';

export const RATE_LIMITS: SecurityConfig = {
    DRUG_PURCHASE: 3000,      // 购买毒品间隔3秒
    WEAPON_PURCHASE: 3000,    // 购买武器间隔3秒
    INDUSTRY_PURCHASE: 5000,  // 购买产业间隔5秒
    GENERAL_PURCHASE: 2000,   // 一般购买间隔2秒
    BLACK_MONEY_EXCHANGE: 5000, // 黑钱兑换间隔5秒
};

export const TREASURE_ITEMS: ItemExchangeConfig[] = [
    { name: '古代金币', price: 500, description: '古老的金币，具有收藏价值' },
    { name: '海洋珍珠', price: 800, description: '深海中的珍贵珍珠' },
    { name: '蓝宝石', price: 2000, description: '稀有的蓝色宝石' },
    { name: '祖母绿', price: 5000, description: '珍贵的绿色宝石' },
    { name: '钻石', price: 15000, description: '最珍贵的宝石' },
    { name: '古代文物', price: 8000, description: '具有历史价值的文物' },
    { name: '沉船宝箱钥匙', price: 25000, description: '开启沉船宝箱的钥匙' }
];

export const INDUSTRY_PRICES = {
    '走私仓库': 100000,
    '化学工作室': 110000,
    '武器工作室': 230000,
    '违法车库': 80000,
};

export const DRUG_ITEMS = [
    { name: '洗衣粉', price: 1000, quantity: 10 },
    { name: '火毒', price: 800, quantity: 1 },
    { name: '小麻', price: 1000, quantity: 5 },
    { name: '山洛因', price: 2200, quantity: 1 },
];

export const WEAPON_ITEMS = [
    { name: '手枪', price: 20000, weight: 5 },
    { name: '双管霰弹枪', price: 40000, weight: 5 },
    { name: '子弹', price: 400, quantity: 10, weight: 1 },
];

export const MISC_ITEMS = [
    { name: '小麻种子', price: 8000, quantity: 10, weight: 0.1 },
    { name: '头套', price: 1000, quantity: 1, weight: 1 },
];

export const TOR_PERMISSION_PRICE = 1000;
export const TOR_PERMISSION_DURATION = 15 * 24 * 60 * 60 * 1000; // 15天
export const MAX_BLACK_MONEY_EXCHANGE = 10000;
export const BLACK_MONEY_EXCHANGE_RATE = 0.4;