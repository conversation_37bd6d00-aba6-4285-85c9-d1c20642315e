import { useRebarClient } from '@Client/index.js';
import * as native from 'natives';
import * as alt from 'alt-client';

const Rebar = useRebarClient();
const webview = Rebar.webview.useWebview();
const messenger = Rebar.messenger.useMessenger();


/*
const localplayer = alt.Player.local;
let object = null;

webview.on('previewobject', (modelname: string) => {
    if (object) {
        native.deleteObject(object);
        object = null;
    }
    object = native.createObject(alt.hash(modelname), localplayer.pos.x, localplayer.pos.y, localplayer.pos.z, false, false, false);

    native.freezeEntityPosition(object, true);

    previewobjectinfoupdate(object)
});


function previewobjectinfoupdate(object: number) {
    const pos = native.getEntityCoords(object, false);
    const rot = native.getEntityRotationVelocity(object);
    webview.emit('previewobject:infoupdate', native.getEntityModel(object), { x: pos.x, y: pos.y, z: pos.z }, { x: rot.x, y: rot.y, z: rot.z })
}



webview.on('changepreviewobjectposandrot', (pos: alt.Vector3, rot: alt.Vector3) => {
    if (!object) return;

    native.setEntityCoords(object, pos.x, pos.y, pos.z, false, false, true, false);
    native.setEntityRotation(object, rot.x, rot.y, rot.z, 2, true);

    previewobjectinfoupdate(object)

});


webview.show('previewobject', 'overlay');



let ispreviewobjectvueshow = false

webview.on('previewobject:state', (state: boolean) => {
    ispreviewobjectvueshow = state

    if (state) {
        previewobjectinfoupdate(object)
    }
})



alt.on('keyup', (key) => {
    if (key === alt.KeyCode.Tab) {

        if (alt.isConsoleOpen()) {
            return
        }
        if (messenger.isChatFocused()) {
            return
        }
        if (webview.isAnyPageOpen()) {
            return
        }

        if (ispreviewobjectvueshow) {
            webview.emit('previewobject:hide')
            alt.showCursor(false)
            Rebar.player.useControls().setCameraControlsDisabled(false)
            webview.unfocus()
        } else {
            webview.emit('previewobject:show')
            alt.showCursor(true)
            Rebar.player.useControls().setCameraControlsDisabled(true)
            webview.focus()
        }

    }
})



*/