import * as alt from 'alt-client';
import * as native from 'natives';
import { useRebarClient } from '@Client/index.js';

const Rebar = useRebarClient();
const webview = Rebar.webview.useWebview();

const boardProp = "prop_police_id_board";
const textProp = "prop_police_id_text";
const renderTargetName = "ID_Text";
const animDict = "mp_character_creation@lineup@male_a";
const animName = "loop_raised";

native.requestAnimDict(animDict);

// 存储其他玩家的文字牌数据
const otherPlayersMugshots = new Map();

class MugshotBoard {
    static active = false;
    static everyTickHandle = null;
    static handles = {
        board: null,
        text: null,
        scaleform: null,
        renderTarget: null,
    };
    static customContent = {
        title: '',
        topText: '',
        midText: '',
        bottomText: ''
    };

    static async start(customTitle = '', customTopText = '', customMidText = '', customBottomText = '') {
        if (MugshotBoard.active) {
            MugshotBoard.stop();
            return;
        }
        
        MugshotBoard.active = true;
        
        // 存储自定义内容
        MugshotBoard.customContent = {
            title: customTitle || '',
            topText: customTopText || '',
            midText: customMidText || '',
            bottomText: customBottomText || ''
        };

        MugshotBoard.setupObjects();
        await MugshotBoard.setupScaleform(
            MugshotBoard.customContent.title,
            MugshotBoard.customContent.topText,
            MugshotBoard.customContent.midText,
            MugshotBoard.customContent.bottomText,
            -1
        );
        MugshotBoard.setupRendertarget();
        MugshotBoard.attachObjects();
        MugshotBoard.playAnimation();

        MugshotBoard.everyTickHandle = alt.everyTick(MugshotBoard.everyTick);
        
        // 通知服务器玩家开始举牌，并发送自定义内容
        alt.emitServerRaw('mugshot:playerStart', 
            MugshotBoard.customContent.title,
            MugshotBoard.customContent.topText, 
            MugshotBoard.customContent.midText,
            MugshotBoard.customContent.bottomText
        );
    }

    static setupObjects() {
        MugshotBoard.handles.board = native.createObject(
            alt.hash(boardProp),
            alt.Player.local.pos.x,
            alt.Player.local.pos.y,
            alt.Player.local.pos.z,
            false,
            false,
            false
        );
        MugshotBoard.handles.text = native.createObject(
            alt.hash(textProp),
            alt.Player.local.pos.x,
            alt.Player.local.pos.y,
            alt.Player.local.pos.z,
            false,
            false,
            false
        );
    }

    static async setupScaleform(title, topText, midText, bottomText, rank) {
        let handle = native.requestScaleformMovie("mugshot_board_01");
        await awaitScaleformLoad(handle);
        native.beginScaleformMovieMethod(handle, "SET_BOARD");
        native.scaleformMovieMethodAddParamPlayerNameString(title);
        native.scaleformMovieMethodAddParamPlayerNameString(midText);
        native.scaleformMovieMethodAddParamPlayerNameString(bottomText);
        native.scaleformMovieMethodAddParamPlayerNameString(topText);
        native.scaleformMovieMethodAddParamInt(0);
        if (rank > -1) native.scaleformMovieMethodAddParamInt(rank);
        native.endScaleformMovieMethod();

        MugshotBoard.handles.scaleform = handle;
    }

    static setupRendertarget() {
        native.registerNamedRendertarget(renderTargetName, false);
        native.linkNamedRendertarget(alt.hash(textProp));
        MugshotBoard.handles.renderTarget = native.getNamedRendertargetRenderId(
            renderTargetName
        );
    }

    static attachObjects() {
        native.attachEntityToEntity(
            MugshotBoard.handles.board,
            alt.Player.local.scriptID,
            native.getPedBoneIndex(alt.Player.local.scriptID, 28422),
            0,
            0,
            0,
            0,
            0,
            0,
            false,
            false,
            false,
            false,
            2,
            true,
            0
        );
        native.attachEntityToEntity(
            MugshotBoard.handles.text,
            alt.Player.local.scriptID,
            native.getPedBoneIndex(alt.Player.local.scriptID, 28422),
            0,
            0,
            0,
            0,
            0,
            0,
            false,
            false,
            false,
            false,
            2,
            true,
            0,
        );
    }

    static playAnimation() {
        native.taskPlayAnim(
            alt.Player.local.scriptID,
            animDict,
            animName,
            8,
            -8,
            -1,
            1,
            0,
            false,
            false,
            false
        );
    }

    static everyTick() {
        native.setTextRenderId(MugshotBoard.handles.renderTarget);
        native.drawScaleformMovie(
            MugshotBoard.handles.scaleform,
            0.405,
            0.37,
            0.81,
            0.74,
            255,
            255,
            255,
            255,
            0
        );
        native.setTextRenderId(1);
    }

    static stop() {
        if (!MugshotBoard.active) return;
        MugshotBoard.active = false;

        native.deleteObject(MugshotBoard.handles.board);
        native.deleteObject(MugshotBoard.handles.text);
        native.setScaleformMovieAsNoLongerNeeded(
            MugshotBoard.handles.scaleform
        );
        native.releaseNamedRendertarget(MugshotBoard.handles.renderTarget);

        native.stopAnimTask(alt.Player.local.scriptID, animDict, animName, -4);

        alt.clearEveryTick(MugshotBoard.everyTickHandle);
        
        // 通知服务器玩家停止举牌
        alt.emitServerRaw('mugshot:playerStop');
    }

    // 为其他玩家创建文字牌显示
    static async startForOtherPlayer(player, title, topText, midText, bottomText) {
        if (!player || !player.valid) return;
        
        const playerId = player.id;
        
        // 如果已经存在，先清理
        if (otherPlayersMugshots.has(playerId)) {
            MugshotBoard.stopForOtherPlayer(player);
        }
        
        // 创建objects
        const board = native.createObject(
            alt.hash(boardProp),
            player.pos.x,
            player.pos.y,
            player.pos.z,
            false,
            false,
            false
        );
        
        const text = native.createObject(
            alt.hash(textProp),
            player.pos.x,
            player.pos.y,
            player.pos.z,
            false,
            false,
            false
        );
        
        // 创建scaleform
        let scaleform = native.requestScaleformMovie("mugshot_board_01");
        await awaitScaleformLoad(scaleform);
        native.beginScaleformMovieMethod(scaleform, "SET_BOARD");
        native.scaleformMovieMethodAddParamPlayerNameString(title);
        native.scaleformMovieMethodAddParamPlayerNameString(midText);
        native.scaleformMovieMethodAddParamPlayerNameString(bottomText);
        native.scaleformMovieMethodAddParamPlayerNameString(topText);
        native.scaleformMovieMethodAddParamInt(0);
        native.endScaleformMovieMethod();
        
        // 设置render target
        const renderTargetName2 = `ID_Text_${playerId}`;
        native.registerNamedRendertarget(renderTargetName2, false);
        native.linkNamedRendertarget(alt.hash(textProp));
        const renderTarget = native.getNamedRendertargetRenderId(renderTargetName2);
        
        // 附加到玩家
        native.attachEntityToEntity(
            board,
            player.scriptID,
            native.getPedBoneIndex(player.scriptID, 28422),
            0, 0, 0, 0, 0, 0,
            false, false, false, false, 2, true, 0
        );
        
        native.attachEntityToEntity(
            text,
            player.scriptID,
            native.getPedBoneIndex(player.scriptID, 28422),
            0, 0, 0, 0, 0, 0,
            false, false, false, false, 2, true, 0
        );
        
        // 存储数据
        otherPlayersMugshots.set(playerId, {
            board,
            text,
            scaleform,
            renderTarget,
            renderTargetName: renderTargetName2,
            everyTickHandle: null
        });
        
        // 开始渲染循环
        const handles = otherPlayersMugshots.get(playerId);
        handles.everyTickHandle = alt.everyTick(() => {
            if (!player.valid || !otherPlayersMugshots.has(playerId)) {
                if (handles.everyTickHandle) {
                    alt.clearEveryTick(handles.everyTickHandle);
                }
                return;
            }
            
            native.setTextRenderId(handles.renderTarget);
            native.drawScaleformMovie(
                handles.scaleform,
                0.405, 0.37, 0.81, 0.74,
                255, 255, 255, 255, 0
            );
            native.setTextRenderId(1);
        });
    }
    
    static stopForOtherPlayer(player) {
        if (!player) return;
        
        const playerId = player.id;
        const handles = otherPlayersMugshots.get(playerId);
        
        if (handles) {
            // 清理everyTick
            if (handles.everyTickHandle) {
                alt.clearEveryTick(handles.everyTickHandle);
            }
            
            // 清理objects和scaleform
            native.deleteObject(handles.board);
            native.deleteObject(handles.text);
            native.setScaleformMovieAsNoLongerNeeded(handles.scaleform);
            native.releaseNamedRendertarget(handles.renderTarget);
            
            // 从map中移除
            otherPlayersMugshots.delete(playerId);
        }
    }
}

function awaitScaleformLoad(scaleform) {
    return new Promise<void>((resolve) => {
        let interval = alt.setInterval(() => {
            if (native.hasScaleformMovieLoaded(scaleform)) {
                alt.clearInterval(interval);
                resolve();
            }
        }, 50);
    });
}

// Vue界面控制变量
let isEditorOpen = false;

// 打开Vue编辑器界面
function openSignEditor() {
    if (isEditorOpen) return;
    
    isEditorOpen = true;
    
    // 使用rebar的webview API显示Vue组件
    webview.show('SignEditor', 'page', true); // 使用page类型，允许ESC关闭
    
    // 禁用玩家控制
    Rebar.player.useControls().setControls(false);
    
    alt.log('打开Vue编辑器界面');
}

// 关闭Vue编辑器界面
function closeSignEditor() {
    if (!isEditorOpen) return;
    
    isEditorOpen = false;
    
    // 隐藏Vue组件
    webview.hide('SignEditor');
    
    // 恢复玩家控制
    Rebar.player.useControls().setControls(true);
    
    alt.log('关闭Vue编辑器界面');
}

// 设置webview事件监听器
webview.on('camerapose:closeEditor', () => {
    closeSignEditor();
});

webview.on('camerapose:displaySign', (signData: any) => {
    // 通知服务器展示木牌
    alt.emitServerRaw('camerapose:displaySign', signData);
    closeSignEditor();
});

// 监听webview关闭事件（当用户按ESC键时）
webview.onClose('SignEditor', () => {
    isEditorOpen = false;
    // 恢复玩家控制
    Rebar.player.useControls().setControls(true);
    alt.log('用户关闭了Vue编辑器界面');
});

// Start - 修改为打开Vue编辑器
alt.on("mugshot:start", () => {
    openSignEditor();
});

alt.onServer("mugshot:start", () => {
    openSignEditor();
});

// 接收服务器展示木牌的事件
alt.onServer("camerapose:displayMugshot", (title: string, topText: string, midText: string, bottomText: string) => {
    MugshotBoard.start(title, topText, midText, bottomText);
});

// 接收其他玩家的举牌信息
alt.onServer("mugshot:showOtherPlayer", (playerId, title, topText, midText, bottomText) => {
    const player = alt.Player.getByID(playerId);
    if (player && player !== alt.Player.local) {
        // 为其他玩家创建文字牌显示
        MugshotBoard.startForOtherPlayer(player, title, topText, midText, bottomText);
    }
});

alt.onServer("mugshot:hideOtherPlayer", (playerId) => {
    const player = alt.Player.getByID(playerId);
    if (player && player !== alt.Player.local) {
        MugshotBoard.stopForOtherPlayer(player);
    }
});

// Stop
alt.on("mugshot:stop", MugshotBoard.stop);
alt.onServer("mugshot:stop", MugshotBoard.stop);

// 清理断开连接的玩家
alt.on('playerDisconnect', (player) => {
    if (otherPlayersMugshots.has(player.id)) {
        MugshotBoard.stopForOtherPlayer(player);
    }
});