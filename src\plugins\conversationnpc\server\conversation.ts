import * as alt from 'alt-server';
import { useRebar } from '@Server/index.js';
import { PageNames } from '@Shared/webview/index.js';
import { makeplayerfacetopos } from '@Plugins/choiceofoneoption/server/index.js';
import { ConversationData, NPC, Conversation } from '../shared/types.js';

const Rebar = useRebar();

class ConversationManager {
    private static instance: ConversationManager;
    private npcList: NPC[] = [];

    public static getInstance(): ConversationManager {
        if (!ConversationManager.instance) {
            ConversationManager.instance = new ConversationManager();
        }
        return ConversationManager.instance;
    }

    private constructor() {
        this.setupEventHandlers();
    }

    private setupEventHandlers(): void {
        alt.onClient('conversation:open', this.handleConversationOpen.bind(this));
        alt.onClient('conversation:option', this.handleConversationOption.bind(this));
        alt.on('rebar:playerPageOpened', this.handlePageOpened.bind(this));
        alt.on('rebar:playerPageClosed', this.handlePageClosed.bind(this));
    }

    public addNPC(npc: NPC): void {
        this.npcList.push(npc);
    }

    public getNPCList(): NPC[] {
        return this.npcList;
    }

    public findNPC(name: string): NPC | undefined {
        return this.npcList.find(npc => npc.name === name);
    }

    private async handleConversationOpen(player: alt.Player, npcname: string): Promise<void> {
        Rebar.player.useWebview(player).show('conversation', 'page', true);

        let isReady = await Rebar.player.useWebview(player).isReady('conversation', 'page');
        while (!isReady) {
            await alt.Utils.wait(100);
            isReady = await Rebar.player.useWebview(player).isReady('conversation', 'page');
        }

        const npc = this.findNPC(npcname);
        if (!npc) return;

        const startConversation = npc.conversations.find(conv => conv.id === 'start') || npc.conversations[0];
        this.sendConversationToPlayer(player, npc, startConversation);

        makeplayerfacetopos(player, npc.pos);
        player.setStreamSyncedMeta('do', '正在与' + npc.name + '对话');
    }

    private sendConversationToPlayer(player: alt.Player, npc: NPC, conversation: Conversation): void {
        if (!npc || !conversation) return;

        if (conversation.callback) {
            conversation.callback(player);
        }

        const convIndex = npc.conversations.indexOf(conversation);
        const data: ConversationData = {
            name: npc.name,
            text: conversation.text,
            options: conversation.options.map((option, optIndex) => ({
                text: option.text,
                optionIndex: convIndex + '-' + optIndex
            }))
        };

        Rebar.player.useWebview(player).emit('conversation:npc:sync', data);
        player.setStreamSyncedMeta('currentConversationNPC', npc.name);
    }

    private handleConversationOption(player: alt.Player, optionData: string): void {
        const [convIndex, optIndex] = optionData.split('-').map(Number);

        const npcName = player.getStreamSyncedMeta('currentConversationNPC') as string;
        const npc = this.findNPC(npcName);
        if (!npc) return;

        const conversation = npc.conversations[convIndex];
        if (!conversation) return;

        const option = conversation.options[optIndex];
        if (!option) return;

        if (option.callback) {
            option.callback(player);
        }

        if (option.endConversation) {
            Rebar.player.useWebview(player).hide('conversation');
            return;
        }

        if (option.nextId) {
            const nextConversation = npc.conversations.find(conv => conv.id === option.nextId);
            if (nextConversation) {
                this.sendConversationToPlayer(player, npc, nextConversation);
            }
        } else {
            Rebar.player.useWebview(player).hide('conversation');
        }
    }

    private handlePageOpened(player: alt.Player, page: PageNames): void {
        if (page === 'conversation') {
            Rebar.player.useWorld(player).disableControls();
        }
    }

    private handlePageClosed(player: alt.Player, page: PageNames): void {
        if (page === 'conversation') {
            Rebar.player.useWorld(player).enableControls();
            player.deleteStreamSyncedMeta('do');
        }
    }
}

export const conversationManager = ConversationManager.getInstance();