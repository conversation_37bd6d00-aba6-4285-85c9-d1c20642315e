<template>
    <Transition name="slidein">
        <div v-if="isVisible" class="choice-overlay">
            <div class="choice-container">
                <!-- 标题区域 -->
                <div class="choice-header">
                    <div class="header-icon">
                        <i class="fas fa-list-ul"></i>
                    </div>
                    <div class="header-text">
                        <h2>{{ config.title }}</h2>
                        <p v-if="config.description" class="description">{{ config.description }}</p>
                    </div>
                </div>

                <!-- 选项区域 -->
                <div class="choice-content">
                    <div class="options-grid">
                        <button 
                            v-for="option in config.options" 
                            :key="option.id"
                            class="option-btn"
                            :class="[
                                option.color || 'primary',
                                { 'disabled': option.disabled }
                            ]"
                            :disabled="option.disabled"
                            @click="selectOption(option)"
                        >
                            <i v-if="option.icon" :class="option.icon" class="option-icon"></i>
                            <span class="option-text">{{ option.text }}</span>
                        </button>
                    </div>

                    <!-- 取消按钮 -->
                    <div v-if="config.allowCancel" class="cancel-section">
                        <button class="cancel-btn" @click="cancel">
                            <i class="fas fa-times"></i>
                            {{ config.cancelText || '取消' }}
                        </button>
                    </div>
                </div>

                <!-- 倒计时显示 -->
                <div v-if="timeLeft > 0" class="timeout-indicator">
                    <div class="timeout-bar">
                        <div class="timeout-progress" :style="{ width: timeProgress + '%' }"></div>
                    </div>
                    <span class="timeout-text">{{ Math.ceil(timeLeft / 1000) }}秒后自动关闭</span>
                </div>
            </div>
        </div>
    </Transition>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import { useEvents } from '@Composables/useEvents'

interface ChoiceOption {
    id: string;
    text: string;
    value: any;
    icon?: string;
    color?: 'primary' | 'success' | 'warning' | 'error' | 'info';
    disabled?: boolean;
}

interface ChoiceConfig {
    title: string;
    description?: string;
    options: ChoiceOption[];
    allowCancel?: boolean;
    cancelText?: string;
    timeout?: number;
}

const events = useEvents()
const isVisible = ref(false)
const config = ref<ChoiceConfig>({
    title: '',
    options: [],
    allowCancel: false
})

// Mock数据用于前端测试
const mockData: ChoiceConfig = {
    title: '选择操作',
    description: '请选择您要执行的操作',
    options: [
        {
            id: '1',
            text: '确认购买',
            value: 'confirm',
            icon: 'fas fa-check',
            color: 'success'
        },
        {
            id: '2',
            text: '查看详情',
            value: 'details',
            icon: 'fas fa-info-circle',
            color: 'info'
        },
        {
            id: '3',
            text: '编辑设置',
            value: 'edit',
            icon: 'fas fa-edit',
            color: 'primary'
        },
        {
            id: '4',
            text: '删除项目',
            value: 'delete',
            icon: 'fas fa-trash',
            color: 'error'
        },
        {
            id: '5',
            text: '警告操作',
            value: 'warning',
            icon: 'fas fa-exclamation-triangle',
            color: 'warning'
        },
        {
            id: '6',
            text: '禁用选项',
            value: 'disabled',
            icon: 'fas fa-ban',
            color: 'primary',
            disabled: true
        }
    ],
    allowCancel: true,
    cancelText: '取消',
    timeout: 10000 // 10秒超时
}

// 用于前端测试的函数
function showMockData() {
    config.value = mockData
    isVisible.value = true
    
    // 设置超时倒计时
    if (mockData.timeout && mockData.timeout > 0) {
        timeLeft.value = mockData.timeout
        startTime = Date.now()
        startTimeout()
    }
}

// 在组件挂载时自动显示mock数据（仅用于前端测试）
onMounted(() => {
    // 延迟1秒后显示mock数据
    setTimeout(() => {
        showMockData()
    }, 1000)
})

const timeLeft = ref(0)
const timeProgress = ref(100)
let timeoutTimer: NodeJS.Timeout | null = null
let startTime = 0

events.on('choice:show', (choiceConfig: ChoiceConfig) => {
    config.value = choiceConfig
    isVisible.value = true
    
    // 设置超时倒计时
    if (choiceConfig.timeout && choiceConfig.timeout > 0) {
        timeLeft.value = choiceConfig.timeout
        startTime = Date.now()
        startTimeout()
    }
})

function startTimeout() {
    if (timeoutTimer) {
        clearInterval(timeoutTimer)
    }
    
    timeoutTimer = setInterval(() => {
        const elapsed = Date.now() - startTime
        timeLeft.value = Math.max(0, (config.value.timeout || 0) - elapsed)
        timeProgress.value = (timeLeft.value / (config.value.timeout || 1)) * 100
        
        if (timeLeft.value <= 0) {
            clearInterval(timeoutTimer!)
            timeoutTimer = null
            // 超时自动关闭
            selectOption(null)
        }
    }, 100)
}

function selectOption(option: ChoiceOption | null) {
    if (timeoutTimer) {
        clearInterval(timeoutTimer)
        timeoutTimer = null
    }
    
    const value = option ? option.value : null
    /*
    // 前端测试时输出选择结果到控制台
    if (option) {
        console.log('选择了选项:', option.text, '值:', value)
    } else {
        console.log('取消选择或超时')
    }
    */
    // 在实际环境中发送到服务器
    events.emitServer('choice:select', value)
    isVisible.value = false
    
    // 重置状态
    timeLeft.value = 0
    timeProgress.value = 100
}

function cancel() {
    selectOption(null)
}

onUnmounted(() => {
    if (timeoutTimer) {
        clearInterval(timeoutTimer)
    }

    
})
</script>

<style scoped>
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

/* 设计变量 - 绿色主题 */
.choice-overlay {
    --primary-color: #64ffda;
    --primary-dark: #00d4aa;
    --card-bg: rgba(15, 23, 42, 0.95);
    --card-secondary: rgba(30, 41, 59, 0.8);
    --card-tertiary: rgba(51, 65, 85, 0.6);
    --text-primary: #ffffff;
    --text-secondary: rgba(255, 255, 255, 0.8);
    --text-tertiary: rgba(255, 255, 255, 0.6);
    --border-primary: rgba(100, 255, 218, 0.2);
    --border-secondary: rgba(100, 255, 218, 0.1);
    --accent-success: #34c759;
    --accent-warning: #ff9500;
    --accent-error: #ff4444;
    --accent-info: #007aff;
    --shadow-sm: 0 0.2vh 0.4vh rgba(0, 0, 0, 0.1);
    --shadow-md: 0 0.4vh 0.8vh rgba(0, 0, 0, 0.15);
    --shadow-lg: 0 0.8vh 1.6vh rgba(0, 0, 0, 0.2);
    --shadow-xl: 0 1.6vh 3.2vh rgba(0, 0, 0, 0.25);
}

.choice-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
}

.choice-container {
    width: min(90vw, 72vh);
    max-height: 90vh;
    background: var(--card-bg);
    border: 1px solid var(--border-primary);
    border-radius: 1.6vh;
    box-shadow: var(--shadow-xl);
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.choice-header {
    display: flex;
    align-items: flex-start;
    gap: 2vh;
    padding: 3.2vh 3.2vh 2.4vh;
    background: var(--card-secondary);
    border-bottom: 1px solid var(--border-secondary);
}

.header-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 4.8vh;
    height: 4.8vh;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    border: 1px solid var(--border-primary);
    border-radius: 1.2vh;
    color: #0f172a;
    font-size: 2vh;
    flex-shrink: 0;
    box-shadow: 0 0.4vh 0.8vh rgba(100, 255, 218, 0.3);
}

.header-text {
    flex: 1;
    min-width: 0;
}

.header-text h2 {
    color: var(--text-primary);
    font-size: 2.2vh;
    font-weight: 600;
    margin: 0 0 0.8vh 0;
    line-height: 1.25;
    letter-spacing: -0.025em;
}

.description {
    color: var(--text-tertiary);
    font-size: 1.5vh;
    margin: 0;
    line-height: 1.5;
    font-weight: 400;
}

.choice-content {
    flex: 1;
    padding: 3.2vh;
    overflow-y: auto;
    min-height: 0;
}

.choice-content::-webkit-scrollbar {
    width: 0.8vh;
}

.choice-content::-webkit-scrollbar-track {
    background: var(--card-secondary);
    border-radius: 0.4vh;
}

.choice-content::-webkit-scrollbar-thumb {
    background: var(--border-primary);
    border-radius: 0.4vh;
}

.choice-content::-webkit-scrollbar-thumb:hover {
    background: var(--primary-color);
}

.options-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(30vh, 1fr));
    gap: 1.2vh;
    margin-bottom: 3.2vh;
}

.option-btn {
    display: flex;
    align-items: center;
    gap: 1.6vh;
    padding: 2vh;
    border: 1px solid var(--border-secondary);
    border-radius: 1.2vh;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 1.5vh;
    font-weight: 500;
    text-align: left;
    min-height: 6.8vh;
    background: var(--card-secondary);
    position: relative;
    overflow: hidden;
}

.option-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(100, 255, 218, 0.1), transparent);
    transition: all 0.3s ease;
}

.option-btn:hover:not(.disabled)::before {
    left: 100%;
}

.option-btn.primary {
    border-color: rgba(100, 255, 218, 0.3);
    color: var(--primary-color);
}

.option-btn.success {
    border-color: rgba(52, 199, 89, 0.3);
    color: var(--accent-success);
}

.option-btn.warning {
    border-color: rgba(255, 149, 0, 0.3);
    color: var(--accent-warning);
}

.option-btn.error {
    border-color: rgba(255, 68, 68, 0.3);
    color: var(--accent-error);
}

.option-btn.info {
    border-color: rgba(0, 122, 255, 0.3);
    color: var(--accent-info);
}

.option-btn:hover:not(.disabled) {
    background: var(--card-tertiary);
    border-color: var(--border-primary);
    transform: translateY(-0.2vh);
    box-shadow: var(--shadow-md);
}

.option-btn:active:not(.disabled) {
    transform: translateY(0);
    box-shadow: var(--shadow-sm);
}

.option-btn.disabled {
    opacity: 0.5;
    cursor: not-allowed;
    color: var(--text-tertiary);
}

.option-btn.disabled:hover {
    transform: none;
    box-shadow: none;
    background: var(--card-secondary);
    border-color: var(--border-secondary);
}

.option-icon {
    font-size: 1.8vh;
    flex-shrink: 0;
}

.option-text {
    flex: 1;
    line-height: 1.4;
    color: var(--text-primary);
}

.cancel-section {
    border-top: 1px solid var(--border-secondary);
    padding-top: 2.4vh;
    display: flex;
    justify-content: center;
}

.cancel-btn {
    display: flex;
    align-items: center;
    gap: 1vh;
    padding: 1.2vh 2.4vh;
    background: var(--card-tertiary);
    border: 1px solid var(--border-secondary);
    border-radius: 0.8vh;
    color: var(--text-secondary);
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 1.4vh;
    font-weight: 500;
    font-family: inherit;
}

.cancel-btn:hover {
    background: var(--card-bg);
    border-color: var(--border-primary);
    color: var(--text-primary);
    transform: translateY(-0.1vh);
    box-shadow: var(--shadow-sm);
}

.timeout-indicator {
    padding: 2.4vh 3.2vh;
    background: var(--card-secondary);
    border-top: 1px solid var(--border-secondary);
    display: flex;
    align-items: center;
    gap: 1.6vh;
}

.timeout-bar {
    flex: 1;
    height: 0.4vh;
    background: var(--card-tertiary);
    border-radius: 0.2vh;
    overflow: hidden;
}

.timeout-progress {
    height: 100%;
    background: linear-gradient(90deg, var(--primary-color), var(--primary-dark));
    transition: width 0.1s linear;
    border-radius: 0.2vh;
}

.timeout-text {
    color: var(--text-tertiary);
    font-size: 1.3vh;
    font-weight: 500;
    white-space: nowrap;
    font-variant-numeric: tabular-nums;
}

/* 过渡动画 */
.slidein-enter-active {
    animation: slideIn 0.25s ease-out;
}

.slidein-leave-active {
    animation: slideOut 0.2s ease-in;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(1.6vh) scale(0.98);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

@keyframes slideOut {
    from {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
    to {
        opacity: 0;
        transform: translateY(-0.8vh) scale(0.98);
    }
}

</style> 