import * as alt from 'alt-server';
import { useRebar } from '@Server/index.js';
import { PageNames } from '@Shared/webview/index.js';
import { ChoiceConfig, ChoiceOption } from '../shared/config.js';

const Rebar = useRebar();

declare global {
    export interface ServerPlugin {
        ['choice-api']: ReturnType<typeof useChoiceApi>;
    }
}

function useChoiceApi() {
    // 存储每个玩家当前的处理函数
    const playerHandlers = new Map<number, (p: alt.Player, value: any) => void>();
    const playerTimeouts = new Map<number, NodeJS.Timeout>();

    /**
     * 显示选择对话框
     * @param player 玩家
     * @param config 选择配置
     * @returns Promise<any> 返回选择的值，如果取消则返回null
     */
    function showChoice(player: alt.Player, config: ChoiceConfig): Promise<any> {
        Rebar.player.useWebview(player).show('choicebar', 'page', true);

        return new Promise((resolve) => {
            const webview = Rebar.player.useWebview(player);
            
            // 清理该玩家可能存在的旧监听器和超时
            if (playerHandlers.has(player.id)) {
                alt.offClient('choice:select', playerHandlers.get(player.id)!);
                playerHandlers.delete(player.id);
            }
            
            if (playerTimeouts.has(player.id)) {
                clearTimeout(playerTimeouts.get(player.id)!);
                playerTimeouts.delete(player.id);
            }

            const handler = (p: alt.Player, value: any) => {
                if (p !== player) return;
                
                // 移除事件监听器和超时
                alt.offClient('choice:select', handler);
                playerHandlers.delete(player.id);
                
                if (playerTimeouts.has(player.id)) {
                    clearTimeout(playerTimeouts.get(player.id)!);
                    playerTimeouts.delete(player.id);
                }
                
                resolve(value);
                webview.hide('choicebar');
            };
            
            // 存储这个玩家的处理函数
            playerHandlers.set(player.id, handler);
            alt.onClient('choice:select', handler);

            // 设置超时
            if (config.timeout && config.timeout > 0) {
                const timeoutId = setTimeout(() => {
                    // 超时处理
                    if (playerHandlers.has(player.id)) {
                        alt.offClient('choice:select', playerHandlers.get(player.id)!);
                        playerHandlers.delete(player.id);
                    }
                    playerTimeouts.delete(player.id);
                    resolve(null);
                    webview.hide('choicebar');
                }, config.timeout);
                
                playerTimeouts.set(player.id, timeoutId);
            }

            // 发送配置到前端
            webview.emit('choice:show', config);
        });
    }

    /**
     * 简化版显示选择对话框 - 快速创建选择
     * @param player 玩家
     * @param title 标题
     * @param options 选项数组，可以是字符串数组或选项对象数组
     * @returns Promise<any> 返回选择的值
     */
    function showSimpleChoice(player: alt.Player, title: string, options: string[] | ChoiceOption[]): Promise<any> {
        const choiceOptions: ChoiceOption[] = options.map((option, index) => {
            if (typeof option === 'string') {
                return {
                    id: index.toString(),
                    text: option,
                    value: option
                };
            }
            return option;
        });

        const config: ChoiceConfig = {
            title,
            options: choiceOptions,
            allowCancel: true,
            cancelText: '取消'
        };

        return showChoice(player, config);
    }

    /**
     * 清理玩家的处理函数
     * @param player 玩家
     */
    function cleanupHandler(player: alt.Player) {
        if (playerHandlers.has(player.id)) {
            alt.offClient('choice:select', playerHandlers.get(player.id)!);
            playerHandlers.delete(player.id);
        }
        
        if (playerTimeouts.has(player.id)) {
            clearTimeout(playerTimeouts.get(player.id)!);
            playerTimeouts.delete(player.id);
        }
    }

    return {
        showChoice,
        showSimpleChoice,
        cleanupHandler
    };
}

const choiceApi = useChoiceApi();
Rebar.useApi().register('choice-api', choiceApi);

// ========== 使用示例 ==========

// 获取消息器用于注册命令
const messenger = Rebar.messenger.useMessenger();

// 演示命令：/choice-demo
messenger.commands.register({
    name: 'choice-demo',
    desc: '演示Choice API的使用',
    options: { permissions: ['admin'] },
    callback: async (player: alt.Player, ...args: string[]) => {
        try {
            console.log('演示Choice API的使用...');
            
            // 示例1：简单选择
            const simpleResult = await choiceApi.showSimpleChoice(player, '选择你的行动', [
                '前往商店',
                '返回家中',
                '探索城市'
            ]);
            
            console.log('简单选择结果:', simpleResult);
            messenger.message.send(player, { content: `简单选择结果: ${simpleResult}`, type: 'info' });
            
            // 示例2：高级选择配置
            const advancedConfig: ChoiceConfig = {
                title: '购买物品',
                description: '选择你要购买的物品类型',
                options: [
                    {
                        id: 'weapon',
                        text: '武器',
                        value: 'weapon_category',
                        icon: 'fas fa-gun',
                        color: 'error'
                    },
                    {
                        id: 'food',
                        text: '食物',
                        value: 'food_category',
                        icon: 'fas fa-hamburger',
                        color: 'success'
                    },
                    {
                        id: 'vehicle',
                        text: '载具',
                        value: 'vehicle_category',
                        icon: 'fas fa-car',
                        color: 'primary'
                    },
                    {
                        id: 'clothes',
                        text: '服装',
                        value: 'clothes_category',
                        icon: 'fas fa-tshirt',
                        color: 'info'
                    }
                ],
                allowCancel: true,
                cancelText: '不购买',
                timeout: 10000 // 10秒超时
            };
            
            const advancedResult = await choiceApi.showChoice(player, advancedConfig);
            console.log('高级选择结果:', advancedResult);
            
            // 根据选择结果执行不同的操作
            if (advancedResult === 'weapon_category') {
                console.log('玩家选择了武器类别');
                messenger.message.send(player, { content: '你选择了武器类别', type: 'info' });
            } else if (advancedResult === 'food_category') {
                console.log('玩家选择了食物类别');
                messenger.message.send(player, { content: '你选择了食物类别', type: 'info' });
            } else if (advancedResult === 'vehicle_category') {
                console.log('玩家选择了载具类别');
                messenger.message.send(player, { content: '你选择了载具类别', type: 'info' });
            } else if (advancedResult === 'clothes_category') {
                console.log('玩家选择了服装类别');
                messenger.message.send(player, { content: '你选择了服装类别', type: 'info' });
            } else if (advancedResult === null) {
                console.log('玩家取消了选择或超时');
                messenger.message.send(player, { content: '你取消了选择或超时', type: 'warning' });
            }
            
        } catch (error) {
            console.error('Choice API 演示出错:', error);
            messenger.message.send(player, { content: 'Choice API 演示出错', type: 'alert' });
        }
    },
});

// 页面打开时禁用控制
alt.on('rebar:playerPageOpened', (player: alt.Player, page: PageNames) => {
    if (page === 'choicebar') {
        Rebar.player.useWorld(player).disableControls();
    }
});

// 页面关闭时启用控制并清理
alt.on('rebar:playerPageClosed', (player: alt.Player, page: PageNames) => {
    if (page === 'choicebar') {
        Rebar.player.useWorld(player).enableControls();
        choiceApi.cleanupHandler(player);
    }
});

// 玩家断开连接时清理
alt.on('playerDisconnect', (player: alt.Player) => {
    choiceApi.cleanupHandler(player);
});

