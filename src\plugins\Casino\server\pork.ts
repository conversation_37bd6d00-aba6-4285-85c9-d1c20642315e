import * as alt from 'alt-server';
import { useRebar } from '@Server/index.js';
import { PageNames } from '@Shared/webview/index.js';

const Rebar = useRebar();
const notifyapi = await Rebar.useApi().getAsync('notify-api')
const promptbarapi = await Rebar.useApi().getAsync('promptbar-api')
const inventoryapi = await Rebar.useApi().getAsync('inventory-api')

// 导入牌型比较器
import { evaluate7Cards } from '../shared/porkCompare.js';

// 游戏类型定义
interface Card {
    suit: 'H' | 'D' | 'C' | 'S';
    rank: 'A' | 'K' | 'Q' | 'J' | 'T' | '9' | '8' | '7' | '6' | '5' | '4' | '3' | '2';
}

interface PokerPlayer {
    id: number;
    player: alt.Player;
    name: string;
    chips: number;
    cards: Card[];
    currentBet: number;
    totalBet: number;
    folded: boolean;
    allIn: boolean;
    isDealer: boolean;
    isSmallBlind: boolean;
    isBigBlind: boolean;
    hasActed: boolean;
    seatPosition: number;
}

interface PokerRoom {
    id: string;
    players: Map<number, PokerPlayer>;
    maxPlayers: number;
    smallBlind: number;
    bigBlind: number;
    pot: number;
    currentBet: number;
    minRaise: number;
    phase: 'waiting' | 'preflop' | 'flop' | 'turn' | 'river' | 'showdown';
    communityCards: Card[];
    deck: Card[];
    dealerPosition: number;
    currentPlayerPosition: number;
    round: number;
    maxRounds: number;
    gameStarted: boolean;
    actionTimer?: NodeJS.Timeout;
    actionTimeLeft: number;
}

// 房间管理
const pokerRooms = new Map<string, PokerRoom>();
const playerToRoom = new Map<number, string>();

// 德州扑克位置
const vectorarray = [
    new alt.Vector3(1143.6790, 265.5296, -52.8520),
    new alt.Vector3(1142.5054, 265.0681, -52.8520),
    new alt.Vector3(1142.0307, 263.8549, -52.8520),
    new alt.Vector3(1145.9736, 259.9648, -52.8520),
    new alt.Vector3(1147.1604, 260.4131, -52.8520),
    new alt.Vector3(1147.6219, 261.5868, -52.8520),
]

// 创建一副牌
function createDeck(): Card[] {
    const suits: Card['suit'][] = ['H', 'D', 'C', 'S'];
    const ranks: Card['rank'][] = ['A', 'K', 'Q', 'J', 'T', '9', '8', '7', '6', '5', '4', '3', '2'];
    const deck: Card[] = [];
    
    suits.forEach(suit => {
        ranks.forEach(rank => {
            deck.push({ suit, rank });
        });
    });
    
    return shuffleDeck(deck);
}

// 洗牌
function shuffleDeck(deck: Card[]): Card[] {
    const shuffled = [...deck];
    for (let i = shuffled.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
    }
    return shuffled;
}

// 生成房间ID
function generateRoomId(): string {
    return Math.random().toString(36).substr(2, 6).toUpperCase();
}

// 创建交互点
vectorarray.forEach((vector, index) => {
    const interaction = Rebar.controllers.useInteraction(new alt.ColshapeCylinder(vector.x, vector.y, vector.z , 0.8, 2), 'player');
    
    interaction.onEnter((player) => {
        promptbarapi.showPromptBar(player, '德州扑克');
    });
    
    interaction.onLeave((player) => {
        promptbarapi.hidePromptBar(player);
    });
    
    interaction.on(async (player) => {
        await openPokerGame(player);
    });

    interaction.addMarker({
        pos: vector,
        color: new alt.RGBA(0, 255, 0, 75),
        type: 1,
        scale: new alt.Vector3(1, 1, 0.2),
    })
});

// 打开德州扑克游戏
async function openPokerGame(player: alt.Player) {
    try {
        // 显示webview
        Rebar.player.useWebview(player).show('pork', 'page', true);

        // 等待页面加载完成
        let ready = await Rebar.player.useWebview(player).isReady('pork', 'page')
        while (!ready) {
            await alt.Utils.wait(100);
            ready = await Rebar.player.useWebview(player).isReady('pork', 'page')
        }

        // 隐藏提示条
        promptbarapi.hidePromptBar(player);

        // 发送初始游戏数据
        Rebar.player.useWebview(player).emit('poker:gameData', {
            playerId: player.id.toString(),
            stats: { wins: 0, winRate: 0 } // 可以从数据库获取
        });

        // 发送房间列表
        sendRoomsList(player);

    } catch (error) {
        console.error('打开德州扑克游戏失败:', error);
        notifyapi.shownotify(player, '打开游戏失败', 'error');
    }
}

// 发送房间列表
function sendRoomsList(player: alt.Player) {
    const rooms = Array.from(pokerRooms.values()).map(room => ({
        id: room.id,
        players: room.players.size,
        maxPlayers: room.maxPlayers,
        smallBlind: room.smallBlind,
        bigBlind: room.bigBlind
    }));
    
    Rebar.player.useWebview(player).emit('poker:roomsList', rooms);
}

// 创建房间
alt.onClient('poker:createRoom', async (player: alt.Player, settings: { smallBlind: number, maxPlayers: number }) => {
    try {
        // 检查玩家是否已在房间中
        if (playerToRoom.has(player.id)) {
            notifyapi.shownotify(player, '您已在房间中', 'error');
            return;
        }

        // 验证设置
        if (settings.smallBlind < 50 || settings.smallBlind > 1000) {
            notifyapi.shownotify(player, '小盲注必须在50-1000之间', 'error');
            return;
        }

        if (settings.maxPlayers < 2 || settings.maxPlayers > 10) {
            notifyapi.shownotify(player, '最大玩家数必须在2-10之间', 'error');
            return;
        }

        // 检查玩家筹码
        const playerChips = await inventoryapi.getitemquantity('筹码', { player: player });
        if (playerChips < settings.smallBlind * 20) {
            notifyapi.shownotify(player, `至少需要${settings.smallBlind * 20}筹码才能创建房间`, 'error');
            return;
        }

        // 创建房间
        const roomId = generateRoomId();
        const room: PokerRoom = {
            id: roomId,
            players: new Map(),
            maxPlayers: settings.maxPlayers,
            smallBlind: settings.smallBlind,
            bigBlind: settings.smallBlind * 2,
            pot: 0,
            currentBet: 0,
            minRaise: settings.smallBlind * 2,
            phase: 'waiting',
            communityCards: [],
            deck: [],
            dealerPosition: 0,
            currentPlayerPosition: 0,
            round: 1,
            maxRounds: 10,
            gameStarted: false,
            actionTimeLeft: 30
        };

        pokerRooms.set(roomId, room);
        
        // 加入房间
        await joinRoomInternal(player, roomId);
        
        notifyapi.shownotify(player, `房间${roomId}创建成功`, 'success');

    } catch (error) {
        console.error('创建房间失败:', error);
        notifyapi.shownotify(player, '创建房间失败', 'error');
    }
});

// 加入房间
alt.onClient('poker:joinRoom', async (player: alt.Player, data: { roomId: string }) => {
    try {
        await joinRoomInternal(player, data.roomId);
    } catch (error) {
        console.error('加入房间失败:', error);
        notifyapi.shownotify(player, '加入房间失败', 'error');
    }
});

// 内部加入房间逻辑
async function joinRoomInternal(player: alt.Player, roomId: string) {
    // 检查玩家是否已在房间中
    if (playerToRoom.has(player.id)) {
        notifyapi.shownotify(player, '您已在房间中', 'error');
        return;
    }

    const room = pokerRooms.get(roomId);
    if (!room) {
        notifyapi.shownotify(player, '房间不存在', 'error');
        return;
    }

    if (room.players.size >= room.maxPlayers) {
        notifyapi.shownotify(player, '房间已满', 'error');
        return;
    }

    // 检查玩家筹码
    const playerChips = await inventoryapi.getitemquantity('筹码', { player: player });
    if (playerChips < room.smallBlind * 20) {
        notifyapi.shownotify(player, `至少需要${room.smallBlind * 20}筹码才能加入房间`, 'error');
        return;
    }

    // 创建玩家对象
    const pokerPlayer: PokerPlayer = {
        id: player.id,
        player: player,
        name: player.name || `玩家${player.id}`,
        chips: playerChips,
        cards: [],
        currentBet: 0,
        totalBet: 0,
        folded: false,
        allIn: false,
        isDealer: room.players.size === 0, // 第一个玩家是庄家
        isSmallBlind: false,
        isBigBlind: false,
        hasActed: false,
        seatPosition: room.players.size
    };

    // 加入房间
    room.players.set(player.id, pokerPlayer);
    playerToRoom.set(player.id, roomId);

    // 发送加入成功消息
    const playersData = Array.from(room.players.values()).map(p => ({
        id: p.id.toString(),
        name: p.name,
        chips: p.chips,
        cards: p.id === player.id ? p.cards : [],
        currentBet: p.currentBet,
        folded: p.folded,
        allIn: p.allIn,
        isDealer: p.isDealer,
        isSmallBlind: p.isSmallBlind,
        isBigBlind: p.isBigBlind
    }));

    Rebar.player.useWebview(player).emit('poker:joinedRoom', {
        roomId: roomId,
        players: playersData
    });

    // 通知房间内其他玩家
    broadcastToRoom(room, 'poker:playerJoined', {
        player: {
            id: player.id.toString(),
            name: pokerPlayer.name,
            chips: pokerPlayer.chips
        }
    }, player.id);

    // 如果人数够了，开始游戏
    if (room.players.size >= 2 && !room.gameStarted) {
        setTimeout(() => startNewRound(room), 3000);
    }

    notifyapi.shownotify(player, `成功加入房间${roomId}`, 'success');
}

// 离开房间
alt.onClient('poker:leaveRoom', (player: alt.Player) => {
    leaveRoomInternal(player);
});

// 内部离开房间逻辑
function leaveRoomInternal(player: alt.Player) {
    const roomId = playerToRoom.get(player.id);
    if (!roomId) return;

    const room = pokerRooms.get(roomId);
    if (!room) return;

    // 如果游戏进行中且玩家还有筹码，自动弃牌
    const pokerPlayer = room.players.get(player.id);
    if (pokerPlayer && room.gameStarted && !pokerPlayer.folded) {
        pokerPlayer.folded = true;
        checkRoundEnd(room);
    }

    // 移除玩家
    room.players.delete(player.id);
    playerToRoom.delete(player.id);

    // 通知其他玩家
    broadcastToRoom(room, 'poker:playerLeft', {
        playerId: player.id.toString()
    });

    // 如果房间空了，删除房间
    if (room.players.size === 0) {
        if (room.actionTimer) {
            clearTimeout(room.actionTimer);
        }
        pokerRooms.delete(roomId);
    }
}

// 开始新一轮游戏
function startNewRound(room: PokerRoom) {
    if (room.players.size < 2) return;

    // 重置玩家状态
    room.players.forEach(player => {
        player.cards = [];
        player.currentBet = 0;
        player.totalBet = 0;
        player.folded = false;
        player.allIn = false;
        player.hasActed = false;
        player.isSmallBlind = false;
        player.isBigBlind = false;
    });

    // 重置游戏状态
    room.deck = createDeck();
    room.communityCards = [];
    room.pot = 0;
    room.currentBet = 0;
    room.minRaise = room.bigBlind;
    room.phase = 'preflop';
    room.gameStarted = true;

    // 设置盲注位置
    const playerArray = Array.from(room.players.values());
    if (playerArray.length === 2) {
        // heads-up: 庄家是小盲
        playerArray[room.dealerPosition].isSmallBlind = true;
        playerArray[room.dealerPosition].isDealer = true;
        playerArray[(room.dealerPosition + 1) % playerArray.length].isBigBlind = true;
    } else {
        // 多人游戏
        playerArray[room.dealerPosition].isDealer = true;
        playerArray[(room.dealerPosition + 1) % playerArray.length].isSmallBlind = true;
        playerArray[(room.dealerPosition + 2) % playerArray.length].isBigBlind = true;
    }

    // 收取盲注
    collectBlinds(room);

    // 发牌
    dealCards(room);

    // 开始行动
    setNextPlayer(room);

    // 广播游戏开始
    broadcastGameState(room);
}

// 收取盲注
async function collectBlinds(room: PokerRoom) {
    try {
        for (const player of room.players.values()) {
            if (player.isSmallBlind) {
                const blindAmount = Math.min(room.smallBlind, player.chips);
                
                // 验证盲注金额
                if (!Number.isFinite(blindAmount) || blindAmount <= 0) {
                    console.error(`德州扑克：无效的小盲注金额 - 玩家: ${player.name}, 金额: ${blindAmount}`);
                    continue;
                }

                // 原子性操作：扣除筹码
                const [success, error] = await inventoryapi.subItem('筹码', blindAmount, { player: player.player });
                if (!success) {
                    console.error(`德州扑克：小盲注扣除失败 - 玩家: ${player.name}, 金额: ${blindAmount}, 错误: ${error}`);
                    continue;
                }

                player.currentBet = blindAmount;
                player.totalBet = blindAmount;
                player.chips -= blindAmount;
                room.pot += blindAmount;
                
                console.log(`德州扑克：收取小盲注 - 玩家: ${player.name}, 金额: ${blindAmount}`);
                
            } else if (player.isBigBlind) {
                const blindAmount = Math.min(room.bigBlind, player.chips);
                
                // 验证盲注金额
                if (!Number.isFinite(blindAmount) || blindAmount <= 0) {
                    console.error(`德州扑克：无效的大盲注金额 - 玩家: ${player.name}, 金额: ${blindAmount}`);
                    continue;
                }

                // 原子性操作：扣除筹码
                const [success, error] = await inventoryapi.subItem('筹码', blindAmount, { player: player.player });
                if (!success) {
                    console.error(`德州扑克：大盲注扣除失败 - 玩家: ${player.name}, 金额: ${blindAmount}, 错误: ${error}`);
                    continue;
                }

                player.currentBet = blindAmount;
                player.totalBet = blindAmount;
                player.chips -= blindAmount;
                room.pot += blindAmount;
                room.currentBet = blindAmount;
                
                console.log(`德州扑克：收取大盲注 - 玩家: ${player.name}, 金额: ${blindAmount}`);
            }
        }
    } catch (error) {
        console.error('德州扑克收取盲注过程中发生错误:', error);
    }
}

// 发牌
function dealCards(room: PokerRoom) {
    // 给每个玩家发2张牌
    room.players.forEach(player => {
        player.cards = [room.deck.pop()!, room.deck.pop()!];
        
        // 发送玩家手牌
        Rebar.player.useWebview(player.player).emit('poker:dealCards', {
            cards: player.cards
        });
    });
    
    // 发牌后立即广播游戏状态，确保前端能正确显示所有信息
    setTimeout(() => {
        broadcastGameState(room);
    }, 100);
}

// 设置下一个行动的玩家
function setNextPlayer(room: PokerRoom) {
    const playerArray = Array.from(room.players.values()).filter(p => !p.folded && !p.allIn);
    
    if (playerArray.length <= 1) {
        endRound(room);
        return;
    }

    // 找到下一个需要行动的玩家
    let startPosition = room.currentPlayerPosition;
    if (room.phase === 'preflop') {
        // 翻牌前从大盲注后一位开始
        const bigBlindPlayer = Array.from(room.players.values()).find(p => p.isBigBlind);
        if (bigBlindPlayer) {
            startPosition = (bigBlindPlayer.seatPosition + 1) % room.players.size;
        }
    }

    for (let i = 0; i < room.players.size; i++) {
        const pos = (startPosition + i) % room.players.size;
        const playerAtPos = playerArray.find(p => p.seatPosition === pos);
        
        if (playerAtPos && !playerAtPos.folded && !playerAtPos.allIn) {
            // 检查是否需要行动
            if (!playerAtPos.hasActed || playerAtPos.currentBet < room.currentBet) {
                room.currentPlayerPosition = pos;
                startActionTimer(room, playerAtPos);
                return;
            }
        }
    }

    // 如果所有人都行动过，进入下一阶段
    nextPhase(room);
}

// 开始行动计时器
function startActionTimer(room: PokerRoom, player: PokerPlayer) {
    if (room.actionTimer) {
        clearTimeout(room.actionTimer);
    }

    room.actionTimeLeft = 30;
    
    // 广播当前玩家
    broadcastToRoom(room, 'poker:gameUpdate', {
        currentPlayerId: player.id.toString(),
        actionTimer: room.actionTimeLeft
    });

    // 启动计时器
    const timer = setInterval(() => {
        room.actionTimeLeft--;
        
        broadcastToRoom(room, 'poker:actionTimer', {
            timeLeft: room.actionTimeLeft
        });

        if (room.actionTimeLeft <= 0) {
            clearInterval(timer);
            // 自动弃牌
            player.folded = true;
            notifyapi.shownotify(player.player, '超时自动弃牌', 'error');
            setNextPlayer(room);
        }
    }, 1000);

    room.actionTimer = timer as any;
}

// 进入下一阶段
function nextPhase(room: PokerRoom) {
    // 重置玩家行动状态
    room.players.forEach(player => {
        player.hasActed = false;
        player.currentBet = 0;
    });
    
    room.currentBet = 0;

    switch (room.phase) {
        case 'preflop':
            room.phase = 'flop';
            // 发3张公共牌
            room.communityCards = [room.deck.pop()!, room.deck.pop()!, room.deck.pop()!];
            break;
        case 'flop':
            room.phase = 'turn';
            // 发1张公共牌
            room.communityCards.push(room.deck.pop()!);
            break;
        case 'turn':
            room.phase = 'river';
            // 发1张公共牌
            room.communityCards.push(room.deck.pop()!);
            break;
        case 'river':
            room.phase = 'showdown';
            endRound(room);
            return;
    }

    // 广播公共牌
    broadcastToRoom(room, 'poker:communityCards', {
        cards: room.communityCards
    });

    // 从庄家后开始行动
    const dealerPlayer = Array.from(room.players.values()).find(p => p.isDealer);
    if (dealerPlayer) {
        room.currentPlayerPosition = (dealerPlayer.seatPosition + 1) % room.players.size;
    }

    setNextPlayer(room);
    broadcastGameState(room);
}

// 广播游戏状态
function broadcastGameState(room: PokerRoom) {
    const gameData = {
        pot: room.pot,
        currentBet: room.currentBet,
        minRaise: room.minRaise,
        phase: room.phase,
        communityCards: room.communityCards,
        players: Array.from(room.players.values()).map(p => ({
            id: p.id.toString(),
            name: p.name,
            chips: p.chips,
            currentBet: p.currentBet,
            folded: p.folded,
            allIn: p.allIn,
            isDealer: p.isDealer,
            isSmallBlind: p.isSmallBlind,
            isBigBlind: p.isBigBlind
        }))
    };

    broadcastToRoom(room, 'poker:gameUpdate', gameData);
}

// 广播消息到房间
function broadcastToRoom(room: PokerRoom, event: string, data: any, excludePlayerId?: number) {
    room.players.forEach(player => {
        if (excludePlayerId && player.id === excludePlayerId) return;
        Rebar.player.useWebview(player.player).emit(event, data);
    });
}

// 玩家行动处理
alt.onClient('poker:fold', (player: alt.Player) => {
    const room = getRoomByPlayer(player.id);
    if (!room) return;

    const pokerPlayer = room.players.get(player.id);
    if (!pokerPlayer || pokerPlayer.folded) return;

    pokerPlayer.folded = true;
    pokerPlayer.hasActed = true;

    if (room.actionTimer) {
        clearTimeout(room.actionTimer);
    }

    broadcastToRoom(room, 'poker:playerAction', {
        playerId: player.id.toString(),
        action: 'fold',
        playerState: {
            folded: true,
            hasActed: true
        },
        pot: room.pot,
        currentBet: room.currentBet
    });

    checkRoundEnd(room);
});

alt.onClient('poker:check', (player: alt.Player) => {
    const room = getRoomByPlayer(player.id);
    if (!room) return;

    const pokerPlayer = room.players.get(player.id);
    if (!pokerPlayer || pokerPlayer.folded || pokerPlayer.currentBet !== room.currentBet) return;

    pokerPlayer.hasActed = true;

    if (room.actionTimer) {
        clearTimeout(room.actionTimer);
    }

    broadcastToRoom(room, 'poker:playerAction', {
        playerId: player.id.toString(),
        action: 'check',
        playerState: {
            hasActed: true
        },
        pot: room.pot,
        currentBet: room.currentBet
    });

    setNextPlayer(room);
});

alt.onClient('poker:call', async (player: alt.Player) => {
    try {
        const room = getRoomByPlayer(player.id);
        if (!room) return;

        const pokerPlayer = room.players.get(player.id);
        if (!pokerPlayer || pokerPlayer.folded) return;

        const callAmount = Math.min(room.currentBet - pokerPlayer.currentBet, pokerPlayer.chips);
        
        // 验证跟注金额
        if (!Number.isFinite(callAmount) || callAmount <= 0) {
            console.error(`德州扑克：无效的跟注金额 - 玩家: ${pokerPlayer.name}, 金额: ${callAmount}`);
            return;
        }

        // 原子性操作：扣除筹码
        const [success, error] = await inventoryapi.subItem('筹码', callAmount, { player: player });
        if (!success) {
            console.error(`德州扑克：跟注扣除筹码失败 - 玩家: ${pokerPlayer.name}, 金额: ${callAmount}, 错误: ${error}`);
            notifyapi.shownotify(player, '筹码扣除失败', 'error');
            return;
        }
        
        pokerPlayer.chips -= callAmount;
        pokerPlayer.currentBet += callAmount;
        pokerPlayer.totalBet += callAmount;
        pokerPlayer.hasActed = true;
        room.pot += callAmount;

        if (pokerPlayer.chips === 0) {
            pokerPlayer.allIn = true;
        }

        if (room.actionTimer) {
            clearTimeout(room.actionTimer);
        }

        console.log(`德州扑克：跟注 - 玩家: ${pokerPlayer.name}, 金额: ${callAmount}`);

        broadcastToRoom(room, 'poker:playerAction', {
            playerId: player.id.toString(),
            action: 'call',
            amount: callAmount,
            playerState: {
                chips: pokerPlayer.chips,
                currentBet: pokerPlayer.currentBet,
                allIn: pokerPlayer.allIn,
                hasActed: true
            },
            pot: room.pot,
            currentBet: room.currentBet
        });

        setNextPlayer(room);

    } catch (error) {
        console.error('德州扑克跟注过程中发生错误:', error);
        notifyapi.shownotify(player, '操作失败', 'error');
    }
});

alt.onClient('poker:raise', async (player: alt.Player, data: { amount: number }) => {
    const room = getRoomByPlayer(player.id);
    if (!room) return;

    const pokerPlayer = room.players.get(player.id);
    if (!pokerPlayer || pokerPlayer.folded) return;

    const raiseAmount = Math.min(data.amount, pokerPlayer.chips);
    if (raiseAmount < room.minRaise) return;

    // 扣除筹码
    await inventoryapi.subItem('筹码', raiseAmount, { player: player });

    pokerPlayer.chips -= raiseAmount;
    pokerPlayer.currentBet += raiseAmount;
    pokerPlayer.totalBet += raiseAmount;
    pokerPlayer.hasActed = true;
    room.pot += raiseAmount;
    room.currentBet = pokerPlayer.currentBet;
    room.minRaise = raiseAmount;

    // 重置其他玩家的行动状态
    room.players.forEach(p => {
        if (p.id !== player.id && !p.folded && !p.allIn) {
            p.hasActed = false;
        }
    });

    if (pokerPlayer.chips === 0) {
        pokerPlayer.allIn = true;
    }

    if (room.actionTimer) {
        clearTimeout(room.actionTimer);
    }

    broadcastToRoom(room, 'poker:playerAction', {
        playerId: player.id.toString(),
        action: 'raise',
        amount: raiseAmount,
        playerState: {
            chips: pokerPlayer.chips,
            currentBet: pokerPlayer.currentBet,
            allIn: pokerPlayer.allIn,
            hasActed: true
        },
        pot: room.pot,
        currentBet: room.currentBet
    });

    setNextPlayer(room);
});

alt.onClient('poker:allIn', async (player: alt.Player) => {
    const room = getRoomByPlayer(player.id);
    if (!room) return;

    const pokerPlayer = room.players.get(player.id);
    if (!pokerPlayer || pokerPlayer.folded || pokerPlayer.chips === 0) return;

    const allInAmount = pokerPlayer.chips;

    // 扣除筹码
    await inventoryapi.subItem('筹码', allInAmount, { player: player });

    pokerPlayer.currentBet += allInAmount;
    pokerPlayer.totalBet += allInAmount;
    pokerPlayer.chips = 0;
    pokerPlayer.allIn = true;
    pokerPlayer.hasActed = true;
    room.pot += allInAmount;

    if (pokerPlayer.currentBet > room.currentBet) {
        room.currentBet = pokerPlayer.currentBet;
        // 重置其他玩家的行动状态
        room.players.forEach(p => {
            if (p.id !== player.id && !p.folded && !p.allIn) {
                p.hasActed = false;
            }
        });
    }

    if (room.actionTimer) {
        clearTimeout(room.actionTimer);
    }

    broadcastToRoom(room, 'poker:playerAction', {
        playerId: player.id.toString(),
        action: 'allin',
        amount: allInAmount,
        playerState: {
            chips: 0,
            currentBet: pokerPlayer.currentBet,
            allIn: true,
            hasActed: true
        },
        pot: room.pot,
        currentBet: room.currentBet
    });

    setNextPlayer(room);
});

// 检查回合是否结束
function checkRoundEnd(room: PokerRoom) {
    const activePlayers = Array.from(room.players.values()).filter(p => !p.folded);
    
    if (activePlayers.length === 1) {
        // 只剩一个玩家，直接结束
        endRound(room);
        return;
    }

    setNextPlayer(room);
}

// 结束回合
async function endRound(room: PokerRoom) {
    if (room.actionTimer) {
        clearTimeout(room.actionTimer);
    }

    const activePlayers = Array.from(room.players.values()).filter(p => !p.folded);
    
    if (activePlayers.length === 1) {
        // 只有一个玩家，直接获胜
        const winner = activePlayers[0];
        
        // 验证奖池金额
        if (!Number.isFinite(room.pot) || room.pot <= 0) {
            console.error(`德州扑克：无效的奖池金额 - 房间: ${room.id}, 金额: ${room.pot}`);
            room.pot = 0;
        } else {
            await inventoryapi.addItem('筹码', room.pot, { player: winner.player });
            winner.chips += room.pot;
            console.log(`德州扑克：获胜奖励 - 玩家: ${winner.name}, 奖励: ${room.pot}筹码`);
        }

        broadcastToRoom(room, 'poker:gameResult', {
            winners: [{
                playerId: winner.id.toString(),
                playerName: winner.name,
                handName: '对手弃牌',
                amount: room.pot
            }],
            tournamentOver: false
        });
    } else {
        // 多个玩家，比较牌型
        await compareHands(room, activePlayers);
    }

    // 准备下一轮
    room.dealerPosition = (room.dealerPosition + 1) % room.players.size;
    room.round++;
    room.gameStarted = false;
    
    // 重置奖池
    room.pot = 0;

    // 移除自动开始下一轮的逻辑，改由玩家手动控制
    // 这样避免重复开始游戏导致重复扣筹码
    console.log(`德州扑克：第${room.round - 1}轮结束，等待玩家开始下一轮`);
}

// 比较牌型
async function compareHands(room: PokerRoom, activePlayers: PokerPlayer[]) {
    const handResults = activePlayers.map(player => {
        const all7Cards = [...player.cards, ...room.communityCards];
        const handRank = evaluate7Cards(all7Cards);
        return {
            player,
            handRank,
            all7Cards
        };
    });

    // 按牌型排序
    handResults.sort((a, b) => {
        if (a.handRank.rank !== b.handRank.rank) {
            return b.handRank.rank - a.handRank.rank;
        }
        // 同牌型比较values
        for (let i = 0; i < a.handRank.values.length; i++) {
            if (a.handRank.values[i] !== b.handRank.values[i]) {
                return b.handRank.values[i] - a.handRank.values[i];
            }
        }
        return 0;
    });

    // 分配奖池
    const winners = [handResults[0]];
    let i = 1;
    while (i < handResults.length && compareHandResults(handResults[0].handRank, handResults[i].handRank) === 0) {
        winners.push(handResults[i]);
        i++;
    }

    const winAmount = Math.floor(room.pot / winners.length);
    const resultData = [];

    for (const winner of winners) {
        // 验证奖励金额
        if (!Number.isFinite(winAmount) || winAmount <= 0 || winAmount > 10000000) {
            console.error(`德州扑克：无效的奖励金额 - 玩家: ${winner.player.name}, 金额: ${winAmount}`);
            continue;
        }

        // 安全地发放奖励
        const [addSuccess, addError] = await inventoryapi.addItem('筹码', winAmount, { player: winner.player.player });
        if (!addSuccess) {
            console.error(`德州扑克：奖励发放失败 - 玩家: ${winner.player.name}, 金额: ${winAmount}, 错误: ${addError}`);
            notifyapi.shownotify(winner.player.player, '奖励发放失败，请联系管理员', 'error');
            continue;
        }

        winner.player.chips += winAmount;
        console.log(`德州扑克：获胜奖励 - 玩家: ${winner.player.name}, 牌型: ${winner.handRank.name}, 奖励: ${winAmount}筹码`);
        
        resultData.push({
            playerId: winner.player.id.toString(),
            playerName: winner.player.name,
            handName: winner.handRank.name,
            amount: winAmount
        });
    }

    broadcastToRoom(room, 'poker:gameResult', {
        winners: resultData,
        tournamentOver: false
    });
}

// 比较两个牌型结果
function compareHandResults(handA: any, handB: any): number {
    if (handA.rank !== handB.rank) {
        return handB.rank - handA.rank;
    }
    for (let i = 0; i < handA.values.length; i++) {
        if (handA.values[i] !== handB.values[i]) {
            return handB.values[i] - handA.values[i];
        }
    }
    return 0;
}

// 获取玩家所在房间
function getRoomByPlayer(playerId: number): PokerRoom | undefined {
    const roomId = playerToRoom.get(playerId);
    return roomId ? pokerRooms.get(roomId) : undefined;
}

// 其他事件处理
alt.onClient('poker:getRooms', (player: alt.Player) => {
    sendRoomsList(player);
});

alt.onClient('poker:init', (player: alt.Player) => {
    sendRoomsList(player);
});

alt.onClient('poker:nextRound', (player: alt.Player) => {
    const room = getRoomByPlayer(player.id);
    if (!room) {
        notifyapi.shownotify(player, '未找到房间', 'error');
        return;
    }

    // 检查游戏状态，防止重复开始
    if (room.gameStarted) {
        notifyapi.shownotify(player, '游戏已在进行中', 'warning');
        return;
    }

    // 检查房间人数
    if (room.players.size < 2) {
        notifyapi.shownotify(player, '至少需要2个玩家才能开始游戏', 'warning');
        return;
    }

    console.log(`德州扑克：玩家 ${player.name} 请求开始新一轮`);
    startNewRound(room);
});

alt.onClient('poker:close', (player: alt.Player) => {
    leaveRoomInternal(player);
    Rebar.player.useWebview(player).hide('pork');
    Rebar.player.useWorld(player).enableControls();
});

// 断线处理
alt.on('real:playerDisconnect', (player: alt.Player) => {
    leaveRoomInternal(player);
});


alt.on('rebar:playerPageOpened', (player: alt.Player, page: PageNames) => {
    if (page === 'pork') {
        Rebar.player.useWorld(player).disableControls();
    }
});

alt.on('rebar:playerPageClosed', (player: alt.Player, page: PageNames) => {
    if (page === 'pork') {
        Rebar.player.useWorld(player).enableControls();
    }
});








