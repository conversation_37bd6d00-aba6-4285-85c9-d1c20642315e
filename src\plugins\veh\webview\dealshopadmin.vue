<template>
  <div class="admin-container">
    <div class="admin-panel">
      <!-- 页面头部 -->
      <div class="page-header">
        <div class="header-content">
          <div class="title-section">
            <div class="header-icon">
              <i class="fas fa-car-side"></i>
            </div>
            <div class="title-info">
              <h1>车辆商店管理</h1>
              <span class="subtitle">Vehicle Shop Management</span>
            </div>
          </div>
          <div class="status-indicator">
            <span class="status-badge">{{ vehicles.length }} 车辆</span>
          </div>
        </div>
      </div>

      <div class="content-area">
        <!-- 搜索和操作区域 -->
        <div class="search-section">
          <div class="search-card">
            <div class="search-header">
              <div class="header-title">
                <i class="fas fa-search"></i>
                <h3>搜索车辆</h3>
              </div>
            </div>
            <div class="search-content">
              <div class="search-box">
                <i class="fas fa-search"></i>
                <input 
                  v-model="searchTerm" 
                  placeholder="输入车辆名称..." 
                  class="search-input"
                >
              </div>
              <div class="filter-section">
                <select v-model="filterType" class="filter-select">
                  <option value="">所有类型</option>
                  <option v-for="type in vehicleTypes" :key="type" :value="type">{{ type }}</option>
                </select>
                <select v-model="sortOrder" class="filter-select">
                  <option value="name">按名称排序</option>
                  <option value="price">按价格排序</option>
                  <option value="type">按类型排序</option>
                </select>
              </div>
            </div>
          </div>
        </div>

        <!-- 新建车辆区域 -->
        <div class="add-vehicle-section">
          <div class="add-card">
            <div class="card-header">
              <div class="header-title">
                <i class="fas fa-plus-circle"></i>
                <h3>新建车辆</h3>
              </div>
              <button @click="toggleAddForm" class="toggle-btn">
                <i :class="showAddForm ? 'fas fa-chevron-up' : 'fas fa-chevron-down'"></i>
              </button>
            </div>
            <div v-if="showAddForm" class="add-form">
              <div class="form-grid">
                <div class="form-group">
                  <label>车辆名称</label>
                  <input v-model="newVehicle.name" placeholder="例如: adder" class="form-input">
                </div>
                <div class="form-group">
                  <label>车辆类型</label>
                  <select v-model="newVehicle.type" class="form-select">
                    <option value="">选择类型</option>
                    <option value="Super">超级跑车</option>
                    <option value="Sports">运动车</option>
                    <option value="Muscle">肌肉车</option>
                    <option value="Sedan">轿车</option>
                    <option value="SUV">SUV</option>
                    <option value="Coupe">双门轿跑</option>
                    <option value="Compact">紧凑型</option>
                    <option value="OffRoad">越野车</option>
                    <option value="Van">面包车</option>
                    <option value="Truck">卡车</option>
                    <option value="Motorcycle">摩托车</option>
                  </select>
                </div>
                <div class="form-group">
                  <label>价格</label>
                  <input v-model.number="newVehicle.price" type="number" min="0" placeholder="0" class="form-input">
                </div>
                <div class="form-group">
                  <label>最大速度</label>
                  <input v-model.number="newVehicle.maxSpeed" type="number" min="0" placeholder="120" class="form-input">
                </div>
                <div class="form-group">
                  <label>座位数</label>
                  <input v-model.number="newVehicle.seats" type="number" min="1" max="8" placeholder="4" class="form-input">
                </div>
                <div class="form-group">
                  <label>后备箱容量</label>
                  <input v-model.number="newVehicle.trunkCapacity" type="number" min="0" placeholder="40" class="form-input">
                </div>
                <div class="form-group">
                  <label>油箱容量</label>
                  <input v-model.number="newVehicle.fuelTank" type="number" min="0" placeholder="50" class="form-input">
                </div>
              </div>
              <div class="form-actions">
                <button @click="addVehicle" class="add-btn" :disabled="!canAddVehicle">
                  <i class="fas fa-plus"></i>
                  <span>添加车辆</span>
                </button>
                <button @click="resetForm" class="reset-btn">
                  <i class="fas fa-undo"></i>
                  <span>重置表单</span>
                </button>
              </div>
            </div>
          </div>
        </div>

        <!-- 车辆列表区域 -->
        <div class="vehicles-section">
          <div class="vehicles-card">
            <div class="card-header">
              <div class="header-title">
                <i class="fas fa-list"></i>
                <h3>车辆列表</h3>
              </div>
              <div class="count-badge">
                {{ filteredVehicles.length }} / {{ vehicles.length }}
              </div>
            </div>
            <div class="vehicles-list">
              <div v-if="filteredVehicles.length === 0" class="empty-state">
                <div class="empty-icon">
                  <i class="fas fa-inbox"></i>
                </div>
                <div class="empty-text">
                  <h3>暂无车辆</h3>
                  <p>{{ searchTerm ? '没有找到匹配的车辆' : '暂时没有车辆上架' }}</p>
                </div>
              </div>
              
              <div v-for="vehicle in filteredVehicles" :key="vehicle._id" class="vehicle-item">
                <div class="vehicle-info">
                  <div class="vehicle-icon">
                    <i class="fas fa-car"></i>
                  </div>
                  <div class="vehicle-details">
                    <div class="vehicle-name">{{ vehicle.name }}</div>
                    <div class="vehicle-meta">
                      <span class="vehicle-type">{{ vehicle.type }}</span>
                      <span class="vehicle-specs">{{ vehicle.maxSpeed }}km/h · {{ vehicle.seats }}座</span>
                    </div>
                  </div>
                </div>
                
                <div class="vehicle-stats">
                  <div class="stat-item">
                    <span class="stat-label">后备箱</span>
                    <span class="stat-value">{{ vehicle.trunkCapacity }}kg</span>
                  </div>
                  <div class="stat-item">
                    <span class="stat-label">油箱</span>
                    <span class="stat-value">{{ vehicle.fuelTank }}L</span>
                  </div>
                </div>
                
                <div class="vehicle-price">
                  <input 
                    v-model.number="vehicle.price" 
                    type="number" 
                    min="0" 
                    class="price-input"
                    @keyup.enter="updateVehicle(vehicle)"
                  >
                  <span class="price-currency">元</span>
                </div>
                
                <div class="vehicle-actions">
                  <button @click="updateVehicle(vehicle)" class="action-btn save-btn">
                    <i class="fas fa-save"></i>
                    <span>保存</span>
                  </button>
                  <button @click="removeVehicle(vehicle)" class="action-btn remove-btn">
                    <i class="fas fa-trash"></i>
                    <span>删除</span>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 底部操作区域 -->
      <div class="footer-actions">
        <button @click="close" class="close-button">
          <i class="fas fa-times"></i>
          <span>关闭</span>
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { useEvents } from '@Composables/useEvents.js';

const events = useEvents();
const vehicles = ref<any[]>([]);
const searchTerm = ref('');
const filterType = ref('');
const sortOrder = ref('name');
const showAddForm = ref(false);

// 新建车辆表单数据
const newVehicle = ref({
  name: '',
  type: '',
  price: 0,
  maxSpeed: 120,
  seats: 4,
  trunkCapacity: 40,
  fuelTank: 50
});

// 加载FontAwesome
const loadFontAwesome = () => {
  if (!document.querySelector('link[href*="font-awesome"]')) {
    const link = document.createElement('link');
    link.rel = 'stylesheet';
    link.href = 'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css';
    link.integrity = 'sha512-iecdLmaskl7CVkqkXNQ/ZH/XLlvWZOJyj7Yy7tcenmpD1ypASozpmT/E0iPtmFIB46ZmdtAc9eNBvH0H/ZpiBw==';
    link.crossOrigin = 'anonymous';
    link.referrerPolicy = 'no-referrer';
    document.head.appendChild(link);
  }
};

// 接收服务器发送的车辆列表
events.on('adminVehicleShop:list', (list: any[]) => {
  vehicles.value = list || [];
});

// 车辆类型列表
const vehicleTypes = computed(() => {
  const types = new Set(vehicles.value.map(v => v.type).filter(Boolean));
  return Array.from(types);
});

// 过滤后的车辆列表
const filteredVehicles = computed(() => {
  let filtered = vehicles.value.filter(v => {
    const matchesSearch = !searchTerm.value || 
      (v.name && v.name.toLowerCase().includes(searchTerm.value.toLowerCase()));
    const matchesType = !filterType.value || v.type === filterType.value;
    return matchesSearch && matchesType;
  });

  // 排序
  filtered.sort((a, b) => {
    switch (sortOrder.value) {
      case 'price':
        return (a.price || 0) - (b.price || 0);
      case 'type':
        return (a.type || '').localeCompare(b.type || '');
      default:
        return (a.name || '').localeCompare(b.name || '');
    }
  });

  return filtered;
});

// 是否可以添加车辆
const canAddVehicle = computed(() => {
  return newVehicle.value.name && 
         newVehicle.value.type && 
         newVehicle.value.price >= 0;
});

// 切换添加表单显示
const toggleAddForm = () => {
  showAddForm.value = !showAddForm.value;
};

// 重置表单
const resetForm = () => {
  newVehicle.value = {
    name: '',
    type: '',
    price: 0,
    maxSpeed: 120,
    seats: 4,
    trunkCapacity: 40,
    fuelTank: 50
  };
};

// 添加车辆
const addVehicle = () => {
  if (!canAddVehicle.value) return;
  
  events.emitServer('adminVehicleShop:addVehicle', newVehicle.value);
  resetForm();
  showAddForm.value = false;
};

// 删除车辆
const removeVehicle = (veh: any) => {
  if (confirm(`确定要删除车辆 "${veh.name}" 吗？`)) {
    events.emitServer('adminVehicleShop:removeVehicle', veh._id);
  }
};

// 更新车辆
const updateVehicle = (veh: any) => {
  if (veh.price < 0) {
    alert('价格不能为负数');
    return;
  }
  events.emitServer('adminVehicleShop:updateVehicle', veh._id, veh.price);
};

// 关闭页面
const close = () => {
  events.emitClient('closeAdminVehicleShop');
};

onMounted(() => {
  loadFontAwesome();
});
</script>

<style scoped>
/* CSS重置和基础样式 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

.admin-container {
  --primary-color: #64ffda;
  --primary-dark: #1de9b6;
  --primary-light: #4fd1c7;
  --card-bg: rgba(15, 23, 42, 0.95);
  --card-bg-light: rgba(30, 41, 59, 0.8);
  --text-primary: #ffffff;
  --text-secondary: rgba(255, 255, 255, 0.7);
  --text-muted: rgba(255, 255, 255, 0.5);
  --border-color: rgba(100, 255, 218, 0.2);
  --border-hover: rgba(100, 255, 218, 0.3);
  --shadow-sm: 0 0.2vh 0.8vh rgba(0, 0, 0, 0.2);
  --shadow-md: 0 0.4vh 1.6vh rgba(0, 0, 0, 0.3);
  --shadow-lg: 0 0.8vh 3.2vh rgba(0, 0, 0, 0.4);
  --shadow-primary: 0 0.4vh 2vh rgba(100, 255, 218, 0.3);

  display: flex;
  align-items: center;
  justify-content: center;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  overflow: hidden;
  font-family: "Inter", "PingFang SC", "Microsoft YaHei", sans-serif;
  position: fixed;
  z-index: 1000;
  background: rgba(20, 25, 40, 0.95);
}

.admin-panel {
  position: relative;
  background: var(--card-bg);
  border-radius: 1.2vh;
  box-shadow: var(--shadow-lg), 0 0 2vh rgba(100, 255, 218, 0.1);
  border: 0.1vh solid var(--border-color);
  overflow: hidden;
  width: 90vw;
  max-width: 1200px;
  max-height: 90vh;
  display: flex;
  flex-direction: column;
  animation: panelSlideIn 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}


/* 页面头部 */
.page-header {
  padding: 2vh 2.5vh 1.5vh;
  background: linear-gradient(135deg, rgba(100, 255, 218, 0.05) 0%, transparent 60%);
  border-bottom: 0.1vh solid var(--border-color);
  flex-shrink: 0;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.title-section {
  display: flex;
  align-items: center;
  gap: 12px;
  color: #ffffff;
}

.header-icon {
  font-size: 2.4vh;
  color: #0f172a;
  width: 4vh;
  height: 4vh;
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
  border: 0.1vh solid var(--border-color);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: var(--shadow-sm);
}

.title-info h1 {
  font-size: 2vh;
  font-weight: 600;
  margin: 0;
  color: var(--primary-color);
}

.subtitle {
  color: var(--text-secondary);
  font-size: 1.2vh;
  font-weight: 400;
  text-transform: uppercase;
  letter-spacing: 0.1em;
}

.status-indicator {
  display: flex;
  align-items: center;
}

.status-badge {
  background: linear-gradient(135deg, rgba(100, 255, 218, 0.15) 0%, rgba(100, 255, 218, 0.08) 100%);
  border: 0.1vh solid var(--border-color);
  border-radius: 1.5vh;
  padding: 0.4vh 1.2vh;
  color: var(--primary-color);
  font-size: 1.2vh;
  font-weight: 600;
  box-shadow: var(--shadow-sm);
}

/* 内容区域 */
.content-area {
  flex: 1;
  overflow-y: auto;
  padding: 1.5vh;
  display: flex;
  flex-direction: column;
  gap: 1.5vh;
  min-height: 0;
}

/* 卡片通用样式 */
.search-card,
.add-card,
.vehicles-card {
  background: var(--card-bg-light);
  border: 0.1vh solid var(--border-color);
  border-radius: 1.2vh;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: var(--shadow-sm);
}

.search-card:hover,
.add-card:hover,
.vehicles-card:hover {
  background: var(--card-bg);
  border-color: var(--border-hover);
  box-shadow: var(--shadow-primary);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5vh 2vh;
  background: linear-gradient(135deg, rgba(100, 255, 218, 0.05) 0%, transparent 60%);
  border-bottom: 0.1vh solid var(--border-color);
}

.search-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5vh 2vh;
  background: linear-gradient(135deg, rgba(100, 255, 218, 0.05) 0%, transparent 60%);
  border-bottom: 0.1vh solid var(--border-color);
}

.header-title {
  display: flex;
  align-items: center;
  gap: 10px;
}

.header-title i {
  color: var(--primary-color);
  font-size: 1.4vh;
}

.header-title h3 {
  font-size: 1.6vh;
  font-weight: 600;
  margin: 0;
  color: var(--primary-color);
}

.count-badge {
  background: linear-gradient(135deg, rgba(100, 255, 218, 0.15) 0%, rgba(100, 255, 218, 0.08) 100%);
  color: var(--primary-color);
  padding: 0.4vh 0.8vh;
  border-radius: 1vh;
  font-size: 1.2vh;
  font-weight: 600;
  box-shadow: var(--shadow-sm);
}

.toggle-btn {
  width: 3vh;
  height: 3vh;
  background: linear-gradient(135deg, rgba(100, 255, 218, 0.1) 0%, rgba(100, 255, 218, 0.05) 100%);
  border: 0.1vh solid var(--border-color);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--primary-color);
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: var(--shadow-sm);
}

.toggle-btn:hover {
  background: linear-gradient(135deg, rgba(100, 255, 218, 0.2) 0%, rgba(100, 255, 218, 0.1) 100%);
  border-color: var(--border-hover);
  transform: scale(1.05);
  box-shadow: var(--shadow-primary);
}

/* 搜索区域 */
.search-content {
  padding: 1.5vh;
  display: flex;
  flex-direction: column;
  gap: 1.5vh;
}

.search-box {
  position: relative;
  width: 100%;
}

.search-box i {
  position: absolute;
  left: 1.2vh;
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-secondary);
  font-size: 1.4vh;
}

.search-input {
  width: 100%;
  padding: 1.2vh 1.2vh 1.2vh 4vh;
  background: var(--card-bg-light);
  border: 0.1vh solid var(--border-color);
  border-radius: 1vh;
  color: var(--text-primary);
  font-size: 1.4vh;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.search-input:focus {
  border-color: var(--primary-color);
  background-color: var(--card-bg);
  outline: none;
  box-shadow: var(--shadow-primary);
}

.filter-section {
  display: flex;
  gap: 1.5vh;
}

.filter-select {
  flex: 1;
  padding: 1vh;
  background: var(--card-bg-light);
  border: 0.1vh solid var(--border-color);
  border-radius: 1vh;
  color: var(--text-primary);
  font-size: 1.4vh;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.filter-select:focus {
  border-color: var(--primary-color);
  outline: none;
  box-shadow: var(--shadow-primary);
}

.filter-select option {
  background: var(--card-bg);
  color: var(--text-primary);
}

/* 新建车辆表单 */
.add-form {
  padding: 1.5vh;
  border-top: 0.1vh solid var(--border-color);
  animation: formSlideDown 0.3s ease;
}



.form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(20vh, 1fr));
  gap: 1.5vh;
  margin-bottom: 2vh;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5vh;
}

.form-group label {
  color: var(--text-secondary);
  font-size: 1.2vh;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.form-input,
.form-select {
  padding: 1vh;
  background: var(--card-bg-light);
  border: 0.1vh solid var(--border-color);
  border-radius: 1vh;
  color: var(--text-primary);
  font-size: 1.4vh;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.form-input:focus,
.form-select:focus {
  border-color: var(--primary-color);
  background-color: var(--card-bg);
  outline: none;
  box-shadow: var(--shadow-primary);
}

.form-select option {
  background: var(--card-bg);
  color: var(--text-primary);
}

.form-actions {
  display: flex;
  gap: 1vh;
  justify-content: flex-end;
}

.add-btn,
.reset-btn {
  display: flex;
  align-items: center;
  gap: 0.8vh;
  padding: 1vh 2vh;
  border: 0.1vh solid;
  border-radius: 1vh;
  cursor: pointer;
  font-size: 1.4vh;
  font-weight: 500;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: var(--shadow-sm);
}

.add-btn {
  background: linear-gradient(135deg, rgba(100, 255, 218, 0.1) 0%, rgba(100, 255, 218, 0.05) 100%);
  border-color: var(--border-color);
  color: var(--primary-color);
}

.add-btn:hover:not(:disabled) {
  background: linear-gradient(135deg, rgba(100, 255, 218, 0.2) 0%, rgba(100, 255, 218, 0.1) 100%);
  border-color: var(--primary-color);
  transform: translateY(-0.1vh);
  box-shadow: var(--shadow-primary);
}

.add-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.reset-btn {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.05) 0%, rgba(255, 255, 255, 0.02) 100%);
  border-color: var(--border-color);
  color: var(--text-secondary);
}

.reset-btn:hover {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
  border-color: var(--border-hover);
  transform: translateY(-0.1vh);
  box-shadow: var(--shadow-primary);
}

/* 车辆列表 */
.vehicles-list {
  padding: 1.5vh;
  display: flex;
  flex-direction: column;
  gap: 1.5vh;
  max-height: 40vh;
  overflow-y: auto;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1.5vh;
  padding: 4vh;
  text-align: center;
}

.empty-icon {
  font-size: 4.8vh;
  color: rgba(100, 255, 218, 0.3);
}

.empty-text h3 {
  color: var(--text-secondary);
  font-size: 1.8vh;
  margin-bottom: 0.5vh;
}

.empty-text p {
  color: var(--text-muted);
  font-size: 1.4vh;
}

.vehicle-item {
  display: flex;
  align-items: center;
  gap: 1.5vh;
  padding: 1.5vh;
  background: var(--card-bg-light);
  border: 0.1vh solid var(--border-color);
  border-radius: 1vh;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: var(--shadow-sm);
}

.vehicle-item:hover {
  background: var(--card-bg);
  border-color: var(--border-hover);
  transform: translateY(-0.1vh);
  box-shadow: var(--shadow-primary);
}

.vehicle-info {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
}

.vehicle-icon {
  width: 4vh;
  height: 4vh;
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
  border: 0.1vh solid var(--border-color);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #0f172a;
  font-size: 1.6vh;
  box-shadow: var(--shadow-sm);
}

.vehicle-details {
  flex: 1;
}

.vehicle-name {
  font-size: 1.6vh;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 0.4vh;
}

.vehicle-meta {
  display: flex;
  gap: 1vh;
  font-size: 1.2vh;
  color: var(--text-muted);
}

.vehicle-type {
  color: var(--primary-color);
  font-weight: 500;
}

.vehicle-stats {
  display: flex;
  gap: 1.5vh;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.2vh;
}

.stat-label {
  font-size: 1vh;
  color: var(--text-muted);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.stat-value {
  font-size: 1.2vh;
  color: var(--text-primary);
  font-weight: 500;
}

.vehicle-price {
  display: flex;
  align-items: center;
  gap: 0.5vh;
}

.price-input {
  width: 10vh;
  padding: 0.8vh;
  background: var(--card-bg-light);
  border: 0.1vh solid var(--border-color);
  border-radius: 0.8vh;
  color: var(--text-primary);
  font-size: 1.4vh;
  text-align: right;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.price-input:focus {
  border-color: var(--primary-color);
  outline: none;
  box-shadow: var(--shadow-primary);
}

.price-currency {
  color: var(--text-muted);
  font-size: 1.2vh;
}

.vehicle-actions {
  display: flex;
  gap: 0.8vh;
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 0.5vh;
  padding: 0.8vh 1.2vh;
  border: 0.1vh solid;
  border-radius: 0.8vh;
  cursor: pointer;
  font-size: 1.2vh;
  font-weight: 500;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: var(--shadow-sm);
}

.save-btn {
  background: linear-gradient(135deg, rgba(100, 255, 218, 0.1) 0%, rgba(100, 255, 218, 0.05) 100%);
  border-color: var(--border-color);
  color: var(--primary-color);
}

.save-btn:hover {
  background: linear-gradient(135deg, rgba(100, 255, 218, 0.2) 0%, rgba(100, 255, 218, 0.1) 100%);
  border-color: var(--primary-color);
  transform: translateY(-0.1vh);
  box-shadow: var(--shadow-primary);
}

.remove-btn {
  background: linear-gradient(135deg, rgba(255, 68, 68, 0.1) 0%, rgba(255, 68, 68, 0.05) 100%);
  border-color: rgba(255, 68, 68, 0.3);
  color: #ff4444;
}

.remove-btn:hover {
  background: linear-gradient(135deg, rgba(255, 68, 68, 0.2) 0%, rgba(255, 68, 68, 0.1) 100%);
  border-color: rgba(255, 68, 68, 0.5);
  transform: translateY(-0.1vh);
  box-shadow: 0 0.4vh 2vh rgba(255, 68, 68, 0.3);
}

/* 底部操作区域 */
.footer-actions {
  padding: 1.5vh 2.5vh;
  border-top: 0.1vh solid var(--border-color);
  background: var(--card-bg-light);
  flex-shrink: 0;
}

.close-button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.8vh;
  width: 100%;
  padding: 1.2vh 2vh;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
  border: 0.1vh solid var(--border-color);
  border-radius: 1vh;
  color: var(--text-secondary);
  font-size: 1.4vh;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: var(--shadow-sm);
}

.close-button:hover {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.15) 0%, rgba(255, 255, 255, 0.1) 100%);
  border-color: var(--border-hover);
  transform: translateY(-0.1vh);
  box-shadow: var(--shadow-primary);
}

/* 滚动条样式 */
.content-area::-webkit-scrollbar,
.vehicles-list::-webkit-scrollbar {
  width: 0.8vh;
}

.content-area::-webkit-scrollbar-track,
.vehicles-list::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 0.4vh;
  margin: 0.4vh 0;
}

.content-area::-webkit-scrollbar-thumb,
.vehicles-list::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
  border-radius: 0.4vh;
  transition: background 0.3s ease;
}

.content-area::-webkit-scrollbar-thumb:hover,
.vehicles-list::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, var(--primary-light) 0%, var(--primary-color) 100%);
  box-shadow: var(--shadow-primary);
}
</style> 