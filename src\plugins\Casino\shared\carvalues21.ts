

/**
 * 卡牌表示: { rank: 'A'/'K'/'Q'/'J'/'T'/'9'/'8'/'7'/'6'/'5'/'4'/'3'/'2' }
 * (花色在21点中不重要，可以省略)
 *
 * 返回结果:
 * 'PLAYER_BLACKJACK'
 * 'PLAYER_WINS'
 * 'DEALER_BLACKJACK'
 * 'DEALER_WINS'
 * 'PUSH' (平局)
 * 'PLAYER_BUSTS'
 * 'DEALER_BUSTS' (通常结合玩家未爆牌，简化为 PLAYER_WINS)
 */

const CardValues21 = {
    '2': 2, '3': 3, '4': 4, '5': 5, '6': 6, '7': 7, '8': 8, '9': 9,
    'T': 10, 'J': 10, 'Q': 10, 'K': 10,
    'A': 11 // Ace 默认算 11
};

/**
 * 计算一手牌的点数总和 (考虑A的灵活性)
 * @param {Array<Object>} hand - 手牌数组，例如 [{rank:'A'}, {rank:'K'}]
 * @returns {number} 手牌的总点数
 */
function calculateHandValue(hand) {
    let value = 0;
    let aceCount = 0;

    for (const card of hand) {
        const rank = card.rank;
        value += CardValues21[rank];
        if (rank === 'A') {
            aceCount++;
        }
    }

    // 如果总点数 > 21 且手中有A，将A从11点改为1点，直到总点数 <= 21 或没有A可以改
    while (value > 21 && aceCount > 0) {
        value -= 10; // (11 - 1 = 10)
        aceCount--;
    }
    return value;
}

/**
 * 检查一手牌是否是 Blackjack (自然21点)
 * @param {Array<Object>} hand - 手牌数组
 * @param {number} handValue - 手牌的点数值 (可选, 提前计算好的)
 * @returns {boolean}
 */
function isBlackjack(hand, handValue = null) {
    const value = handValue !== null ? handValue : calculateHandValue(hand);
    return hand.length === 2 && value === 21;
}

/**
 * 判断21点游戏的结果
 * @param {Array<Object>} playerHand - 玩家手牌
 * @param {Array<Object>} dealerHand - 庄家手牌
 * @returns {string} 游戏结果的字符串表示
 */
function determineBlackjackWinner(playerHand, dealerHand) {
    const playerValue = calculateHandValue(playerHand);
    const dealerValue = calculateHandValue(dealerHand);

    const playerHasBlackjack = isBlackjack(playerHand, playerValue);
    const dealerHasBlackjack = isBlackjack(dealerHand, dealerValue);

    // 1. 检查玩家是否爆牌
    if (playerValue > 21) {
        return 'PLAYER_BUSTS'; // 玩家输
    }

    // 2. 检查双方是否有Blackjack
    if (playerHasBlackjack && dealerHasBlackjack) {
        return 'PUSH'; // 双方都是Blackjack，平局
    }
    if (playerHasBlackjack) {
        return 'PLAYER_BLACKJACK'; // 玩家Blackjack，庄家没有，玩家赢
    }
    if (dealerHasBlackjack) {
        return 'DEALER_BLACKJACK'; // 庄家Blackjack，玩家没有，玩家输
    }

    // 3. 检查庄家是否爆牌 (此时玩家肯定没爆牌，也没Blackjack)
    if (dealerValue > 21) {
        return 'DEALER_BUSTS'; // 庄家爆牌，玩家赢 (或者直接返回 PLAYER_WINS)
    }

    // 4. 双方都未爆牌，且都没有Blackjack，比较点数
    if (playerValue > dealerValue) {
        return 'PLAYER_WINS';
    } else if (playerValue < dealerValue) {
        return 'DEALER_WINS';
    } else {
        return 'PUSH'; // 点数相同，平局
    }
}


// --- 测试示例 ---
function runBlackjackTests() {
    console.log("--- 21点规则测试 ---");

    const testCases = [
        {
            name: "玩家Blackjack, 庄家普通21点",
            playerHand: [{ rank: 'A' }, { rank: 'K' }],
            dealerHand: [{ rank: 'T' }, { rank: '5' }, { rank: '6' }],
            expected: 'PLAYER_BLACKJACK'
        },
        {
            name: "庄家Blackjack, 玩家普通21点",
            playerHand: [{ rank: 'T' }, { rank: '5' }, { rank: '6' }],
            dealerHand: [{ rank: 'A' }, { rank: 'J' }],
            expected: 'DEALER_BLACKJACK'
        },
        {
            name: "双方Blackjack",
            playerHand: [{ rank: 'A' }, { rank: 'Q' }],
            dealerHand: [{ rank: 'K' }, { rank: 'A' }],
            expected: 'PUSH'
        },
        {
            name: "玩家赢 (点数高)",
            playerHand: [{ rank: 'T' }, { rank: '9' }], // 19
            dealerHand: [{ rank: 'T' }, { rank: '8' }], // 18
            expected: 'PLAYER_WINS'
        },
        {
            name: "庄家赢 (点数高)",
            playerHand: [{ rank: 'T' }, { rank: '7' }], // 17
            dealerHand: [{ rank: 'T' }, { rank: '9' }], // 19
            expected: 'DEALER_WINS'
        },
        {
            name: "平局 (点数相同)",
            playerHand: [{ rank: 'T' }, { rank: 'K' }], // 20
            dealerHand: [{ rank: 'Q' }, { rank: 'T' }], // 20
            expected: 'PUSH'
        },
        {
            name: "玩家爆牌",
            playerHand: [{ rank: 'T' }, { rank: '7' }, { rank: '5' }], // 22
            dealerHand: [{ rank: 'T' }, { rank: '8' }],       // 18
            expected: 'PLAYER_BUSTS'
        },
        {
            name: "庄家爆牌, 玩家未爆",
            playerHand: [{ rank: 'T' }, { rank: '9' }],       // 19
            dealerHand: [{ rank: 'T' }, { rank: '6' }, { rank: 'K' }], // 26
            expected: 'DEALER_BUSTS' // 或者 PLAYER_WINS
        },
        {
            name: "玩家爆牌, 庄家也爆牌 (玩家先爆则玩家输)",
            playerHand: [{ rank: 'T' }, { rank: '8' }, { rank: '5' }], // 23
            dealerHand: [{ rank: 'T' }, { rank: '7' }, { rank: '6' }], // 23
            expected: 'PLAYER_BUSTS'
        },
        {
            name: "玩家有A, 算11点",
            playerHand: [{ rank: 'A' }, { rank: '7' }], // 18
            dealerHand: [{ rank: 'T' }, { rank: '6' }], // 16
            expected: 'PLAYER_WINS'
        },
        {
            name: "玩家有多个A, 灵活计算",
            playerHand: [{ rank: 'A' }, { rank: 'A' }, { rank: '5' }], // 17 (11+1+5 or 1+1+5, not 11+11+5)
            dealerHand: [{ rank: 'T' }, { rank: '6' }],          // 16
            expected: 'PLAYER_WINS'
        },
        {
            name: "玩家有A, 爆牌后A算1",
            playerHand: [{ rank: 'A' }, { rank: '7' }, { rank: '8' }], // 16 (1+7+8, not 11+7+8=26)
            dealerHand: [{ rank: 'T' }, { rank: '9' }],          // 19
            expected: 'DEALER_WINS'
        }
    ];

    testCases.forEach(tc => {
        const result = determineBlackjackWinner(tc.playerHand, tc.dealerHand);
        const playerValue = calculateHandValue(tc.playerHand);
        const dealerValue = calculateHandValue(tc.dealerHand);
        const pass = result === tc.expected;
        console.log(`测试: ${tc.name}`);
        console.log(`  玩家手牌: ${tc.playerHand.map(c=>c.rank).join(',')} (点数: ${playerValue})`);
        console.log(`  庄家手牌: ${tc.dealerHand.map(c=>c.rank).join(',')} (点数: ${dealerValue})`);
        console.log(`  结果: ${result}, 预期: ${tc.expected}`);
        console.log(`  通过: ${pass ? '✅' : '❌'}`);
        console.log('---');
    });
}

// 运行测试:
// runBlackjackTests();