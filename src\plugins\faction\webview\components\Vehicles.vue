<template>
  <div class="vehicles-container">
    <!-- 顶部统计卡片 -->
    <div class="stats-card">
      <div class="stat-item">
        <div class="stat-icon faction-icon">
          <svg viewBox="0 0 24 24">
            <path d="M18,18.5A1.5,1.5 0 0,1 16.5,17A1.5,1.5 0 0,1 18,15.5A1.5,1.5 0 0,1 19.5,17A1.5,1.5 0 0,1 18,18.5M19.5,9.5L21.46,12H17V9.5M6,18.5A1.5,1.5 0 0,1 4.5,17A1.5,1.5 0 0,1 6,15.5A1.5,1.5 0 0,1 7.5,17A1.5,1.5 0 0,1 6,18.5M20,8L23,12V17H21A3,3 0 0,1 18,20A3,3 0 0,1 15,17H9A3,3 0 0,1 6,20A3,3 0 0,1 3,17H1V6C1,4.89 1.89,4 3,4H17V8H20Z" />
          </svg>
        </div>
        <div class="stat-info">
          <div class="stat-number">{{ factionData.vehicles ? factionData.vehicles.length : 0 }}</div>
          <div class="stat-label">派系载具</div>
        </div>
      </div>
      
      <div class="stat-item">
        <div class="stat-icon transfer-icon">
          <svg viewBox="0 0 24 24">
            <path d="M14,12L10,8V11H2V13H10V16M20,18V6C20,4.89 19.1,4 18,4H6A2,2 0 0,0 4,6V9H6V6H18V18H6V15H4V18A2,2 0 0,0 6,20H18C19.1,20 20,19.1 20,18Z" />
          </svg>
        </div>
        <div class="stat-info">
          <div class="stat-number">{{ personalVehicles ? personalVehicles.length : 0 }}</div>
          <div class="stat-label">可转移载具</div>
        </div>
      </div>
      
      <div class="stat-item">
        <div class="stat-icon shop-icon">
          <svg viewBox="0 0 24 24">
            <path d="M17,18A2,2 0 0,1 19,20A2,2 0 0,1 17,22C15.89,22 15,21.1 15,20C15,18.89 15.89,18 17,18M1,2H4.27L5.21,4H20A1,1 0 0,1 21,5C21,5.17 20.95,5.34 20.88,5.5L17.3,11.97C16.96,12.58 16.3,13 15.55,13H8.1L7.2,14.63L7.17,14.75A0.25,0.25 0 0,0 7.42,15H19V17H7C5.89,17 5,16.1 5,15C5,14.65 5.09,14.32 5.24,14.04L6.6,11.59L3,4H1V2M7,18A2,2 0 0,1 9,20A2,2 0 0,1 7,22C5.89,22 5,21.1 5,20C5,18.89 5.89,18 7,18M16,11L18.78,6H6.14L8.5,11H16Z" />
          </svg>
        </div>
        <div class="stat-info">
          <div class="stat-number">{{ factionData.vehicles_for_sale ? factionData.vehicles_for_sale.length : 0 }}</div>
          <div class="stat-label">可购买载具</div>
        </div>
      </div>
      
      <div class="stat-item funds-stat">
        <div class="stat-icon funds-icon">
          <svg viewBox="0 0 24 24">
            <path d="M11.8,10.9C9.53,10.31 8.8,9.7 8.8,8.75C8.8,7.66 9.81,6.9 11.5,6.9C13.28,6.9 13.94,7.75 14,9H16.21C16.14,7.28 15.09,5.7 13,5.19V3H10V5.16C8.06,5.58 6.5,6.84 6.5,8.77C6.5,11.08 8.41,12.23 11.2,12.9C13.7,13.5 14.2,14.38 14.2,15.31C14.2,16 13.71,17.1 11.5,17.1C9.44,17.1 8.63,16.18 8.5,15H6.32C6.44,17.19 8.08,18.42 10,18.83V21H13V18.85C14.95,18.5 16.5,17.35 16.5,15.3C16.5,12.46 14.07,11.5 11.8,10.9Z" />
          </svg>
        </div>
        <div class="stat-info">
          <div class="stat-number funds-number">${{ formatFunds(factionData.funds) }}</div>
          <div class="stat-label">可用资金</div>
        </div>
      </div>
    </div>

    <!-- 派系车库卡片 -->
    <div class="card current-vehicles-card">
        <div class="card-header">
          <div class="header-left">
            <svg viewBox="0 0 24 24">
              <path d="M18,18.5A1.5,1.5 0 0,1 16.5,17A1.5,1.5 0 0,1 18,15.5A1.5,1.5 0 0,1 19.5,17A1.5,1.5 0 0,1 18,18.5M19.5,9.5L21.46,12H17V9.5M6,18.5A1.5,1.5 0 0,1 4.5,17A1.5,1.5 0 0,1 6,15.5A1.5,1.5 0 0,1 7.5,17A1.5,1.5 0 0,1 6,18.5M20,8L23,12V17H21A3,3 0 0,1 18,20A3,3 0 0,1 15,17H9A3,3 0 0,1 6,20A3,3 0 0,1 3,17H1V6C1,4.89 1.89,4 3,4H17V8H20M8,6V9H5V11H8V14H10V11H13V9H10V6H8Z" />
            </svg>
            <h2>派系车库</h2>
          </div>
          <div class="vehicle-count">{{ factionData.vehicles ? factionData.vehicles.length : 0 }} 辆载具</div>
        </div>
        
        <div class="vehicles-list-container">
          <div v-if="factionData.vehicles && factionData.vehicles.length > 0" class="vehicles-grid">
            <div v-for="vehicle in factionData.vehicles" :key="vehicle.vehicle_id" class="vehicle-card">
              <div class="vehicle-header">
                <div class="vehicle-model">{{ vehicle.model }}</div>
                <div class="vehicle-status online">在线</div>
              </div>
              <div class="vehicle-details">
                <div class="detail-item">
                  <span class="detail-label">车牌:</span>
                  <span class="detail-value">{{ vehicle.plate }}</span>
                </div>
              </div>
              <div class="vehicle-actions">
                <button @click="locateVehicle(vehicle.vehicle_id)" class="action-btn locate-btn">
                  <svg viewBox="0 0 24 24">
                    <path d="M12,11.5A2.5,2.5 0 0,1 9.5,9A2.5,2.5 0 0,1 12,6.5A2.5,2.5 0 0,1 14.5,9A2.5,2.5 0 0,1 12,11.5M12,2A7,7 0 0,0 5,9C5,14.25 12,22 12,22C12,22 19,14.25 19,9A7,7 0 0,0 12,2Z" />
                  </svg>
                  <span>定位</span>
                </button>
                
                <button 
                  v-if="hasPermission('manage_vehicles') || permissions.includes('admin')"
                  @click="removeFactionVehicle(vehicle.vehicle_id)" 
                  class="action-btn sell-btn"
                >
                  <svg viewBox="0 0 24 24">
                    <path d="M14,18L12,15L15,18H14M18,15V18H21L18,15M11,18L8,18L11,15V18M7,18V15L4,18H7M8,4L4,8H7V11H11V8H14L12,4H8M16,11V8H20L16,4V7H12L16,11Z" />
                  </svg>
                  <span>出售</span>
                </button>
              </div>
            </div>
          </div>
          
          <div v-else class="empty-state">
            <div class="empty-icon">
              <svg viewBox="0 0 24 24">
                <path d="M18,18.5A1.5,1.5 0 0,1 16.5,17A1.5,1.5 0 0,1 18,15.5A1.5,1.5 0 0,1 19.5,17A1.5,1.5 0 0,1 18,18.5M19.5,9.5L21.46,12H17V9.5M6,18.5A1.5,1.5 0 0,1 4.5,17A1.5,1.5 0 0,1 6,15.5A1.5,1.5 0 0,1 7.5,17A1.5,1.5 0 0,1 6,18.5M20,8L23,12V17H21A3,3 0 0,1 18,20A3,3 0 0,1 15,17H9A3,3 0 0,1 6,20A3,3 0 0,1 3,17H1V6C1,4.89 1.89,4 3,4H17V8H20Z" />
              </svg>
            </div>
            <div class="empty-text">
              <h3>暂无派系载具</h3>
              <p>您可以通过转移个人载具或购买新载具来添加派系载具</p>
            </div>
          </div>
        </div>
      </div>

      <!-- 个人载具转换卡片 -->
      <div v-if="hasPermission('manage_vehicles') || permissions.includes('admin')" class="card personal-vehicles-card">
        <div class="card-header">
          <div class="header-left">
            <svg viewBox="0 0 24 24">
              <path d="M12,3L20.5,7.5V16.5L12,21L3.5,16.5V7.5L12,3M12,12L16.5,9.5V7.5L12,10L7.5,7.5V9.5L12,12M12,12V21L20.5,16.5V14.5L12,19L3.5,14.5V16.5L12,21V12Z" />
            </svg>
            <h2>个人载具转换</h2>
          </div>
          
          <div class="help-tip">
            <svg viewBox="0 0 24 24">
              <path d="M11,18H13V16H11V18M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M12,20C7.59,20 4,16.41 4,12C7.59,8 12,8 12,8C16.41,8 20,11.59 20,12A10,10 0 0,0 12,20Z" />
            </svg>
            <span class="tip-text">转为派系载具（不可逆转）</span>
          </div>
        </div>
        
        <div class="personal-vehicles-list">
          <div v-if="personalVehicles && personalVehicles.length > 0" class="vehicles-grid">
            <div v-for="vehicle in personalVehicles" :key="(vehicle as any)?._id || (vehicle as any)?.id" class="vehicle-card personal-vehicle-card">
              <div class="vehicle-header">
                <div class="vehicle-model">{{ getVehicleDisplayName(vehicle) }}</div>
                <div class="vehicle-type personal">个人</div>
              </div>
              <div class="vehicle-details">
                <div class="detail-item">
                  <span class="detail-label">车牌:</span>
                  <span class="detail-value">{{ (vehicle as any)?.numberPlateText || '未知' }}</span>
                </div>
                <div class="detail-item">
                  <span class="detail-label">价值:</span>
                  <span class="detail-value price">${{ formatVehiclePrice(vehicle) }}</span>
                </div>
              </div>
              <div class="vehicle-actions">
                <button @click="convertToFactionVehicle((vehicle as any)?._id || (vehicle as any)?.id)" class="action-btn convert-btn">
                  <svg viewBox="0 0 24 24">
                    <path d="M14,12L10,8V11H2V13H10V16M20,18V6C20,4.89 19.1,4 18,4H6A2,2 0 0,0 4,6V9H6V6H18V18H6V15H4V18A2,2 0 0,0 6,20H18C19.1,20 20,19.1 20,18Z" />
                  </svg>
                  <span>转为派系载具</span>
                </button>
              </div>
            </div>
          </div>
          
          <div v-else class="empty-state">
            <div class="empty-icon">
              <svg viewBox="0 0 24 24">
                <path d="M18,18.5A1.5,1.5 0 0,1 16.5,17A1.5,1.5 0 0,1 18,15.5A1.5,1.5 0 0,1 19.5,17A1.5,1.5 0 0,1 18,18.5M19.5,9.5L21.46,12H17V9.5M6,18.5A1.5,1.5 0 0,1 4.5,17A1.5,1.5 0 0,1 6,15.5A1.5,1.5 0 0,1 7.5,17A1.5,1.5 0 0,1 6,18.5M20,8L23,12V17H21A3,3 0 0,1 18,20A3,3 0 0,1 15,17H9A3,3 0 0,1 6,20A3,3 0 0,1 3,17H1V6C1,4.89 1.89,4 3,4H17V8H20Z" />
              </svg>
            </div>
            <div class="empty-text">
              <h3>暂无可转换载具</h3>
              <p>您的个人载具将在这里显示，可以转换为派系载具</p>
            </div>
          </div>
        </div>
      </div>

      <!-- 载具商店卡片 -->
      <div v-if="hasPermission('manage_vehicles') || permissions.includes('admin')" class="card vehicle-shop-card">
        <div class="card-header">
          <div class="header-left">
            <svg viewBox="0 0 24 24">
              <path d="M18,18.5A1.5,1.5 0 0,1 16.5,17A1.5,1.5 0 0,1 18,15.5A1.5,1.5 0 0,1 19.5,17A1.5,1.5 0 0,1 18,18.5M19.5,9.5L21.46,12H17V9.5M6,18.5A1.5,1.5 0 0,1 4.5,17A1.5,1.5 0 0,1 6,15.5A1.5,1.5 0 0,1 7.5,17A1.5,1.5 0 0,1 6,18.5M20,8L23,12V17H21A3,3 0 0,1 18,20A3,3 0 0,1 15,17H9A3,3 0 0,1 6,20A3,3 0 0,1 3,17H1V6C1,4.89 1.89,4 3,4H17V8H20M8,6V9H5V11H8V14H10V11H13V9H10V6H8Z" />
            </svg>
            <h2>载具商店</h2>
          </div>
          
          <div class="header-actions">
            <!-- 添加新的按钮，仅管理员可见 -->
            <button v-if="permissions.includes('admin')" @click="showAddVehicleModal = true" class="add-vehicle-btn">
              <svg viewBox="0 0 24 24">
                <path d="M19,13H13V19H11V13H5V11H11V5H13V11H19V13Z" />
              </svg>
              <span>添加载具</span>
            </button>
          </div>
        </div>
        
        <div class="vehicle-shop-list">
          <div v-if="factionData.vehicles_for_sale && factionData.vehicles_for_sale.length > 0" class="vehicles-grid shop-grid">
            <div 
              v-for="vehicle in factionData.vehicles_for_sale" 
              :key="vehicle.model" 
              class="vehicle-card shop-vehicle-card"
              @click="selectVehicleForSale(vehicle)"
            >
              <div class="vehicle-header">
                <div class="vehicle-model">{{ vehicle.model }}</div>
                <div class="vehicle-availability" :class="{ unavailable: factionData.funds < vehicle.price }">
                  {{ factionData.funds >= vehicle.price ? '可购买' : '资金不足' }}
                </div>
              </div>
              <div class="vehicle-details">
                <div class="detail-item price-item">
                  <span class="detail-label">价格:</span>
                  <span class="detail-value price">${{ formatFunds(vehicle.price) }}</span>
                </div>
              </div>
              <div class="vehicle-actions">
                <button 
                  @click.stop="purchaseVehicle(vehicle)"
                  :disabled="factionData.funds < vehicle.price"
                  class="action-btn purchase-btn"
                >
                  <svg viewBox="0 0 24 24">
                    <path d="M17,18A2,2 0 0,1 19,20A2,2 0 0,1 17,22C15.89,22 15,21.1 15,20C15,18.89 15.89,18 17,18M1,2H4.27L5.21,4H20A1,1 0 0,1 21,5C21,5.17 20.95,5.34 20.88,5.5L17.3,11.97C16.96,12.58 16.3,13 15.55,13H8.1L7.2,14.63L7.17,14.75A0.25,0.25 0 0,0 7.42,15H19V17H7C5.89,17 5,16.1 5,15C5,14.65 5.09,14.32 5.24,14.04L6.6,11.59L3,4H1V2M7,18A2,2 0 0,1 9,20A2,2 0 0,1 7,22C5.89,22 5,21.1 5,20C5,18.89 5.89,18 7,18M16,11L18.78,6H6.14L8.5,11H16Z" />
                  </svg>
                  <span>购买</span>
                </button>
                <button 
                  v-if="permissions.includes('admin')"
                  @click.stop="removeVehicleForSale(vehicle)"
                  class="action-btn remove-btn"
                >
                  <svg viewBox="0 0 24 24">
                    <path d="M19,4H15.5L14.5,3H9.5L8.5,4H5V6H19M6,19A2,2 0 0,0 8,21H16A2,2 0 0,0 18,19V7H6V19Z" />
                  </svg>
                  <span>移除</span>
                </button>
              </div>
            </div>
          </div>
          
          <div v-else class="empty-state">
            <div class="empty-icon">
              <svg viewBox="0 0 24 24">
                <path d="M19,8L15,12H18C18,15.31 15.31,18 12,18C11,18 10.03,17.75 9.2,17.3L7.74,18.76C8.97,19.54 10.43,20 12,20C16.42,20 20,16.42 20,12H23L19,8M6,12C6,8.69 8.69,6 12,6C13,6 13.97,6.25 14.8,6.7L16.26,5.24C15.03,4.46 13.57,4 12,4C7.58,4 4,7.58 4,12H1L5,16L9,12H6Z" />
              </svg>
            </div>
            <div class="empty-text">
              <h3>暂无可购买载具</h3>
              <p v-if="permissions.includes('admin')">点击右上角"添加载具"按钮来添加可购买的载具</p>
              <p v-else>管理员还未添加可购买的载具</p>
            </div>
          </div>
        </div>
      </div>

    <!-- 车辆详情模态框 -->
    <div v-if="showVehicleForSaleModal" class="modal-backdrop" @click="showVehicleForSaleModal = false"></div>
    <div v-if="showVehicleForSaleModal" class="modal">
      <div class="modal-card">
        <div class="modal-header">
          <h3>车辆详情</h3>
          <button @click="showVehicleForSaleModal = false" class="close-btn">
            <svg viewBox="0 0 24 24">
              <path d="M19,6.41L17.59,5L12,10.59L6.41,5L5,6.41L10.59,12L5,17.59L6.41,19L12,13.41L17.59,19L19,17.59L13.41,12L19,6.41Z" />
            </svg>
          </button>
        </div>
        
        <div class="modal-body">
          <div v-if="selectedVehicleForSale" class="vehicle-details">
            <div class="vehicle-image">
              <svg viewBox="0 0 24 24">
                <path d="M23,10V12H21V17A1,1 0 0,1 20,18H19V20H17V18H7V20H5V18H4A1,1 0 0,1 3,17V12H1V10H3V5A1,1 0 0,1 4,4H20A1,1 0 0,1 21,5V10H23M5,7.5C5,6.67 4.33,6 3.5,6C2.67,6 2,6.67 2,7.5C2,8.33 2.67,9 3.5,9C4.33,9 5,8.33 5,7.5M19,5H5V12H19V5M19,14C18.45,14 18,14.45 18,15A1,1 0 0,0 19,16A1,1 0 0,0 20,15C20,14.45 19.55,14 19,14M5,13.5A1.5,1.5 0 0,0 6.5,15A1.5,1.5 0 0,0 8,13.5A1.5,1.5 0 0,0 6.5,12A1.5,1.5 0 0,0 5,13.5Z" />
              </svg>
            </div>
            
            <div class="detail-row">
              <div class="detail-label">型号</div>
              <div class="detail-value">{{ selectedVehicleForSale.model }}</div>
            </div>
            
            <div class="detail-row">
              <div class="detail-label">价格</div>
              <div class="detail-value price">${{ formatFunds(selectedVehicleForSale.price) }}</div>
            </div>
            
            <div class="funds-status" :class="{ insufficient: factionData.funds < selectedVehicleForSale.price }">
              <svg v-if="factionData.funds >= selectedVehicleForSale.price" viewBox="0 0 24 24">
                <path d="M12 2C6.5 2 2 6.5 2 12S6.5 22 12 22 22 17.5 22 12 17.5 2 12 2M10 17L5 12L6.41 10.59L10 14.17L17.59 6.58L19 8L10 17Z" />
              </svg>
              <svg v-else viewBox="0 0 24 24">
                <path d="M13 13H11V7H13M13 17H11V15H13M12 2A10 10 0 0 0 2 12A10 10 0 0 0 12 22A10 10 0 0 0 22 12A10 10 0 0 0 12 2Z" />
              </svg>
              <span>{{ factionData.funds >= selectedVehicleForSale.price ? '资金充足' : '资金不足' }}</span>
            </div>
          </div>
        </div>
        
        <div class="modal-footer">
          <button @click="showVehicleForSaleModal = false" class="cancel-btn">取消</button>
          <button 
            @click="purchaseVehicle(selectedVehicleForSale)"
            :disabled="!selectedVehicleForSale || factionData.funds < selectedVehicleForSale.price"
            class="purchase-btn-lg"
          >
            <svg viewBox="0 0 24 24">
              <path d="M17,18A2,2 0 0,1 19,20A2,2 0 0,1 17,22C15.89,22 15,21.1 15,20C15,18.89 15.89,18 17,18M1,2H4.27L5.21,4H20A1,1 0 0,1 21,5C21,5.17 20.95,5.34 20.88,5.5L17.3,11.97C16.96,12.58 16.3,13 15.55,13H8.1L7.2,14.63L7.17,14.75A0.25,0.25 0 0,0 7.42,15H19V17H7C5.89,17 5,16.1 5,15C5,14.65 5.09,14.32 5.24,14.04L6.6,11.59L3,4H1V2M7,18A2,2 0 0,1 9,20A2,2 0 0,1 7,22C5.89,22 5,21.1 5,20C5,18.89 5.89,18 7,18M16,11L18.78,6H6.14L8.5,11H16Z" />
            </svg>
            <span>购买载具</span>
          </button>
        </div>
      </div>
    </div>

    <!-- 添加新车辆模态框 -->
    <div v-if="showAddVehicleModal" class="modal-backdrop" @click="showAddVehicleModal = false"></div>
    <div v-if="showAddVehicleModal" class="modal">
      <div class="modal-card">
        <div class="modal-header">
          <h3>添加可购买车辆</h3>
          <button @click="showAddVehicleModal = false" class="close-btn">
            <svg viewBox="0 0 24 24">
              <path d="M19,6.41L17.59,5L12,10.59L6.41,5L5,6.41L10.59,12L5,17.59L6.41,19L12,13.41L17.59,19L19,17.59L13.41,12L19,6.41Z" />
            </svg>
          </button>
        </div>
        
        <div class="modal-body">
          <div class="form-group">
            <label for="vehicleModel">车辆型号</label>
            <input 
              type="text" 
              id="vehicleModel" 
              v-model="newVehicle.model" 
              placeholder="输入车辆型号"
            >
          </div>
          
          <div class="form-group">
            <label for="vehiclePrice">价格</label>
            <input 
              type="number" 
              id="vehiclePrice" 
              v-model="newVehicle.price" 
              placeholder="输入车辆价格"
            >
          </div>
        </div>
        
        <div class="modal-footer">
          <button @click="showAddVehicleModal = false" class="cancel-btn">取消</button>
          <button 
            @click="addNewVehicle"
            :disabled="!newVehicle.model || !newVehicle.price"
            class="confirm-btn"
          >
            确认添加
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, PropType } from 'vue';

const props = defineProps({
  factionData: {
    type: Object,
    required: true
  },
  personalVehicles: {
    type: Array,
    default: () => []
  },
  permissions: {
    type: Array,
    required: true
  },
  hasPermission: {
    type: Function,
    required: true
  },
  formatFunds: {
    type: Function,
    required: true
  },
  purchaseVehicleAction: {
    type: Function as PropType<(model: string, price: number) => Promise<boolean>>,
    required: true
  },
  removeFactionVehicleAction: {
    type: Function as PropType<(vehicleId: string) => Promise<boolean>>,
    required: true
  },
  locateVehicleAction: {
    type: Function as PropType<(vehicleId: string) => Promise<boolean>>,
    required: true
  },
  addnewfactionvehiclesforsale: {
    type: Function as PropType<(model: string, price: number) => Promise<boolean>>,
    required: true
  },
  removeVehicleForSaleAction: {
    type: Function as PropType<(vehicle: any) => Promise<boolean>>,
    required: true
  },
  convertPersonalVehicleToFactionAction: {
    type: Function as PropType<(vehicleId: string) => Promise<boolean>>,
    required: true
  }
});

// 车辆选择相关状态
const showVehicleForSaleModal = ref(false);
const selectedVehicleForSale = ref(null);

function selectVehicleForSale(vehicle) {
  selectedVehicleForSale.value = vehicle;
  showVehicleForSaleModal.value = true;
}

async function purchaseVehicle(vehicle) {
  if (!vehicle || props.factionData.funds < vehicle.price) {
    return;
  }

  const success = await props.purchaseVehicleAction(vehicle.model, vehicle.price);
  if (success) {
    showVehicleForSaleModal.value = false;
    selectedVehicleForSale.value = null;
  }
}

function removeFactionVehicle(vehicleId) {
  return props.removeFactionVehicleAction(vehicleId);
}

function locateVehicle(vehicleId) {
  return props.locateVehicleAction(vehicleId);
}

function addnewfactionvehiclesforsale(model, price) {
  return props.addnewfactionvehiclesforsale(model, price);
}

async function removeVehicleForSale(vehicle) {
  if (!vehicle || !props.permissions.includes('admin')) {
    return false;
  }
  return props.removeVehicleForSaleAction(vehicle);

}

// 添加新车辆相关状态
const showAddVehicleModal = ref(false);
const newVehicle = ref({
  model: '',
  price: null
});

// 添加新车辆方法
const addNewVehicle = async () => {
  if (!newVehicle.value.model || !newVehicle.value.price) {
    return;
  }

  try {
    const success = await addnewfactionvehiclesforsale(newVehicle.value.model, newVehicle.value.price);
    if (success) {
      // 重置表单
    // 重置表单
    newVehicle.value = {
      model: '',
      price: null
    };
    showAddVehicleModal.value = false;
    }
    else {
      console.error('添加车辆失败');
    }
    
    // 通知父组件刷新数据
  } catch (error) {
    console.error('添加车辆失败:', error);
  }
};

// 个人载具相关方法
function getVehicleDisplayName(vehicle: any): string {
  if (typeof vehicle.model === 'string') {
    return vehicle.model;
  }
  return vehicle.model?.toString() || '未知载具';
}

function formatVehiclePrice(vehicle: any): string {
  const price = vehicle.priceinshop || 0;
  return props.formatFunds(price);
}

async function convertToFactionVehicle(vehicleId: string) {
  try {
    const success = await props.convertPersonalVehicleToFactionAction(vehicleId);
    if (!success) {
      console.error('转换载具失败');
    }
  } catch (error) {
    console.error('转换载具时出错:', error);
  }
}


</script>

<style scoped>
/* 主容器 */
.vehicles-container {
  display: flex;
  flex-direction: column;
  gap: 2vh;
  padding: 0;
}

/* 统计卡片样式 */
.stats-card {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
  gap: 1vh;
  margin-bottom: 1vh;
}

.stat-item {
  display: flex;
  align-items: center;
  padding: 1.5vh;
  background: rgba(34, 42, 53, 0.8);
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
}

.stat-item:hover {
  background: rgba(34, 42, 53, 0.9);
  border-color: rgba(255, 255, 255, 0.2);
  transform: translateY(-2px);
}

.stat-icon {
  width: 4vh;
  height: 4vh;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 1.5vh;
}

.faction-icon {
  background: rgba(100, 149, 237, 0.2);
}

.transfer-icon {
  background: rgba(255, 152, 0, 0.2);
}

.shop-icon {
  background: rgba(72, 187, 120, 0.2);
}

.funds-icon {
  background: rgba(255, 215, 0, 0.2);
}

.stat-icon svg {
  width: 2.4vh;
  height: 2.4vh;
  fill: currentColor;
}

.faction-icon svg {
  fill: #6495ed;
}

.transfer-icon svg {
  fill: #ff9800;
}

.shop-icon svg {
  fill: #48bb78;
}

.funds-icon svg {
  fill: #ffd700;
}

.stat-info {
  display: flex;
  flex-direction: column;
}

.stat-number {
  font-size: 2.4vh;
  font-weight: 700;
  color: #e2e8f0;
  line-height: 1;
  margin-bottom: 0.5vh;
}

.funds-number {
  font-size: 2vh;
  color: #ffd700;
}

.stat-label {
  font-size: 1.2vh;
  color: #a0aec0;
  font-weight: 500;
}

/* 卡片通用样式 */
.card {
  background: rgba(34, 42, 53, 0.7);
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  flex-direction: column;
  flex-shrink: 0;
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 2vh 2.5vh;
  background: rgba(26, 32, 44, 0.6);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.header-left {
  display: flex;
  align-items: center;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 1vh;
}

.vehicle-count {
  font-size: 1.2vh;
  color: #a0aec0;
  background: rgba(255, 255, 255, 0.05);
  padding: 0.5vh 1vh;
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.card-header svg {
  width: 2vh;
  height: 2vh;
  margin-right: 1vh;
  fill: #a995f4;
}

.card-header h2 {
  font-size: 1.6vh;
  font-weight: 600;
  color: #e2e8f0;
  margin: 0;
  flex: 1;
}

/* 派系载具列表 */
.vehicles-list-container {
  flex: 1;
  padding: 1vh;
}

/* 网格布局替代列表 */
.vehicles-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
  gap: 1vh;
  padding: 1vh;
}

.shop-grid {
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
}

/* 车辆卡片样式 */
.vehicle-card {
  background: rgba(45, 55, 72, 0.6);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  padding: 1.2vh;
  transition: all 0.3s ease;
  cursor: pointer;
  min-height: 12vh;
}

.vehicle-card:hover {
  background: rgba(45, 55, 72, 0.8);
  border-color: rgba(100, 149, 237, 0.4);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.personal-vehicle-card {
  border-left: 3px solid #ff9800;
  background: rgba(255, 152, 0, 0.08);
}

.personal-vehicle-card:hover {
  background: rgba(255, 152, 0, 0.12);
  border-color: #ff9800;
}

.shop-vehicle-card {
  border-left: 3px solid #48bb78;
  background: rgba(72, 187, 120, 0.08);
}

.shop-vehicle-card:hover {
  background: rgba(72, 187, 120, 0.12);
  border-color: #48bb78;
}

.vehicle-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1vh;
}

.vehicle-model {
  font-size: 1.4vh;
  font-weight: 600;
  color: #e2e8f0;
}

.vehicle-status, .vehicle-type, .vehicle-availability {
  font-size: 0.9vh;
  padding: 0.3vh 0.6vh;
  border-radius: 8px;
  font-weight: 500;
}

.vehicle-status.online {
  background: rgba(72, 187, 120, 0.2);
  color: #68d391;
}

.vehicle-type.personal {
  background: rgba(255, 152, 0, 0.2);
  color: #ff9800;
}

.vehicle-availability {
  background: rgba(72, 187, 120, 0.2);
  color: #68d391;
}

.vehicle-availability.unavailable {
  background: rgba(229, 62, 62, 0.2);
  color: #fc8181;
}

.vehicle-details {
  display: flex;
  flex-direction: column;
  gap: 0.6vh;
  margin-bottom: 1vh;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.price-item {
  background: rgba(72, 187, 120, 0.1);
  padding: 0.6vh 0.8vh;
  border-radius: 4px;
  border: 1px solid rgba(72, 187, 120, 0.2);
}

.detail-label {
  font-size: 1vh;
  color: #a0aec0;
}

.detail-value {
  font-size: 1.1vh;
  font-weight: 500;
  color: #e2e8f0;
}

.detail-value.price {
  color: #68d391;
  font-weight: 600;
}

.vehicle-actions {
  display: flex;
  gap: 1vh;
  margin-top: auto;
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 0.6vh;
  padding: 0.8vh 1.2vh;
  border-radius: 6px;
  font-size: 1vh;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  border: none;
  flex: 1;
  justify-content: center;
}

.locate-btn {
  background: rgba(66, 153, 225, 0.15);
  color: #63b3ed;
  border: 1px solid rgba(66, 153, 225, 0.3);
}

.locate-btn:hover {
  background: rgba(66, 153, 225, 0.25);
  border-color: rgba(66, 153, 225, 0.5);
}

.sell-btn, .remove-btn {
  background: rgba(237, 137, 54, 0.15);
  color: #f6ad55;
  border: 1px solid rgba(237, 137, 54, 0.3);
}

.sell-btn:hover, .remove-btn:hover {
  background: rgba(237, 137, 54, 0.25);
  border-color: rgba(237, 137, 54, 0.5);
}

.convert-btn {
  background: #ff9800;
  color: white;
  border: 1px solid #ff9800;
}

.convert-btn:hover {
  background: #f57c00;
  border-color: #f57c00;
  transform: translateY(-1px);
}

.purchase-btn {
  background: rgba(72, 187, 120, 0.15);
  color: #68d391;
  border: 1px solid rgba(72, 187, 120, 0.3);
}

.purchase-btn:hover:not(:disabled) {
  background: rgba(72, 187, 120, 0.25);
  border-color: rgba(72, 187, 120, 0.5);
}

.purchase-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.action-btn svg {
  width: 1.2vh;
  height: 1.2vh;
  fill: currentColor;
}

/* 载具商店样式 */
.vehicle-shop-card {
  position: relative;
}

.vehicle-shop-list {
  flex: 1;
  padding: 1vh;
}

/* 空状态样式优化 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 6vh 2vh;
  color: #a0aec0;
  text-align: center;
  min-height: 25vh;
}

.empty-icon {
  width: 8vh;
  height: 8vh;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 2vh;
}

.empty-icon svg {
  width: 5vh;
  height: 5vh;
  fill: #a0aec0;
  opacity: 0.7;
}

.empty-text h3 {
  font-size: 1.8vh;
  font-weight: 600;
  color: #e2e8f0;
  margin: 0 0 1vh 0;
}

.empty-text p {
  font-size: 1.2vh;
  color: #a0aec0;
  margin: 0;
  line-height: 1.5;
}

/* 模态框样式 */
.modal-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 100;
  animation: fadeIn 0.2s ease forwards;
}

.modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 101;
}

.modal-card {
  background: rgba(34, 42, 53, 0.95);
  width: 45vh;
  border-radius: 12px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.5);
  overflow: hidden;
  max-height: 90vh;
  display: flex;
  flex-direction: column;
  animation: scaleIn 0.2s ease forwards;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 2vh;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.modal-header h3 {
  font-size: 1.8vh;
  font-weight: 500;
  color: #e2e8f0;
  margin: 0;
}

.close-btn {
  background: transparent;
  padding: 0.5vh;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s;
  border: none;
}

.close-btn:hover {
  background: rgba(255, 255, 255, 0.1);
}

.close-btn svg {
  width: 2vh;
  height: 2vh;
  fill: rgba(255, 255, 255, 0.5);
}

.modal-body {
  padding: 2vh;
  overflow-y: auto;
  flex: 1;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  gap: 1vh;
  padding: 1.5vh 2vh;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

/* 车辆详情样式 */
.vehicle-details {
  display: flex;
  flex-direction: column;
  gap: 2vh;
}

.vehicle-image {
  display: flex;
  justify-content: center;
  padding: 2vh;
  background: rgba(26, 32, 44, 0.3);
  border-radius: 8px;
}

.vehicle-image svg {
  width: 12vh;
  height: 12vh;
  fill: #a995f4;
}

.detail-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5vh;
  background: rgba(26, 32, 44, 0.2);
  border-radius: 8px;
}

.detail-label {
  font-size: 1.2vh;
  color: #a0aec0;
}

.detail-value {
  font-size: 1.4vh;
  font-weight: 600;
  color: #e2e8f0;
}

.detail-value.price {
  color: #68d391;
}

.funds-status {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 1.2vh;
  background: rgba(72, 187, 120, 0.1);
  color: #68d391;
  border-radius: 8px;
  font-size: 1.3vh;
  font-weight: 500;
  margin-top: 1vh;
}

.funds-status.insufficient {
  background: rgba(229, 62, 62, 0.1);
  color: #fc8181;
}

.funds-status svg {
  width: 2vh;
  height: 2vh;
  margin-right: 1vh;
  fill: currentColor;
}

/* 按钮样式 */
.cancel-btn, .purchase-btn-lg {
  padding: 1vh 2vh;
  border-radius: 6px;
  font-size: 1.4vh;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  border: none;
}

.cancel-btn {
  background: rgba(255, 255, 255, 0.1);
  color: #e2e8f0;
}

.cancel-btn:hover {
  background: rgba(255, 255, 255, 0.15);
}

.purchase-btn-lg {
  display: flex;
  align-items: center;
  background: #68d391;
  color: rgba(34, 42, 53, 0.95);
}

.purchase-btn-lg:hover:not(:disabled) {
  background: #48bb78;
}

.purchase-btn-lg:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.purchase-btn-lg svg {
  width: 1.8vh;
  height: 1.8vh;
  margin-right: 0.8vh;
  fill: rgba(34, 42, 53, 0.95);
}

.add-vehicle-btn {
  display: flex;
  align-items: center;
  gap: 0.8vh;
  padding: 1vh 1.5vh;
  background: rgba(72, 187, 120, 0.15);
  color: #68d391;
  border: 1px solid rgba(72, 187, 120, 0.3);
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 1.2vh;
  font-weight: 500;
}

.add-vehicle-btn:hover {
  background: rgba(72, 187, 120, 0.25);
  border-color: rgba(72, 187, 120, 0.5);
}

.add-vehicle-btn svg {
  width: 1.4vh;
  height: 1.4vh;
  fill: currentColor;
}

.form-group {
  margin-bottom: 16px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
}

.form-group input {
  width: 100%;
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
}

.confirm-btn {
  background-color: #4CAF50;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.confirm-btn:hover {
  background-color: #45a049;
}

.confirm-btn:disabled {
  background-color: #cccccc;
  cursor: not-allowed;
}

/* 个人载具转换卡片样式 */
.personal-vehicles-card {
  margin-bottom: 2vh;
}

.help-tip {
  display: flex;
  align-items: center;
  gap: 0.8vh;
  padding: 1vh 1.5vh;
  background: rgba(255, 193, 7, 0.1);
  border: 1px solid rgba(255, 193, 7, 0.3);
  border-radius: 20px;
  font-size: 1.1vh;
  color: #ffc107;
  transition: all 0.3s ease;
}

.help-tip:hover {
  background: rgba(255, 193, 7, 0.15);
  border-color: rgba(255, 193, 7, 0.4);
}

.tip-text {
  white-space: nowrap;
}

.help-tip svg {
  width: 1.4vh;
  height: 1.4vh;
  fill: #ffc107;
}

.personal-vehicles-list {
  flex: 1;
  padding: 1vh;
}
</style>