# 内容截断检查规则

在使用文件操作工具时，必须严格检查内容是否被提前截断，确保代码完整性。

## 截断问题识别

### 常见截断模式
- [ ] CSS规则突然中断（如：`.class {color: red;` 缺少 `}`）
- [ ] HTML标签未闭合（如：`<div>内容` 缺少 `</div>`）
- [ ] JavaScript函数/对象未完整（如：`function test() {` 缺少闭合）
- [ ] 文件末尾异常结束（如：`</sty` 应该是 `</style>`）

### 检查要点
1. **开始标记必须有结束标记**
   - `{` 必须有对应的 `}`
   - `<tag>` 必须有对应的 `</tag>`
   - `(` 必须有对应的 `)`
   - `[` 必须有对应的 `]`

2. **文件结构完整性**
   - Vue文件必须有完整的 `<template>`、`<script>`、`<style>` 标签
   - CSS文件必须以完整的规则结束
   - JavaScript文件必须有完整的语法结构

3. **内容逻辑连贯性**
   - 代码片段不能在句子中间截断
   - 属性声明必须完整
   - 注释必须完整

## 检查流程

### 写入前检查
在使用 `fsWrite`、`fsAppend`、`strReplace` 前：

1. **计数检查**
   ```
   开始标记数量 = 结束标记数量
   { 的数量 = } 的数量
   ( 的数量 = ) 的数量
   [ 的数量 = ] 的数量
   ```

2. **结构检查**
   - Vue组件：`<template>` + `<script>` + `<style>` 都完整
   - CSS规则：每个选择器都有完整的属性块
   - HTML标签：所有标签都正确闭合

3. **末尾检查**
   - 文件不能以不完整的单词结束
   - 不能以未闭合的标签结束
   - 不能以不完整的CSS规则结束

### 写入后验证
写入完成后立即检查：

1. **语法验证**
   - CSS语法是否正确
   - HTML结构是否完整
   - JavaScript语法是否有效

2. **内容完整性**
   - 预期的所有内容都已写入
   - 没有意外的截断
   - 文件结构符合预期

## 常见错误示例

### ❌ 错误：CSS截断
```css
.class-name {
  color: red;
  background: blue;
/* 缺少闭合大括号，内容被截断 */
```

### ❌ 错误：HTML截断
```html
<div class="container">
  <p>内容</p>
<!-- 缺少 </div>，标签未闭合 -->
```

### ❌ 错误：Vue组件截断
```vue
<template>
  <div>内容</div>
</template>

<script>
// JavaScript代码
</script>

<sty
<!-- style标签被截断 -->
```

### ✅ 正确：完整结构
```vue
<template>
  <div>内容</div>
</template>

<script>
// JavaScript代码
</script>

<style scoped>
.class-name {
  color: red;
  background: blue;
}
</style>
```

## 修复策略

### 发现截断时
1. **立即停止**：不要继续在截断的基础上修改
2. **重新读取**：使用 `readFile` 确认当前文件状态
3. **完整重写**：从完整的内容开始重新写入
4. **分段验证**：将大文件分段处理，每段都验证完整性

### 预防措施
1. **内容预检**：写入前在本地验证内容完整性
2. **分段写入**：避免一次性写入过大的内容
3. **及时验证**：每次写入后立即检查结果
4. **备份策略**：重要修改前先备份原文件

## 特殊检查规则

### Vue文件检查
```
必须包含：
- <template> ... </template>
- <script> ... </script>  
- <style> ... </style>

标签不能被截断：
- 不能出现 <sty 这样的不完整标签
- 不能出现 </scri 这样的不完整闭合标签
```

### CSS文件检查
```
每个CSS规则必须完整：
- 选择器 { 属性: 值; }
- 不能缺少开始或结束大括号
- 不能在属性声明中间截断
```

### 大文件处理
```
对于超过50行的文件：
1. 先写入基础结构
2. 分段添加内容
3. 每段都验证完整性
4. 最后统一检查整体结构
```

## 执行原则

- **完整性优先**：宁可分多次写入，也要确保每次都完整
- **及时验证**：每次操作后立即检查结果
- **错误零容忍**：发现截断立即修复，不允许在错误基础上继续
- **用户体验**：避免因截断问题影响用户的开发体验

这个规则必须在每次文件操作时严格执行，确保代码的完整性和正确性。