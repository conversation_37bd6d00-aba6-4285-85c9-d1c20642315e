<template>
  <div :class="['preview-container', { 'dragging': isDragging }]">
    <button :class="['toggle-btn', { 'closed': !isMenuOpen }]" @click="toggleMenu">
      <i :class="['fas', isMenuOpen ? 'fa-chevron-right' : 'fa-chevron-left']"></i>
    </button>

    <div :class="['preview-panel', { 'closed': !isMenuOpen }]">
      <div class="preview-content">
        <div class="preview-header">
          <div class="header-icon">
            <i class="fas fa-shopping-bag"></i>
          </div>
          <div class="title-info">
            <h1>背包外观预览</h1>
            <span class="subtitle">Bag Appearance Preview</span>
          </div>
          <button @click="closePreview" class="close-btn">
            <i class="fas fa-times"></i>
          </button>
        </div>

        <div class="search-section">
          <div class="search-box">
            <i class="fas fa-search"></i>
            <input v-model="searchTerm" placeholder="搜索背包外观..." class="styled-input" />
          </div>

          <!-- 旋转提示 -->
          <div class="rotation-hint">
            <i class="fas fa-hand-paper"></i>
            <span>按住右键拖拽可旋转角色</span>
            <div class="rotation-indicator">
              <span class="rotation-angle">{{ Math.round(rotationY) }}°</span>
            </div>
            <button @click="resetRotation" class="reset-rotation-btn" title="重置角色朝向">
              <i class="fas fa-undo"></i>
            </button>
          </div>

          <!-- 视角控制 -->
          <div class="camera-control">
            <div class="camera-header">
              <i class="fas fa-video"></i>
              <span>视角控制</span>
              <button @click="toggleAutoCamera" :class="['auto-toggle', { 'active': isAutoCamera }]">
                <i :class="isAutoCamera ? 'fas fa-eye' : 'fas fa-eye-slash'"></i>
                <span>{{ isAutoCamera ? '自动' : '手动' }}</span>
              </button>
            </div>
            <div class="camera-buttons" v-show="!isAutoCamera">
              <button v-for="(position, viewName) in CAMERA_POSITIONS" :key="viewName"
                @click="setManualCameraPosition(viewName)"
                :class="['camera-btn', { 'active': currentFocusedView === viewName }]"
                :title="`查看${getViewName(viewName)}`">
                <i :class="getCameraIcon(viewName)"></i>
                <span>{{ getViewName(viewName) }}</span>
              </button>
            </div>
          </div>
        </div>

        <div class="appearance-grid">
          <div v-for="(option, index) in filteredOptions" :key="`${option.drawable}-${option.texture}`"
            :class="['appearance-card', { 'selected': selectedIndex === index }]" @click="selectAppearance(index)">
            <div class="card-preview">
              <div class="preview-icon">
                <i class="fas fa-shopping-bag"></i>
              </div>
              <div class="appearance-info">
                <div class="appearance-name">背包外观 #{{ index + 1 }}</div>
                <div class="appearance-details">

                </div>
              </div>
            </div>
            <div class="selection-indicator" v-if="selectedIndex === index">
              <i class="fas fa-check"></i>
            </div>
          </div>
        </div>

        <div class="preview-footer">
          <div class="action-buttons">
            <button @click="closePreview" class="action-btn cancel">
              <div class="btn-icon">
                <i class="fas fa-times"></i>
              </div>
              <span>取消</span>
            </button>
            <button @click="applyAppearance" class="action-btn apply" :disabled="selectedIndex === -1">
              <div class="btn-icon">
                <i class="fas fa-check"></i>
              </div>
              <span>应用外观</span>
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue';
import { useEvents } from '@Composables/useEvents.js';

interface BagAppearance {
  sex: number;
  dlc: string;
  drawable: number;
  texture: number;
  id: number;
  isProp: boolean;
}

const event = useEvents();

// 外观选项数据
const appearances = ref<BagAppearance[]>([]);
const selectedIndex = ref(-1);
const searchTerm = ref('');

// 菜单控制
const isMenuOpen = ref(true);

// 旋转控制
const isDragging = ref(false);
const lastMouseX = ref(0);
const rotationY = ref(0);

// 视角控制
const currentFocusedView = ref('bag');
const isAutoCamera = ref(true); // 新增：自动/手动切换

// 定义背包预览的摄像机位置
const CAMERA_POSITIONS = ref({
  overview: { zpos: 0.0, fov: 90 },    // 全景 - 整体视角
  bag: { zpos: -0.2, fov: 60 },        // 背包 - 聚焦背包位置
  back: { zpos: -0.1, fov: 70 },       // 背部 - 查看背包背面
  side: { zpos: -0.15, fov: 65 },      // 侧面 - 侧面视角
  close: { zpos: -0.25, fov: 45 }      // 近景 - 背包细节
});

// 过滤后的外观选项
const filteredOptions = computed(() => {
  if (!searchTerm.value) {
    return appearances.value;
  }

  return appearances.value.filter((_, index) => {
    const name = `背包外观 #${index + 1}`;
    return name.toLowerCase().includes(searchTerm.value.toLowerCase());
  });
});

// 选择外观
const selectAppearance = (index: number) => {
  selectedIndex.value = index;
  const selectedOption = filteredOptions.value[index];

  if (selectedOption) {
    // 发送预览请求到服务器
    event.emitServer('bagAppearance:preview',
      selectedOption.dlc,
      selectedOption.drawable,
      selectedOption.id,
      selectedOption.texture,
      selectedOption.isProp
    );
  }
};

// 应用外观
const applyAppearance = () => {
  if (selectedIndex.value !== -1) {
    const selectedOption = filteredOptions.value[selectedIndex.value];
    event.emitServer('bagAppearanceCard:applyAppearance', selectedOption);
    closePreview();
  }
};

// 关闭预览
const closePreview = () => {
  // 发送关闭信号到客户端处理
  event.emitClient('bagAppearance:close');
};

// 切换菜单显示/隐藏
const toggleMenu = () => {
  isMenuOpen.value = !isMenuOpen.value;
};

// 重置旋转
const resetRotation = () => {
  rotationY.value = 0;
  event.emitClient("PLAYER_ROTATE", 0);
};

// 切换自动/手动相机
const toggleAutoCamera = () => {
  isAutoCamera.value = !isAutoCamera.value;
  if (isAutoCamera.value) {
    // 自动相机：设置一个默认位置
    setManualCameraPosition('bag');
  } else {
    // 手动相机：设置一个默认位置
    setManualCameraPosition('overview');
  }
};

// 设置手动相机位置
const setManualCameraPosition = (viewName: string) => {
  const position = CAMERA_POSITIONS.value[viewName];
  if (position) {
    currentFocusedView.value = viewName;
    console.log(`切换摄像机视角到: ${viewName}, zpos: ${position.zpos}, fov: ${position.fov}`);
    event.emitClient("CAMERA_SET_POSITION", position.zpos, position.fov);
  }
};

// 获取视角名称
const getViewName = (viewName: string) => {
  const viewNames: Record<string, string> = {
    overview: '全景',
    bag: '背包',
    back: '背部',
    side: '侧面',
    close: '近景'
  };
  return viewNames[viewName] || viewName;
};

// 获取摄像机图标
const getCameraIcon = (viewName: string) => {
  const icons: Record<string, string> = {
    overview: 'fas fa-expand-arrows-alt',
    bag: 'fas fa-shopping-bag',
    back: 'fas fa-user',
    side: 'fas fa-eye',
    close: 'fas fa-search-plus'
  };
  return icons[viewName] || 'fas fa-video';
};

// 鼠标拖拽事件处理
const handleMouseDown = (e: MouseEvent) => {
  if (e.button === 2) { // 右键
    isDragging.value = true;
    lastMouseX.value = e.clientX;
    e.preventDefault();
  }
};

const handleMouseMove = (e: MouseEvent) => {
  if (isDragging.value) {
    const deltaX = e.clientX - lastMouseX.value;
    const rotationSpeed = 0.3;

    let newRotation = rotationY.value + deltaX * rotationSpeed;

    if (newRotation > 180) {
      newRotation = newRotation - 360;
    } else if (newRotation < -180) {
      newRotation = newRotation + 360;
    }

    rotationY.value = newRotation;
    event.emitClient("PLAYER_ROTATE", rotationY.value);

    lastMouseX.value = e.clientX;
    e.preventDefault();
  }
};

const handleMouseUp = (e: MouseEvent) => {
  if (e.button === 2) { // 右键
    isDragging.value = false;
    e.preventDefault();
  }
};

// 监听外观数据
event.on('bagAppearance:setOptions', (options: BagAppearance[]) => {
  appearances.value = options;
  selectedIndex.value = -1;

  // 设置摄像机聚焦背包，使用cloth插件的事件
  event.emitClient("CAMERA_SET_POSITION", 0.0, 70);
});

onMounted(() => {
  // 添加模拟数据用于样式预览

  /* const mockData: BagAppearance[] = [
     { sex: 0, dlc: "mp_m_freemode_01", drawable: 0, texture: 0, id: 1, isProp: false },
     { sex: 0, dlc: "mp_m_freemode_01", drawable: 1, texture: 0, id: 2, isProp: false },
     { sex: 0, dlc: "mp_m_freemode_01", drawable: 2, texture: 0, id: 3, isProp: false },
     { sex: 0, dlc: "mp_m_freemode_01", drawable: 3, texture: 0, id: 4, isProp: false },
     { sex: 0, dlc: "mp_m_freemode_01", drawable: 4, texture: 0, id: 5, isProp: false },
     { sex: 0, dlc: "mp_m_freemode_01", drawable: 5, texture: 0, id: 6, isProp: false },
     { sex: 0, dlc: "mp_m_freemode_01", drawable: 0, texture: 1, id: 7, isProp: false },
     { sex: 0, dlc: "mp_m_freemode_01", drawable: 1, texture: 1, id: 8, isProp: false },
     { sex: 0, dlc: "mp_m_freemode_01", drawable: 2, texture: 1, id: 9, isProp: false },
     { sex: 0, dlc: "mp_m_freemode_01", drawable: 3, texture: 1, id: 10, isProp: false },
     { sex: 0, dlc: "mp_m_freemode_01", drawable: 4, texture: 1, id: 11, isProp: false },
     { sex: 0, dlc: "mp_m_freemode_01", drawable: 5, texture: 1, id: 12, isProp: false },
     { sex: 0, dlc: "mp_m_freemode_01", drawable: 0, texture: 2, id: 13, isProp: false },
     { sex: 0, dlc: "mp_m_freemode_01", drawable: 1, texture: 2, id: 14, isProp: false },
     { sex: 0, dlc: "mp_m_freemode_01", drawable: 2, texture: 2, id: 15, isProp: false },
     { sex: 0, dlc: "mp_m_freemode_01", drawable: 3, texture: 2, id: 16, isProp: false },
     { sex: 0, dlc: "mp_m_freemode_01", drawable: 4, texture: 2, id: 17, isProp: false },
     { sex: 0, dlc: "mp_m_freemode_01", drawable: 5, texture: 2, id: 18, isProp: false },
     { sex: 0, dlc: "mp_m_freemode_01", drawable: 0, texture: 3, id: 19, isProp: false },
     { sex: 0, dlc: "mp_m_freemode_01", drawable: 1, texture: 3, id: 20, isProp: false }
   ];
   
    appearances.value = mockData;*/

  // 使用cloth插件的控制事件
  event.emitClient('clothshopopendisablecontrols');

  // 添加鼠标事件监听
  document.addEventListener('mousedown', handleMouseDown);
  document.addEventListener('mousemove', handleMouseMove);
  document.addEventListener('mouseup', handleMouseUp);

  // 防止拖拽时选中文本
  document.addEventListener('selectstart', (e) => {
    if (isDragging.value) {
      e.preventDefault();
    }
  });
});

onUnmounted(() => {
  // 移除事件监听
  document.removeEventListener('mousedown', handleMouseDown);
  document.removeEventListener('mousemove', handleMouseMove);
  document.removeEventListener('mouseup', handleMouseUp);

  // 恢复原始外观
  event.emitServer('bagAppearance:restore');
});
</script>

<style scoped>
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

/* 设计变量 - 绿色主题 */
.preview-container {
    --primary-color: #64ffda;
    --primary-dark: #00d4aa;
    --card-bg: rgba(15, 23, 42, 0.95);
    --card-secondary: rgba(30, 41, 59, 0.8);
    --card-tertiary: rgba(51, 65, 85, 0.6);
    --text-primary: #ffffff;
    --text-secondary: rgba(255, 255, 255, 0.8);
    --text-tertiary: rgba(255, 255, 255, 0.6);
    --border-primary: rgba(100, 255, 218, 0.2);
    --border-secondary: rgba(100, 255, 218, 0.1);
    --shadow-sm: 0 0.2vh 0.4vh rgba(0, 0, 0, 0.1);
    --shadow-md: 0 0.4vh 0.8vh rgba(0, 0, 0, 0.15);
    --shadow-lg: 0 0.8vh 1.6vh rgba(0, 0, 0, 0.2);
    --shadow-xl: 0 1.6vh 3.2vh rgba(0, 0, 0, 0.25);
}

/* CSS重置和基础样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

.preview-container {
    position: fixed;
    top: 0;
    right: 0;
    height: 100vh;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
    z-index: 1000;
    animation: fadeIn 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 拖拽状态样式 */
.preview-container.dragging {
    cursor: grabbing !important;
    user-select: none;
}

.preview-container.dragging * {
    cursor: grabbing !important;
    user-select: none;
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

.preview-panel {
    position: fixed;
    top: 0;
    right: 0;
    width: 30vw;
    min-width: 42vh;
    height: 100vh;
    background: var(--card-bg);
    border-left: 1px solid var(--border-primary);
    border-radius: 2vh 0 0 2vh;
    transition: transform 0.5s cubic-bezier(0.4, 0, 0.2, 1);
    overflow: hidden;
    box-shadow: var(--shadow-xl);
}

.preview-panel.closed {
    transform: translateX(100%);
}

.toggle-btn {
    position: fixed;
    right: 30vw;
    top: 50%;
    transform: translateY(-50%);
    width: 3vh;
    height: 12vh;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    border: none;
    border-radius: 1.2vh 0 0 1.2vh;
    color: #0f172a;
    font-size: 1.4vh;
    cursor: pointer;
    z-index: 10;
    transition: right 0.5s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 0.4vh 2vh rgba(100, 255, 218, 0.3);
    display: flex;
    align-items: center;
    justify-content: center;
}

.toggle-btn:hover {
    background: var(--primary-color);
    box-shadow: 0 0.6vh 2.5vh rgba(100, 255, 218, 0.5);
    transform: translateY(-50%) scale(1.05);
}

.toggle-btn:active {
    transform: translateY(-50%) scale(0.95);
}

.toggle-btn.closed {
    right: 0;
}

.toggle-btn.closed:hover {
    transform: translateY(-50%) scale(1.05);
}

.toggle-btn.closed:active {
    transform: translateY(-50%) scale(0.95);
}

.preview-content {
    height: 100%;
    padding: 2vh;
    display: flex;
    flex-direction: column;
    gap: 1.5vh;
    overflow: hidden;
}

.preview-header {
    display: flex;
    align-items: center;
    gap: 1.2vh;
    padding: 2vh 1.5vh;
    background: var(--card-secondary);
    border: 1px solid var(--border-primary);
    border-radius: 1.5vh;
    position: relative;
    overflow: hidden;
    flex-shrink: 0;
}

.header-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 4vh;
    height: 4vh;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    border: 1px solid var(--border-primary);
    border-radius: 50%;
    color: #0f172a;
    font-size: 1.8vh;
    box-shadow: 0 0.4vh 0.8vh rgba(100, 255, 218, 0.3);
}

.title-info {
    flex: 1;
}

.title-info h1 {
    color: var(--text-primary);
    font-size: 2vh;
    font-weight: 700;
    letter-spacing: 0.05em;
    margin: 0 0 0.2vh 0;
}

.subtitle {
    font-size: 1vh;
    color: var(--primary-color);
    font-weight: 400;
    text-transform: uppercase;
    letter-spacing: 0.1em;
}

.close-btn {
    background: rgba(239, 68, 68, 0.1);
    border: 1px solid rgba(239, 68, 68, 0.3);
    border-radius: 50%;
    width: 3vh;
    height: 3vh;
    color: #f87171;
    font-size: 1.2vh;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.close-btn:hover {
    background: rgba(239, 68, 68, 0.2);
    border-color: #f87171;
    transform: scale(1.1);
    box-shadow: 0 0.4vh 1.5vh rgba(239, 68, 68, 0.3);
}

.search-section {
    display: flex;
    flex-direction: column;
    gap: 1.2vh;
    flex-shrink: 0;
}

.search-box {
    position: relative;
}

.search-box i {
    position: absolute;
    left: 1.2vh;
    top: 50%;
    transform: translateY(-50%);
    color: var(--primary-color);
    font-size: 1.4vh;
    z-index: 2;
}

.styled-input {
    width: 100%;
    padding: 1.2vh 1.2vh 1.2vh 3.5vh;
    background: var(--card-bg);
    border: 1px solid var(--border-primary);
    border-radius: 1.2vh;
    color: var(--text-primary);
    font-size: 1.4vh;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    font-family: inherit;
    outline: none;
}

.styled-input:focus {
    border-color: var(--primary-color);
    background: var(--card-secondary);
    box-shadow: 0 0 0 0.2vh rgba(100, 255, 218, 0.2), var(--shadow-md);
}

.rotation-hint {
    display: flex;
    align-items: center;
    gap: 0.5vh;
    color: var(--text-secondary);
    font-size: 1.1vh;
    font-weight: 400;
    letter-spacing: 0.05em;
    padding: 0.8vh 1.2vh;
    background: var(--card-secondary);
    border: 1px solid var(--border-primary);
    border-radius: 0.8vh;
}

.rotation-hint i {
    font-size: 1.2vh;
    color: var(--primary-color);
}

.rotation-indicator {
    background: rgba(100, 255, 218, 0.1);
    border: 1px solid var(--border-primary);
    border-radius: 0.6vh;
    padding: 0.3vh 0.8vh;
    font-size: 1.1vh;
    font-weight: 600;
    color: var(--primary-color);
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.reset-rotation-btn {
    background: rgba(100, 255, 218, 0.1);
    border: 1px solid var(--border-primary);
    border-radius: 50%;
    width: 2.5vh;
    height: 2.5vh;
    color: var(--primary-color);
    font-size: 1.2vh;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.reset-rotation-btn:hover {
    background: rgba(100, 255, 218, 0.2);
    border-color: var(--primary-color);
    transform: scale(1.1);
    box-shadow: 0 0.4vh 1.5vh rgba(100, 255, 218, 0.3);
}

.camera-control {
    background: var(--card-secondary);
    border: 1px solid var(--border-primary);
    border-radius: 0.8vh;
    padding: 1vh;
    display: flex;
    flex-direction: column;
    gap: 1vh;
}

.camera-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 0.5vh;
    color: var(--primary-color);
    font-size: 1.1vh;
    font-weight: 500;
}

.camera-header i {
    font-size: 1.2vh;
    color: var(--primary-color);
}

.camera-buttons {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(6vh, 1fr));
    gap: 0.5vh;
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(-1vh);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.camera-btn {
    background: var(--card-tertiary);
    border: 1px solid var(--border-secondary);
    border-radius: 0.6vh;
    padding: 0.6vh 0.8vh;
    color: var(--text-secondary);
    font-size: 0.9vh;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.3vh;
    min-height: 4vh;
    justify-content: center;
}

.camera-btn i {
    font-size: 1.2vh;
}

.camera-btn span {
    font-size: 0.8vh;
    font-weight: 500;
    white-space: nowrap;
}

.camera-btn:hover {
    background: var(--card-bg);
    border-color: var(--border-primary);
    color: var(--text-primary);
    transform: translateY(-0.1vh);
    box-shadow: var(--shadow-md);
}

.camera-btn.active {
    background: rgba(100, 255, 218, 0.1);
    border-color: var(--border-primary);
    color: var(--primary-color);
    box-shadow: 0 0 1.5vh rgba(100, 255, 218, 0.4);
    transform: scale(1.05);
}

.camera-btn:active {
    transform: translateY(0) scale(0.95);
}

.auto-toggle {
    background: rgba(100, 255, 218, 0.1);
    border: 1px solid var(--border-primary);
    border-radius: 2vh;
    padding: 0.4vh 0.8vh;
    color: var(--primary-color);
    font-size: 1.1vh;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 0.3vh;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    flex-shrink: 0;
}

.auto-toggle i {
    font-size: 1.2vh;
}

.auto-toggle span {
    font-size: 0.8vh;
    font-weight: 500;
    white-space: nowrap;
}

.auto-toggle:hover {
    background: rgba(100, 255, 218, 0.2);
    border-color: var(--primary-color);
    transform: scale(1.05);
    box-shadow: 0 0.4vh 1.5vh rgba(100, 255, 218, 0.3);
}

.auto-toggle.active {
    background: rgba(100, 255, 218, 0.2);
    border-color: var(--primary-color);
    box-shadow: 0 0 1vh rgba(100, 255, 218, 0.4);
}

.auto-toggle:active {
    transform: scale(0.95);
}

.appearance-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(20vh, 1fr));
    gap: 1vh;
    padding: 0.5vh;
    overflow-y: auto;
    flex: 1;
    scrollbar-width: thin;
    scrollbar-color: rgba(100, 255, 218, 0.5) rgba(15, 23, 42, 0.3);
}

.appearance-grid::-webkit-scrollbar {
    width: 0.5vh;
}

.appearance-grid::-webkit-scrollbar-track {
    background: rgba(100, 255, 218, 0.05);
    border-radius: 0.25vh;
}

.appearance-grid::-webkit-scrollbar-thumb {
    background: rgba(100, 255, 218, 0.3);
    border-radius: 0.25vh;
    transition: background-color 0.3s ease;
}

.appearance-grid::-webkit-scrollbar-thumb:hover {
    background: rgba(100, 255, 218, 0.5);
}

.appearance-card {
    background: var(--card-tertiary);
    border: 1px solid var(--border-secondary);
    border-radius: 0.8vh;
    padding: 1vh;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
    min-height: 8vh;
    flex-shrink: 0;
    min-width: 20vh;
    height: auto;
}

.appearance-card:hover {
    background: var(--card-bg);
    border-color: var(--border-primary);
    box-shadow: var(--shadow-md);
    transform: translateY(-0.2vh);
}

.appearance-card.selected {
    background: rgba(100, 255, 218, 0.1);
    border-color: var(--border-primary);
    box-shadow: 0 0 2vh rgba(100, 255, 218, 0.3);
}

.card-preview {
    display: flex;
    align-items: center;
    gap: 0.8vh;
    flex-shrink: 0;
    min-height: 5vh;
}

.preview-icon {
    width: 2.8vh;
    height: 2.8vh;
    background: rgba(100, 255, 218, 0.1);
    border: 1px solid var(--border-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--primary-color);
    font-size: 1.2vh;
    flex-shrink: 0;
}

.appearance-info {
    text-align: left;
    width: 100%;
    min-height: auto;
    flex-shrink: 0;
    flex: 1;
}

.appearance-name {
    color: var(--text-primary);
    font-size: 1.2vh;
    font-weight: 600;
    margin-bottom: 0.2vh;
    line-height: 1.1;
    white-space: nowrap;
    overflow: visible;
    text-overflow: clip;
}

.appearance-details {
    display: flex;
    flex-direction: column;
    gap: 0.1vh;
    font-size: 0.9vh;
    color: var(--text-tertiary);
    line-height: 1;
}

.appearance-details span {
    white-space: nowrap;
    overflow: visible;
    text-overflow: clip;
}

.selection-indicator {
    position: absolute;
    top: 0.3vh;
    right: 0.3vh;
    width: 1.6vh;
    height: 1.6vh;
    background: var(--primary-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #0f172a;
    font-size: 0.8vh;
    font-weight: bold;
    flex-shrink: 0;
}

.preview-footer {
    flex-shrink: 0;
    padding-top: 1vh;
    border-top: 1px solid var(--border-primary);
}

.action-buttons {
    display: flex;
    gap: 1.5vh;
    justify-content: center;
}

.action-btn {
    padding: 1.4vh 2vh;
    border-radius: 1.2vh;
    font-size: 1.3vh;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.8vh;
    border: 1px solid transparent;
    min-width: 10vh;
}

.action-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.action-btn:disabled:hover {
    transform: none;
    box-shadow: none;
}

.btn-icon {
    width: 2.5vh;
    height: 2.5vh;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2vh;
    flex-shrink: 0;
}

.action-btn.cancel {
    background: rgba(239, 68, 68, 0.1);
    color: #f87171;
    border-color: rgba(239, 68, 68, 0.3);
}

.action-btn.cancel:hover:not(:disabled) {
    background: rgba(239, 68, 68, 0.2);
    border-color: #f87171;
    transform: translateY(-0.2vh);
    box-shadow: 0 0.4vh 1.5vh rgba(239, 68, 68, 0.3);
}

.action-btn.apply {
    background: rgba(100, 255, 218, 0.1);
    color: var(--primary-color);
    border-color: var(--border-primary);
}

.action-btn.apply:hover:not(:disabled) {
    background: rgba(100, 255, 218, 0.2);
    border-color: var(--primary-color);
    transform: translateY(-0.2vh);
    box-shadow: 0 0.4vh 1.5vh rgba(100, 255, 218, 0.3);
}

.action-btn:active:not(:disabled) {
    transform: translateY(0) scale(0.98);
}
</style>