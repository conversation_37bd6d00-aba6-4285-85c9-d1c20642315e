# Vue组件开发准则

## 设计原则

### 功能导向
- **服务功能**：组件设计首要服务于功能需求
- **现代简约**：采用现代简约设计风格，追求精致感
- **游戏UI**：本质是游戏界面，避免过度复杂化

### 美化前分析
- **理解原功能**：美化前必须先搞懂原Vue组件的用途和功能
- **分析布局特点**：了解原有布局结构和位置关系
- **保持功能完整性**：美化过程中不能破坏原有功能逻辑

### 设计参考
- **参考成功案例**：在设计时参考知名游戏和网站的成功设计
  - **游戏参考**：Steam、Epic Games、Battle.net等游戏平台的UI设计
  - **网站参考**：Discord、Spotify、Figma等现代应用的界面设计
  - **设计原则**：学习其布局结构、色彩搭配、交互设计等优秀元素
  - **适配游戏环境**：将参考的设计元素适配到CEF游戏环境中

### 元素作用区分原则
- **装饰元素**：纯装饰性质，可以精致美观，注重视觉效果
- **互动元素**：玩家需要交互的元素，根据重要性和数量决定设计风格
  - **重要互动元素**：核心功能按钮等，采用精致设计
  - **次要互动元素**：辅助功能按钮等，可采用紧凑设计
  - **数量判断**：互动元素多时采用紧凑设计，少时采用精致设计
- **信息元素**：纯展示信息的元素，根据场合和数量决定设计风格
  - **商品展示类**：需要精致美观，突出商品价值
  - **列表信息类**：类目多时采用紧凑设计，提高信息密度
  - **重要信息**：关键数据、状态等，采用精致设计突出重要性
  - **可读性优先**：无论信息多寡，必须保证字体大小和显示区域足够，确保玩家不费力地看清信息

### 样式规范
```vue
<style scoped>
/* 必须使用scoped样式 */
/* 尺寸单位：vh和vw，禁止使用px */
/* 界面默认居中显示 */
.container {
  width: 80vw;
  height: 90vh;
  font-size: 2vh;
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}
</style>
```

### 尺寸单位规范
- **主要尺寸**：使用vh/vw（如容器大小、字体大小）
- **边框和阴影**：可以使用1px（如border: 1px solid）
- **过渡动画**：可以使用秒数（如transition: 0.2s）
- **避免使用**：具体的像素值（如padding: 16px、margin: 12px等）

### 推荐配色方案
```vue
<style scoped>
/* CSS变量定义 - 在组件内部定义，不使用:root */
.component-container {
  /* 主色调 - 青绿色系 */
  --primary-color: #64ffda;        /* 主色 */
  --primary-hover: #ffffff;        /* 主色悬停 */
  --primary-bg: #0f172a;           /* 主背景 */

  /* 卡片背景 */
  --card-bg: rgba(30, 41, 59, 0.8);     /* 卡片背景 */
  --card-border: rgba(100, 255, 218, 0.1); /* 卡片边框 */

  /* 文字颜色 */
  --text-primary: #f8fafc;         /* 主要文字 */
  --text-secondary: #94a3b8;       /* 次要文字 */
  --text-muted: #64748b;           /* 辅助文字 */

  /* 状态颜色 */
  --success: #64ffda;              /* 成功/主要操作 */
  --warning: #a78bfa;              /* 警告/次要操作 */
  --danger: #ef4444;               /* 危险操作 */
}
</style>
```

## 性能优化

### 核心要求
- **运行性能**：保证界面流畅运行
- **避免复杂效果**：不使用过于复杂的动画和特效，尤其是毛玻璃效果
- **响应式设计**：使用vh/vw单位确保适配性，不允许使用媒体查询
- **CEF环境限制**：禁止使用alert、confirm、prompt等浏览器原生弹窗

### 最佳实践
```vue
<script setup>
// 使用组合式API
import { ref, computed, onMounted, onUnmounted } from 'vue'

// 避免不必要的响应式数据
const staticData = ref(null)
const computedValue = computed(() => {
  // 计算属性缓存
})

// 错误处理 - 使用自定义弹窗而非浏览器原生弹窗
function handleError(message: string) {
  // 使用自定义UI组件显示错误信息
  events.emitClient('show-error', message)
}
</script>
```

## 事件通信

### useEvents 核心用法

```vue
<script lang="ts" setup>
import { useEvents } from '@Composables/useEvents'

const events = useEvents()

// 接收服务器或客户端事件
events.on('event-from-server-or-client', (var1: string, var2: number) => {
  console.log(var1, var2)
})

// 发送到客户端事件
events.emitClient('emit-to-client-event', myVariable)

// 发送到服务器事件
events.emitServer('emit-to-server-event', myVariable)

// RPC调用 - 服务器端
const serverResult = await events.emitServerRpc('get-something', 'hello')

// RPC调用 - 客户端
const clientResult = await events.emitClientRpc('get-something', 'hello')
</script>
```

### 按键监听

```vue
<script setup>
import { onMounted, onUnmounted } from 'vue'
import { useEvents } from '@Composables/useEvents'

const Events = useEvents()

function handleKeyPress() {
  console.log('按键被触发')
}

onMounted(() => {
  Events.onKeyUp('inventory', 73, handleKeyPress) // 73 = I键
})

onUnmounted(() => {
  Events.offKeyUp('inventory')
})
</script>
```

## 数据存储

### useLocalStorage 使用

```vue
<script setup>
import { useLocalStorage } from '@Composables/useLocalStorage'

const localStorage = useLocalStorage()

// 存储数据
await localStorage.set('myKey', 'someValue')

// 获取数据
const result = await localStorage.get('myKey')

// 删除数据
await localStorage.remove('myKey')
</script>
```

## 玩家状态管理

### usePlayerStats 使用

```vue
<script lang="ts" setup>
import { usePlayerStats } from '@Composables/usePlayerStats'

const {
  armour,
  health,
  stamina,
  speed,
  inVehicle,
  vehicleHealth,
  weapon,
  time,
  weather,
  zone
} = usePlayerStats()
</script>

<template>
  <div class="stats-container">
    <div class="stat-item">
      <span>生命值: {{ health }}</span>
    </div>
    <div class="stat-item">
      <span>护甲: {{ armour }}</span>
    </div>
    <div class="stat-item">
      <span>速度: {{ speed }}</span>
    </div>
  </div>
</template>
```

## 组件结构规范

### 基础模板

```vue
<template>
  <div class="component-container">
    <!-- 内容区域 -->
  </div>
</template>

<script lang="ts" setup>
// 导入依赖
import { ref, computed } from 'vue'
import { useEvents } from '@Composables/useEvents'

// 组件逻辑
const events = useEvents()

// 响应式数据
const data = ref(null)

// 计算属性
const computedData = computed(() => {
  return data.value
})

// 方法定义
function handleAction() {
  events.emitServer('action-event', data.value)
}
</script>

<style scoped>
.component-container {
  width: 80vw;
  height: 90vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  /* 默认居中显示 */
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  /* 推荐背景色 */
  background: rgba(30, 41, 59, 0.8);
  border: 1px solid rgba(100, 255, 218, 0.1);
  border-radius: 1.2vh;
}
</style>
```

### 卡片组件样式

```vue
<template>
  <div class="card-container">
    <!-- 卡片头部 -->
    <div class="card-header">
      <div class="header-icon">
        <i class="fas fa-icon"></i>
      </div>
      <div class="header-content">
        <h3 class="card-title">卡片标题</h3>
        <span class="card-subtitle">副标题</span>
      </div>
    </div>
    
    <!-- 卡片内容 -->
    <div class="card-content">
      <div class="content-section">
        <!-- 内容区域 -->
      </div>
    </div>
    
    <!-- 卡片操作 -->
    <div class="card-actions">
      <button class="action-btn primary">
        <i class="fas fa-check"></i>
        <span>主要操作</span>
      </button>
      <button class="action-btn secondary">
        <i class="fas fa-times"></i>
        <span>次要操作</span>
      </button>
    </div>
  </div>
</template>

<style scoped>
/* CSS变量定义 - 在组件内部定义 */
.card-container {
  /* 主色调 - 青绿色系 */
  --primary-color: #64ffda;        /* 主色 */
  --primary-hover: #ffffff;        /* 主色悬停 */
  --primary-bg: #0f172a;           /* 主背景 */
  --card-bg: rgba(30, 41, 59, 0.8);     /* 卡片背景 */
  --card-border: rgba(100, 255, 218, 0.1); /* 卡片边框 */
  --text-primary: #f8fafc;         /* 主要文字 */
  --text-secondary: #94a3b8;       /* 次要文字 */
  --text-muted: #64748b;           /* 辅助文字 */
  --success: #64ffda;              /* 成功/主要操作 */
  --warning: #a78bfa;              /* 警告/次要操作 */
  --danger: #ef4444;               /* 危险操作 */

  background: var(--card-bg);
  border: 1px solid var(--card-border);
  border-radius: 1.2vh;
  padding: 1.6vh;
  transition: all 0.2s ease;
}

.card-container:hover {
  border-color: rgba(100, 255, 218, 0.2);
  box-shadow: 0 0.4vh 1.2vh rgba(0, 0, 0, 0.1);
}

/* 卡片头部 */
.card-header {
  display: flex;
  align-items: center;
  gap: 1.2vh;
  margin-bottom: 1.6vh;
}

.header-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 3.2vh;
  height: 3.2vh;
  background: var(--primary-color);
  border-radius: 50%;
  color: var(--primary-bg);
  font-size: 1.6vh;
  font-weight: 600;
}

.card-title {
  color: var(--text-primary);
  font-size: 1.6vh;
  font-weight: 600;
  margin: 0;
  letter-spacing: -0.01em;
}

.card-subtitle {
  font-size: 1.1vh;
  color: var(--text-muted);
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 1px;
  margin-top: 0.2vh;
}

/* 卡片内容 */
.card-content {
  margin-bottom: 1.6vh;
}

.content-section {
  background: var(--primary-bg);
  border: 1px solid var(--card-border);
  border-radius: 1.2vh;
  padding: 1.2vh;
}

/* 卡片操作 */
.card-actions {
  display: flex;
  gap: 1.2vh;
}

.action-btn {
  padding: 1vh 1.6vh;
  border-radius: 1.2vh;
  font-size: 1.3vh;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 0.6vh;
  border: 1px solid transparent;
  min-height: 4vh;
}

.action-btn.primary {
  background: var(--primary-color);
  color: var(--primary-bg);
  font-weight: 600;
  border-color: var(--primary-color);
}

.action-btn.primary:hover {
  background: var(--primary-hover);
  color: var(--primary-color);
  box-shadow: 0 0.4vh 1.2vh rgba(100, 255, 218, 0.3);
  transform: translateY(-0.1vh);
}

.action-btn.secondary {
  background: var(--card-bg);
  color: var(--text-secondary);
  border-color: rgba(148, 163, 184, 0.2);
}

.action-btn.secondary:hover {
  background: rgba(148, 163, 184, 0.15);
  color: var(--text-primary);
  border-color: rgba(148, 163, 184, 0.4);
  transform: translateY(-0.1vh);
}
</style>
```

## 注意事项

1. **尺寸限制**：最大高度90vh，最大宽度80vw（无最小限制）
2. **界面居中**：除非特意声明，否则界面始终居中屏幕中央
3. **字体单位**：统一使用vh/vw，确保适配性
4. **事件清理**：组件卸载时清理事件监听器
5. **性能监控**：避免频繁的DOM操作和计算
6. **错误处理**：对异步操作进行适当的错误处理
7. **美化原则**：美化前必须理解原组件功能和布局特点
8. **元素区分**：根据元素作用（装饰/互动/信息）采用不同设计策略
9. **可读性保障**：信息元素必须保证足够的字体大小和显示区域，确保玩家轻松阅读
10. **CEF环境**：禁止使用alert、confirm、prompt等浏览器原生弹窗
11. **设计风格**：推荐使用青绿色系配色方案和卡片式布局（非强制）
12. **CSS变量**：在组件内部定义CSS变量，不使用:root选择器
13. **设计参考**：参考知名游戏平台和现代应用的成功设计，学习其优秀元素并适配到游戏环境 