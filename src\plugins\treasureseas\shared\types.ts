export interface TreasureItem {
    name: string;
    quantity: number;
}

export interface TreasureReward {
    goldBars: number;
    treasureItems: TreasureItem[];
}

export interface TreasureDisplayData extends TreasureReward {
    depth: number;
    depthLevel: string;
}

export interface TreasureRarityConfig {
    [itemName: string]: {
        rarity: string;
        class: string;
    };
}

export const DEPTH_LEVELS = {
    SHALLOW: '浅层',
    MEDIUM: '中层',
    DEEP: '深层'
} as const;

export const TREASURE_ITEMS = {
    ANCIENT_GOLD_COIN: '古代金币',
    OCEAN_PEARL: '海洋珍珠',
    SAPPHIRE: '蓝宝石',
    EMERALD: '祖母绿',
    DIAMOND: '钻石',
    ANCIENT_ARTIFACT: '古代文物',
    TREASURE_KEY: '沉船宝箱钥匙'
} as const;

export const ITEM_RARITY = {
    COMMON: 'common',
    RARE: 'rare',
    EPIC: 'epic',
    LEGENDARY: 'legendary'
} as const;

export const ITEM_RARITY_CONFIG: TreasureRarityConfig = {
    [TREASURE_ITEMS.ANCIENT_GOLD_COIN]: { rarity: '史诗', class: ITEM_RARITY.EPIC },
    [TREASURE_ITEMS.OCEAN_PEARL]: { rarity: '稀有', class: ITEM_RARITY.RARE },
    [TREASURE_ITEMS.SAPPHIRE]: { rarity: '史诗', class: ITEM_RARITY.EPIC },
    [TREASURE_ITEMS.EMERALD]: { rarity: '史诗', class: ITEM_RARITY.EPIC },
    [TREASURE_ITEMS.DIAMOND]: { rarity: '传奇', class: ITEM_RARITY.LEGENDARY },
    [TREASURE_ITEMS.ANCIENT_ARTIFACT]: { rarity: '传奇', class: ITEM_RARITY.LEGENDARY },
    [TREASURE_ITEMS.TREASURE_KEY]: { rarity: '传奇', class: ITEM_RARITY.LEGENDARY }
};

export const TREASURE_EVENTS = {
    SHOW_TREASURE_DISPLAY: 'treasureseas:showTreasureDisplay',
    CLOSE_TREASURE_DISPLAY: 'treasureseas:closeTreasureDisplay'
} as const; 