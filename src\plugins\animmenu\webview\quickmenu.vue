<template>
  <div class="quick-menu-container">
    <div class="quick-menu-panel">
      <div class="menu-grid" v-if="allMenuItems.length > 0">
        <div 
          v-for="(item, index) in allMenuItems" 
          :key="`${item.type}-${index}`"
          :class="getItemClass(item)"
          @click="handleItemClick(item)"
        >
          <div class="item-icon">
            <i :class="getItemIcon(item)"></i>
          </div>
          <span class="item-label">{{ item.label }}</span>
        </div>
      </div>

      <div v-else class="empty-state">
        <i class="fas fa-inbox"></i>
        <p>暂无快捷操作</p>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { onMounted, ref, computed } from 'vue'
import { useEvents } from '@Composables/useEvents.js'
import { useLocalStorage } from '@Composables/useLocalStorage.js'

const events = useEvents()
const localStorage = useLocalStorage()
const animations = ref<any[]>([])
const quickMeCommands = ref<string[]>([])
const quickDoCommands = ref<string[]>([])

const allMenuItems = computed(() => {
  const items: any[] = []
  
  // 添加动画项
  animations.value.forEach(favAnim => {
    const name = favAnim.chineseName.length > 6 ? favAnim.chineseName.substring(0, 6) + '..' : favAnim.chineseName
    items.push({
      type: 'animation',
      data: favAnim,
      label: `${name}${favAnim.upperBody ? '·上' : '·全'}`
    })
  })
  
  // 添加命令项
  quickMeCommands.value.forEach(cmd => {
    items.push({
      type: 'me-command',
      data: cmd,
      label: cmd.length > 8 ? cmd.substring(0, 8) + '..' : cmd
    })
  })
  
  quickDoCommands.value.forEach(cmd => {
    items.push({
      type: 'do-command', 
      data: cmd,
      label: cmd.length > 8 ? cmd.substring(0, 8) + '..' : cmd
    })
  })
  
  // 添加停止按钮
  items.push({
    type: 'stop',
    data: null,
    label: '停止动画'
  })
  
  return items
})



function getItemIcon(item: any) {
  const icons = {
    'animation': 'fas fa-play',
    'me-command': 'fas fa-user',
    'do-command': 'fas fa-comment',
    'stop': 'fas fa-stop'
  }
  return icons[item.type as keyof typeof icons] || 'fas fa-play'
}

function getItemClass(item: any) {
  return `menu-item ${item.type}-item`
}



function playAnimation(favAnim: any) {
  const anim = favAnim.animation
  if ('Scenario' in anim && anim.Scenario) {
    events.emitClient('playQuickAnim', {
      scenario: anim.Scenario,
      type: 'scenario',
      chineseName: anim.chineseName
    })
  } else if ('dict' in anim && 'name' in anim) {
    events.emitClient('playQuickAnim', {
      dict: anim.dict,
      name: anim.name,
      upperBody: favAnim.upperBody,
      chineseName: anim.chineseName
    })
  }
}

function stopAnimation() {
  events.emitClient('stopQuickAnim')
}

function handleItemClick(item: any) {
  const actions = {
    'animation': () => playAnimation(item.data),
    'me-command': () => executeCommand('me', item.data),
    'do-command': () => executeCommand('do', item.data),
    'stop': () => stopAnimation()
  }
  actions[item.type as keyof typeof actions]?.()
}

function executeCommand(type: string, message: string) {
  events.emitServer('executeQuickCommand', type, message)
}

async function loadQuickCommands() {
  try {
    const [savedMeCommands, savedDoCommands] = await Promise.all([
      localStorage.get('quickMeCommands'),
      localStorage.get('quickDoCommands')
    ])
    
    if (savedMeCommands) {
      quickMeCommands.value = JSON.parse(savedMeCommands)
    }
    
    if (savedDoCommands) {
      quickDoCommands.value = JSON.parse(savedDoCommands)
    }
  } catch (e) {
    console.error('加载命令失败', e)
  }
}

events.on('showQuickAnimMenu', (anims: any[]) => {
  animations.value = Array.isArray(anims) ? anims : []
  loadQuickCommands()
})

onMounted(() => {
  loadQuickCommands()



/*
// Mock数据用于测试 - 提供不同数量的测试场景
const mockAnimations = [
  {
    animation: { chineseName: "挥手", dict: "mp_player_inteat@pnq", name: "intro" },
    upperBody: false,
    chineseName: "挥手",
    category: "手势"
  },
  {
    animation: { chineseName: "点头", dict: "mp_player_inteat@pnq", name: "intro" },
    upperBody: true,
    chineseName: "点头", 
    category: "表情"
  },
  {
    animation: { chineseName: "跳舞", dict: "anim@amb@nightclub@dancers@crowddance_facedj@hi_intensity", name: "hi_dance_facedj_17_v2_male^1" },
    upperBody: false,
    chineseName: "跳舞",
    category: "娱乐"
  },
  {
    animation: { chineseName: "思考", dict: "mp_player_inteat@pnq", name: "intro" },
    upperBody: true,
    chineseName: "思考",
    category: "表情" 
  },
  {
    animation: { chineseName: "敬礼", dict: "mp_player_inteat@pnq", name: "intro" },
    upperBody: true,
    chineseName: "敬礼",
    category: "动作"
  }
];

const mockMeCommands = ["摸了摸下巴", "看了看手表", "整理衣服", "伸了个懒腰"];
const mockDoCommands = ["天空飘着白云", "鸟儿在歌唱", "微风轻抚", "阳光很温暖"];

  animations.value = mockAnimations
  quickMeCommands.value = mockMeCommands
  quickDoCommands.value = mockDoCommands
*/

})
</script>

<style scoped>
.quick-menu-container {
  --primary-color: #64ffda;
  --card-bg: rgba(15, 20, 30, 0.8);
  --text-primary: #ffffff;
  --text-secondary: rgba(255, 255, 255, 0.7);
  --border-color: rgba(100, 255, 218, 0.15);

  position: fixed;
  bottom: 4vh;
  left: 50%;
  transform: translateX(-50%);
  z-index: 9999;
  font-family: "Inter", system-ui, sans-serif;
}

.quick-menu-panel {
  background: var(--card-bg);
  border: 1px solid var(--border-color);
  border-radius: 1.2vh;
  padding: 1.2vh;
  box-shadow: 0 0.4vh 1.2vh rgba(0, 0, 0, 0.6);
  max-width: 80vw;
}

.menu-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 1vh;
  justify-content: center;
  align-items: center;
}

.menu-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.6vh;
  padding: 1.2vh;
  background: rgba(255, 255, 255, 0.02);
  border: 1px solid rgba(100, 255, 218, 0.08);
  border-radius: 0.8vh;
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: center;
  min-width: 8vh;
  position: relative;
}

.menu-item:hover {
  background: rgba(100, 255, 218, 0.1);
  border-color: rgba(100, 255, 218, 0.3);
  box-shadow: 0 0.3vh 0.8vh rgba(100, 255, 218, 0.2);
  transform: translateY(-0.2vh);
}

.item-icon {
  width: 4vh;
  height: 4vh;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.8vh;
  color: var(--text-primary);
  transition: all 0.2s ease;
}

.item-label {
  font-size: 1.3vh;
  font-weight: 500;
  color: var(--text-secondary);
  max-width: 10vh;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  transition: color 0.2s ease;
}

.menu-item:hover .item-label {
  color: var(--text-primary);
}

.animation-item .item-icon {
  background: rgba(100, 255, 218, 0.2);
  border: 1px solid rgba(100, 255, 218, 0.3);
}

.animation-item:hover .item-icon {
  background: rgba(100, 255, 218, 0.3);
  border-color: rgba(100, 255, 218, 0.5);
}

.me-command-item .item-icon {
  background: rgba(68, 255, 170, 0.2);
  border: 1px solid rgba(68, 255, 170, 0.3);
}

.me-command-item:hover .item-icon {
  background: rgba(68, 255, 170, 0.3);
  border-color: rgba(68, 255, 170, 0.5);
}

.do-command-item .item-icon {
  background: rgba(255, 170, 68, 0.2);
  border: 1px solid rgba(255, 170, 68, 0.3);
}

.do-command-item:hover .item-icon {
  background: rgba(255, 170, 68, 0.3);
  border-color: rgba(255, 170, 68, 0.5);
}

.stop-item .item-icon {
  background: rgba(255, 68, 68, 0.2);
  border: 1px solid rgba(255, 68, 68, 0.3);
}

.stop-item:hover .item-icon {
  background: rgba(255, 68, 68, 0.3);
  border-color: rgba(255, 68, 68, 0.5);
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1.2vh;
  padding: 3vh 2vh;
  text-align: center;
}

.empty-state i {
  font-size: 3vh;
  color: rgba(100, 255, 218, 0.3);
}

.empty-state p {
  font-size: 1.4vh;
  color: var(--text-secondary);
  margin: 0;
  font-weight: 500;
}
</style> 