import * as alt from 'alt-server';
import { useRebar } from '@Server/index.js';
import { Vehicle } from '@Shared/types/vehicle.js';
import { CollectionNames } from '@Server/document/shared.js';
import { getGarageTypeFromVehicleClass, GarageVehicleType } from '../shared/vehicleTypes.js';

const Rebar = useRebar();
const db = Rebar.database.useDatabase();

/**
 * 载具类型管理器
 * 用于检测、存储和检索载具的车库类型
 */
export class VehicleTypeManager {
    
    /**
     * 从数据库获取载具的车库类型
     * @param vehicleId 载具数据库ID
     * @returns 车库类型或null（如果不存在）
     */
    static async getVehicleTypeFromDB(vehicleId: string): Promise<GarageVehicleType | null> {
        try {
            const vehicleData = await db.getMany<Vehicle & { vehicleType?: GarageVehicleType }>({ _id: vehicleId }, CollectionNames.Vehicles);
            if (vehicleData && vehicleData.length > 0 && vehicleData[0].vehicleType) {
                return vehicleData[0].vehicleType;
            }
            return null;
        } catch (error) {
            console.warn('Failed to get vehicle type from DB:', error);
            return null;
        }
    }

    /**
     * 保存载具类型到数据库
     * @param vehicleId 载具数据库ID
     * @param vehicleType 车库类型
     */
    static async saveVehicleTypeToDB(vehicleId: string, vehicleType: GarageVehicleType): Promise<void> {
        try {
            await db.update({ _id: vehicleId, vehicleType }, CollectionNames.Vehicles);
        } catch (error) {
            console.warn('Failed to save vehicle type to DB:', error);
        }
    }

    /**
     * 检测并存储载具类型（如果数据库中没有的话）
     * @param player 玩家对象
     * @param vehicle 载具实例
     * @param vehicleId 载具数据库ID
     * @returns 载具的车库类型
     */
    static async detectAndStoreVehicleType(player: alt.Player, vehicle: alt.Vehicle, vehicleId: string): Promise<GarageVehicleType> {
        // 首先尝试从数据库获取
        let vehicleType = await this.getVehicleTypeFromDB(vehicleId);
        
        if (vehicleType) {
            return vehicleType;
        }

        // 使用本地透明载具检测载具类型
        try {
            vehicleType = await player.emitRpc('garage:detectVehicleTypeByModel', vehicle.model) as GarageVehicleType;
            
            // 将检测到的类型保存到数据库
            await this.saveVehicleTypeToDB(vehicleId, vehicleType);
            
            return vehicleType;
        } catch (error) {
            console.warn('Failed to detect vehicle type by model:', error);
            // 如果都失败，返回默认类型
            return GarageVehicleType.CAR;
        }
    }

    /**
     * 通过载具模型检测类型（用于跨车库调车等场景）
     * @param player 玩家对象
     * @param vehicleModel 载具模型
     * @param vehicleId 载具数据库ID
     * @returns 载具的车库类型
     */
    static async detectVehicleTypeByModel(player: alt.Player, vehicleModel: string | number, vehicleId?: string): Promise<GarageVehicleType> {
        // 如果有vehicleId，先尝试从数据库获取
        if (vehicleId) {
            const vehicleType = await this.getVehicleTypeFromDB(vehicleId);
            if (vehicleType) {
                return vehicleType;
            }
        }

        // 使用客户端本地载具进行检测（无碰撞、透明）
        try {
            const vehicleType = await player.emitRpc('garage:detectVehicleTypeByModel', vehicleModel) as GarageVehicleType;
            
            // 如果有vehicleId，保存到数据库
            if (vehicleId) {
                await this.saveVehicleTypeToDB(vehicleId, vehicleType);
            }
            
            return vehicleType;
        } catch (error) {
            console.warn('Failed to detect vehicle type by model:', error);
            return GarageVehicleType.CAR;
        }
    }

    /**
     * 检查载具类型是否可以存入指定车库
     * @param vehicleType 载具类型
     * @param allowedTypes 车库允许的类型列表
     * @returns 是否可以存入
     */
    static canStoreVehicleType(vehicleType: GarageVehicleType, allowedTypes: string[]): boolean {
        return allowedTypes.includes(vehicleType);
    }

    /**
     * 批量为现有载具补充类型信息
     * @param player 执行操作的玩家（用于RPC调用）
     * @param batchSize 批处理大小（默认10）
     */
    static async migrateExistingVehicles(player: alt.Player, batchSize: number = 10): Promise<void> {
        try {
            // 获取所有载具，然后过滤出没有vehicleType字段的
            const allVehicles = await db.getMany<Vehicle & { vehicleType?: GarageVehicleType }>({}, CollectionNames.Vehicles);
            const vehiclesWithoutType = allVehicles.filter(vehicle => !vehicle.vehicleType);
            
            console.log(`Found ${vehiclesWithoutType.length} vehicles without type information`);
            
            // 分批处理
            for (let i = 0; i < vehiclesWithoutType.length; i += batchSize) {
                const batch = vehiclesWithoutType.slice(i, i + batchSize);
                
                for (const vehicleData of batch) {
                    if (vehicleData._id && vehicleData.model) {
                        const vehicleType = await this.detectVehicleTypeByModel(player, vehicleData.model, vehicleData._id);
                        console.log(`Set vehicle ${vehicleData._id} (model: ${vehicleData.model}) type to: ${vehicleType}`);
                    }
                }
                
                // 避免过于频繁的操作
                await new Promise(resolve => setTimeout(resolve, 100));
            }
            
            console.log('Vehicle type migration completed');
        } catch (error) {
            console.error('Failed to migrate existing vehicles:', error);
        }
    }
}