<template>
  <div class="uniform-assignment">
    <div class="assignment-header">
      <h3>制服分配管理</h3>
      <div class="header-actions">
        <button @click="refreshUniforms" class="btn-secondary">
          <i class="fas fa-sync-alt"></i>
          <span>刷新</span>
        </button>
      </div>
    </div>

    <div class="assignment-content" v-if="availableUniforms.length > 0">
      <!-- 制服列表 -->
      <div class="uniforms-section">
        <h4>可用制服</h4>
        <div class="uniforms-grid">
          <div 
            v-for="uniform in availableUniforms" 
            :key="uniform.uid"
            :class="['uniform-item', { 'selected': selectedUniform?.uid === uniform.uid }]"
            @click="selectUniform(uniform)"
          >
            <div class="uniform-header">
              <span class="uniform-name">{{ uniform.uniformname }}</span>
              <div class="uniform-actions">
                <button @click.stop="deleteUniform(uniform)" class="action-btn delete" title="删除制服" v-if="hasPermission('manage_members')">
                  <i class="fas fa-trash"></i>
                </button>
              </div>
            </div>
            <div class="uniform-info">
              <span class="components-count">{{ uniform.uniformcomponents.length }} 个组件</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 等级分配区域 -->
      <div class="assignment-section" v-if="selectedUniform">
        <h4>为 "{{ selectedUniform.uniformname }}" 分配等级权限</h4>
        
        <div class="rank-assignment">
          <div class="assignment-form">
            <div class="form-group">
              <label>最低等级要求</label>
              <select v-model.number="assignmentForm.minRank" class="form-select">
                <option :value="null">无要求</option>
                <option v-for="job in factionJobs" :key="job.rank" :value="job.rank">
                  等级 {{ job.rank }} - {{ job.name }}
                </option>
              </select>
            </div>
            
            <div class="form-group">
              <label>最高等级限制</label>
              <select v-model.number="assignmentForm.maxRank" class="form-select">
                <option :value="null">无限制</option>
                <option v-for="job in factionJobs" :key="job.rank" :value="job.rank">
                  等级 {{ job.rank }} - {{ job.name }}
                </option>
              </select>
            </div>

            <div class="form-actions">
              <button @click="saveAssignment" class="btn-primary" :disabled="!selectedUniform">
                <i class="fas fa-save"></i>
                <span>保存分配</span>
              </button>
              <button @click="clearAssignment" class="btn-secondary">
                <i class="fas fa-times"></i>
                <span>清除限制</span>
              </button>
            </div>
          </div>

          <!-- 当前分配状态 -->
          <div class="current-assignment">
            <h5>当前分配状态</h5>
            <div class="assignment-status">
              <div class="status-item">
                <span class="label">最低等级：</span>
                <span class="value">
                  {{ selectedUniform.requiredRank ? `等级 ${selectedUniform.requiredRank}` : '无要求' }}
                </span>
              </div>
              <div class="status-item">
                <span class="label">最高等级：</span>
                <span class="value">
                  {{ selectedUniform.maxRank ? `等级 ${selectedUniform.maxRank}` : '无限制' }}
                </span>
              </div>
              <div class="status-item">
                <span class="label">适用成员：</span>
                <span class="value">{{ getEligibleMembers(selectedUniform) }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 空状态 -->
    <div v-else class="empty-state">
      <i class="fas fa-tshirt"></i>
      <h3>暂无制服配置</h3>
      <p>请先创建制服套装，然后为不同等级的成员分配制服权限</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue';
import { useEvents } from '@Composables/useEvents.js';

interface FactionUniform {
  uid: string;
  uniformname: string;
  uniformcomponents: any[];
  requiredRank?: number;
  maxRank?: number;
}

interface Props {
  factionData: any;
  hasPermission: (permission: string) => boolean;
}

const props = defineProps<Props>();
const events = useEvents();

// 状态管理
const availableUniforms = ref<FactionUniform[]>([]);
const selectedUniform = ref<FactionUniform | null>(null);
const assignmentForm = ref({
  minRank: null as number | null,
  maxRank: null as number | null
});

// 计算属性
const factionJobs = computed(() => {
  return props.factionData?.jobs || [];
});

// 选择制服
const selectUniform = (uniform: FactionUniform) => {
  selectedUniform.value = uniform;
  assignmentForm.value.minRank = uniform.requiredRank || null;
  assignmentForm.value.maxRank = uniform.maxRank || null;
};

// 获取符合条件的成员数量
const getEligibleMembers = (uniform: FactionUniform) => {
  if (!props.factionData?.members) return '0 人';
  
  const members = props.factionData.members.filter(member => {
    const memberRank = member.job?.rank || 0;
    const meetsMin = !uniform.requiredRank || memberRank >= uniform.requiredRank;
    const meetsMax = !uniform.maxRank || memberRank <= uniform.maxRank;
    return meetsMin && meetsMax;
  });
  
  return `${members.length} 人`;
};

// 加载制服数据
const loadUniforms = async () => {
  try {
    const uniforms = await events.emitServerRpc('faction:getUniforms');
    availableUniforms.value = uniforms || [];
  } catch (error) {
    console.error('加载制服数据失败:', error);
    availableUniforms.value = [];
  }
};

// 保存分配
const saveAssignment = async () => {
  if (!selectedUniform.value) return;
  
  try {
    const updateData = {
      uniformId: selectedUniform.value.uid,
      requiredRank: assignmentForm.value.minRank || undefined,
      maxRank: assignmentForm.value.maxRank || undefined
    };
    
    const success = await events.emitServerRpc('faction:updateUniformAssignment', updateData);
    if (success) {
      // 更新本地数据
      selectedUniform.value.requiredRank = updateData.requiredRank;
      selectedUniform.value.maxRank = updateData.maxRank;
      
      // 刷新列表
      await loadUniforms();
    }
  } catch (error) {
    console.error('保存分配失败:', error);
  }
};

// 清除分配限制
const clearAssignment = () => {
  assignmentForm.value.minRank = null;
  assignmentForm.value.maxRank = null;
};


// 删除制服
const deleteUniform = async (uniform: FactionUniform) => {
  if (confirm(`确定要删除制服 "${uniform.uniformname}" 吗？`)) {
    try {
      const success = await events.emitServerRpc('faction:deleteUniform', uniform.uid);
      if (success) {
        await loadUniforms();
        if (selectedUniform.value?.uid === uniform.uid) {
          selectedUniform.value = null;
        }
      }
    } catch (error) {
      console.error('删除制服失败:', error);
    }
  }
};



// 刷新制服列表
const refreshUniforms = async () => {
  await loadUniforms();
};

// 监听制服更新
events.on('faction:uniformsUpdated', async () => {
  await loadUniforms();
});

// 组件挂载时加载数据
onMounted(async () => {
  await loadUniforms();
});
</script>

<style scoped>
.uniform-assignment {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: rgba(22, 28, 36, 0.5);
  border-radius: 1.2vh;
  overflow: hidden;
}

.assignment-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 2vh;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  background: rgba(30, 41, 59, 0.8);
}

.assignment-header h3 {
  color: #e2e8f0;
  font-size: 2vh;
  font-weight: 600;
  margin: 0;
}

.header-actions {
  display: flex;
  gap: 1vh;
}

.assignment-content {
  flex: 1;
  padding: 2vh;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 2vh;
}

.uniforms-section h4,
.assignment-section h4 {
  color: #a995f4;
  font-size: 1.6vh;
  font-weight: 600;
  margin-bottom: 1vh;
}

.uniforms-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(25vh, 1fr));
  gap: 1.5vh;
  margin-bottom: 2vh;
}

.uniform-item {
  background: rgba(30, 41, 59, 0.8);
  border: 1px solid rgba(100, 255, 218, 0.1);
  border-radius: 1vh;
  padding: 1.5vh;
  cursor: pointer;
  transition: all 0.3s ease;
}

.uniform-item:hover {
  background: rgba(30, 41, 59, 0.9);
  border-color: rgba(100, 255, 218, 0.3);
  transform: translateY(-0.1vh);
}

.uniform-item.selected {
  background: rgba(100, 255, 218, 0.1);
  border-color: #64ffda;
  box-shadow: 0 0 1vh rgba(100, 255, 218, 0.3);
}

.uniform-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.8vh;
}

.uniform-name {
  color: #e2e8f0;
  font-weight: 600;
  font-size: 1.4vh;
}

.uniform-actions {
  display: flex;
  gap: 0.5vh;
}

.action-btn {
  background: rgba(30, 41, 59, 0.8);
  border: 1px solid rgba(100, 255, 218, 0.2);
  border-radius: 0.6vh;
  width: 2.5vh;
  height: 2.5vh;
  color: #94a3b8;
  font-size: 1vh;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.action-btn.preview:hover {
  background: #64ffda;
  color: #0f172a;
  border-color: #64ffda;
}

.action-btn.delete:hover {
  background: #ef4444;
  color: #fff;
  border-color: #ef4444;
}

.uniform-info {
  color: #94a3b8;
  font-size: 1.2vh;
}

.assignment-section {
  background: rgba(30, 41, 59, 0.6);
  border-radius: 1vh;
  padding: 2vh;
}

.rank-assignment {
  display: flex;
  gap: 2vh;
}

.assignment-form {
  flex: 1;
}

.form-group {
  margin-bottom: 1.5vh;
}

.form-group label {
  display: block;
  color: #94a3b8;
  font-size: 1.3vh;
  font-weight: 500;
  margin-bottom: 0.5vh;
}

.form-select {
  width: 100%;
  padding: 1vh;
  background: rgba(30, 41, 59, 0.8);
  border: 1px solid rgba(100, 255, 218, 0.2);
  border-radius: 0.8vh;
  color: #e2e8f0;
  font-size: 1.3vh;
  outline: none;
  transition: all 0.3s ease;
}

.form-select:focus {
  border-color: #64ffda;
  box-shadow: 0 0 0 0.2vh rgba(100, 255, 218, 0.2);
}

.form-actions {
  display: flex;
  gap: 1vh;
  margin-top: 2vh;
}

.current-assignment {
  flex: 1;
  background: rgba(15, 23, 42, 0.8);
  border-radius: 0.8vh;
  padding: 1.5vh;
}

.current-assignment h5 {
  color: #a995f4;
  font-size: 1.4vh;
  font-weight: 600;
  margin-bottom: 1vh;
}

.assignment-status {
  display: flex;
  flex-direction: column;
  gap: 0.8vh;
}

.status-item {
  display: flex;
  justify-content: space-between;
  padding: 0.5vh 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

.status-item .label {
  color: #94a3b8;
  font-size: 1.2vh;
}

.status-item .value {
  color: #e2e8f0;
  font-size: 1.2vh;
  font-weight: 500;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #94a3b8;
  text-align: center;
  padding: 4vh;
}

.empty-state i {
  font-size: 6vh;
  margin-bottom: 2vh;
  opacity: 0.5;
  color: #a995f4;
}

.empty-state h3 {
  color: #e2e8f0;
  font-size: 2vh;
  font-weight: 500;
  margin-bottom: 1vh;
}

.empty-state p {
  font-size: 1.4vh;
  margin-bottom: 2vh;
  opacity: 0.8;
  max-width: 40vh;
}

.btn-primary {
  background: var(--primary-color);
  color: white;
  border: none;
  padding: 1vh 2vh;
  border-radius: 0.8vh;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5vh;
  font-size: 1.3vh;
  font-weight: 500;
}

.btn-primary:hover:not(:disabled) {
  background: var(--primary-dark);
  transform: translateY(-0.1vh);
}

.btn-primary:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.btn-secondary {
  background: rgba(148, 163, 184, 0.1);
  color: #94a3b8;
  border: 1px solid rgba(148, 163, 184, 0.2);
  padding: 1vh 2vh;
  border-radius: 0.8vh;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5vh;
  font-size: 1.3vh;
}

.btn-secondary:hover {
  background: rgba(148, 163, 184, 0.2);
  color: #e2e8f0;
  border-color: rgba(148, 163, 184, 0.4);
}

:root {
  --primary-color: #8774e1;
  --primary-dark: #6856b9;
}
</style>