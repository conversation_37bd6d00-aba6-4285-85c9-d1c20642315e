<template>
  <div class="container">
    <div class="header">
      <div class="title">飞行驾校报名系统</div>
      <div class="subtitle">选择您需要的飞行执照类型进行报名</div>
    </div>
    
    <div class="licence-list">
      <div v-for="licenceData in computedLicences" :key="licenceData.type" class="licence-item">
        <div class="licence-badge">{{ licenceData.badge }}</div>
        <div class="licence-header">{{ licenceData.name }}驾照</div>
        <div class="licence-price">
          <span class="price-label">考试费用</span>
          <span class="price-amount">¥{{ licenceData.price }}</span>
          <span class="price-unit">/科</span>
        </div>
        
        <div class="divider"></div>
        
        <div class="exam-steps">
          <div v-if="licenceData.completed" class="licence-completed">
            <span class="complete-icon">✅</span> 已获得飞行执照
          </div>
          <template v-else>
            <button 
              v-for="exam in licenceData.exams" 
              :key="exam.step"
              @click="signUp(licenceData.type, exam.step)"
              :disabled="exam.disabled"
              class="exam-btn"
            >
              <span class="exam-name">{{ exam.name }}</span>
              <span class="exam-price">¥{{ licenceData.price }}</span>
            </button>
          </template>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, computed } from 'vue';
import { useEvents } from '@Composables/useEvents.js';

const events = useEvents();

// 飞行执照类型
const licenceTypes = ['Plane', 'Helicopter'] as const;

// 玩家的飞行执照数据
const licences = ref<any[]>([]);

// 同步飞行执照数据
events.on('flightschoolexam:sync', (data: any[]) => {
  licences.value = data;
});

// 获取飞行执照价格
const getLicencePrice = (type: string): number => {
  switch(type) {
    case 'Plane':
      return 5000;
    case 'Helicopter':
      return 3000;
    default:
      return 0;
  }
}

// 检查是否已通过某个科目
const hasExamStep = (type: string, step: number) => {
  if (!licences.value) return false;
  
  const licence = licences.value.find(l => l.name === type);
  if (!licence) return false;
  
  switch(step) {
    case 1:
      return licence.first;
    case 2:
      return licence.second;
    case 3:
      return licence.third;
    default:
      return false;
  }
}

// 检查是否已获得飞行执照
const hasCompletedLicence = (type: string) => {
  if (!licences.value) return false;
  
  const licence = licences.value.find(l => l.name === type);
  if (!licence) return false;
  
  return licence.first && licence.second;
}

// 计算属性 - 优化性能，避免重复计算
const computedLicences = computed(() => {
  return licenceTypes.map(type => {
    const price = getLicencePrice(type);
    const completed = hasCompletedLicence(type);
    const step1 = hasExamStep(type, 1);
    const step2 = hasExamStep(type, 2);
    
    return {
      type,
      name: type === 'Plane' ? '飞机' : '直升机',
      badge: type === 'Plane' ? '✈️' : '🚁',
      price,
      completed,
      exams: [
        {
          step: 1,
          name: '科目一 理论考试',
          disabled: step1
        },
        {
          step: 2,
          name: '科目二 起降训练',
          disabled: !step1 || step2
        }
      ]
    };
  });
});

// 报名考试
const signUp = (type: string, step: number) => {
  events.emitServer('flightschoolexam:SignUp', type, step);
}
</script>

<style scoped>
.container {
  margin: auto;
  margin-top: 10vh;
  width: 80vw;
  max-height: 85vh;
  padding: 3vh 2.5vw;
  color: white;
  background: rgba(10, 20, 40, 0.92);
  display: flex;
  flex-direction: column;
  align-items: center;
  border-radius: 1.5vw;
  overflow-y: auto;
  box-shadow: 0 1vh 2vh rgba(0, 0, 0, 0.4);
  border: 1px solid rgba(100, 150, 255, 0.25);
}

.header {
  text-align: center;
  margin-bottom: 4vh;
}

.title {
  font-size: 2.8vw;
  font-weight: bold;
  color: #00a8ff;
  margin-bottom: 1vh;
}

.subtitle {
  font-size: 1.2vw;
  color: rgba(255, 255, 255, 0.7);
}

.licence-list {
  display: flex;
  gap: 3vw;
  justify-content: center;
  width: 100%;
  padding: 1vh 0;
}

.licence-item {
  background: rgba(255, 255, 255, 0.06);
  padding: 3vh 2.5vw;
  border-radius: 1.2vw;
  box-shadow: 0 0.5vh 1vh rgba(0, 0, 0, 0.25);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  flex: 1;
  min-width: 20vw;
  max-width: 35vw;
  position: relative;
  border: 1px solid rgba(100, 150, 255, 0.2);
}

.licence-item:hover {
  transform: translateY(-0.5vh);
  box-shadow: 0 0.8vh 1.5vh rgba(0, 168, 255, 0.2);
}

.licence-badge {
  position: absolute;
  top: -1.5vh;
  right: 1.5vw;
  font-size: 2vw;
}

.licence-header {
  font-size: 2vw;
  margin: 2vh 0;
  text-align: center;
  color: white;
  font-weight: bold;
}

.licence-price {
  text-align: center;
  margin-bottom: 2.5vh;
}

.price-label {
  font-size: 1vw;
  color: rgba(255, 255, 255, 0.6);
  display: block;
  margin-bottom: 0.5vh;
}

.price-amount {
  font-size: 2.5vw;
  color: #00a8ff;
  font-weight: bold;
  margin: 0.5vh 0.3vw;
}

.price-unit {
  font-size: 1vw;
  color: rgba(255, 255, 255, 0.6);
}

.divider {
  height: 1px;
  background: rgba(100, 150, 255, 0.3);
  margin: 2.5vh 0;
}

.exam-steps {
  display: flex;
  flex-direction: column;
  gap: 1.8vh;
}

.exam-btn {
  padding: 1.8vh 2vw;
  border: none;
  border-radius: 0.8vw;
  background: #0078ff;
  color: white;
  cursor: pointer;
  font-size: 1.15vw;
  transition: background-color 0.2s ease, transform 0.2s ease;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 0.3vh 0.6vh rgba(0, 120, 255, 0.25);
}

.exam-btn:hover:not(:disabled) {
  background: #00a8ff;
  transform: scale(1.02);
}

.exam-btn:disabled {
  background: #666666;
  cursor: not-allowed;
  opacity: 0.6;
}

.exam-name {
  font-weight: 600;
}

.exam-price {
  font-size: 1vw;
  color: rgba(255, 255, 255, 0.9);
  background: rgba(0, 0, 0, 0.3);
  padding: 0.4vh 1vw;
  border-radius: 0.5vw;
}

.licence-completed {
  text-align: center;
  padding: 2vh 2vw;
  background: #00c851;
  border-radius: 0.8vw;
  color: white;
  font-weight: bold;
  font-size: 1.2vw;
  box-shadow: 0 0.3vh 0.6vh rgba(0, 200, 81, 0.25);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1vw;
}

.complete-icon {
  font-size: 1.5vw;
}
</style>