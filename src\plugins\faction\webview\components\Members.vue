<template>
  <div class="members-container">
    <!-- 搜索和操作区域 -->
    <div class="action-bar">
      <div class="search-section">
        <div class="search-input">
          <svg viewBox="0 0 24 24">
            <path d="M9.5,3A6.5,6.5 0 0,1 16,9.5C16,11.11 15.41,12.59 14.44,13.73L14.71,14H15.5L20.5,19L19,20.5L14,15.5V14.71L13.73,14.44C12.59,15.41 11.11,16 9.5,16A6.5,6.5 0 0,1 3,9.5A6.5,6.5 0 0,1 9.5,3M9.5,5C7,5 5,7 5,9.5C5,12 7,14 9.5,14C12,14 14,12 14,9.5C14,7 12,5 9.5,5Z" />
          </svg>
          <input v-model="memberFilter" placeholder="搜索成员..." class="search-field">
        </div>
      </div>
      
      <div v-if="hasPermission('manage_members') || permissions.includes('admin')" class="invite-section">
        <div class="invite-input">
          <svg viewBox="0 0 24 24">
            <path d="M15,14C12.33,14 7,15.33 7,18V20H23V18C23,15.33 17.67,14 15,14M6,10V7H4V10H1V12H4V15H6V12H9V10M15,12A4,4 0 0,0 19,8A4,4 0 0,0 15,4A4,4 0 0,0 11,8A4,4 0 0,0 15,12Z" />
          </svg>
          <input v-model="newMemberId" placeholder="输入成员ID" class="invite-field" type="number">
        </div>
        <button @click="addMember" class="invite-btn">邀请成员</button>
      </div>
    </div>

    <!-- 成员列表 -->
    <div class="members-list-container">
      <div class="members-header">
        <div class="member-col name-col">成员姓名</div>
        <div class="member-col position-col">职位</div>
        <div class="member-col salary-col">薪资</div>
        <div class="member-col join-date-col">入职日期</div>
      </div>
      
      <div class="members-list">
        <div 
          v-for="member in filteredAndSortedMembers" 
          :key="member.id" 
          @click="selectMember(member)"
          :class="['member-row', {'selected': selectedMember === member}]"
        >
          <div class="member-col name-col">
            <div class="member-avatar">{{ member.name.charAt(0) }}</div>
            <span>{{ member.name }}</span>
          </div>
          <div class="member-col position-col">{{ member.job.name }}</div>
          <div class="member-col salary-col">${{ member.job.salary || 0 }}</div>
          <div class="member-col join-date-col">{{ new Date(member.job.joinDate).toLocaleString() }}</div>
        </div>
        
        <div v-if="filteredAndSortedMembers.length === 0" class="empty-list">
          <svg viewBox="0 0 24 24">
            <path d="M12,4A4,4 0 0,1 16,8A4,4 0 0,1 12,12A4,4 0 0,1 8,8A4,4 0 0,1 12,4M12,6A2,2 0 0,0 10,8A2,2 0 0,0 12,10A2,2 0 0,0 14,8A2,2 0 0,0 12,6M12,13C14.67,13 20,14.33 20,17V20H4V17C4,14.33 9.33,13 12,13M12,14.9C9.03,14.9 5.9,16.36 5.9,17V18.1H18.1V17C18.1,16.36 14.97,14.9 12,14.9Z" />
          </svg>
          <p>没有找到符合条件的成员</p>
        </div>
      </div>
    </div>

    <!-- 成员详情 -->
    <div v-if="selectedMember && (hasPermission('manage_members') || permissions.includes('admin'))" class="member-detail">
      <div class="detail-header">
        <div class="detail-avatar">{{ selectedMember.name.charAt(0) }}</div>
        <div class="detail-title">
          <h3>{{ selectedMember.name }}</h3>
          <span class="detail-position">{{ selectedMember.job.name }}</span>
        </div>
      </div>
      
      <div class="detail-info">
        <div class="info-item">
          <div class="info-label">职位</div>
          <div class="info-value">{{ selectedMember.job.name }}</div>
        </div>
        <div class="info-item">
          <div class="info-label">薪资</div>
          <div class="info-value">${{ selectedMember.job.salary }}</div>
        </div>
        <div class="info-item">
          <div class="info-label">入职日期</div>
          <div class="info-value">{{ new Date(selectedMember.job.joinDate).toLocaleString() }}</div>
        </div>
      </div>
      
      <div class="detail-actions">
        <button @click="promoteMember" class="action-btn promote-btn">
          <svg viewBox="0 0 24 24">
            <path d="M13,20H11V8L5.5,13.5L4.08,12.08L12,4.16L19.92,12.08L18.5,13.5L13,8V20Z" />
          </svg>
          <span>晋升</span>
        </button>
        <button @click="demoteMember" class="action-btn demote-btn">
          <svg viewBox="0 0 24 24">
            <path d="M11,4H13V16L18.5,10.5L19.92,11.92L12,19.84L4.08,11.92L5.5,10.5L11,16V4Z" />
          </svg>
          <span>降职</span>
        </button>
        <button @click="removeMember" class="action-btn remove-btn">
          <svg viewBox="0 0 24 24">
            <path d="M19,4H15.5L14.5,3H9.5L8.5,4H5V6H19M6,19A2,2 0 0,0 8,21H16A2,2 0 0,0 18,19V7H6V19Z" />
          </svg>
          <span>开除</span>
        </button>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, PropType } from 'vue';

const props = defineProps({
  factionData: {
    type: Object,
    required: true
  },
  permissions: {
    type: Array,
    required: true
  },
  hasPermission: {
    type: Function,
    required: true
  },
  promoteMemberAction: {
    type: Function as PropType<(memberId: number) => Promise<boolean>>,
    required: true
  },
  demoteMemberAction: {
    type: Function as PropType<(memberId: number) => Promise<boolean>>,
    required: true
  },
  removeMemberAction: {
    type: Function as PropType<(memberId: number) => Promise<boolean>>,
    required: true
  },
  addMemberAction: {
    type: Function as PropType<(memberId: number) => Promise<boolean>>,
    required: true
  }
});

const selectedMember = ref(null);
const memberFilter = ref('');
const newMemberId = ref('');

// 成员过滤和排序
const filteredAndSortedMembers = computed(() => {
  let members = props.factionData?.members || [];
  if (memberFilter.value) {
    members = members.filter(member =>
      member.name.toLowerCase().includes(memberFilter.value.toLowerCase())
    );
  }
  return members.sort((a, b) => a.job.rank - b.job.rank);
});

function selectMember(member) {
  if (selectedMember.value === member) {
    selectedMember.value = null; // 如果再次点击同一成员，取消选择
  } else {
    selectedMember.value = member;
  }
}

function promoteMember() {
  if (selectedMember.value) {
    props.promoteMemberAction(selectedMember.value.id);
  }
}

function demoteMember() {
  if (selectedMember.value) {
    props.demoteMemberAction(selectedMember.value.id);
  }
}

async function removeMember() {
  if (selectedMember.value) {
    const success = await props.removeMemberAction(selectedMember.value.id);
    if (success) {
      selectedMember.value = null; // 清除选择
    }
  }
}

function addMember() {
  if (newMemberId.value) {
    props.addMemberAction(parseInt(newMemberId.value)).then(success => {
      if (success) {
        newMemberId.value = '';
      }
    });
  }
}
</script>

<style scoped>
.members-container {
  display: grid;
  grid-template-columns: 1fr;
  grid-template-rows: auto 1fr auto;
  gap: 2vh;
  height: 100%;
}

/* 行动栏样式 */
.action-bar {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  gap: 1.5vh;
  margin-bottom: 1vh;
}

.search-section, .invite-section {
  display: flex;
  align-items: center;
  gap: 1vh;
}

.search-input, .invite-input {
  position: relative;
  display: flex;
  align-items: center;
}

.search-input svg, .invite-input svg {
  position: absolute;
  left: 1vh;
  width: 1.8vh;
  height: 1.8vh;
  fill: #a0aec0;
}

.search-field, .invite-field {
  height: 4vh;
  padding: 0 1vh 0 3.5vh;
  background: rgba(26, 32, 44, 0.6);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 6px;
  color: #e2e8f0;
  font-size: 1.4vh;
  width: 25vh;
}

.search-field::placeholder, .invite-field::placeholder {
  color: #718096;
}

.search-field:focus, .invite-field:focus {
  outline: none;
  border-color: rgba(135, 116, 225, 0.5);
  box-shadow: 0 0 0 1px rgba(135, 116, 225, 0.2);
}

.invite-btn {
  height: 4vh;
  padding: 0 1.5vh;
  background: rgba(135, 116, 225, 0.2);
  color: #a995f4;
  border: none;
  border-radius: 6px;
  font-size: 1.4vh;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.invite-btn:hover {
  background: rgba(135, 116, 225, 0.3);
}

/* 成员列表容器 */
.members-list-container {
  background: rgba(34, 42, 53, 0.7);
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  flex-direction: column;
}

.members-header {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 2fr;
  padding: 1.5vh 2vh;
  background: rgba(26, 32, 44, 0.5);
  border-bottom: 1px solid rgba(255, 255, 255, 0.08);
}

.member-col {
  font-size: 1.2vh;
  font-weight: 600;
  color: #a0aec0;
}

.members-list {
  overflow-y: auto;
  max-height: 40vh;
}

.members-list::-webkit-scrollbar {
  width: 6px;
}

.members-list::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 3px;
}

.members-list::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 3px;
}

.members-list::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.3);
}

.member-row {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 2fr;
  padding: 1.5vh 2vh;
  border-bottom: 1px solid rgba(255, 255, 255, 0.08);
  transition: background 0.2s;
  cursor: pointer;
}

.member-row:hover {
  background: rgba(26, 32, 44, 0.3);
}

.member-row.selected {
  background: rgba(135, 116, 225, 0.1);
}

.member-avatar {
  width: 3.5vh;
  height: 3.5vh;
  border-radius: 50%;
  background: linear-gradient(135deg, #4c1d95, #5b21b6);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 600;
  margin-right: 1vh;
}

.name-col {
  display: flex;
  align-items: center;
  font-weight: 500;
  color: #e2e8f0;
}

.position-col {
  color: #a995f4;
}

.salary-col {
  color: #68d391;
}

.join-date-col {
  color: #cbd5e0;
  font-size: 1.2vh;
}

.empty-list {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 5vh 0;
  color: #a0aec0;
}

.empty-list svg {
  width: 5vh;
  height: 5vh;
  fill: #a0aec0;
  margin-bottom: 1vh;
  opacity: 0.7;
}

.empty-list p {
  font-size: 1.4vh;
}

/* 成员详情 */
.member-detail {
  background: rgba(34, 42, 53, 0.7);
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.1);
  padding: 2vh;
}

.detail-header {
  display: flex;
  align-items: center;
  margin-bottom: 2vh;
}

.detail-avatar {
  width: 5vh;
  height: 5vh;
  border-radius: 50%;
  background: linear-gradient(135deg, #4c1d95, #5b21b6);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 2vh;
  font-weight: 600;
  margin-right: 1.5vh;
}

.detail-title h3 {
  font-size: 1.8vh;
  font-weight: 600;
  color: #e2e8f0;
  margin-bottom: 0.5vh;
}

.detail-position {
  font-size: 1.3vh;
  color: #a995f4;
}

.detail-info {
  background: rgba(26, 32, 44, 0.3);
  border-radius: 8px;
  padding: 1.5vh;
  margin-bottom: 2vh;
}

.info-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 1vh;
  padding-bottom: 1vh;
  border-bottom: 1px solid rgba(255, 255, 255, 0.08);
}

.info-item:last-child {
  margin-bottom: 0;
  padding-bottom: 0;
  border-bottom: none;
}

.info-label {
  font-size: 1.2vh;
  color: #a0aec0;
}

.info-value {
  font-size: 1.3vh;
  color: #e2e8f0;
  font-weight: 500;
}

.detail-actions {
  display: flex;
  gap: 1vh;
}

.action-btn {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 1.2vh 0;
  border-radius: 8px;
  font-size: 1.3vh;
  font-weight: 500;
  cursor: pointer;
  border: none;
  transition: all 0.2s;
}

.action-btn svg {
  width: 1.8vh;
  height: 1.8vh;
  margin-right: 0.6vh;
}

.promote-btn {
  background: rgba(72, 187, 120, 0.2);
  color: #68d391;
}

.promote-btn svg {
  fill: #68d391;
}

.promote-btn:hover {
  background: rgba(72, 187, 120, 0.3);
}

.demote-btn {
  background: rgba(214, 158, 46, 0.2);
  color: #f6ad55;
}

.demote-btn svg {
  fill: #f6ad55;
}

.demote-btn:hover {
  background: rgba(214, 158, 46, 0.3);
}

.remove-btn {
  background: rgba(229, 62, 62, 0.2);
  color: #fc8181;
}

.remove-btn svg {
  fill: #fc8181;
}

.remove-btn:hover {
  background: rgba(229, 62, 62, 0.3);
}

</style> 