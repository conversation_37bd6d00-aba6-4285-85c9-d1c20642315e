<template>
  <div class="poker-overlay">
    <div class="poker-container">
      <!-- 标题 -->
      <div class="page-header">
        <div class="header-content">
          <div class="title-section">
            <h1 class="page-title">🃏 德州扑克</h1>
            <div class="room-info" v-if="gameState.roomId">
              <span>房间: {{ gameState.roomId }}</span>
              <span>回合: {{ gameState.round }}/{{ gameState.maxRounds }}</span>
            </div>
          </div>
          <button @click="closeGame" class="close-btn">✕</button>
        </div>
      </div>

      <!-- 游戏大厅 -->
      <div class="lobby" v-if="!gameState.inRoom">
        <div class="lobby-content">
          <h2>选择游戏模式</h2>
          
          <div class="game-modes">
            <div class="mode-card" @click="createRoom">
              <h3>创建房间</h3>
              <p>创建新的德州扑克房间</p>
              <div class="mode-settings">
                <label>
                  小盲注: 
                  <select v-model="roomSettings.smallBlind">
                    <option value="50">50筹码</option>
                    <option value="100">100筹码</option>
                    <option value="200">200筹码</option>
                    <option value="500">500筹码</option>
                  </select>
                </label>
                <label>
                  最大玩家数: 
                  <select v-model="roomSettings.maxPlayers">
                    <option value="2">2人</option>
                    <option value="4">4人</option>
                    <option value="6">6人</option>
                    <option value="8">8人</option>
                    <option value="10">10人</option>
                  </select>
                </label>
              </div>
              <button class="mode-btn">创建房间</button>
            </div>

            <div class="mode-card">
              <h3>加入房间</h3>
              <p>加入现有的德州扑克房间</p>
              <div class="room-list">
                <div v-for="room in availableRooms" :key="room.id" class="room-item" @click="joinRoom(room.id)">
                  <div class="room-details">
                    <span class="room-id">房间 {{ room.id }}</span>
                    <span class="room-players">{{ room.players }}/{{ room.maxPlayers }}</span>
                    <span class="room-blinds">盲注: {{ room.smallBlind }}/{{ room.bigBlind }}</span>
                  </div>
                  <button class="join-btn" :disabled="room.players >= room.maxPlayers">
                    {{ room.players >= room.maxPlayers ? '房间满' : '加入' }}
                  </button>
                </div>
              </div>
              <button @click="refreshRooms" class="refresh-btn">刷新房间列表</button>
            </div>
          </div>
        </div>
      </div>

      <!-- 游戏界面 -->
      <div class="game-area" v-if="gameState.inRoom">
        <!-- 调试信息 -->
        <div class="debug-info" style="background: rgba(255,0,0,0.1); padding: 10px; margin-bottom: 10px; font-size: 12px;">
          <div>我的ID: {{ playerId }}</div>
          <div>我的手牌: {{ JSON.stringify(myCards) }}</div>
          <div>当前阶段: {{ gameState.phase }}</div>
          <div>游戏是否结束: {{ gameState.gameOver }}</div>
          <div>当前行动玩家: {{ gameState.currentPlayerId }}</div>
          <div>房间玩家数: {{ gameState.players.length }}</div>
        </div>
        
        <!-- 奖池和公共牌区域 -->
        <div class="board-area">
          <div class="pot-info">
            <h3>奖池: {{ gameState.pot }}筹码</h3>
            <div class="game-phase">{{ getPhaseText() }}</div>
          </div>
          
          <div class="community-cards">
            <h4>公共牌</h4>
            <div class="cards-display">
              <div v-for="(card, index) in gameState.communityCards" :key="index" class="card">
                <div class="card-content">
                  <span class="card-suit" :class="{ 'red': card.suit === 'H' || card.suit === 'D' }">
                    {{ getSuitSymbol(card.suit) }}
                  </span>
                  <span class="card-value">{{ card.rank }}</span>
                </div>
              </div>
              <!-- 占位符卡牌 -->
              <div v-for="n in (5 - gameState.communityCards.length)" :key="'placeholder-' + n" class="card placeholder">
                <div class="card-back">🂠</div>
              </div>
            </div>
          </div>
        </div>

        <!-- 玩家座位区域 -->
        <div class="players-area">
          <div v-for="(player, index) in gameState.players" :key="player.id" 
               class="player-seat" 
               :class="{ 
                 'current-player': player.id === gameState.currentPlayerId,
                 'dealer': player.isDealer,
                 'small-blind': player.isSmallBlind,
                 'big-blind': player.isBigBlind,
                 'folded': player.folded,
                 'all-in': player.allIn
               }">
            
            <div class="player-info">
              <div class="player-name">{{ player.name }}</div>
              <div class="player-chips">💰 {{ player.chips }}</div>
              <div class="player-bet" v-if="player.currentBet > 0">下注: {{ player.currentBet }}</div>
              <div class="player-status">{{ getPlayerStatus(player) }}</div>
            </div>

            <!-- 玩家手牌 (只显示自己的) -->
            <div class="player-cards" v-if="player.id === playerId">
              <div v-for="(card, cardIndex) in myCards" :key="cardIndex" class="card small">
                <div class="card-content">
                  <span class="card-suit" :class="{ 'red': card.suit === 'H' || card.suit === 'D' }">
                    {{ getSuitSymbol(card.suit) }}
                  </span>
                  <span class="card-value">{{ card.rank }}</span>
                </div>
              </div>
              <!-- 如果手牌不足2张，显示占位符 -->
              <div v-for="n in (2 - myCards.length)" :key="'placeholder-' + n" class="card small placeholder">
                <div class="card-back">🂠</div>
              </div>
            </div>
            <!-- 其他玩家显示牌背 -->
            <div class="player-cards" v-else-if="!player.folded">
              <div v-for="n in 2" :key="'back-' + n" class="card small">
                <div class="card-back">🂠</div>
              </div>
            </div>

            <!-- 位置标识 -->
            <div class="position-indicators">
              <span v-if="player.isDealer" class="dealer-button">D</span>
              <span v-if="player.isSmallBlind" class="blind-indicator small">SB</span>
              <span v-if="player.isBigBlind" class="blind-indicator big">BB</span>
            </div>
          </div>
        </div>

        <!-- 操作区域 -->
        <div class="action-area" v-if="gameState.currentPlayerId === playerId && !gameState.gameOver">
          <div class="hand-strength" v-if="myCards.length === 2 && gameState.communityCards.length > 0">
            <span>当前牌型: {{ currentHandRank?.name || '高牌' }}</span>
          </div>

          <div class="action-buttons">
            <button @click="fold" class="action-btn fold-btn" :disabled="!canFold">
              弃牌
            </button>
            
            <button @click="check" class="action-btn check-btn" v-if="canCheck" :disabled="!canCheck">
              过牌
            </button>
            
            <button @click="call" class="action-btn call-btn" v-if="!canCheck" :disabled="!canCall">
              跟注 ({{ gameState.currentBet - myPlayer?.currentBet || 0 }})
            </button>
            
            <button @click="showRaiseOptions" class="action-btn raise-btn" :disabled="!canRaise">
              加注
            </button>
            
            <button @click="allIn" class="action-btn allin-btn" :disabled="!canAllIn">
              全下 ({{ myPlayer?.chips || 0 }})
            </button>
          </div>

          <!-- 加注选项 -->
          <div class="raise-options" v-if="showingRaiseOptions">
            <div class="raise-controls">
              <label>加注金额:</label>
              <div class="raise-buttons">
                <button @click="setRaiseAmount(gameState.minRaise)" class="quick-raise">最小 ({{ gameState.minRaise }})</button>
                <button @click="setRaiseAmount(gameState.pot)" class="quick-raise">底池 ({{ gameState.pot }})</button>
                <button @click="setRaiseAmount(gameState.pot * 2)" class="quick-raise">两倍底池</button>
              </div>
              <div class="custom-raise">
                <input v-model.number="raiseAmount" type="number" :min="gameState.minRaise" :max="myPlayer?.chips || 0">
                <button @click="raise" class="confirm-raise" :disabled="!isValidRaise">确认加注</button>
                <button @click="hideRaiseOptions" class="cancel-raise">取消</button>
              </div>
            </div>
          </div>

          <!-- 计时器 -->
          <div class="action-timer" v-if="gameState.actionTimer > 0">
            <div class="timer-bar" :style="{ width: (gameState.actionTimer / 30) * 100 + '%' }"></div>
            <span>{{ gameState.actionTimer }}秒</span>
          </div>
        </div>

        <!-- 游戏结果 -->
        <div class="game-result" v-if="gameState.gameOver">
          <h3>🏆 游戏结果</h3>
          <div class="winners">
            <div v-for="winner in gameState.winners" :key="winner.playerId" class="winner-info">
              <div class="winner-name">{{ winner.playerName }}</div>
              <div class="winner-hand">{{ winner.handName }}</div>
              <div class="winner-amount">赢得: {{ winner.amount }}筹码</div>
            </div>
          </div>
          <div class="result-actions">
            <button @click="nextRound" class="next-round-btn" v-if="!gameState.tournamentOver">下一局</button>
            <button @click="leaveRoom" class="leave-room-btn">离开房间</button>
          </div>
        </div>

        <!-- 玩家信息面板 -->
        <div class="player-info-panel">
          <div class="my-info">
            <span>💰 我的筹码: {{ myPlayer?.chips || 0 }}</span>
            <span>🏆 胜局: {{ playerStats.wins || 0 }}</span>
            <span>🎯 胜率: {{ playerStats.winRate || 0 }}%</span>
          </div>
          <button @click="leaveRoom" class="leave-btn">离开房间</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue';
import { useEvents } from '@Composables/useEvents.js';

const events = useEvents();

// 游戏状态
const gameState = ref({
  inRoom: false,
  roomId: '',
  players: [] as any[],
  currentPlayerId: '',
  communityCards: [] as any[],
  pot: 0,
  currentBet: 0,
  minRaise: 0,
  phase: 'waiting', // waiting, preflop, flop, turn, river, showdown
  round: 1,
  maxRounds: 10,
  gameOver: false,
  tournamentOver: false,
  winners: [] as any[],
  actionTimer: 0
});

// 房间设置
const roomSettings = ref({
  smallBlind: 50,
  maxPlayers: 6
});

// 可用房间列表
const availableRooms = ref([]);

// 玩家相关
const playerId = ref('');
const myCards = ref([]);
const currentHandRank = ref(null as any);
const playerStats = ref({ wins: 0, winRate: 0 });

// UI状态
const showingRaiseOptions = ref(false);
const raiseAmount = ref(0);

// 计算属性
const myPlayer = computed(() => {
  return gameState.value.players.find(p => p.id === playerId.value);
});

const canFold = computed(() => {
  return gameState.value.currentPlayerId === playerId.value && !gameState.value.gameOver;
});

const canCheck = computed(() => {
  const myCurrentBet = myPlayer.value?.currentBet || 0;
  return myCurrentBet === gameState.value.currentBet;
});

const canCall = computed(() => {
  const myCurrentBet = myPlayer.value?.currentBet || 0;
  return myCurrentBet < gameState.value.currentBet && (myPlayer.value?.chips || 0) > 0;
});

const canRaise = computed(() => {
  return (myPlayer.value?.chips || 0) >= gameState.value.minRaise;
});

const canAllIn = computed(() => {
  return (myPlayer.value?.chips || 0) > 0;
});

const isValidRaise = computed(() => {
  return raiseAmount.value >= gameState.value.minRaise && 
         raiseAmount.value <= (myPlayer.value?.chips || 0);
});

// 方法
const getSuitSymbol = (suit: string) => {
  const symbols = { 'H': '♥', 'D': '♦', 'C': '♣', 'S': '♠' };
  return symbols[suit] || suit;
};

const getPhaseText = () => {
  const phases = {
    waiting: '等待玩家',
    preflop: '翻牌前',
    flop: '翻牌',
    turn: '转牌',
    river: '河牌',
    showdown: '摊牌'
  };
  return phases[gameState.value.phase] || '游戏中';
};

const getPlayerStatus = (player: any) => {
  if (player.folded) return '已弃牌';
  if (player.allIn) return '全下';
  if (player.id === gameState.value.currentPlayerId) return '行动中';
  return '等待';
};

const createRoom = () => {
  events.emitServer('poker:createRoom', roomSettings.value);
};

const joinRoom = (roomId: string) => {
  events.emitServer('poker:joinRoom', { roomId });
};

const leaveRoom = () => {
  events.emitServer('poker:leaveRoom');
  gameState.value.inRoom = false;
  gameState.value.roomId = '';
};

const refreshRooms = () => {
  events.emitServer('poker:getRooms');
};

const fold = () => {
  events.emitServer('poker:fold');
};

const check = () => {
  events.emitServer('poker:check');
};

const call = () => {
  events.emitServer('poker:call');
};

const showRaiseOptions = () => {
  showingRaiseOptions.value = true;
  raiseAmount.value = gameState.value.minRaise;
};

const hideRaiseOptions = () => {
  showingRaiseOptions.value = false;
};

const setRaiseAmount = (amount: number) => {
  raiseAmount.value = Math.min(amount, myPlayer.value?.chips || 0);
};

const raise = () => {
  if (isValidRaise.value) {
    events.emitServer('poker:raise', { amount: raiseAmount.value });
    hideRaiseOptions();
  }
};

const allIn = () => {
  events.emitServer('poker:allIn');
};

const nextRound = () => {
  events.emitServer('poker:nextRound');
};

const closeGame = () => {
  if (gameState.value.inRoom) {
    leaveRoom();
  }
  events.emitServer('poker:close');
};

// 事件监听
onMounted(() => {
  // 游戏初始化
  events.on('poker:gameData', (data: any) => {
    console.log('收到游戏初始化数据:', data);
    playerId.value = data.playerId;
    playerStats.value = data.stats || { wins: 0, winRate: 0 };
    console.log('playerId设置为:', playerId.value);
  });

  // 房间列表
  events.on('poker:roomsList', (rooms: any[]) => {
    console.log('收到房间列表:', rooms);
    availableRooms.value = rooms;
  });

  // 加入房间成功
  events.on('poker:joinedRoom', (data: any) => {
    console.log('加入房间成功:', data);
    gameState.value.inRoom = true;
    gameState.value.roomId = data.roomId;
    gameState.value.players = data.players;
    console.log('当前房间玩家:', gameState.value.players);
    console.log('我的playerId:', playerId.value);
  });

  // 游戏状态更新
  events.on('poker:gameUpdate', (data: any) => {
    console.log('收到游戏状态更新:', data);
    Object.assign(gameState.value, data);
  });

  // 发牌
  events.on('poker:dealCards', (data: any) => {
    console.log('收到发牌事件:', data);
    myCards.value = data.cards;
    console.log('我的手牌已更新:', myCards.value);
  });

  // 公共牌
  events.on('poker:communityCards', (data: any) => {
    console.log('收到公共牌事件:', data);
    gameState.value.communityCards = data.cards;
  });

  // 手牌强度
  events.on('poker:handStrength', (data: any) => {
    currentHandRank.value = data.handRank;
  });

  // 玩家行动
  events.on('poker:playerAction', (data: any) => {
    // 更新玩家状态
    const player = gameState.value.players.find(p => p.id === data.playerId);
    if (player) {
      Object.assign(player, data.playerState);
    }
    gameState.value.pot = data.pot;
    gameState.value.currentBet = data.currentBet;
  });

  // 游戏结果
  events.on('poker:gameResult', (data: any) => {
    gameState.value.gameOver = true;
    gameState.value.winners = data.winners;
    gameState.value.tournamentOver = data.tournamentOver;
  });

  // 下一轮
  events.on('poker:nextRound', () => {
    console.log('收到下一轮事件，重置游戏状态');
    gameState.value.gameOver = false;
    gameState.value.communityCards = [];
    myCards.value = [];
    currentHandRank.value = null;
    // 重置玩家状态
    gameState.value.players.forEach(player => {
      player.folded = false;
      player.currentBet = 0;
      player.allIn = false;
    });
  });

  // 玩家离开
  events.on('poker:playerLeft', (data: any) => {
    gameState.value.players = gameState.value.players.filter(p => p.id !== data.playerId);
  });

  // 行动计时器
  events.on('poker:actionTimer', (data: any) => {
    gameState.value.actionTimer = data.timeLeft;
  });

  // 错误处理
  events.on('poker:error', (data: any) => {
    console.error('Poker error:', data.message);
  });

  // 请求初始数据
  events.emitServer('poker:init');
});

onUnmounted(() => {
  // Vue组件卸载时自动清理
});
</script>

<style scoped>
.poker-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  color: white;
  font-family: "Microsoft YaHei", sans-serif;
}

.poker-container {
  width: 95vw;
  max-width: 1400px;
  height: 95vh;
  background: linear-gradient(135deg, #0a4d3a, #1a5f4a, #2a5f3a);
  border-radius: 15px;
  border: 2px solid #4a90e2;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.page-header {
  padding: 15px 20px;
  background: rgba(74, 144, 226, 0.1);
  border-bottom: 2px solid #4a90e2;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.title-section {
  display: flex;
  align-items: center;
  gap: 20px;
}

.page-title {
  margin: 0;
  font-size: 24px;
  color: #4a90e2;
}

.room-info {
  display: flex;
  gap: 15px;
  font-size: 14px;
  color: #bdc3c7;
}

.close-btn {
  background: #e74c3c;
  color: white;
  border: none;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  cursor: pointer;
  font-size: 20px;
}

/* 大厅样式 */
.lobby {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
}

.lobby-content {
  max-width: 800px;
  margin: 0 auto;
}

.game-modes {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 20px;
  margin-top: 20px;
}

.mode-card {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 10px;
  padding: 20px;
  border: 2px solid transparent;
  transition: border-color 0.3s;
  cursor: pointer;
}

.mode-card:hover {
  border-color: #4a90e2;
}

.mode-settings {
  display: flex;
  flex-direction: column;
  gap: 10px;
  margin: 15px 0;
}

.mode-settings label {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.mode-settings select {
  background: rgba(255, 255, 255, 0.1);
  color: white;
  border: 1px solid #4a90e2;
  border-radius: 5px;
  padding: 5px;
}

.mode-btn, .join-btn, .refresh-btn {
  background: #4a90e2;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 8px;
  cursor: pointer;
  transition: background 0.3s;
}

.mode-btn:hover, .join-btn:hover, .refresh-btn:hover {
  background: #357abd;
}

.room-list {
  max-height: 200px;
  overflow-y: auto;
  margin: 15px 0;
}

.room-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px;
  border: 1px solid #34495e;
  border-radius: 5px;
  margin-bottom: 5px;
  cursor: pointer;
}

.room-item:hover {
  background: rgba(255, 255, 255, 0.05);
}

.room-details {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

/* 游戏界面样式 */
.game-area {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 20px;
  gap: 20px;
}

.board-area {
  text-align: center;
}

.pot-info {
  margin-bottom: 20px;
}

.pot-info h3 {
  margin: 0;
  color: #f39c12;
  font-size: 24px;
}

.game-phase {
  color: #4a90e2;
  font-size: 16px;
  margin-top: 5px;
}

.community-cards h4 {
  margin: 0 0 15px 0;
  color: #4a90e2;
}

.cards-display {
  display: flex;
  justify-content: center;
  gap: 10px;
  flex-wrap: wrap;
}

.card {
  width: 80px;
  height: 120px;
  background: white;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px solid #ddd;
  position: relative;
}

.card.small {
  width: 60px;
  height: 90px;
}

.card.placeholder {
  background: rgba(255, 255, 255, 0.1);
  border-color: #555;
}

.card-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  color: black;
  font-weight: bold;
}

.card-suit {
  font-size: 20px;
  margin-bottom: 5px;
}

.card.small .card-suit {
  font-size: 16px;
}

.card-suit.red {
  color: #e74c3c;
}

.card-value {
  font-size: 16px;
}

.card.small .card-value {
  font-size: 12px;
}

.card-back {
  font-size: 30px;
  color: #4a90e2;
}

.card.small .card-back {
  font-size: 24px;
}

/* 玩家座位 */
.players-area {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
  margin: 20px 0;
}

.player-seat {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 10px;
  padding: 15px;
  border: 2px solid transparent;
  position: relative;
}

.player-seat.current-player {
  border-color: #f39c12;
  background: rgba(243, 156, 18, 0.1);
}

.player-seat.dealer {
  border-color: #e74c3c;
}

.player-seat.folded {
  opacity: 0.5;
}

.player-info {
  margin-bottom: 10px;
}

.player-name {
  font-weight: bold;
  color: #4a90e2;
  margin-bottom: 5px;
}

.player-chips, .player-bet {
  font-size: 14px;
  color: #f39c12;
  margin-bottom: 3px;
}

.player-status {
  font-size: 12px;
  color: #bdc3c7;
}

.player-cards {
  display: flex;
  gap: 5px;
  margin-bottom: 10px;
}

.position-indicators {
  position: absolute;
  top: 5px;
  right: 5px;
  display: flex;
  gap: 3px;
}

.dealer-button {
  background: #e74c3c;
  color: white;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: bold;
}

.blind-indicator {
  background: #f39c12;
  color: white;
  padding: 2px 6px;
  border-radius: 10px;
  font-size: 10px;
  font-weight: bold;
}

/* 操作区域 */
.action-area {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 10px;
  padding: 20px;
}

.hand-strength {
  text-align: center;
  margin-bottom: 15px;
  color: #f39c12;
  font-weight: bold;
}

.action-buttons {
  display: flex;
  gap: 10px;
  justify-content: center;
  flex-wrap: wrap;
  margin-bottom: 15px;
}

.action-btn {
  padding: 12px 24px;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: bold;
  cursor: pointer;
  transition: background 0.3s;
}

.fold-btn {
  background: #e74c3c;
  color: white;
}

.fold-btn:hover {
  background: #c0392b;
}

.check-btn, .call-btn {
  background: #27ae60;
  color: white;
}

.check-btn:hover, .call-btn:hover {
  background: #229954;
}

.raise-btn {
  background: #f39c12;
  color: white;
}

.raise-btn:hover {
  background: #e67e22;
}

.allin-btn {
  background: #9b59b6;
  color: white;
}

.allin-btn:hover {
  background: #8e44ad;
}

.action-btn:disabled {
  background: #7f8c8d;
  cursor: not-allowed;
}

/* 加注选项 */
.raise-options {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  padding: 15px;
  margin-top: 15px;
}

.raise-buttons {
  display: flex;
  gap: 10px;
  margin: 10px 0;
  flex-wrap: wrap;
}

.quick-raise {
  background: rgba(255, 255, 255, 0.1);
  color: white;
  border: 1px solid #4a90e2;
  padding: 8px 16px;
  border-radius: 5px;
  cursor: pointer;
}

.custom-raise {
  display: flex;
  gap: 10px;
  align-items: center;
  margin-top: 10px;
}

.custom-raise input {
  background: rgba(255, 255, 255, 0.1);
  color: white;
  border: 1px solid #4a90e2;
  border-radius: 5px;
  padding: 8px;
  width: 100px;
}

.confirm-raise, .cancel-raise {
  padding: 8px 16px;
  border: none;
  border-radius: 5px;
  cursor: pointer;
}

.confirm-raise {
  background: #27ae60;
  color: white;
}

.cancel-raise {
  background: #e74c3c;
  color: white;
}

/* 计时器 */
.action-timer {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 10px;
  padding: 10px;
  text-align: center;
  position: relative;
  overflow: hidden;
}

.timer-bar {
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  background: linear-gradient(90deg, #e74c3c, #f39c12, #27ae60);
  transition: width 1s linear;
}

.action-timer span {
  position: relative;
  z-index: 1;
  font-weight: bold;
}

/* 游戏结果 */
.game-result {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 10px;
  padding: 20px;
  text-align: center;
}

.winners {
  margin: 20px 0;
}

.winner-info {
  background: rgba(39, 174, 96, 0.2);
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 10px;
}

.winner-name {
  font-weight: bold;
  font-size: 18px;
  color: #27ae60;
}

.winner-hand {
  color: #f39c12;
  margin: 5px 0;
}

.winner-amount {
  color: #4a90e2;
  font-weight: bold;
}

.result-actions {
  display: flex;
  gap: 15px;
  justify-content: center;
}

.next-round-btn, .leave-room-btn {
  padding: 12px 24px;
  border: none;
  border-radius: 8px;
  font-weight: bold;
  cursor: pointer;
}

.next-round-btn {
  background: #27ae60;
  color: white;
}

.leave-room-btn {
  background: #e74c3c;
  color: white;
}

/* 玩家信息面板 */
.player-info-panel {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 10px;
  padding: 15px;
}

.my-info {
  display: flex;
  gap: 20px;
  font-size: 14px;
  color: #f39c12;
}

.leave-btn {
  background: #e74c3c;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 5px;
  cursor: pointer;
}

</style>