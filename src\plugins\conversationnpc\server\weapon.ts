import * as alt from 'alt-server';
import { useRebar } from '@Server/index.js';
import { registerItemEffect } from '@Plugins/inventory/server/itemEffects.js';
import { BagItem } from '@Plugins/inventory/server/index.js';
import { sendCustomMessage } from '@Plugins/phonemessagenotify/server/index.js';
import { getForwardVector } from '@Shared/utility/vector.js';


const Rebar = useRebar();
const notify = await Rebar.useApi().getAsync('notify-api');
const progressbarapi = await Rebar.useApi().getAsync('progressbar-api');
const inventoryapi = await Rebar.useApi().getAsync('inventory-api');
const notifyapi = await Rebar.useApi().getAsync('notify-api');

// 获取物品API
const dbItemApi = await Rebar.useApi().getAsync('dbitem-api');

// 注册电话炸弹物品
dbItemApi.createitem({
    name: '电话炸弹',
    desc: '一种可以远程引爆的爆炸装置',
    type: '消耗品',
    icon: 'phonebomb.webp',
    weight: 2.0,
    maxStack: 5,
    effect: 'phonebomb',
});

// 注册拆弹器物品
dbItemApi.createitem({
    name: '拆弹器',
    desc: '专业的拆弹工具，可以安全拆除炸弹',
    type: '工具',
    icon: 'defuser.webp',
    weight: 1.5,
    maxStack: 3,
});


// 维护电话炸弹号码和车辆ID的映射
export const phoneBombMap = new Map<string, number>();

// 维护车辆ID和炸弹号码数组的映射，用于支持多人对同一目标放置炸弹
export const vehicleBombMap = new Map<number, string[]>();

// 维护空地炸弹的映射：炸弹号码 -> 炸弹对象
export const groundBombMap = new Map<string, alt.Object>();

// 存储已放置的炸弹对象数组
const placedBombs: alt.Object[] = [];

alt.onClient('use:phonebomb', async (player: alt.Player, vehicle: alt.Vehicle) => {

    const success = await inventoryapi.subItem( '电话炸弹', 1,{player:player})
    if(!success){
        notify.shownotify(player, '没有电话炸弹', 'error')
        return
    }

    // 计算从玩家到物体的方向向量
    const directionVector = {
        x: vehicle.pos.x - player.pos.x,
        y: vehicle.pos.y - player.pos.y,
        z: vehicle.pos.z - player.pos.z
    };

    // 计算角度（这里我们颠倒了 x 和 y 的顺序来匹配 GTA V 的坐标系）
    let angle = Math.atan2(directionVector.x, directionVector.y);

    // 调整角度以匹配 GTA V 的坐标系
    angle = -angle;

    // 确保角度在 -π 到 π 之间
    while (angle > Math.PI) angle -= 2 * Math.PI;
    while (angle < -Math.PI) angle += 2 * Math.PI;

    // 设置玩家的旋转
    player.rot = new alt.Vector3(0, 0, angle);

    player.playAnimation('mini@repair', 'fixing_a_ped', 8.0, 8.0, 10000, 9)

    progressbarapi.showProgressBar(player, '放置电话炸弹...', 10);

    player.emit('icantmove')

    player.setStreamSyncedMeta('do', '正在放置电话炸弹')

    await alt.Utils.wait(10000)

    player.deleteStreamSyncedMeta('do')

    player.emit('icanmove')

    player.clearTasks()

    notify.shownotify(player, '电话炸弹放置成功', 'success')

    const phone = Rebar.document.character.useCharacter(player).getField('phone');

    // 生成不重复的随机四位号码
    let bombNumber: string;
    do {
        bombNumber = Math.floor(1000 + Math.random() * 9000).toString();
    } while (phoneBombMap.has(bombNumber));
    
    // 将号码和车辆ID存储到map中
    phoneBombMap.set(bombNumber, vehicle.id);
    
    // 将炸弹号码添加到车辆的炸弹列表中
    if (!vehicleBombMap.has(vehicle.id)) {
        vehicleBombMap.set(vehicle.id, []);
    }
    vehicleBombMap.get(vehicle.id)!.push(bombNumber);

    vehicle.setStreamSyncedMeta('isPhonebomb', true)

    await sendCustomMessage('？', `引爆号码：${bombNumber}`, phone);

});



alt.onClient('remove:phonebomb', async (player: alt.Player, vehicle: alt.Vehicle) => {
    // 检查玩家是否有拆弹器
    const hasDefuser = await inventoryapi.hasitem('拆弹器', 1, { player: player });
    
    // 计算从玩家到物体的方向向量
    const directionVector = {
        x: vehicle.pos.x - player.pos.x,
        y: vehicle.pos.y - player.pos.y,
        z: vehicle.pos.z - player.pos.z
    };

    // 计算角度（这里我们颠倒了 x 和 y 的顺序来匹配 GTA V 的坐标系）
    let angle = Math.atan2(directionVector.x, directionVector.y);

    // 调整角度以匹配 GTA V 的坐标系
    angle = -angle;

    // 确保角度在 -π 到 π 之间
    while (angle > Math.PI) angle -= 2 * Math.PI;
    while (angle < -Math.PI) angle += 2 * Math.PI;

    // 设置玩家的旋转
    player.rot = new alt.Vector3(0, 0, angle);

    player.playAnimation('mini@repair', 'fixing_a_ped', 8.0, 8.0, 15000, 9)

    progressbarapi.showProgressBar(player, '拆除电话炸弹...', 15);

    player.emit('icantmove')

    player.setStreamSyncedMeta('do', '正在拆除电话炸弹')

    await alt.Utils.wait(15000)

    player.deleteStreamSyncedMeta('do')

    player.emit('icanmove')

    player.clearTasks()

    // 获取该车辆上的所有炸弹数量
    const bombNumbers = vehicleBombMap.get(vehicle.id) || [];
    const bombCount = bombNumbers.length;

    let success = false;

    if (hasDefuser) {
        // 有拆弹器，消耗一个并保证成功
        const consumed = await inventoryapi.subItem('拆弹器', 1, { player: player });
        if (consumed) {
            success = true;
            if (bombCount > 1) {
                notify.shownotify(player, `使用拆弹器，成功拆除了 ${bombCount} 个炸弹`, 'success');
            } else {
                notify.shownotify(player, '使用拆弹器，拆除成功', 'success');
            }
        } else {
            notify.shownotify(player, '拆弹器使用失败', 'error');
            return;
        }
    } else {
        // 没有拆弹器，五成概率成功
        const random = Math.random();
        if (random < 0.5) {
            success = true;
            if (bombCount > 1) {
                notify.shownotify(player, `徒手拆除成功，拆除了 ${bombCount} 个炸弹`, 'success');
            } else {
                notify.shownotify(player, '徒手拆除成功', 'success');
            }
        } else {
            if (bombCount > 1) {
                notify.shownotify(player, `徒手拆除失败，${bombCount} 个炸弹全部引爆！`, 'error');
            } else {
                notify.shownotify(player, '徒手拆除失败，炸弹引爆！', 'error');
            }
            
            // 拆除失败，引爆炸弹
            const explosionPos = vehicle.pos;
            
            // 发送载具炸弹爆炸事件给所有玩家，确保载具被摧毁
            alt.Player.all.forEach(p => {
                alt.emitClientRaw(p, 'vehicle:bomb:explode', vehicle.id, explosionPos);
            });
        }
    }

    if (success) {
        // 拆除成功，移除炸弹标记和映射
        vehicle.deleteStreamSyncedMeta('isPhonebomb');
        
        // 从映射中移除对应的所有炸弹号码
        bombNumbers.forEach(bombNumber => {
            phoneBombMap.delete(bombNumber);
        });
        vehicleBombMap.delete(vehicle.id);
        
        // 清理所有可能指向该车辆的foundphonebomb meta
        alt.Player.all.forEach(p => {
            if (p.hasStreamSyncedMeta('foundphonebomb') && 
                p.getStreamSyncedMeta('foundphonebomb') === vehicle.id) {
                p.deleteStreamSyncedMeta('foundphonebomb');
            }
        });
    } else {
        // 拆除失败，同样移除炸弹标记和映射（因为已经爆炸了）
        vehicle.deleteStreamSyncedMeta('isPhonebomb');
        
        // 从映射中移除对应的所有炸弹号码
        bombNumbers.forEach(bombNumber => {
            phoneBombMap.delete(bombNumber);
        });
        vehicleBombMap.delete(vehicle.id);
        
        // 清理所有可能指向该车辆的foundphonebomb meta
        alt.Player.all.forEach(p => {
            if (p.hasStreamSyncedMeta('foundphonebomb') && 
                p.getStreamSyncedMeta('foundphonebomb') === vehicle.id) {
                p.deleteStreamSyncedMeta('foundphonebomb');
            }
        });
    }
})

// 拆除空地炸弹
alt.onClient('remove:groundbomb', async (player: alt.Player, bombObject: alt.Object) => {
    // 检查玩家是否有拆弹器
    const hasDefuser = await inventoryapi.hasitem('拆弹器', 1, { player: player });
    
    // 计算从玩家到炸弹的方向向量
    const directionVector = {
        x: bombObject.pos.x - player.pos.x,
        y: bombObject.pos.y - player.pos.y,
        z: bombObject.pos.z - player.pos.z
    };

    // 计算角度
    let angle = Math.atan2(directionVector.x, directionVector.y);
    angle = -angle;

    // 确保角度在 -π 到 π 之间
    while (angle > Math.PI) angle -= 2 * Math.PI;
    while (angle < -Math.PI) angle += 2 * Math.PI;

    // 设置玩家的旋转
    player.rot = new alt.Vector3(0, 0, angle);

    player.playAnimation('mini@repair', 'fixing_a_ped', 8.0, 8.0, 15000, 9)

    progressbarapi.showProgressBar(player, '拆除空地炸弹...', 15);

    player.emit('icantmove')

    player.setStreamSyncedMeta('do', '正在拆除空地炸弹')

    await alt.Utils.wait(15000)

    player.deleteStreamSyncedMeta('do')

    player.emit('icanmove')

    player.clearTasks()

    let success = false;

    if (hasDefuser) {
        // 有拆弹器，消耗一个并保证成功
        const consumed = await inventoryapi.subItem('拆弹器', 1, { player: player });
        if (consumed) {
            success = true;
            notify.shownotify(player, '使用拆弹器，空地炸弹拆除成功', 'success');
        } else {
            notify.shownotify(player, '拆弹器使用失败', 'error');
            return;
        }
    } else {
        // 没有拆弹器，五成概率成功
        const random = Math.random();
        if (random < 0.5) {
            success = true;
            notify.shownotify(player, '徒手拆除空地炸弹成功', 'success');
        } else {
            notify.shownotify(player, '徒手拆除失败，空地炸弹引爆！', 'error');
            
            // 拆除失败，引爆炸弹
            const explosionPos = bombObject.pos;
            
            // 发送爆炸事件给所有玩家
            alt.Player.all.forEach(p => {
                alt.emitClientRaw(p, 'explosion:create', explosionPos);
            });
        }
    }

    if (success) {
        // 拆除成功，移除炸弹对象和映射
        
        // 从映射中找到并移除对应的炸弹号码
        for (const [bombNumber, bomb] of groundBombMap.entries()) {
            if (bomb === bombObject) {
                groundBombMap.delete(bombNumber);
                break;
            }
        }
        
        // 从已放置炸弹数组中移除
        const index = placedBombs.indexOf(bombObject);
        if (index > -1) {
            placedBombs.splice(index, 1);
        }
        
        // 销毁炸弹对象
        bombObject.destroy();
        
        player.deleteStreamSyncedMeta('foundgroundbomb');
    } else {
        // 拆除失败，同样移除炸弹对象和映射（因为已经爆炸了）
        
        // 从映射中找到并移除对应的炸弹号码
        for (const [bombNumber, bomb] of groundBombMap.entries()) {
            if (bomb === bombObject) {
                groundBombMap.delete(bombNumber);
                break;
            }
        }
        
        // 从已放置炸弹数组中移除
        const index = placedBombs.indexOf(bombObject);
        if (index > -1) {
            placedBombs.splice(index, 1);
        }
        
        // 销毁炸弹对象
        bombObject.destroy();
        
        player.deleteStreamSyncedMeta('foundgroundbomb');
    }
})

// 处理玩家断线时清理空地炸弹
alt.on('real:playerDisconnect', (player: alt.Player) => {
    const playerId = player.getStreamSyncedMeta('id');
    // 清理placedBombs数组中该玩家的炸弹
    for (let i = placedBombs.length - 1; i >= 0; i--) {
        const bomb = placedBombs[i];
        // 检查炸弹对象是否仍然有效
        if (bomb && bomb.valid && bomb.getStreamSyncedMeta('bombowner') === playerId) {
            // 从映射中找到并移除对应的炸弹号码
            for (const [bombNumber, mappedBomb] of groundBombMap.entries()) {
                if (mappedBomb === bomb) {
                    groundBombMap.delete(bombNumber);
                    break;
                }
            }
            // 销毁炸弹对象
            bomb.destroy();
            placedBombs.splice(i, 1);
        } else if (!bomb || !bomb.valid) {
            // 如果炸弹对象无效，直接从数组中移除
            placedBombs.splice(i, 1);
        }
    }
});







// 注册空地炸弹物品效果
registerItemEffect('phonebomb', async (player: alt.Player, item: BagItem) => {
    // 检查玩家是否已经放置了炸弹
    const existingBomb = placedBombs.find(bomb => 
        bomb.getStreamSyncedMeta('bombowner') === Rebar.document.character.useCharacter(player).getField('id')
    );
    
    if (existingBomb) {
        notifyapi.shownotify(player, '你已经放置了一个炸弹，无法再放置。', 'error');
        return;
    }

    // 消耗物品
    const success = await inventoryapi.subItem(item.name, 1, { player: player }, item.slot);
    if (!success) {
        notifyapi.shownotify(player, '没有电话炸弹', 'error');
        return;
    }

    // 播放放置动画
    player.playAnimation('anim@mp_fireworks', 'place_firework_2_cylinder', 8.0, 8.0, 4000, 49);
    
    // 计算放置位置
    const fwd = getForwardVector(player.rot);
    let fwdPos = player.pos.add(fwd.x * 0.8, fwd.y * 0.8, 0);
    
    // 延迟放置炸弹
    setTimeout(async () => {
        // 通过RPC获取正确的地面位置
        const correctPos = await player.emitRpc('getObjectGroundPosition', 'prop_ld_bomb', fwdPos.x, fwdPos.y, player.pos.z);
        
        // 创建炸弹对象
        const bombObject = new alt.Object(
            alt.hash('prop_ld_bomb'),
            { x: correctPos.x, y: correctPos.y, z: correctPos.z },
            { x: 0, y: 0, z: player.rot.z }
        );
        
        bombObject.frozen = true;
        
        // 设置炸弹属性
        bombObject.setStreamSyncedMeta('bomb', true);
        bombObject.setStreamSyncedMeta('bombowner', Rebar.document.character.useCharacter(player).getField('id'));
        
        // 生成炸弹号码
        const phone = Rebar.document.character.useCharacter(player).getField('phone');
        let bombNumber: string;
        do {
            bombNumber = Math.floor(1000 + Math.random() * 9000).toString();
        } while (phoneBombMap.has(bombNumber) || groundBombMap.has(bombNumber));
        
        // 存储炸弹映射
        groundBombMap.set(bombNumber, bombObject);
        placedBombs.push(bombObject);
        
        // 发送炸弹号码给玩家
        await sendCustomMessage('？', `空地炸弹引爆号码：${bombNumber}`, phone);
        
        notifyapi.shownotify(player, '炸弹已成功放置。', 'success');
    }, 1500);
});



