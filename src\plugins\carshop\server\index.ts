import * as alt from 'alt-server';
import { useRebar } from '@Server/index.js';
import { special_CAR_MODEL } from '../shared/config.js';
import { PageNames } from '@Shared/webview/index.js';
import { GlobalTimerManager } from '@Plugins/animmenu/server/interval.js';

const Rebar = useRebar();
const promptbarapi = await Rebar.useApi().getAsync('promptbar-api');
const notifyapi = await Rebar.useApi().getAsync('notify-api');
const currencyApi = await Rebar.useApi().getAsync('currency-api');
const vehApi = await Rebar.useApi().getAsync('veh-api');
const inventoryApi = await Rebar.useApi().getAsync('inventory-api');
const db = Rebar.database.useDatabase();

// 数据库集合
await db.createCollection('carshop_showroom');
await db.createCollection('truck_orders');
await db.createCollection('truck_weekly_data');
await db.createCollection('vehicle_vote_sessions');
await db.createCollection('vehicle_votes');
await db.createCollection('vehicle_import_orders');



// 展台车辆数据接口
interface ShowroomVehicle {
    _id?: string;
    showroomIndex: number;
    vehicleModel: string;
    position: { x: number; y: number; z: number };
    createdAt: number;
}

// 货车订单接口
interface TruckOrder {
    _id?: string;
    characterId: number;
    truckModel: string;
    orderCost: number;
    materials: { [key: string]: number };
    materialsSubmitted: { [key: string]: number }; // 已提交的材料数量
    submitted: boolean;
    createdAt: number;
    expiresAt: number;
}

// 周期货车数据接口
interface WeeklyTruckData {
    _id?: string;
    weekNumber: number;
    currentTruckIndex: number;
    currentTruckModel: string;
    lastUpdateTime: number;
}

// 进口车辆投票会话接口
interface VehicleVoteSession {
    _id?: string;
    sessionId: string;
    vehicles: string[]; // 10个车辆模型名称
    vehiclePrices: { [model: string]: number }; // 订单价格（玩家直接支付）
    materialPrices: { [model: string]: number }; // 材料价格（用于生成材料需求）
    startTime: number;
    endTime: number; // 投票结束时间（2天后）
    resultsCalculated: boolean;
    topVehicles: string[]; // 前5名车辆
    createdBy: number; // 管理员ID
    status: 'active' | 'ended' | 'orders_active'; // 投票状态
}

// 投票记录接口
interface VehicleVote {
    _id?: string;
    sessionId: string;
    characterId: number;
    vehicleModel: string;
    voteTime: number;
}

// 进口车辆订单接口
interface VehicleImportOrder {
    _id?: string;
    sessionId: string;
    characterId: number;
    vehicleModel: string;
    orderCost: number;
    materials: { [key: string]: number };
    materialsSubmitted: { [key: string]: number };
    submitted: boolean;
    createdAt: number;
    expiresAt: number; // 订单过期时间（1个月）
}

// 材料价格表
const MATERIAL_PRICES = {
    '钢锭': 106,
    '铜锭': 82,
    '铁锭': 80,
    '铝锭': 106,
    '润滑油': 30,
    '塑料': 34,
    '玻璃': 48,
    '催化剂': 22
};

// 进口车店展台位置
const importCarShowroom = [
    new alt.Vector3(143.3934, -165.1516, 54.8579), // 展台1
    new alt.Vector3(137.5648, -162.5670, 54.8579), // 展台2
    new alt.Vector3(132.1978, -160.9582, 54.8579), // 展台3
    new alt.Vector3(126.6330, -159.0725, 54.8579), // 展台4
    new alt.Vector3(120.8571, -156.7253, 54.8579), // 展台5
    new alt.Vector3(138.4483, -149.6044, 55.0432), // 展台6
    new alt.Vector3(114.6593, -140.0967, 54.7905), // 展台7
];

const controlPanel = new alt.Vector3(125.9077, -144.0000, 53.7905);

// 存储当前展台上的车辆
const showroomVehicles: Map<number, alt.Vehicle> = new Map();

// 当前周期货车数据
let currentWeeklyTruck: WeeklyTruckData | null = null;

// 当前活跃的投票会话
let currentVoteSession: VehicleVoteSession | null = null;

// 获取当前周数（基于Unix时间戳）
function getCurrentWeekNumber(): number {
    const now = Date.now();
    const oneWeek = 7 * 24 * 60 * 60 * 1000; // 一周的毫秒数
    return Math.floor(now / oneWeek);
}

// 初始化或更新周期货车
async function initializeWeeklyTruck() {
    const currentWeek = getCurrentWeekNumber();
    const weeklyData = await db.getAll('truck_weekly_data') as WeeklyTruckData[] | undefined;

    if (!weeklyData || weeklyData.length === 0) {
        // 首次初始化
        const initialData: Omit<WeeklyTruckData, '_id'> = {
            weekNumber: currentWeek,
            currentTruckIndex: 0,
            currentTruckModel: special_CAR_MODEL[0],
            lastUpdateTime: Date.now()
        };
        const createdId = await db.create(initialData, 'truck_weekly_data');
        if (createdId) {
            currentWeeklyTruck = { ...initialData, _id: createdId };
        }
    } else {
        const latestData = weeklyData[0];

        if (latestData.weekNumber < currentWeek) {
            // 需要更新到新周
            const nextIndex = (latestData.currentTruckIndex + 1) % special_CAR_MODEL.length;
            const updateData = {
                _id: latestData._id,
                weekNumber: currentWeek,
                currentTruckIndex: nextIndex,
                currentTruckModel: special_CAR_MODEL[nextIndex],
                lastUpdateTime: Date.now()
            };
            await db.update(updateData, 'truck_weekly_data');
            currentWeeklyTruck = updateData as WeeklyTruckData;
        } else {
            currentWeeklyTruck = latestData;
        }
    }
}

// 生成随机材料需求（总价值约50w，用于货车订单）
function generateMaterialRequirements(): { [key: string]: number } {
    return generateMaterialRequirementsForPrice(500000); // 50w
}

// 根据指定价格生成材料需求
function generateMaterialRequirementsForPrice(targetValue: number): { [key: string]: number } {
    const materials: { [key: string]: number } = {};
    let currentValue = 0;

    // 主要金属矿物（占70-80%的价值）
    const mainMetals = ['钢锭', '铜锭', '铁锭', '铝锭'];

    for (const metal of mainMetals) {
        const baseAmount = Math.floor(targetValue * 0.2 / MATERIAL_PRICES[metal]); // 每种金属约占20%
        const randomVariation = Math.floor(baseAmount * 0.5 * Math.random()); // ±25%变化
        const amount = baseAmount + randomVariation - Math.floor(baseAmount * 0.25);
        materials[metal] = Math.max(50, amount); // 最少50个
        currentValue += materials[metal] * MATERIAL_PRICES[metal];
    }

    // 化工产物（占剩余价值）
    const chemicals = ['润滑油', '塑料', '玻璃', '催化剂'];
    const remainingValue = targetValue - currentValue;

    for (let i = 0; i < chemicals.length; i++) {
        const chemical = chemicals[i];
        const shareValue = remainingValue / chemicals.length;
        const baseAmount = Math.floor(shareValue / MATERIAL_PRICES[chemical]);
        const randomVariation = Math.floor(baseAmount * 0.3 * Math.random());
        const amount = Math.max(10, baseAmount + randomVariation - Math.floor(baseAmount * 0.15));
        materials[chemical] = amount;
    }

    return materials;
}

// 检查玩家背包中的材料
function getPlayerMaterials(player: alt.Player): { [key: string]: number } {
    const character = Rebar.document.character.useCharacter(player);
    const inventory = character.get().inventory || [];
    const playerMaterials: { [key: string]: number } = {};

    for (const item of inventory) {
        if (Object.keys(MATERIAL_PRICES).includes(item.name)) {
            playerMaterials[item.name] = (playerMaterials[item.name] || 0) + (item.quantity || 1);
        }
    }

    return playerMaterials;
}

// 发送货车数据到客户端
async function sendTruckDataToClient(player: alt.Player) {
    const character = Rebar.document.character.useCharacter(player);
    const characterData = character.get();

    if (!characterData) return;

    // 获取当前订单（已完成的订单会被直接删除，所以只查询存在的订单）
    const orders = await db.getMany({
        characterId: characterData.id
    }, 'truck_orders') as TruckOrder[];

    const currentOrder = orders.length > 0 ? orders[0] : null;

    // 获取玩家背包中的材料数量
    const playerMaterials = getPlayerMaterials(player);

    // 发送数据到前端
    Rebar.player.useWebview(player).emit('truck:updateData', {
        currentTruck: currentWeeklyTruck,
        currentOrder: currentOrder,
        playerMaterials: playerMaterials
    });
}

// 加载展台车辆（持久化）
async function loadShowroomVehicles() {
    const showroomData = await db.getAll('carshop_showroom');

    if (showroomData) {
        for (const data of showroomData as ShowroomVehicle[]) {
            if (data.showroomIndex >= 0 && data.showroomIndex < importCarShowroom.length) {
                const position = importCarShowroom[data.showroomIndex];
                const vehicle = new alt.Vehicle(data.vehicleModel, position.x, position.y, position.z, 0, 0, 0);

                // 延迟1秒后冻结车辆和设置元数据
                setTimeout(() => {
                    if (vehicle && vehicle.valid) {
                        vehicle.frozen = true;
                        vehicle.setStreamSyncedMeta('carshop:showroom', true);
                        vehicle.setStreamSyncedMeta('carshop:showroomIndex', data.showroomIndex);
                    }
                }, 5000);

                showroomVehicles.set(data.showroomIndex, vehicle);
            }
        }
    }
}

// 启动时加载展台车辆和初始化周期货车
loadShowroomVehicles();
initializeWeeklyTruck();

// 创建交互点
const interaction = Rebar.controllers.useInteraction(new alt.ColshapeCylinder(controlPanel.x, controlPanel.y, controlPanel.z, 0.8, 2), 'player');

interaction.onEnter((player) => {
    if (Rebar.document.character.useCharacter(player).get().permissions.includes('carshop') || Rebar.document.character.useCharacter(player).get().permissions.includes('admin')) {
        promptbarapi.showPromptBar(player, '调整展台');
    }
});

interaction.onLeave((player) => {
    if (Rebar.document.character.useCharacter(player).get().permissions.includes('carshop') || Rebar.document.character.useCharacter(player).get().permissions.includes('admin')) {
        promptbarapi.hidePromptBar(player);
    }
});

interaction.on(async (player) => {
    if (Rebar.document.character.useCharacter(player).get().permissions.includes('carshop') || Rebar.document.character.useCharacter(player).get().permissions.includes('admin')) {
        // 检查权限
        const character = Rebar.document.character.useCharacter(player);
        if (!character.get()) {
            notifyapi.shownotify(player, '无法获取角色数据', 'error');
            return;
        }

        // 显示webview
        (Rebar.player.useWebview(player) as any).show('showroompanel', 'page', true);
        promptbarapi.hidePromptBar(player);

        // 发送数据到前端
        await sendDataToClient(player);
    }
});

interaction.addMarker({
    pos: controlPanel,
    color: new alt.RGBA(255, 255, 0, 75),
    type: 1,
    scale: new alt.Vector3(1, 1, 0.2),
});

// 发送数据到客户端
async function sendDataToClient(player: alt.Player) {
    const showroomData = await db.getAll('carshop_showroom') as ShowroomVehicle[] || [];

    // 构建展台状态数据
    const showroomStatus: { [key: number]: string } = {};
    for (const data of showroomData) {
        showroomStatus[data.showroomIndex] = data.vehicleModel;
    }

    Rebar.player.useWebview(player).emit('showroompanel:updateShowroom', showroomStatus);
}

// 处理展台调整
alt.onClient('carshop:adjustShowroom', async (player: alt.Player, showroomIndex: number, vehicleName: string) => {
    try {
        if (showroomIndex < 0 || showroomIndex >= importCarShowroom.length) {
            notifyapi.shownotify(player, '无效的展台索引', 'error');
            return;
        }

        const position = importCarShowroom[showroomIndex];

        // 移除现有车辆
        if (showroomVehicles.has(showroomIndex)) {
            const oldVehicle = showroomVehicles.get(showroomIndex);
            if (oldVehicle && oldVehicle.valid) {
                oldVehicle.destroy();
            }
            showroomVehicles.delete(showroomIndex);
        }

        // 创建新车辆
        const vehicle = new alt.Vehicle(vehicleName, position.x, position.y, position.z, 0, 0, 0);
        showroomVehicles.set(showroomIndex, vehicle);

        // 延迟1秒后冻结车辆和保存到数据库
        setTimeout(async () => {
            if (vehicle && vehicle.valid) {
                vehicle.frozen = true;
                vehicle.setStreamSyncedMeta('carshop:showroom', true);
                vehicle.setStreamSyncedMeta('carshop:showroomIndex', showroomIndex);

                // 保存到数据库
                const existingShowroom = await db.getMany({ showroomIndex }, 'carshop_showroom');

                if (existingShowroom.length > 0) {
                    // 更新现有记录
                    const showroomRecord = existingShowroom[0] as ShowroomVehicle;
                    const updateData = {
                        _id: showroomRecord._id,
                        vehicleModel: vehicleName,
                        position: { x: position.x, y: position.y, z: position.z },
                    };
                    await db.update(updateData, 'carshop_showroom');
                } else {
                    // 创建新记录
                    const newData: Omit<ShowroomVehicle, '_id'> = {
                        showroomIndex,
                        vehicleModel: vehicleName,
                        position: { x: position.x, y: position.y, z: position.z },
                        createdAt: Date.now()
                    };
                    await db.create(newData, 'carshop_showroom');
                }

                // 重新发送数据到客户端
                await sendDataToClient(player);
            }
        }, 5000);

        notifyapi.shownotify(player, `展台 ${showroomIndex + 1} 已更新`, 'success');

    } catch (error) {
        console.error('调整展台失败:', error);
        notifyapi.shownotify(player, '调整展台失败', 'error');
    }
});

// 处理移除展台车辆
alt.onClient('carshop:removeShowroomVehicle', async (player: alt.Player, showroomIndex: number) => {
    try {
        if (showroomVehicles.has(showroomIndex)) {
            const vehicle = showroomVehicles.get(showroomIndex);
            if (vehicle && vehicle.valid) {
                vehicle.destroy();
            }
            showroomVehicles.delete(showroomIndex);
        }

        // 从数据库删除
        const existingShowroom = await db.getMany({ showroomIndex }, 'carshop_showroom');
        if (existingShowroom.length > 0) {
            const showroomRecord = existingShowroom[0] as ShowroomVehicle;
            await db.deleteDocument(showroomRecord._id!, 'carshop_showroom');
        }

        notifyapi.shownotify(player, `展台 ${showroomIndex + 1} 车辆已移除`, 'success');

        // 重新发送数据到客户端
        await sendDataToClient(player);

    } catch (error) {
        console.error('移除展台车辆失败:', error);
        notifyapi.shownotify(player, '移除车辆失败', 'error');
    }
});

// 处理请求数据
alt.onClient('carshop:requestData', async (player: alt.Player) => {
    await sendDataToClient(player);
});

// 处理关闭面板
alt.onClient('carshop:closePanel', (player: alt.Player) => {
    (Rebar.player.useWebview(player) as any).hide('showroompanel');
});

// 处理webview关闭事件
alt.onClient('webview:close', (player: alt.Player) => {
    // 关闭所有可能打开的webview页面
    const webview = Rebar.player.useWebview(player) as any;
    webview.hide('vehiclevote');
});

// 页面打开/关闭事件
alt.on('rebar:playerPageOpened', (player: alt.Player, page: PageNames) => {
    if (page === 'showroompanel' || page === 'truckorder' || page === 'vehiclevote') {
        Rebar.player.useWorld(player).disableControls();
    }
});

alt.on('rebar:playerPageClosed', (player: alt.Player, page: PageNames) => {
    if (page === 'showroompanel' || page === 'truckorder' || page === 'vehiclevote') {
        Rebar.player.useWorld(player).enableControls();
    }
});



const blip = Rebar.controllers.useBlipGlobal({
    pos: new alt.Vector3(111.8110, -151.4242, 53.8074),
    color: 43,
    sprite: 663,
    shortRange: true,
    text: '进口车店',
});







const ticketPos = new alt.Vector3(185.2088, -175.8593, 53.1333);

const ticketblip = Rebar.controllers.useBlipGlobal({
    pos: ticketPos,
    color: 0,
    sprite: 663,
    shortRange: true,
    text: '进口车辆投票站',
});

// 创建交互点
const ticketinteraction = Rebar.controllers.useInteraction(new alt.ColshapeCylinder(ticketPos.x, ticketPos.y, ticketPos.z, 0.8, 2), 'player');

ticketinteraction.onEnter((player) => {
    promptbarapi.showPromptBar(player, '投票进口车辆');
});

ticketinteraction.onLeave((player) => {
    promptbarapi.hidePromptBar(player);
});

ticketinteraction.on(async (player) => {
    const character = Rebar.document.character.useCharacter(player);
    const characterData = character.get();

    if (!characterData) {
        notifyapi.shownotify(player, '无法获取角色数据', 'error');
        return;
    }

    // 检查是否有管理员权限
    const hasAdminPermission = Rebar.permissions.usePermissions(player).character.permissions.has('admin');

    // 检查玩家是否有进口车辆订单
    const importOrders = await db.getMany({
        characterId: characterData.id,
        submitted: false
    }, 'vehicle_import_orders') as VehicleImportOrder[];

    const hasImportOrder = importOrders.length > 0;

    if (hasImportOrder) {
        // 玩家有订单（包括管理员），打开材料提交界面
        Rebar.player.useWebview(player).show('vehiclevote', 'page', true);

        // 等待界面准备就绪
        let isReady = await Rebar.player.useWebview(player).isReady('vehiclevote', 'page');
        while (!isReady) {
            await alt.Utils.wait(100);
            isReady = await Rebar.player.useWebview(player).isReady('vehiclevote', 'page');
        }

        await sendImportOrderDataToClient(player);
    } else if (hasAdminPermission) {
        // 管理员没有订单，打开投票管理界面
        Rebar.player.useWebview(player).show('vehiclevote', 'page', true);

        // 等待界面准备就绪
        let isReady = await Rebar.player.useWebview(player).isReady('vehiclevote', 'page');
        while (!isReady) {
            await alt.Utils.wait(100);
            isReady = await Rebar.player.useWebview(player).isReady('vehiclevote', 'page');
        }

        await sendVoteDataToClient(player);
    } else {
        // 普通玩家没有订单，检查投票状态
        if (!currentVoteSession) {
            notifyapi.shownotify(player, '当前没有进行中的投票或订单', 'error');
            return;
        }

        if (currentVoteSession.status === 'active') {
            if (Date.now() > currentVoteSession.endTime) {
                notifyapi.shownotify(player, '投票已结束', 'error');
                return;
            }
            // 打开投票界面
            Rebar.player.useWebview(player).show('vehiclevote', 'page', true);

            // 等待界面准备就绪
            let isReady = await Rebar.player.useWebview(player).isReady('vehiclevote', 'page');
            while (!isReady) {
                await alt.Utils.wait(100);
                isReady = await Rebar.player.useWebview(player).isReady('vehiclevote', 'page');
            }

            await sendVoteDataToClient(player);
        } else if (currentVoteSession.status === 'orders_active') {
            // 打开订单界面
            Rebar.player.useWebview(player).show('vehiclevote', 'page', true);

            // 等待界面准备就绪
            let isReady = await Rebar.player.useWebview(player).isReady('vehiclevote', 'page');
            while (!isReady) {
                await alt.Utils.wait(100);
                isReady = await Rebar.player.useWebview(player).isReady('vehiclevote', 'page');
            }

            await sendVoteDataToClient(player);
        } else {
            notifyapi.shownotify(player, '当前没有进行中的投票或订单', 'error');
            return;
        }
    }
});

ticketinteraction.addMarker({
    pos: ticketPos,
    color: new alt.RGBA(255, 255, 0, 75),
    type: 1,
    scale: new alt.Vector3(1, 1, 0.2),
});







// 进口货车订单
const truckOrderPos = new alt.Vector3(1240.8396, -3322.4438, 5.0271);

const truckOrderBlip = Rebar.controllers.useBlipGlobal({
    pos: truckOrderPos,
    color: 43,
    sprite: 318,
    shortRange: true,
    text: '进口货车订单',
});

const truckOrderInteraction = Rebar.controllers.useInteraction(new alt.ColshapeCylinder(truckOrderPos.x, truckOrderPos.y, truckOrderPos.z, 0.8, 2), 'player');

truckOrderInteraction.onEnter((player) => {
    promptbarapi.showPromptBar(player, '进口货车订单');
});

truckOrderInteraction.onLeave((player) => {
    promptbarapi.hidePromptBar(player);
});

truckOrderInteraction.on(async (player) => {
    if (!currentWeeklyTruck) {
        notifyapi.shownotify(player, '系统未初始化，请稍后再试', 'error');
        return;
    }

    const character = Rebar.document.character.useCharacter(player);
    if (!character.get()) {
        notifyapi.shownotify(player, '无法获取角色数据', 'error');
        return;
    }

    // 显示货车订单界面
    Rebar.player.useWebview(player).show('truckorder', 'page', true);
    promptbarapi.hidePromptBar(player);

    // 发送初始数据
    await sendTruckDataToClient(player);
});

truckOrderInteraction.addMarker({
    pos: truckOrderPos,
    color: new alt.RGBA(255, 255, 0, 75),
    type: 1,
    scale: new alt.Vector3(1, 1, 0.2),
});

// 处理货车订单相关事件
// 确认订单
alt.onClient('truck:confirmOrder', async (player: alt.Player) => {
    if (!currentWeeklyTruck) {
        notifyapi.shownotify(player, '系统未初始化，请稍后再试', 'error');
        return;
    }

    const character = Rebar.document.character.useCharacter(player);
    const characterData = character.get();

    if (!characterData) {
        notifyapi.shownotify(player, '无法获取角色数据', 'error');
        return;
    }

    // 检查金钱
    const canAfford = await currencyApi.cancost({ player }, 100000);
    if (!canAfford) {
        notifyapi.shownotify(player, '余额不足，需要100,000元', 'error');
        return;
    }

    // 检查是否已有订单（已完成的订单会被删除，所以只查询存在的订单）
    const existingOrder = await db.getMany({
        characterId: characterData.id
    }, 'truck_orders') as TruckOrder[];

    if (existingOrder.length > 0) {
        notifyapi.shownotify(player, '您已有一个进行中的订单', 'error');
        return;
    }

    // 扣除费用
    const success = await currencyApi.cost({ player }, 100000, '进口货车订单');
    if (!success) {
        notifyapi.shownotify(player, '扣费失败，请重试', 'error');
        return;
    }

    // 生成材料需求
    const materials = generateMaterialRequirements();
    const materialsSubmitted: { [key: string]: number } = {};

    // 初始化已提交材料为0
    for (const material of Object.keys(materials)) {
        materialsSubmitted[material] = 0;
    }

    // 创建订单
    const orderData: Omit<TruckOrder, '_id'> = {
        characterId: characterData.id,
        truckModel: currentWeeklyTruck.currentTruckModel,
        orderCost: 100000,
        materials,
        materialsSubmitted,
        submitted: true,
        createdAt: Date.now(),
        expiresAt: Date.now() + (30 * 24 * 60 * 60 * 1000) // 30天有效期（1个月）
    };

    await db.create(orderData, 'truck_orders');
    notifyapi.shownotify(player, '订单已提交！请准备材料。', 'success');

    // 发送订单提交确认到前端
    Rebar.player.useWebview(player).emit('truck:orderSubmitted');

    // 重新发送数据
    await sendTruckDataToClient(player);
});

// 提交材料
alt.onClient('truck:submitMaterials', async (player: alt.Player, materialName: string, amount: number) => {
    const character = Rebar.document.character.useCharacter(player);
    const characterData = character.get();

    if (!characterData) {
        notifyapi.shownotify(player, '无法获取角色数据', 'error');
        return;
    }

    // 获取订单（已完成的订单会被删除，所以只查询存在的订单）
    const orders = await db.getMany({
        characterId: characterData.id
    }, 'truck_orders') as TruckOrder[];

    if (orders.length === 0) {
        notifyapi.shownotify(player, '没有找到有效订单', 'error');
        return;
    }

    const order = orders[0];
    const required = order.materials[materialName];
    const submitted = order.materialsSubmitted[materialName] || 0;

    if (!required) {
        notifyapi.shownotify(player, '该材料不在需求列表中', 'error');
        return;
    }

    if (submitted >= required) {
        notifyapi.shownotify(player, '该材料已满足需求', 'info');
        return;
    }

    // 检查玩家是否有足够材料
    const playerMaterials = getPlayerMaterials(player);
    const playerAmount = playerMaterials[materialName] || 0;

    if (playerAmount < amount) {
        notifyapi.shownotify(player, '您没有足够的材料', 'error');
        return;
    }

    // 计算实际可提交的数量
    const canSubmit = Math.min(amount, required - submitted, playerAmount);

    if (canSubmit <= 0) {
        notifyapi.shownotify(player, '无法提交材料', 'error');
        return;
    }

    // 从玩家背包移除材料
    const [success, message] = await inventoryApi.subItem(materialName, canSubmit, { player });
    if (!success) {
        notifyapi.shownotify(player, `移除材料失败: ${message}`, 'error');
        return;
    }

    // 更新订单
    order.materialsSubmitted[materialName] = submitted + canSubmit;
    await db.update(order, 'truck_orders');

    notifyapi.shownotify(player, `已提交 ${canSubmit} 个 ${materialName}`, 'success');

    // 检查是否完成所有材料
    let allCompleted = true;
    for (const [mat, req] of Object.entries(order.materials)) {
        if ((order.materialsSubmitted[mat] || 0) < req) {
            allCompleted = false;
            break;
        }
    }

    if (allCompleted) {
        notifyapi.shownotify(player, '所有材料已齐！可以获得车辆了！', 'success');
        Rebar.player.useWebview(player).emit('truck:orderComplete');
    }

    // 发送材料提交确认到前端
    Rebar.player.useWebview(player).emit('truck:materialSubmitted');

    // 重新发送数据
    await sendTruckDataToClient(player);
});

// 批量提交材料
alt.onClient('truck:submitAllMaterials', async (player: alt.Player, submissionList: { material: string, amount: number }[]) => {
    const character = Rebar.document.character.useCharacter(player);
    const characterData = character.get();

    if (!characterData) {
        notifyapi.shownotify(player, '无法获取角色数据', 'error');
        return;
    }

    // 获取订单（已完成的订单会被删除，所以只查询存在的订单）
    const orders = await db.getMany({
        characterId: characterData.id
    }, 'truck_orders') as TruckOrder[];

    if (orders.length === 0) {
        notifyapi.shownotify(player, '没有找到有效订单', 'error');
        return;
    }

    const order = orders[0];
    let totalSubmitted = 0;
    const successfulSubmissions: string[] = [];
    const failedSubmissions: string[] = [];

    // 获取玩家背包中的材料数量
    const playerMaterials = getPlayerMaterials(player);

    for (const submission of submissionList) {
        const { material, amount } = submission;
        const required = order.materials[material];
        const submitted = order.materialsSubmitted[material] || 0;

        if (!required) {
            failedSubmissions.push(`${material}(不在需求列表中)`);
            continue;
        }

        if (submitted >= required) {
            failedSubmissions.push(`${material}(已满足需求)`);
            continue;
        }

        // 检查玩家是否有足够材料
        const playerAmount = playerMaterials[material] || 0;
        if (playerAmount < amount) {
            failedSubmissions.push(`${material}(材料不足)`);
            continue;
        }

        // 计算实际可提交的数量
        const canSubmit = Math.min(amount, required - submitted, playerAmount);
        if (canSubmit <= 0) {
            failedSubmissions.push(`${material}(无法提交)`);
            continue;
        }

        // 从玩家背包移除材料
        const [success, message] = await inventoryApi.subItem(material, canSubmit, { player });
        if (!success) {
            failedSubmissions.push(`${material}(${message})`);
            continue;
        }

        // 更新订单
        order.materialsSubmitted[material] = submitted + canSubmit;
        await db.update(order, 'truck_orders');

        successfulSubmissions.push(`${material} x${canSubmit}`);
        totalSubmitted += canSubmit;
    }

    // 发送结果通知
    if (successfulSubmissions.length > 0) {
        notifyapi.shownotify(player, `成功提交: ${successfulSubmissions.join(', ')}`, 'success');
    }

    if (failedSubmissions.length > 0) {
        notifyapi.shownotify(player, `提交失败: ${failedSubmissions.join(', ')}`, 'warning');
    }

    // 检查是否完成所有材料
    let allCompleted = true;
    for (const [mat, req] of Object.entries(order.materials)) {
        if ((order.materialsSubmitted[mat] || 0) < req) {
            allCompleted = false;
            break;
        }
    }

    if (allCompleted) {
        notifyapi.shownotify(player, '所有材料已齐！可以获得车辆了！', 'success');
        Rebar.player.useWebview(player).emit('truck:orderComplete');
    }

    // 发送批量提交完成确认到前端
    Rebar.player.useWebview(player).emit('truck:allMaterialsSubmitted');

    // 重新发送数据
    await sendTruckDataToClient(player);
});

// 获得车辆
alt.onClient('truck:claimVehicle', async (player: alt.Player) => {
    const character = Rebar.document.character.useCharacter(player);
    const characterData = character.get();

    if (!characterData) {
        notifyapi.shownotify(player, '无法获取角色数据', 'error');
        return;
    }

    // 获取订单（已完成的订单会被删除，所以只查询存在的订单）
    const orders = await db.getMany({
        characterId: characterData.id
    }, 'truck_orders') as TruckOrder[];

    if (orders.length === 0) {
        notifyapi.shownotify(player, '没有找到有效订单', 'error');
        return;
    }

    const order = orders[0];

    // 检查是否完成所有材料
    let allCompleted = true;
    for (const [mat, req] of Object.entries(order.materials)) {
        if ((order.materialsSubmitted[mat] || 0) < req) {
            allCompleted = false;
            break;
        }
    }

    if (!allCompleted) {
        notifyapi.shownotify(player, '还有材料未完成', 'error');
        return;
    }

    // 创建车辆
    const result: {
        success: boolean;
        vehicle?: alt.Vehicle;
        plate?: string;
        message: string;
    } = await vehApi.createVehicleForPlayer({
        player: player,
        vehicleName: order.truckModel,
        garage: 'legion-square', // 直接存入车库
        doNotify: false, // 我们自己处理通知
        fuelTank: 120,
        trunkCapacity: 1000,
        key: true
    });

    if (!result || !result.success) {
        const errorMessage = result && result.message ? result.message : '车辆创建失败，请联系管理员';
        notifyapi.shownotify(player, `获得车辆失败：${errorMessage}`, 'error');

        // 重新发送数据以更新前端状态
        await sendTruckDataToClient(player);
        return;
    }

    // 删除已完成的订单记录
    await db.destroy(order._id!, 'truck_orders');

    notifyapi.shownotify(player, `恭喜！您的进口货车 ${order.truckModel} 已存入车库！车牌号：${result.plate}`, 'success');

    // 发送车辆获得确认到前端
    Rebar.player.useWebview(player).emit('truck:vehicleClaimed');

    // 重新发送数据
    await sendTruckDataToClient(player);
});

// 取消订单
alt.onClient('truck:cancelOrder', async (player: alt.Player) => {
    const character = Rebar.document.character.useCharacter(player);
    const characterData = character.get();

    if (!characterData) {
        notifyapi.shownotify(player, '无法获取角色数据', 'error');
        return;
    }

    // 获取订单（已完成的订单会被删除，所以只查询存在的订单）
    const orders = await db.getMany({
        characterId: characterData.id
    }, 'truck_orders') as TruckOrder[];

    if (orders.length === 0) {
        notifyapi.shownotify(player, '没有找到有效订单', 'error');
        return;
    }

    const order = orders[0];

    // 退还订单费用
    await currencyApi.add({ player }, 'cash', order.orderCost);

    // 删除订单
    await db.destroy(order._id!, 'truck_orders');

    notifyapi.shownotify(player, `订单已取消，退还 ¥${order.orderCost.toLocaleString()}`, 'success');

    // 发送取消确认到前端
    Rebar.player.useWebview(player).emit('truck:orderCancelled');

    // 重新发送数据
    await sendTruckDataToClient(player);
});

// 请求数据
alt.onClient('truck:requestData', async (player: alt.Player) => {
    await sendTruckDataToClient(player);
});

// 关闭面板
alt.onClient('truck:closePanel', (player: alt.Player) => {
    Rebar.player.useWebview(player).hide('truckorder');
});

// 定期检查过期订单并自动处理
async function checkExpiredOrders() {
    try {
        const currentTime = Date.now();

        // 获取所有订单（已完成的订单会被删除，所以只查询存在的订单）
        const allOrders = await db.getAll('truck_orders') as TruckOrder[];

        // 筛选出已过期的订单
        const expiredOrders = allOrders.filter(order => order.expiresAt < currentTime);

        for (const order of expiredOrders) {
            // 过期订单不退钱，直接删除
            // 删除过期订单
            await db.destroy(order._id!, 'truck_orders');
            console.log(`[Carshop] 订单 ${order._id} 已过期并删除（不退款）`);
        }

        if (expiredOrders.length > 0) {
            console.log(`[Carshop] 处理了 ${expiredOrders.length} 个过期订单（不退款）`);
        }
    } catch (error) {
        console.error('[Carshop] 检查过期订单时出错:', error);
    }
}

// 检查过期投票会话
async function checkExpiredVoteSessions() {
    try {
        const currentTime = Date.now();

        // 检查当前投票会话是否过期
        if (currentVoteSession && currentVoteSession.status === 'active' && currentTime > currentVoteSession.endTime) {
            await calculateVoteResults(currentVoteSession.sessionId);
           // console.log(`[VoteSystem] 投票会话 ${currentVoteSession.sessionId} 已过期，自动计算结果`);
        }

        // 检查进口车辆订单是否过期（1个月）
        const allImportOrders = await db.getAll('vehicle_import_orders') as VehicleImportOrder[];
        const expiredImportOrders = allImportOrders.filter(order => order.expiresAt < currentTime);

        for (const order of expiredImportOrders) {
            // 过期订单不退钱，直接删除
            await db.destroy(order._id!, 'vehicle_import_orders');
           // console.log(`[VoteSystem] 进口车辆订单 ${order._id} 已过期并删除（不退款）`);
        }

        if (expiredImportOrders.length > 0) {
           // console.log(`[VoteSystem] 处理了 ${expiredImportOrders.length} 个过期进口车辆订单（不退款）`);
        }
    } catch (error) {
       // console.error('[VoteSystem] 检查过期投票会话时出错:', error);
    }
}

// 统一检查函数
async function checkExpiredOrdersAndVotes() {
    await checkExpiredOrders();
    await checkExpiredVoteSessions();
}

// 每小时检查一次过期订单和投票会话
GlobalTimerManager.getInstance().addTask('carshop:checkExpiredOrdersAndVotes', checkExpiredOrdersAndVotes, 60 * 60);

// 服务器启动时立即检查一次过期订单和投票会话
setTimeout(checkExpiredOrdersAndVotes, 10000); // 延迟10秒启动，确保所有系统初始化完成


// 管理员命令：直接完成订单材料（用于测试）
Rebar.messenger.useMessenger().commands.register({
    name: 'completetruckmaterials',
    desc: '直接完成当前货车订单的所有材料需求（测试用）',
    options: { permissions: ['admin'] },
    callback: async (player: alt.Player) => {
        const character = Rebar.document.character.useCharacter(player);
        const characterData = character.get();

        if (!characterData) {
            notifyapi.shownotify(player, '无法获取角色数据', 'error');
            return;
        }

        try {
            // 获取当前玩家的订单
            const orders = await db.getMany({
                characterId: characterData.id
            }, 'truck_orders') as TruckOrder[];

            if (orders.length === 0) {
                notifyapi.shownotify(player, '您没有进行中的货车订单', 'warning');
                return;
            }

            const order = orders[0];

            // 将所有材料设置为已完成
            const newMaterialsSubmitted: { [key: string]: number } = {};
            for (const [material, required] of Object.entries(order.materials)) {
                newMaterialsSubmitted[material] = required;
            }

            // 更新订单
            order.materialsSubmitted = newMaterialsSubmitted;

            await db.update(order, 'truck_orders');

            notifyapi.shownotify(player, '所有订单材料已完成！可以获得车辆了！', 'success');
            console.log(`[Carshop] 管理员 ${characterData.name} 完成了订单 ${order._id} 的所有材料`);

            // 重新发送数据到客户端
            await sendTruckDataToClient(player);

        } catch (error) {
            console.error('[Carshop] 完成订单材料失败:', error);
            notifyapi.shownotify(player, '操作失败，请重试', 'error');
        }
    }
});

// ==================== 进口车辆投票系统 ====================

// 初始化投票系统
async function initializeVoteSystem() {
    try {
        // 检查是否有活跃的投票会话
        const allSessions = await db.getAll('vehicle_vote_sessions') as VehicleVoteSession[] | undefined;
        const activeSessions = allSessions?.filter(session =>
            session.status === 'active' || session.status === 'orders_active'
        ) || [];

        if (activeSessions.length > 0) {
            currentVoteSession = activeSessions[0];
          //  console.log(`[VoteSystem] 恢复投票会话: ${currentVoteSession.sessionId}`);

            // 检查投票是否已结束但未计算结果
            if (currentVoteSession.status === 'active' && Date.now() > currentVoteSession.endTime) {
                await calculateVoteResults(currentVoteSession.sessionId);
            }
        }
    } catch (error) {
        console.error('[VoteSystem] 初始化失败:', error);
    }
}

// 发送投票数据到客户端
async function sendVoteDataToClient(player: alt.Player) {
    try {
        const character = Rebar.document.character.useCharacter(player);
        const characterData = character.get();

        if (!characterData) return;

        const hasAdminPermission = Rebar.permissions.usePermissions(player).character.permissions.has('admin');

        // 获取玩家的投票记录（如有）
        let playerVote = null;
        if (currentVoteSession) {
            const votes = await db.getMany({
                sessionId: currentVoteSession.sessionId,
                characterId: characterData.id
            }, 'vehicle_votes') as VehicleVote[];

            if (votes.length > 0) {
                playerVote = votes[0];
            }
        }

        // 获取投票统计（仅管理员可见全部统计）
        let voteStats = null;
        if (hasAdminPermission && currentVoteSession) {
            voteStats = await getVoteStatistics(currentVoteSession.sessionId);
        }

        const voteData = {
            isAdmin: hasAdminPermission,
            currentSession: currentVoteSession,
            playerVote: playerVote,
            voteStats: voteStats
        };

        Rebar.player.useWebview(player).emit('vehicleVote:updateData', voteData);
    } catch (error) {
        console.error('[VoteSystem] 发送数据失败:', error);
    }
}

// 发送进口车辆订单数据到客户端
async function sendImportOrderDataToClient(player: alt.Player) {
    try {
        const character = Rebar.document.character.useCharacter(player);
        const characterData = character.get();
        
        if (!characterData) return;

        const hasAdminPermission = Rebar.permissions.usePermissions(player).character.permissions.has('admin');

        // 获取玩家的进口车辆订单
        const orders = await db.getMany({
            characterId: characterData.id,
            submitted: false
        }, 'vehicle_import_orders') as VehicleImportOrder[];

        const currentOrder = orders.length > 0 ? orders[0] : null;
        
        // 获取玩家背包中的材料数量
        const playerMaterials = getPlayerMaterials(player);

        // 标记为订单模式的数据
        const orderData = {
            isAdmin: hasAdminPermission,
            currentSession: currentVoteSession,
            playerVote: null,
            voteStats: null,
            isOrderMode: true,
            currentOrder: currentOrder,
            playerMaterials: playerMaterials
        };

        Rebar.player.useWebview(player).emit('vehicleVote:updateData', orderData);
    } catch (error) {
        console.error('[VoteSystem] 发送订单数据失败:', error);
    }
}

// 获取投票统计
async function getVoteStatistics(sessionId: string) {
    try {
        const votes = await db.getMany({ sessionId }, 'vehicle_votes') as VehicleVote[];
        const stats: { [model: string]: number } = {};

        votes.forEach(vote => {
            stats[vote.vehicleModel] = (stats[vote.vehicleModel] || 0) + 1;
        });

        return {
            totalVotes: votes.length,
            vehicleVotes: stats
        };
    } catch (error) {
        console.error('[VoteSystem] 获取统计失败:', error);
        return null;
    }
}

// 计算投票结果
async function calculateVoteResults(sessionId: string) {
    try {
        const votes = await db.getMany({ sessionId }, 'vehicle_votes') as VehicleVote[];
        const voteCount: { [model: string]: number } = {};

        // 统计投票
        votes.forEach(vote => {
            voteCount[vote.vehicleModel] = (voteCount[vote.vehicleModel] || 0) + 1;
        });

        // 排序获取前5名
        const sortedVehicles = Object.entries(voteCount)
            .sort(([, a], [, b]) => b - a)
            .slice(0, 5)
            .map(([model]) => model);

        // 更新会话状态
        const session = await db.getMany({ sessionId }, 'vehicle_vote_sessions') as VehicleVoteSession[];
        if (session.length > 0) {
            const updatedSession = {
                ...session[0],
                status: 'orders_active' as const,
                resultsCalculated: true,
                topVehicles: sortedVehicles
            };

            await db.update(updatedSession, 'vehicle_vote_sessions');
            currentVoteSession = updatedSession;

          //  console.log(`[VoteSystem] 投票结果计算完成，前5名: ${sortedVehicles.join(', ')}`);

            // 通知所有在线玩家
            notifyAllPlayers(`投票结果已出炉！前5名车辆现可接受订单，有效期一个月`);
        }
    } catch (error) {
        console.error('[VoteSystem] 计算结果失败:', error);
    }
}

// 通知所有玩家
function notifyAllPlayers(message: string) {
    alt.Player.all.forEach(player => {
        if (player.valid) {
            notifyapi.shownotify(player, message, 'info');
        }
    });
}

// ==================== 客户端事件处理 ====================

// 管理员创建投票会话
alt.onClient('vehicleVote:createSession', async (player: alt.Player, data: {
    vehicles: string[],
    vehiclePrices: { [model: string]: number },
    materialPrices: { [model: string]: number }
}) => {
    try {
        const hasAdminPermission = Rebar.permissions.usePermissions(player).character.permissions.has('admin');
        if (!hasAdminPermission) {
            notifyapi.shownotify(player, '没有权限', 'error');
            return;
        }

        const character = Rebar.document.character.useCharacter(player);
        const characterData = character.get();
        if (!characterData) return;

        // 检查是否已有活跃会话
        if (currentVoteSession && (currentVoteSession.status === 'active' || currentVoteSession.status === 'orders_active')) {
            notifyapi.shownotify(player, '已有活跃的投票或订单会话', 'error');
            return;
        }

        // 验证数据
        if (!data.vehicles || data.vehicles.length !== 10) {
            notifyapi.shownotify(player, '必须设置10个车辆', 'error');
            return;
        }

        const sessionId = `vote_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        const now = Date.now();
        const twoDaysLater = now + (2 * 24 * 60 * 60 * 1000); // 2天后

        const newSession: Omit<VehicleVoteSession, '_id'> = {
            sessionId,
            vehicles: data.vehicles,
            vehiclePrices: data.vehiclePrices,
            materialPrices: data.materialPrices,
            startTime: now,
            endTime: twoDaysLater,
            resultsCalculated: false,
            topVehicles: [],
            createdBy: characterData.id,
            status: 'active'
        };

        const createdId = await db.create(newSession, 'vehicle_vote_sessions');
        if (createdId) {
            currentVoteSession = { ...newSession, _id: createdId };
            notifyapi.shownotify(player, '投票会话创建成功！投票期限2天', 'success');

            // 通知所有玩家
            notifyAllPlayers('新的进口车辆投票已开始！前往投票站参与投票');

           // console.log(`[VoteSystem] 管理员 ${characterData.name} 创建了投票会话: ${sessionId}`);

            // 不再使用 setTimeout，改为每小时检查机制
        }

        await sendVoteDataToClient(player);
    } catch (error) {
        console.error('[VoteSystem] 创建会话失败:', error);
        notifyapi.shownotify(player, '创建失败，请重试', 'error');
    }
});

// 玩家投票
alt.onClient('vehicleVote:vote', async (player: alt.Player, vehicleModel: string) => {
    try {
        const character = Rebar.document.character.useCharacter(player);
        const characterData = character.get();
        if (!characterData) return;

        // 检查投票会话
        if (!currentVoteSession || currentVoteSession.status !== 'active') {
            notifyapi.shownotify(player, '当前没有进行中的投票', 'error');
            return;
        }

        if (Date.now() > currentVoteSession.endTime) {
            notifyapi.shownotify(player, '投票已结束', 'error');
            return;
        }

        // 检查车辆是否在投票列表中
        if (!currentVoteSession.vehicles.includes(vehicleModel)) {
            notifyapi.shownotify(player, '无效的车辆选择', 'error');
            return;
        }

        // 检查是否已投票
        const existingVote = await db.getMany({
            sessionId: currentVoteSession.sessionId,
            characterId: characterData.id
        }, 'vehicle_votes') as VehicleVote[];

        if (existingVote.length > 0) {
            // 更新投票
            const updatedVote = {
                ...existingVote[0],
                vehicleModel,
                voteTime: Date.now()
            };
            await db.update(updatedVote, 'vehicle_votes');
            notifyapi.shownotify(player, `投票已更新为: ${vehicleModel}`, 'success');
        } else {
            // 创建新投票
            const newVote: Omit<VehicleVote, '_id'> = {
                sessionId: currentVoteSession.sessionId,
                characterId: characterData.id,
                vehicleModel,
                voteTime: Date.now()
            };
            await db.create(newVote, 'vehicle_votes');
            notifyapi.shownotify(player, `投票成功: ${vehicleModel}`, 'success');
        }

        await sendVoteDataToClient(player);
    } catch (error) {
        console.error('[VoteSystem] 投票失败:', error);
        notifyapi.shownotify(player, '投票失败，请重试', 'error');
    }
});

// 请求投票数据
alt.onClient('vehicleVote:requestData', async (player: alt.Player) => {
    try {
        const character = Rebar.document.character.useCharacter(player);
        const characterData = character.get();
        if (!characterData) return;

        // 检查玩家是否有进口车辆订单
        const importOrders = await db.getMany({
            characterId: characterData.id,
            submitted: false
        }, 'vehicle_import_orders') as VehicleImportOrder[];

        if (importOrders.length > 0) {
            // 玩家有订单，发送订单数据
            await sendImportOrderDataToClient(player);
        } else {
            // 玩家没有订单，发送投票数据
            await sendVoteDataToClient(player);
        }
    } catch (error) {
        console.error('[VoteSystem] 请求数据失败:', error);
    }
});

// 获取玩家汽车设计图并验证
async function getPlayerCarBlueprints(player: alt.Player): Promise<{ blueprints: any[], averageCompletion: number }> {
    try {
        const inventory = await inventoryApi.getInventory({ player });
        const carBlueprints: any[] = [];

        for (const item of inventory) {
            if (item && item.name === '汽车设计图' && item.customData && item.customData.designCategory === 'car') {
                carBlueprints.push({
                    slot: item.slot,
                    type: item.customData.type,
                    finish: item.customData.finish,
                    customData: item.customData
                });
            }
        }

        // 检查是否有三张不同部位的设计图
        const requiredTypes = ['car_side', 'car_top', 'car_front'];
        const foundTypes = [...new Set(carBlueprints.map(bp => bp.type))];
        
        if (foundTypes.length < 3 || !requiredTypes.every(type => foundTypes.includes(type))) {
            return { blueprints: [], averageCompletion: 0 };
        }

        // 选择每种类型完成度最高的设计图
        const selectedBlueprints: any[] = [];
        for (const type of requiredTypes) {
            const typeBlueprints = carBlueprints.filter(bp => bp.type === type);
            if (typeBlueprints.length > 0) {
                const bestBlueprint = typeBlueprints.reduce((best, current) => 
                    current.finish > best.finish ? current : best
                );
                selectedBlueprints.push(bestBlueprint);
            }
        }

        // 计算平均完成度
        const totalCompletion = selectedBlueprints.reduce((sum, bp) => sum + bp.finish, 0);
        const averageCompletion = selectedBlueprints.length > 0 ? totalCompletion / selectedBlueprints.length : 0;

        return { blueprints: selectedBlueprints, averageCompletion };
    } catch (error) {
        console.error('获取玩家汽车设计图失败:', error);
        return { blueprints: [], averageCompletion: 0 };
    }
}

// 玩家创建进口车辆订单
alt.onClient('vehicleVote:createOrder', async (player: alt.Player, data: {
    vehicleModel: string
}) => {
    try {
        const character = Rebar.document.character.useCharacter(player);
        const characterData = character.get();
        if (!characterData) return;

        // 检查投票会话状态
        if (!currentVoteSession || currentVoteSession.status !== 'orders_active') {
            notifyapi.shownotify(player, '当前没有可订购的进口车辆', 'error');
            return;
        }

        // 检查车辆是否在前5名中
        if (!currentVoteSession.topVehicles.includes(data.vehicleModel)) {
            notifyapi.shownotify(player, '该车辆未在前5名中，无法订购', 'error');
            return;
        }

        // 检查汽车设计图要求
        const { blueprints, averageCompletion } = await getPlayerCarBlueprints(player);
        if (blueprints.length < 3) {
            notifyapi.shownotify(player, '订购进口车辆需要三张不同部位的汽车设计图（侧视图、俯视图、正视图）', 'error');
            return;
        }

        // 检查是否已有订单
        const existingOrder = await db.getMany({
            sessionId: currentVoteSession.sessionId,
            characterId: characterData.id
        }, 'vehicle_import_orders') as VehicleImportOrder[];

        if (existingOrder.length > 0) {
            notifyapi.shownotify(player, '您已有进口车辆订单', 'error');
            return;
        }

        // 获取订单价格
        const orderCost = currentVoteSession.vehiclePrices[data.vehicleModel] || 100000;

        // 检查资金
        const canAfford = await currencyApi.cancost({ player }, orderCost);
        if (!canAfford) {
            notifyapi.shownotify(player, `资金不足，需要 $${orderCost}`, 'error');
            return;
        }

        // 扣除费用
        const success = await currencyApi.cost({ player }, orderCost, `进口车辆订单: ${data.vehicleModel}`);
        if (!success) {
            notifyapi.shownotify(player, '扣费失败，请重试', 'error');
            return;
        }

        // 消耗汽车设计图
        for (const blueprint of blueprints) {
            const [removeSuccess, removeMessage] = await inventoryApi.subItem('汽车设计图', 1, { player }, blueprint.slot);
            if (!removeSuccess) {
                notifyapi.shownotify(player, `消耗汽车设计图失败: ${removeMessage}`, 'error');
                // 如果消耗失败，退还费用
                await currencyApi.add({ player }, 'cash', orderCost);
                return;
            }
        }

        // 根据设计图平均完成度调整材料价格
        // 100%完成度 → 75%材料需求，50%完成度 → 125%材料需求
        // 公式：材料需求 = 150% - 完成度 × 0.75%
        const basePrice = currentVoteSession.materialPrices[data.vehicleModel] || 100000;
        const priceMultiplier = 1.5 - (averageCompletion * 0.0075); // 100%→0.75, 50%→1.125
        const adjustedPrice = Math.round(basePrice * priceMultiplier);
        
        const materials = generateMaterialRequirementsForPrice(adjustedPrice);

        // 初始化已提交材料为0
        const materialsSubmitted: { [key: string]: number } = {};
        for (const material of Object.keys(materials)) {
            materialsSubmitted[material] = 0;
        }

        // 创建订单
        const now = Date.now();
        const oneMonth = 30 * 24 * 60 * 60 * 1000; // 一个月

        const newOrder: Omit<VehicleImportOrder, '_id'> = {
            sessionId: currentVoteSession.sessionId,
            characterId: characterData.id,
            vehicleModel: data.vehicleModel,
            orderCost,
            materials: materials,
            materialsSubmitted: materialsSubmitted,
            submitted: false,
            createdAt: now,
            expiresAt: now + oneMonth
        };

        await db.create(newOrder, 'vehicle_import_orders');
        
        // 根据设计图完成度给出通知
        let qualityMessage = '';
        if (averageCompletion >= 95) {
            qualityMessage = '大师级设计图！材料需求大幅降低';
        } else if (averageCompletion >= 85) {
            qualityMessage = '优秀设计图！材料需求有所降低';
        } else if (averageCompletion > 75) {
            qualityMessage = '良好设计图，材料需求略有降低';
        } else if (averageCompletion === 75) {
            qualityMessage = '标准设计图，材料需求正常';
        } else if (averageCompletion >= 65) {
            qualityMessage = '一般设计图，材料需求略有增加';
        } else if (averageCompletion >= 55) {
            qualityMessage = '粗糙设计图，材料需求有所增加';
        } else {
            qualityMessage = '简陋设计图，材料需求大幅增加';
        }

        notifyapi.shownotify(player, `进口车辆订单创建成功！${qualityMessage}`, 'success');
        notifyapi.shownotify(player, `设计图平均完成度: ${Math.round(averageCompletion)}%，订单有效期30天`, 'info');

        console.log(`[VoteSystem] 玩家 ${characterData.name} 创建了进口车辆订单: ${data.vehicleModel}，设计图完成度: ${averageCompletion}%`);

        // 订单创建成功后，发送通知玩家重新打开界面查看订单状态
        const webview = Rebar.player.useWebview(player);
        webview.emit('vehicleVote:orderCreated');

    } catch (error) {
        console.error('[VoteSystem] 创建订单失败:', error);
        notifyapi.shownotify(player, '创建订单失败，请重试', 'error');
    }
});

// 获取玩家的进口车辆订单信息
alt.onClient('importOrder:getData', async (player: alt.Player) => {
    try {
        const character = Rebar.document.character.useCharacter(player);
        const characterData = character.get();
        if (!characterData) return;

        // 获取玩家的进口车辆订单
        const orders = await db.getMany({
            characterId: characterData.id,
            submitted: false
        }, 'vehicle_import_orders') as VehicleImportOrder[];

        const currentOrder = orders.length > 0 ? orders[0] : null;

        // 获取玩家背包中的材料数量
        const playerMaterials = getPlayerMaterials(player);

        // 发送数据到前端
        Rebar.player.useWebview(player).emit('importOrder:updateData', {
            currentOrder: currentOrder,
            playerMaterials: playerMaterials,
            currentSession: currentVoteSession
        });
    } catch (error) {
        console.error('[ImportOrder] 获取数据失败:', error);
    }
});

// 提交进口车辆订单材料
alt.onClient('importOrder:submitMaterial', async (player: alt.Player, data: {
    materialName: string,
    amount: number
}) => {
    try {
        const character = Rebar.document.character.useCharacter(player);
        const characterData = character.get();
        if (!characterData) return;

        // 获取订单
        const orders = await db.getMany({
            characterId: characterData.id,
            submitted: false
        }, 'vehicle_import_orders') as VehicleImportOrder[];

        if (orders.length === 0) {
            notifyapi.shownotify(player, '您没有进口车辆订单', 'error');
            return;
        }

        const order = orders[0];

        // 检查材料是否在需求列表中
        if (!order.materials[data.materialName]) {
            notifyapi.shownotify(player, '该材料不在订单需求中', 'error');
            return;
        }

        // 检查玩家背包
        const playerMaterials = getPlayerMaterials(player);
        const playerHas = playerMaterials[data.materialName] || 0;

        if (playerHas < data.amount) {
            notifyapi.shownotify(player, `您只有 ${playerHas} 个 ${data.materialName}`, 'error');
            return;
        }

        // 检查提交数量
        const required = order.materials[data.materialName];
        const alreadySubmitted = order.materialsSubmitted[data.materialName] || 0;
        const remaining = required - alreadySubmitted;

        if (data.amount > remaining) {
            notifyapi.shownotify(player, `最多只需要 ${remaining} 个`, 'error');
            return;
        }

        // 移除玩家背包中的材料
        const [removeSuccess, message] = await inventoryApi.subItem(data.materialName, data.amount, { player });
        if (!removeSuccess) {
            notifyapi.shownotify(player, `移除材料失败: ${message}`, 'error');
            return;
        }

        // 更新订单
        const newMaterialsSubmitted = { ...order.materialsSubmitted };
        newMaterialsSubmitted[data.materialName] = alreadySubmitted + data.amount;

        order.materialsSubmitted = newMaterialsSubmitted;
        await db.update(order, 'vehicle_import_orders');

        notifyapi.shownotify(player, `成功提交 ${data.amount} 个 ${data.materialName}`, 'success');

        // 检查是否完成所有材料
        const isCompleted = Object.entries(order.materials).every(([material, required]) => {
            const submitted = newMaterialsSubmitted[material] || 0;
            return submitted >= required;
        });

        if (isCompleted) {
            // 生成车辆
            await generateImportVehicle(player, order);
        }

        // 重新发送数据
        await sendImportOrderDataToClient(player);

        // 发送材料提交成功事件
        Rebar.player.useWebview(player).emit('importOrder:materialSubmitted');

    } catch (error) {
        console.error('[ImportOrder] 提交材料失败:', error);
        notifyapi.shownotify(player, '提交失败，请重试', 'error');
    }
});

// 批量提交进口车辆订单材料
alt.onClient('importOrder:submitAllMaterials', async (player: alt.Player, submissionList: { material: string, amount: number }[]) => {
    try {
        const character = Rebar.document.character.useCharacter(player);
        const characterData = character.get();
        if (!characterData) return;

        // 获取订单
        const orders = await db.getMany({
            characterId: characterData.id,
            submitted: false
        }, 'vehicle_import_orders') as VehicleImportOrder[];

        if (orders.length === 0) {
            notifyapi.shownotify(player, '您没有进口车辆订单', 'error');
            return;
        }

        const order = orders[0];
        let totalSubmitted = 0;
        const successfulSubmissions: string[] = [];
        const failedSubmissions: string[] = [];

        // 获取玩家背包中的材料数量
        const playerMaterials = getPlayerMaterials(player);

        for (const submission of submissionList) {
            const { material, amount } = submission;
            const required = order.materials[material];
            const submitted = order.materialsSubmitted[material] || 0;

            if (!required) {
                failedSubmissions.push(`${material}(不在需求列表中)`);
                continue;
            }

            if (submitted >= required) {
                failedSubmissions.push(`${material}(已满足需求)`);
                continue;
            }

            // 检查玩家是否有足够材料
            const playerAmount = playerMaterials[material] || 0;
            if (playerAmount < amount) {
                failedSubmissions.push(`${material}(材料不足)`);
                continue;
            }

            // 计算实际可提交的数量
            const canSubmit = Math.min(amount, required - submitted, playerAmount);
            if (canSubmit <= 0) {
                failedSubmissions.push(`${material}(无法提交)`);
                continue;
            }

            // 从玩家背包移除材料
            const [success, message] = await inventoryApi.subItem(material, canSubmit, { player });
            if (!success) {
                failedSubmissions.push(`${material}(${message})`);
                continue;
            }

            // 更新订单
            order.materialsSubmitted[material] = submitted + canSubmit;
            await db.update(order, 'vehicle_import_orders');

            successfulSubmissions.push(`${material} x${canSubmit}`);
            totalSubmitted += canSubmit;
        }

        // 发送结果通知
        if (successfulSubmissions.length > 0) {
            notifyapi.shownotify(player, `成功提交: ${successfulSubmissions.join(', ')}`, 'success');
        }

        if (failedSubmissions.length > 0) {
            notifyapi.shownotify(player, `提交失败: ${failedSubmissions.join(', ')}`, 'warning');
        }

        // 检查是否完成所有材料
        const isCompleted = Object.entries(order.materials).every(([material, required]) => {
            const submittedAmount = order.materialsSubmitted[material] || 0;
            return submittedAmount >= required;
        });

        if (isCompleted) {
            notifyapi.shownotify(player, '所有材料已齐！可以获得车辆了！', 'success');
        }

        // 重新发送数据
        await sendImportOrderDataToClient(player);

        // 发送批量材料提交完成事件
        Rebar.player.useWebview(player).emit('importOrder:allMaterialsSubmitted');

    } catch (error) {
        console.error('[ImportOrder] 批量提交材料失败:', error);
        notifyapi.shownotify(player, '提交失败，请重试', 'error');
    }
});

// 获得进口车辆
alt.onClient('importOrder:claimVehicle', async (player: alt.Player) => {
    try {
        const character = Rebar.document.character.useCharacter(player);
        const characterData = character.get();
        if (!characterData) return;

        // 获取订单
        const orders = await db.getMany({
            characterId: characterData.id,
            submitted: false
        }, 'vehicle_import_orders') as VehicleImportOrder[];

        if (orders.length === 0) {
            notifyapi.shownotify(player, '您没有进口车辆订单', 'error');
            return;
        }

        const order = orders[0];

        // 检查是否完成所有材料
        const isCompleted = Object.entries(order.materials).every(([material, required]) => {
            const submittedAmount = order.materialsSubmitted[material] || 0;
            return submittedAmount >= required;
        });

        if (!isCompleted) {
            notifyapi.shownotify(player, '还有材料未完成', 'error');
            return;
        }

        // 生成车辆
        await generateImportVehicle(player, order);

        // 发送车辆获取成功事件
        Rebar.player.useWebview(player).emit('importOrder:vehicleClaimed');

        // 重新发送数据
        await sendImportOrderDataToClient(player);

    } catch (error) {
        console.error('[ImportOrder] 获得车辆失败:', error);
        notifyapi.shownotify(player, '获得车辆失败，请重试', 'error');
    }
});

// 生成进口车辆
async function generateImportVehicle(player: alt.Player, order: VehicleImportOrder) {
    try {
        const character = Rebar.document.character.useCharacter(player);
        const characterData = character.get();
        if (!characterData) return;

        // 使用vehApi创建车辆并直接存入车库
        const result: {
            success: boolean;
            vehicle?: alt.Vehicle;
            plate?: string;
            message: string;
        } = await vehApi.createVehicleForPlayer({
            player: player,
            vehicleName: order.vehicleModel,
            garage: 'legion-square', // 直接存入车库
            doNotify: false, // 我们自己处理通知
            fuelTank: 120,
            trunkCapacity: 1000,
            key: true
        });

        if (!result || !result.success) {
            const errorMessage = result && result.message ? result.message : '车辆创建失败，请联系管理员';
            notifyapi.shownotify(player, `获得进口车辆失败：${errorMessage}`, 'error');
            return;
        }

        // 删除订单
        await db.destroy(order._id!, 'vehicle_import_orders');

        notifyapi.shownotify(player, `恭喜！您的进口车辆 ${order.vehicleModel} 已存入车库！车牌号：${result.plate}`, 'success');
        console.log(`[ImportOrder] 玩家 ${characterData.name} 获得了进口车辆: ${order.vehicleModel}，车牌：${result.plate}`);

    } catch (error) {
        console.error('[ImportOrder] 生成车辆失败:', error);
        notifyapi.shownotify(player, '生成车辆失败，请联系管理员', 'error');
    }
}

// 启动时初始化投票系统
alt.nextTick(async () => {
    await initializeWeeklyTruck();
    await initializeVoteSystem();
    console.log('[Carshop] 系统初始化完成');
});

// ==================== 管理员命令 ====================

// 强制结束当前投票并计算结果
Rebar.messenger.useMessenger().commands.register({
    name: '/endvote',
    desc: '强制结束当前投票并计算结果',
    options: { permissions: ['admin'] },
    callback: async (player: alt.Player) => {
        if (!currentVoteSession || currentVoteSession.status !== 'active') {
            notifyapi.shownotify(player, '当前没有进行中的投票', 'error');
            return;
        }

        await calculateVoteResults(currentVoteSession.sessionId);
        notifyapi.shownotify(player, '投票已强制结束，结果已计算', 'success');
    },
});

// 查看投票状态
Rebar.messenger.useMessenger().commands.register({
    name: '/votestatus',
    desc: '查看当前投票状态',
    options: { permissions: ['admin'] },
    callback: async (player: alt.Player) => {
        if (!currentVoteSession) {
            notifyapi.shownotify(player, '当前没有投票会话', 'info');
            return;
        }

        const stats = await getVoteStatistics(currentVoteSession.sessionId);
        const status = getStatusText(currentVoteSession.status);

        notifyapi.shownotify(player, `状态: ${status}, 总投票: ${stats?.totalVotes || 0}`, 'info');

        if (currentVoteSession.topVehicles.length > 0) {
            notifyapi.shownotify(player, `前5名: ${currentVoteSession.topVehicles.join(', ')}`, 'info');
        }
    },
});

// 清理过期订单
Rebar.messenger.useMessenger().commands.register({
    name: '/cleanexpiredorders',
    desc: '清理过期的进口车辆订单',
    options: { permissions: ['admin'] },
    callback: async (player: alt.Player) => {
        try {
            const now = Date.now();
            const allOrders = await db.getAll('vehicle_import_orders') as VehicleImportOrder[] | undefined;
            const expiredOrders = allOrders?.filter(order => order.expiresAt < now) || [];

            for (const order of expiredOrders) {
                await db.destroy(order._id!, 'vehicle_import_orders');
            }

            notifyapi.shownotify(player, `已清理 ${expiredOrders.length} 个过期订单`, 'success');
          //  console.log(`[VoteSystem] 管理员 ${Rebar.document.character.useCharacter(player).get().name} 清理了 ${expiredOrders.length} 个过期订单`);
        } catch (error) {
            console.error('[VoteSystem] 清理过期订单失败:', error);
            notifyapi.shownotify(player, '清理失败，请重试', 'error');
        }
    },
});

// 完成玩家的进口车辆订单材料（管理员专用）
Rebar.messenger.useMessenger().commands.register({
    name: '/completeimportorder',
    desc: '完成指定玩家的进口车辆订单材料 [角色ID]',
    options: { permissions: ['admin'] },
    callback: async (player: alt.Player, characterId: string) => {
        try {
            const targetCharacterId = parseInt(characterId);
            if (isNaN(targetCharacterId)) {
                notifyapi.shownotify(player, '请输入有效的角色ID', 'error');
                return;
            }

            const orders = await db.getMany({
                characterId: targetCharacterId,
                submitted: false
            }, 'vehicle_import_orders') as VehicleImportOrder[];

            if (orders.length === 0) {
                notifyapi.shownotify(player, '该玩家没有未完成的进口车辆订单', 'error');
                return;
            }

            const order = orders[0];

            // 完成所有材料
            const completedMaterials: { [key: string]: number } = {};
            for (const [material, required] of Object.entries(order.materials)) {
                completedMaterials[material] = required;
            }

            order.materialsSubmitted = completedMaterials;
            await db.update(order, 'vehicle_import_orders');

            notifyapi.shownotify(player, `已完成角色 ${targetCharacterId} 的进口车辆订单材料`, 'success');
          //  console.log(`[VoteSystem] 管理员 ${Rebar.document.character.useCharacter(player).get().name} 完成了角色 ${targetCharacterId} 的进口车辆订单材料`);

            // 通知目标玩家（如果在线）
            const targetPlayer = alt.Player.all.find(p => {
                const char = Rebar.document.character.useCharacter(p).get();
                return char && char.id === targetCharacterId;
            });

            if (targetPlayer) {
                notifyapi.shownotify(targetPlayer, '管理员已完成您的进口车辆订单材料！', 'success');
            }

        } catch (error) {
            console.error('[VoteSystem] 完成订单材料失败:', error);
            notifyapi.shownotify(player, '操作失败，请重试', 'error');
        }
    },
});

// 取消进口车辆订单
alt.onClient('importOrder:cancel', async (player: alt.Player) => {
    try {
        const character = Rebar.document.character.useCharacter(player);
        const characterData = character.get();
        if (!characterData) return;

        // 获取订单
        const orders = await db.getMany({
            characterId: characterData.id,
            submitted: false
        }, 'vehicle_import_orders') as VehicleImportOrder[];

        if (orders.length === 0) {
            notifyapi.shownotify(player, '您没有进口车辆订单', 'error');
            return;
        }

        const order = orders[0];

        // 退还订单费用（玩家主动取消）
        await currencyApi.add({ player }, 'cash', order.orderCost);

        // 删除订单
        await db.destroy(order._id!, 'vehicle_import_orders');

        notifyapi.shownotify(player, `进口车辆订单已取消，退还 ¥${order.orderCost.toLocaleString()}`, 'success');
       // console.log(`[VoteSystem] 玩家 ${characterData.name} 取消了进口车辆订单: ${order.vehicleModel}`);

        // 发送取消确认到前端
        Rebar.player.useWebview(player).emit('importOrder:cancelled');

    } catch (error) {
        console.error('[VoteSystem] 取消订单失败:', error);
        notifyapi.shownotify(player, '取消订单失败，请重试', 'error');
    }
});

function getStatusText(status: string) {
    switch (status) {
        case 'active': return '投票进行中';
        case 'ended': return '已结束';
        case 'orders_active': return '订单开放中';
        default: return '未知状态';
    }
}
