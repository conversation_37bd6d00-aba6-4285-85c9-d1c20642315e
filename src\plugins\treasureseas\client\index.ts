import { useRebarClient } from '@Client/index.js';
import { useWebview } from '@Client/webview/index.js';
import { getDirectionFromRotation } from '@Client/utility/math/index.js';
import { drawText2D, drawText3D } from '@Client/screen/textlabel.js';
import { currentLungSkills } from '../../gym/client/lung.js';
import type { TreasureDisplayData } from '../shared/types.js';
import { TREASURE_EVENTS } from '../shared/types.js';
import * as native from 'natives';
import * as alt from 'alt-client';

const Rebar = useRebarClient();
const webview = Rebar.webview.useWebview();
const messenger = Rebar.messenger.useMessenger();

// 氧气监测相关变量
let oxygenCheckInterval: number | null = null;
let lastOxygenLevel = 100;
let hasRequestedRefill = false;
let isInWater = false;

// 基础潜水时间配置
let baseUnderwaterTime = 15.0; // 基础潜水时间（秒）
let currentMaxUnderwaterTime = 15.0; // 当前最大潜水时间

// 监测玩家氧气值
function startOxygenMonitoring() {
    if (oxygenCheckInterval) return;

    oxygenCheckInterval = alt.setInterval(() => {
        const player = alt.Player.local;

        // 检查玩家是否在水下
        const currentlyInWater = native.isPedSwimmingUnderWater(player);

        // 更新水中状态
        if (currentlyInWater !== isInWater) {
            isInWater = currentlyInWater;
        }

        if (currentlyInWater) {
            // 获取玩家当前氧气值
            const oxygenLevel = native.getPlayerUnderwaterTimeRemaining(player);

            // 转换为百分比（基于当前最大潜水时间）
            const oxygenPercent = (oxygenLevel / underwatertime) * 100;

            // 当氧气低于20%且还没有请求过补充时
            if (oxygenPercent < 20 && !hasRequestedRefill ) {
                hasRequestedRefill = true;
                alt.emitServerRaw('treasureseas:requestOxygenRefill');
            }

            // 当氧气恢复到80%以上时，重置标志
            if (oxygenPercent > 80) {
                hasRequestedRefill = false;
            }

            lastOxygenLevel = oxygenPercent;
        } else {
            // 不在水中时重置
            hasRequestedRefill = false;
            lastOxygenLevel = 100;
            native.setPedMaxTimeUnderwater(player, currentMaxUnderwaterTime);
        }

        // 实时更新最大潜水时间（基于gym插件的肺活量数据）
        updateMaxUnderwaterTime();
    }, 100); // 每100ms检查一次
}

let underwatertime = currentMaxUnderwaterTime;
// 补充氧气
alt.onServer('treasureseas:refillOxygen', () => {
    const player = alt.Player.local;

    // 使用潜水装备效果，持续时间基于当前最大潜水时间
    // 这会立即恢复氧气并提供额外的潜水能力
    underwatertime += currentMaxUnderwaterTime;
    native.setPedMaxTimeUnderwater(player, underwatertime);

    // 计算氧气瓶效果持续时间：基于当前最大潜水时间
    // 最少15秒，最多40秒的额外时间（与GTA V原版肺活量范围一致）
    const oxygenBottleDuration = Math.max(15000, Math.min(40000, currentMaxUnderwaterTime * 1000));

            // 使用氧气瓶，延长潜水时间

    // 视觉效果：显示氧气瓶使用动画
    const animDict = 'mp_player_intdrink';
    native.requestAnimDict(animDict);

    // 等待动画加载
    let attempts = 0;
    const loadInterval = alt.setInterval(() => {
        if (native.hasAnimDictLoaded(animDict) || attempts > 10) {
            if (native.hasAnimDictLoaded(animDict)) {
                // 播放喝水动画（模拟使用氧气瓶）
                native.taskPlayAnim(player, animDict, 'loop_bottle', 8.0, -8.0, 2000, 49, 0, false, false, false);
            }
            alt.clearInterval(loadInterval);
        }
        attempts++;
    }, 50);

    // 重置请求标志
    hasRequestedRefill = false;
});

// 更新最大潜水时间（基于gym插件的肺活量技能）
function updateMaxUnderwaterTime() {
    // 从gym插件获取当前肺活量技能
    const lungCapacity = currentLungSkills.lungCapacity || 0;
    
    // GTA V原版公式：潜水时间（秒） ≈ 15 + 0.25 × 肺活量百分比
    // 0%肺活量 = 15秒，100%肺活量 = 40秒
    const newMaxTime = 15.0 + 0.25 * lungCapacity;

    // 只在时间发生变化时更新
    if (Math.abs(newMaxTime - currentMaxUnderwaterTime) > 0.1) {
        currentMaxUnderwaterTime = newMaxTime;

        // 立即应用新的最大潜水时间
        const player = alt.Player.local;
        if (player && !hasRequestedRefill) {
            native.setPedMaxTimeUnderwater(player, currentMaxUnderwaterTime);
        }

        // 肺活量技能影响最大潜水时间
    }
}

// 启动氧气监测
startOxygenMonitoring();

// 当玩家死亡时停止监测
alt.on('playerDeath', () => {
    if (oxygenCheckInterval) {
        alt.clearInterval(oxygenCheckInterval);
        oxygenCheckInterval = null;
    }
});

// 网络拥有者变更处理 - 鲨鱼
alt.on('netOwnerChange', (entity: alt.Entity, newOwner: alt.Player, oldOwner: alt.Player) => {
    if (entity instanceof alt.Ped) {
        alt.emitServerRaw('treasureseas:netOwnerChange', entity.remoteID);
    }
});

// 肢解鲨鱼按键处理
alt.on('keyup', (key: alt.KeyCode) => {
    if (key != 69) return; // E键

    if (alt.isConsoleOpen()) return;
    if (messenger.isChatFocused()) return;
    if (webview.isAnyPageOpen()) return;

    alt.emitServerRaw('treasureseas:pickupShark');
});

// 设置鲨鱼初始状态
alt.onServer('treasureseas:setSharkInitialStatus', async (shark: alt.Ped) => {
    try {
        // 等待鲨鱼生成，但设置超时时间避免无限等待
        await alt.Utils.waitFor(() => shark.isSpawned, 10000); // 10秒超时

        // 清除所有任务
        native.clearPedTasks(shark.scriptID);
        native.clearPedSecondaryTask(shark.scriptID);
        
        // 参考hunt插件的设置方式
        native.setEntityAsMissionEntity(shark.scriptID, true, false);
        native.setPedCanRagdoll(shark.scriptID, false);
        native.taskSetBlockingOfNonTemporaryEvents(shark.scriptID, true);
        native.setBlockingOfNonTemporaryEvents(shark.scriptID, true); // 这个很重要！
        native.setPedFleeAttributes(shark.scriptID, 0, false);
        native.setPedCombatAttributes(shark.scriptID, 17, true);
        
        // 解除冻结状态
        native.freezeEntityPosition(shark.scriptID, false);
        
        // 设置为可碰撞和物理模拟
        native.setEntityCollision(shark.scriptID, true, true);
        native.setEntityVisible(shark.scriptID, true, false);
        
        // 设置鲨鱼的战斗属性
        native.setPedCombatAttributes(shark.scriptID, 46, true); // 允许攻击
        native.setPedCombatAttributes(shark.scriptID, 5, true);  // 允许近战攻击
        native.setPedSeeingRange(shark.scriptID, 30.0); // 设置视野范围
        native.setPedHearingRange(shark.scriptID, 40.0); // 设置听觉范围
        
        // 防止鲨鱼攻击其他动物
        native.setPedCombatAttributes(shark.scriptID, 1, false); // 禁止攻击动物
        native.setPedCombatAttributes(shark.scriptID, 2, false); // 禁止攻击宠物
        
        // 确保鲨鱼在水中的行为正确
        native.setPedConfigFlag(shark.scriptID, 1, true);  // 允许在水中
        native.setPedConfigFlag(shark.scriptID, 2, true);  // 允许游泳
        
    } catch (error) {
        // 初始化失败，记录错误但不抛出异常
        console.log('[海底寻宝] 鲨鱼初始化失败:', error.message);
    }
});

// 设置鲨鱼游荡状态
alt.onServer('treasureseas:setSharkWandering', async (shark: alt.Ped, targetPos: alt.Vector2, heading: number) => {
    try {
        await alt.Utils.waitFor(() => shark.isSpawned, 10000); // 10秒超时

        // 清除现有任务
        native.clearPedTasks(shark.scriptID);
        
        // 确保鲨鱼没有被冻结
        native.freezeEntityPosition(shark.scriptID, false);
        
        const targetZ = shark.pos.z; // 保持当前深度
        
        // 使用简单的移动任务
        native.taskGoStraightToCoord(shark.scriptID, targetPos.x, targetPos.y, targetZ, 2.0, 60000, heading, 0);
        
    } catch (error) {
        console.log('[海底寻宝] 鲨鱼游荡设置失败:', error.message);
    }
});

// 设置鲨鱼攻击状态 - 简化版本，直接有效的攻击
alt.onServer('treasureseas:setSharkAttacking', async (shark: alt.Ped, target: alt.Player) => {
    try {
        await alt.Utils.waitFor(() => shark.isSpawned, 10000); // 10秒超时
        
        // 清除现有任务
        native.clearPedTasks(shark.scriptID);
        native.clearPedSecondaryTask(shark.scriptID);
        
        // 确保鲨鱼没有被冻结
        native.freezeEntityPosition(shark.scriptID, false);
        
        // 让鲨鱼快速游向目标
        native.taskGoToEntity(shark.scriptID, target.scriptID, -1, 2.0, 4.0, 1073741824, 0);
        
        // 追击和咬击循环
        let sharkAttackLoop = alt.setInterval(() => {
            if (!shark.valid || !target.valid) {
                alt.clearInterval(sharkAttackLoop);
                return;
            }
            
            const currentDistance = shark.pos.distanceTo(target.pos);
            
            if (currentDistance > 25.0) {
                // 目标太远，放弃攻击
                alt.clearInterval(sharkAttackLoop);
                return;
            }
            
            if (currentDistance <= 3.0) {
                // 足够近，执行咬击攻击
                // 播放咬击动画（如果有的话）
                try {
                    native.taskPlayAnim(shark.scriptID, "creatures@shark@swim", "attack_player", 8.0, -8.0, 2000, 0, 0, false, false, false);
                } catch (e) {
                    // 忽略动画播放失败
                }
                
                // 通知服务器执行咬击伤害
                alt.emitServerRaw('treasureseas:sharkBitePlayer', shark.remoteID, target.remoteID);
                
                // 咬击后短暂停顿，然后继续追击
                alt.setTimeout(() => {
                    if (shark.valid && target.valid) {
                        native.taskGoToEntity(shark.scriptID, target.scriptID, -1, 2.0, 4.0, 1073741824, 0);
                    }
                }, 2000);
                
            } else if (currentDistance > 8.0) {
                // 距离较远，重新游向目标
                native.clearPedTasks(shark.scriptID);
                native.taskGoToEntity(shark.scriptID, target.scriptID, -1, 3.0, 4.0, 1073741824, 0);
            }
            // 距离3-8米之间时保持当前追击任务
            
        }, 1500); // 每1.5秒检查一次，更频繁
        
        // 15秒后停止攻击
        alt.setTimeout(() => {
            alt.clearInterval(sharkAttackLoop);
        }, 15000);
        
    } catch (error) {
        console.log('[海底寻宝] 鲨鱼攻击设置失败:', error.message);
    }
});

// 设置鲨鱼逃跑状态 - 参考hunt插件的实现
alt.onServer('treasureseas:setSharkFleeing', async (shark: alt.Ped, fleeDistance: number) => {
    try {
        await alt.Utils.waitFor(() => shark.isSpawned, 10000); // 10秒超时

        // 清除现有任务
        native.clearPedTasks(shark.scriptID);
        
        // 确保鲨鱼没有被冻结
        native.freezeEntityPosition(shark.scriptID, false);
        
        // 获取最近的玩家作为逃跑目标
        const nearestPlayer = alt.Player.all.reduce((closest, player) => {
            const dist = shark.pos.distanceTo(player.pos);
            return !closest || dist < shark.pos.distanceTo(closest.pos) ? player : closest;
        }, null);
        
        if (nearestPlayer) {
            // 使用智能逃跑任务（参考hunt插件）
            native.taskSmartFleePed(shark.scriptID, nearestPlayer.scriptID, fleeDistance, 10000, false, false);
        } else {
            // 没有玩家时，向随机方向逃跑
            const angle = Math.random() * Math.PI * 2;
            const escapeX = shark.pos.x + Math.cos(angle) * fleeDistance;
            const escapeY = shark.pos.y + Math.sin(angle) * fleeDistance;
            const escapeZ = shark.pos.z;
            
            native.taskGoStraightToCoord(shark.scriptID, escapeX, escapeY, escapeZ, 5.0, 10000, 0.0, 0);
        }
        
    } catch (error) {
        console.log('[海底寻宝] 鲨鱼逃跑设置失败:', error.message);
    }
});

// 检测玩家是否在水中（用于鲨鱼攻击判断）
alt.setInterval(() => {
    const player = alt.Player.local;
    if (player) {
        const isInWater = native.isPedSwimmingUnderWater(player.scriptID);
        alt.emitServerRaw('treasureseas:updateWaterStatus', isInWater);
    }
}, 100); // 每秒检测一次

// 氧气瓶功能
alt.onServer('treasureseas:refillOxygen', () => {
    // 显示氧气补充效果
    //alt.log('[海底寻宝] 氧气已补充');
    
    // 这里可以添加视觉效果或其他客户端反馈
    // 实际的氧气恢复会在服务器端或通过其他方式处理
});

// 客户端系统加载完成

// 导出函数供其他模块使用
export {
    currentMaxUnderwaterTime
};

// 通过函数提供当前肺活量（从gym插件获取）
export function getCurrentLungCapacity() {
    return currentLungSkills.lungCapacity || 0;
}

// 移除不必要的客户端中转逻辑
// 服务端会直接控制 webview 的显示和隐藏