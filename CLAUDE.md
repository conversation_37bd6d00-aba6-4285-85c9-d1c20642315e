# 🎯 开发指南 - 简洁版

## 🔥 核心原则

### 最少代码原则
- 用最少的代码实现功能
- 优先修改现有代码，避免新建文件
- 先检查是否有现成工具，别重复造轮子

### 统一架构思维
- 发现多个相似需求 → 立即创建统一解决方案
- 创建工具后 → 立即在所有地方应用
- 保持一致性 > 局部优化

---

## 🏗️ 已建立的架构模式

### 上班系统 - handleDutyLogic
**文件**: `src/plugins/beonduty/server/dutyHelper.ts`

**统一用法**:
```typescript
// 所有上班系统都用这一行代码
await handleDutyLogic(player, '派系名称');
```

**功能**: 自动处理制服检查、上班下班切换、性别过滤、权限控制

**❌ 绝对不要**: 在新上班系统中重写复杂逻辑
**✅ 始终使用**: handleDutyLogic 函数

---

## 🛠️ 常用API速查

### 🚨 IDE错误检查与修复流程

#### 🔴 强制性工作流程 (每次都执行!)
**无论用户是否要求，每次开发任务都必须：**
1. **任务开始时立即使用 `mcp__ide__getDiagnostics` 检查错误**
2. **任务完成前再次检查确认无错误**
3. **发现错误时先询问用户如何修复，不要盲目尝试**
4. **零错误才算任务真正完成**

#### 错误处理策略
- **🟢 明确知道如何修复** → 直接修复
- **🟡 不确定修复方法** → 先问用户："这个错误怎么修复？"
- **🔴 复杂的类型错误** → 展示错误信息，请求指导

#### 标准检查时机
1. **任务开始前** - 了解当前状态
2. **代码修改后** - 确认没有引入新错误  
3. **任务完成前** - 最终验证

#### 错误修复优先级
1. **TypeScript编译错误** - 最高优先级，必须立即修复
2. **API调用错误** - 检查API用法是否正确
3. **类型错误** - 确保类型安全
4. **未使用变量警告** - 清理无用代码

---

### 命令注册API (重要!)
```typescript
// ✅ 正确用法
const messenger = Rebar.messenger.useMessenger();
messenger.commands.register({
    name: 'commandname',
    desc: '命令描述',
    options: { permissions: ['admin'] }, // 权限设置
    callback: async (player: alt.Player, arg1?: string, arg2?: string) => {
        // 命令逻辑 - 参数直接作为函数参数，不是数组
    }
});

// ❌ 错误用法
Rebar.commands.useCommand().register({
    name: 'commandname',
    permission: 'admin', // 错误的权限格式
    callback: async (player: alt.Player, args?: string[]) => {
        // 这种API不存在
    }
});
```

### 命令参数处理示例
```typescript
// 正确的相对位移命令示例
messenger.commands.register({
    name: 'sitrelative',
    desc: '相对位移坐着 [x] [y] [z]',
    options: { permissions: ['admin'] },
    callback: async (player: alt.Player, x?: string, y?: string, z?: string) => {
        const xOffset = parseFloat(x || '0');
        const yOffset = parseFloat(y || '0'); 
        const zOffset = parseFloat(z || '0');
        
        // 使用解析后的参数
        const newPos = new alt.Vector3(
            player.pos.x + xOffset,
            player.pos.y + yOffset,
            player.pos.z + zOffset
        );
        player.pos = newPos;
    }
});
```

### 数据库操作
```typescript
const db = Rebar.database.useDatabase();
await db.create(data, 'collection');
await db.get({ query }, 'collection');
await db.update({ _id, ...data }, 'collection');
```

### 事件通信
```typescript
const events = useEvents();
events.on('event-name', callback);
events.emitServer('event-name', data);
events.emitClient('event-name', data);
```

### 通知系统
```typescript
const notifyApi = await Rebar.useApi().getAsync('notify-api');
notifyApi.shownotify(player, '消息', 'success|error|warning|info');
```

### 交互点创建
```typescript
const interaction = Rebar.controllers.useInteraction(
    new alt.ColshapeCylinder(x, y, z, radius, height), 'player'
);
interaction.onEnter(callback);
interaction.on(callback);
```

### 库存与掉落物系统
```typescript
// 掉落物创建 - discardItem函数
import { discardItem } from '@Plugins/inventory/server/index.js';

// 用法1: 直接通过物品名称创建掉落物
await discardItem(
    new alt.Vector3(x, y, z),  // 掉落位置
    'itemname',               // 物品名称(字符串)
    5,                        // 数量
    'prop_cs_cardbox_01'      // 可选：3D模型
);

// 用法2: 通过完整物品对象创建掉落物
await discardItem(
    player.pos,               // 掉落位置
    itemObject,               // 完整物品对象(包含name, quantity等)
    itemObject.quantity,      // 数量(会被物品对象的quantity覆盖)
    'prop_cs_cardbox_01'      // 可选：3D模型
);

// 物品对象格式:
interface Item {
    name: string;
    quantity: number;
    // 其他属性...
}
```

### 掉落物系统特点
- **自动创建3D对象** - 在指定位置生成可见的掉落物
- **数据库记录** - 自动保存到'dropitem'集合
- **元数据同步** - 设置StreamSyncedMeta用于客户端识别
- **支持自定义模型** - 默认使用纸箱模型
- **时间戳记录** - 自动记录掉落时间

---

## 🎨 Vue组件规范

### 基本结构
```vue
<template>
  <div class="container">
    <!-- 内容 -->
  </div>
</template>

<script setup lang="ts">
import { useEvents } from '@Composables/useEvents';
const events = useEvents();
</script>

<style scoped>
.container {
  width: 80vw; height: 90vh;
  position: fixed; top: 50%; left: 50%;
  transform: translate(-50%, -50%);
}
</style>
```

### 重要规则
- 必须使用 `scoped` 样式
- 尺寸用 vh/vw，不用 px
- 界面默认居中显示
- 禁用 alert/confirm/prompt

---

## 🚫 错误模式 & ✅ 正确做法

### 错误: 忘记已有工具
```typescript
// ❌ 错误 - 重复实现上班逻辑
if (isOnDuty) {
    factionapi.endbeonduty(player);
} else {
    // 复杂的制服检查逻辑...
}
```

```typescript
// ✅ 正确 - 使用统一工具
await handleDutyLogic(player, '派系名称');
```

### 错误: 局部优化破坏一致性
- ❌ 在单个文件中"特殊处理"
- ✅ 在工具层面统一优化

### 错误: 重复造轮子
- ❌ 每个文件都有自己的实现
- ✅ 抽象为通用工具

---

## 📋 开发检查清单

### 修改上班系统时:
- [ ] 是否使用了 `handleDutyLogic`？
- [ ] 是否避免了重复逻辑？
- [ ] 是否保持了一致性？

### 创建新功能时:
- [ ] 这个问题其他地方是否存在？
- [ ] 是否需要创建通用解决方案？
- [ ] 创建后是否立即应用到所有地方？
- [ ] 是否需要更新此文档？

---

## 🔧 推荐配色 (可选)
```css
--primary-color: #64ffda;        /* 青绿主色 */
--primary-bg: #0f172a;           /* 深色背景 */
--card-bg: rgba(30, 41, 59, 0.8); /* 卡片背景 */
--text-primary: #f8fafc;         /* 主要文字 */
--success: #64ffda;              /* 成功色 */
--danger: #ef4444;               /* 危险色 */
```

---

## 💡 记忆要点

1. **有重复 → 立即统一**
2. **创建工具 → 立即应用**  
3. **一致性 > 完美**
4. **最少代码 > 复杂方案**

**核心思想**: 当你发现自己在写类似代码时，立即停下来思考是否需要抽象为通用工具。

---

## ⚡ 高效执行原则

### 并行操作优先
- **同时调用多个工具** - 而不是按顺序执行
- **批量处理独立任务** - 最大化执行效率
- **并发读取相关文件** - 一次性获取所需信息

### 深度反思迭代
- **仔细分析工具结果质量** - 每次执行后评估效果
- **基于新信息重新规划** - 动态调整下一步行动
- **持续优化决策过程** - 从结果中学习改进

### 临时文件管理
- **任务结束后清理** - 删除所有临时文件和脚本
- **保持工作区整洁** - 避免留下开发垃圾
- **只保留最终产出** - 确保代码库干净

---

## 🎨 前端开发增强指导

### 全力以赴原则
- **不要有所保留，全力以赴创造**
- **展示完整的网页开发能力**
- **创建令人印象深刻的演示效果**

### 功能丰富度
- **包含尽可能多的相关功能和交互**
- **添加完整的用户体验流程**
- **实现复杂的业务逻辑处理**

### 交互细节优化
- **添加周到的悬停状态** - hover效果和状态变化
- **流畅的过渡动画** - transition和animation
- **精致的微交互** - 点击反馈、加载状态、错误提示
- **响应式交互反馈** - 用户操作的即时响应

### 设计原则应用
- **层次结构** - 清晰的信息架构和视觉层级
- **对比度** - 突出重要信息，区分不同元素
- **平衡感** - 布局的视觉平衡和空间分配
- **动态性** - 生动的界面表现和动态效果

### Vue组件增强标准
```vue
<template>
  <div class="enhanced-component">
    <!-- 复杂的交互布局 -->
    <transition name="fade" mode="out-in">
      <div class="content-area" :class="dynamicClasses">
        <!-- 丰富的功能组件 -->
      </div>
    </transition>
  </div>
</template>

<script setup lang="ts">
// 复杂的状态管理和业务逻辑
import { ref, computed, watch, onMounted } from 'vue'
import { useEvents } from '@Composables/useEvents'

// 完整的交互功能实现
</script>

<style scoped>
/* 精致的样式设计 */
.enhanced-component {
  /* 应用设计原则的样式 */
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 悬停状态 */
.content-area:hover {
  transform: translateY(-0.2vh);
  box-shadow: 0 0.8vh 2.4vh rgba(100, 255, 218, 0.2);
}

/* 过渡动画 */
.fade-enter-active, .fade-leave-active {
  transition: opacity 0.3s ease;
}
.fade-enter-from, .fade-leave-to {
  opacity: 0;
}
</style>
```

**前端开发座右铭**: 创造复杂、精美、交互丰富的用户界面，展示完整的开发实力！

---

## 🏗️ 项目架构概览

### 项目性质：Rebar框架 + alt:V游戏服务器
- **基础框架**: Rebar - TypeScript框架，专为alt:V多人游戏开发
- **游戏平台**: alt:V - 基于GTA V的多人游戏平台
- **开发语言**: TypeScript + Vue 3 + TailwindCSS
- **数据库**: MongoDB
- **架构模式**: 插件化架构，每个功能模块独立

### 核心技术栈
```typescript
// 服务器端
- alt:V Server API
- TypeScript
- MongoDB数据库
- Node.js生态系统

// 客户端
- alt:V Client API  
- TypeScript
- 游戏内CEF WebView

// WebView界面
- Vue 3 + Composition API
- TailwindCSS样式框架
- Vite构建工具
```

### 项目规模与复杂度
- **插件数量**: 100+ 个独立插件
- **代码规模**: 大型项目，完整的游戏服务器框架
- **功能范围**: 
  - 玩家认证与角色系统
  - 派系与工作系统  
  - 经济与货币系统
  - 载具与房产系统
  - 库存与物品系统
  - 任务与活动系统
  - 通信与社交功能

### 主要功能模块

#### 🏢 派系与职业系统
- **派系管理**: 警察局、医院、市政厅、交管局等
- **制服系统**: 动态制服配置，支持等级权限
- **上班系统**: 统一的`handleDutyLogic`处理
- **权限管理**: 基于角色的权限控制

#### 💰 经济系统
- **货币管理**: 现金、银行、积分系统
- **交易系统**: ATM、转账、账单系统
- **商店系统**: 各类NPC商店，支持自定义价格

#### 🎮 游戏机制
- **库存系统**: 复杂的物品管理，支持制作、交易
- **载具系统**: 载具购买、改装、过户
- **房产系统**: 房屋买卖、装修、存储
- **任务系统**: 工作任务、活动任务

#### 🎨 界面系统
- **WebView组件**: Vue 3组件，运行在游戏内CEF
- **UI规范**: 统一的设计语言，vh/vw响应式单位
- **交互设计**: 丰富的动画效果和用户反馈

### 当前开发状态

#### ✅ 已完成的重要工作
1. **统一上班系统**: 创建了`dutyHelper.ts`统一处理所有派系上班逻辑
2. **制服系统集成**: 所有上班功能现已支持制服检查和选择
3. **架构优化**: 消除了代码重复，建立了"统一优于分散"的开发模式

#### 🔧 技术债务与改进空间
- 部分老代码仍需重构以符合新的统一架构
- 一些Vue组件可以进一步优化用户体验
- 数据库查询性能可以进一步优化

### 开发环境与工具
```json
{
  "构建工具": "Vite + TypeScript + Sucrase",
  "包管理": "pnpm",
  "开发模式": "热重载 + 增量编译",
  "代码质量": "Prettier + ESLint",
  "版本控制": "Git"
}
```

### 部署与运行
- **服务器**: alt:V服务器 + MongoDB数据库
- **客户端**: 通过alt:V客户端连接
- **开发命令**: `pnpm dev` - 开发模式热重载
- **构建命令**: `pnpm build` - 生产环境构建

---

## 🎯 开发方向与优先级

### 当前重点
1. **继续完善制服系统** - 优化Vue组件的用户体验
2. **扩展派系功能** - 添加更多派系管理功能
3. **性能优化** - 优化数据库查询和界面响应

### 技术改进方向
1. **组件复用** - 抽象更多通用组件
2. **API统一** - 建立更多统一的API模式
3. **用户体验** - 改进WebView界面的交互设计

**项目总结**: 这是一个成熟的、大规模的多人游戏服务器项目，采用现代化的TypeScript技术栈，具有完整的游戏功能体系和优雅的插件化架构。

---

## 🔍 IDE错误检查与修复流程

### 强制性工作流程
> **重要**: 每次开发任务中必须执行以下步骤，确保代码质量

#### 1. 开始任务前检查
```typescript
// 在每次开始编码前，必须执行IDE诊断检查
mcp__ide__getDiagnostics();
// 如果发现错误，必须立即修复再继续
```

#### 2. 常见错误类型与修复方案

**API调用错误**:
```typescript
// ❌ 错误 - 使用不存在的同步API
const notifyapi = Rebar.useApi().getSync('notify-api');

// ✅ 正确 - 使用异步API
const notifyapi = await Rebar.useApi().getAsync('notify-api');
```

**类型安全错误**:
```typescript
// ❌ 错误 - 未处理unknown类型
const npcName = player.getStreamSyncedMeta('currentConversationNPC');

// ✅ 正确 - 添加类型断言
const npcName = player.getStreamSyncedMeta('currentConversationNPC') as string;
```

**异步操作错误**:
```typescript
// ❌ 错误 - 在非async函数中使用await
const items = data.map(item => ({
    callback: await createCallback(item)
}));

// ✅ 正确 - 使用Promise.all处理批量异步操作
const callbacks = await Promise.all(data.map(item => createCallback(item)));
const items = callbacks.map((callback, index) => ({
    callback: callback,
    data: data[index]
}));
```

#### 3. 任务完成标准
- [ ] **IDE零错误** - `mcp__ide__getDiagnostics()` 返回空数组
- [ ] **功能测试** - 代码逻辑正确，功能完整
- [ ] **代码规范** - 符合项目TypeScript规范
- [ ] **文档更新** - 重要更改记录到CLAUDE.md

### 检查清单模板
```markdown
## 任务完成检查 ✅

### IDE错误检查
- [ ] 执行 `mcp__ide__getDiagnostics()`
- [ ] 修复所有TypeScript错误
- [ ] 修复所有类型警告
- [ ] 清理未使用的导入

### 功能验证
- [ ] 核心功能正常工作
- [ ] 异步操作正确处理
- [ ] 错误边界处理完善
- [ ] 性能无明显问题

### 代码质量
- [ ] 遵循项目架构模式
- [ ] 使用统一的API调用方式
- [ ] 代码注释充分（如需要）
- [ ] 无重复代码或逻辑
```

### 强制执行原则
1. **任务未完成前不停止** - 直到IDE零错误且功能完整
2. **错误立即修复** - 发现问题立即处理，不留技术债务
3. **完整性验证** - 确保所有相关文件都没有引入新错误
4. **持续监控** - 在整个开发过程中定期检查IDE状态

**记忆要点**: 
- 🔴 **绝不允许**带错误提交代码
- 🟡 **警告级别**的问题也要处理
- 🟢 **只有零错误**才算任务完成

---

## 🎯 任务完成必须原则

### 绝不半途而废的执行标准
> **核心承诺**: 每个任务必须100%完成，绝不中途停止或留下未完成的工作

#### 🔴 强制执行规则
1. **任务接受前必须完全理解** - 确认所有需求和期望
2. **创建详细执行计划** - 使用TodoWrite工具追踪每个步骤
3. **持续状态更新** - 实时告知用户当前进度
4. **验证所有功能** - 确保每个功能都能正常工作
5. **最终确认完成** - 明确告知用户"任务已完全完成"

#### 任务执行检查点
- [ ] **任务开始**: 使用`mcp__ide__getDiagnostics`检查初始状态
- [ ] **需求理解**: 与用户确认所有具体要求
- [ ] **执行计划**: 使用TodoWrite创建完整任务清单
- [ ] **过程追踪**: 每完成一个步骤立即更新状态
- [ ] **功能验证**: 测试所有实现的功能
- [ ] **错误清零**: 再次使用`mcp__ide__getDiagnostics`确认无错误
- [ ] **最终确认**: 明确告知用户任务完成状态

#### 禁止的行为模式
- ❌ 实现一半就停止
- ❌ 假设用户理解未完成的工作
- ❌ 不进行最终验证就结束
- ❌ 留下未解决的错误或警告
- ❌ 没有明确的任务完成确认

#### 必须的沟通模式
- ✅ "我正在执行第X步：[具体内容]"
- ✅ "已完成[具体功能]，正在验证..."
- ✅ "发现问题：[问题描述]，正在修复..."
- ✅ "所有功能已实现并验证完成"
- ✅ "任务已100%完成，无错误，可以正常使用"

### 责任承诺声明
**我承诺**: 对于每个接受的开发任务，我将：
1. 彻底完成所有要求的功能
2. 修复过程中发现的所有错误
3. 验证最终结果的正确性
4. 明确告知任务的完成状态
5. 绝不在任务未完成时停止工作

**用户可以期待**: 交给我的每个任务都会得到完整、可靠、经过验证的解决方案。