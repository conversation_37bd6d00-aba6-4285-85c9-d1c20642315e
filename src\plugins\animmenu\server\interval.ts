import * as alt from 'alt-server';


// 创建一个全局计时器管理器，用于统一管理所有计时器任务
export class GlobalTimerManager {
    private static instance: GlobalTimerManager;
    private tasks: Map<string, { callback: Function, time: number, currentTick: number, shouldRemove?: boolean }>;
    private interval: number | null = null;
    private taskPool: Array<{ callback: Function | null, time: number, currentTick: number, shouldRemove?: boolean }> = [];
    private static readonly MAX_TASKS = 1000;

    private constructor() {
        this.tasks = new Map();
    }

    public static getInstance(): GlobalTimerManager {
        if (!GlobalTimerManager.instance) {
            GlobalTimerManager.instance = new GlobalTimerManager();
        }
        return GlobalTimerManager.instance;
    }

    /**
     * 从对象池获取任务对象，减少GC压力
     */
    private getTaskObject(callback: Function, time: number): { callback: Function, time: number, currentTick: number, shouldRemove?: boolean } {
        const obj = this.taskPool.pop();
        if (obj) {
            obj.callback = callback;
            obj.time = time;
            obj.currentTick = 0;
            obj.shouldRemove = false;
            return obj as { callback: Function, time: number, currentTick: number, shouldRemove?: boolean };
        }
        return { callback, time, currentTick: 0, shouldRemove: false };
    }

    /**
     * 回收任务对象到对象池
     */
    private recycleTaskObject(obj: { callback: Function, time: number, currentTick: number, shouldRemove?: boolean }): void {
        if (this.taskPool.length < 100) { // 限制池大小
            obj.callback = null!;
            obj.time = 0;
            obj.currentTick = 0;
            obj.shouldRemove = false;
            this.taskPool.push(obj as { callback: Function | null, time: number, currentTick: number, shouldRemove?: boolean });
        }
    }

    /**
     * 批量清理标记删除的任务
     */
    private cleanupTasks(): void {
        const toRemove: string[] = [];
        
        for (const [id, taskInfo] of this.tasks) {
            if (taskInfo.shouldRemove) {
                toRemove.push(id);
            }
        }
        
        for (const id of toRemove) {
            const taskInfo = this.tasks.get(id);
            if (taskInfo) {
                this.recycleTaskObject(taskInfo);
                this.tasks.delete(id);
            }
        }
    }
    /**
     * 生成唯一ID
     * @returns 生成的唯一ID字符串
     */
    private generateUniqueId(): string {
        const timestamp = Date.now().toString(36);
        const randomStr = Math.random().toString(36).substring(2, 10);
        let uniqueId = `task_${timestamp}_${randomStr}`;
        
        // 确保ID不重复
        while (this.tasks.has(uniqueId)) {
            uniqueId = `task_${Date.now().toString(36)}_${Math.random().toString(36).substring(2, 10)}`;
        }
        
        return uniqueId;
    }

    /**
     * 初始化全局计时器（如果尚未初始化）
     */
    private initTimer() {
        if (this.interval === null) {
            let cleanupCounter = 0;
            
            this.interval = alt.setInterval(() => {
                // 性能优化：缓存引用，避免重复访问
                const tasks = this.tasks;
                const tasksSize = tasks.size;
                
                if (tasksSize === 0) return;
                
                // 预分配数组避免动态扩容
                const taskEntries = Array.from(tasks.entries());
                
                // 使用索引循环，避免迭代器开销
                for (let i = 0; i < taskEntries.length; i++) {
                    const [id, taskInfo] = taskEntries[i];
                    
                    try {
                        // 增加当前计时器的tick
                        taskInfo.currentTick++;
                        
                        // 当累计的tick达到设定的时间时，执行回调并重置计数器
                        if (taskInfo.currentTick >= taskInfo.time) {
                            taskInfo.callback();
                            taskInfo.currentTick = 0; // 重置计数器
                        }
                    } catch (error) {
                        console.error(`全局计时器任务执行错误 (任务ID: ${id}):`, error);
                        // 标记删除而不是立即删除，避免在循环中修改Map
                        taskInfo.shouldRemove = true;
                    }
                }
                
                // 每10次循环执行一次清理，减少频繁操作
                cleanupCounter++;
                if (cleanupCounter >= 10) {
                    this.cleanupTasks();
                    cleanupCounter = 0;
                }
            }, 1000);
        }
    }

    /**
     * 添加一个任务到全局计时器（兼容旧接口）
     * @param idOrCallback 任务ID或回调函数
     * @param callbackOrTime 回调函数或执行间隔
     * @param timeOrUndefined 可选的执行间隔
     * @returns 任务ID或null
     */
    public addTask(idOrCallback: string | Function, callbackOrTime?: Function | number, timeOrUndefined?: number): string | null {
        // 新接口: addTask(callback, time?, id?)
        if (typeof idOrCallback === 'function') {
            const callback = idOrCallback;
            const time = typeof callbackOrTime === 'number' ? callbackOrTime : 1;
            const id = typeof callbackOrTime === 'string' ? callbackOrTime : undefined;
            
            return this.addTaskInternal(callback, time, id);
        } 
        // 旧接口: addTask(id, callback, time?)
        else if (typeof idOrCallback === 'string' && typeof callbackOrTime === 'function') {
            const id = idOrCallback;
            const callback = callbackOrTime;
            const time = typeof timeOrUndefined === 'number' ? timeOrUndefined : 1;
            
            return this.addTaskInternal(callback, time, id);
        }
        
        return null;
    }
    
    /**
     * 内部添加任务的实现
     */
    private addTaskInternal(callback: Function, time: number = 1, id?: string): string | null {
        // 检查任务数量限制
        if (this.tasks.size >= GlobalTimerManager.MAX_TASKS) {
            console.warn(`[GlobalTimerManager] 任务数量已达上限 ${GlobalTimerManager.MAX_TASKS}`);
            return null;
        }
        
        // 如果没有提供ID，则生成一个唯一ID
        const taskId = id || this.generateUniqueId();
        
        // 如果ID已存在，且是手动指定的ID，则返回null表示添加失败
        if (id && this.tasks.has(taskId)) {
            return null;
        }

        // 使用对象池获取任务对象
        const taskObj = this.getTaskObject(callback, time);
        this.tasks.set(taskId, taskObj);
        
        this.initTimer(); // 确保计时器已启动
        return taskId;
    }

    /**
     * 移除指定的任务
     * @param id 任务唯一标识
     * @returns 是否成功移除
     */
    public removeTask(id: string): boolean {
        const taskInfo = this.tasks.get(id);
        if (!taskInfo) {
            return false;
        }
        
        // 回收对象到池中
        this.recycleTaskObject(taskInfo);
        const result = this.tasks.delete(id);
        
        // 如果没有任务了，清除计时器
        if (this.tasks.size === 0 && this.interval !== null) {
            alt.clearInterval(this.interval);
            this.interval = null;
        }
        
        return result;
    }

    /**
     * 检查任务是否存在
     * @param id 任务唯一标识
     * @returns 任务是否存在
     */
    public hasTask(id: string): boolean {
        return this.tasks.has(id);
    }

    /**
     * 获取所有任务的ID
     * @returns 任务ID数组
     */
    public getAllTaskIds(): string[] {
        return Array.from(this.tasks.keys());
    }
    
    /**
     * 重置指定任务的计时器
     * @param id 任务唯一标识
     * @returns 是否成功重置
     */
    public resetTaskTick(id: string): boolean {
        if (!this.tasks.has(id)) {
            return false;
        }
        
        const taskInfo = this.tasks.get(id);
        taskInfo.currentTick = 0;
        return true;
    }
    
    /**
     * 修改任务的执行间隔
     * @param id 任务唯一标识
     * @param newTime 新的执行间隔（秒）
     * @returns 是否成功修改
     */
    public updateTaskTime(id: string, newTime: number): boolean {
        if (!this.tasks.has(id)) {
            return false;
        }
        
        const taskInfo = this.tasks.get(id);
        taskInfo.time = newTime;
        return true;
    }
}

// 导出单例实例，方便外部使用
export const globalTimer = GlobalTimerManager.getInstance();
