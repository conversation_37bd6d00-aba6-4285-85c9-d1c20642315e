import { useRebarClient } from '@Client/index.js';
import { useWebview } from '@Client/webview/index.js';
import * as native from 'natives';
import * as alt from 'alt-client';

const Rebar = useRebarClient();
const webview = Rebar.webview.useWebview();
const messenger = Rebar.messenger.useMessenger();

// 获取车辆详细信息
function getVehicleInfo(vehicle: alt.Vehicle) {
    const model = vehicle.model;
    const scriptID = vehicle.scriptID;
    
    // 获取车辆显示名称
    const displayName = native.getDisplayNameFromVehicleModel(model);
    const vehicleName = displayName || `Model_${model}`;
    
    // 获取车辆类别
    const vehicleClass = native.getVehicleClass(scriptID);
    const className = getVehicleClassName(vehicleClass);
    
    // 获取座位数
    const maxPassengers = native.getVehicleMaxNumberOfPassengers(scriptID);
    const totalSeats = maxPassengers + 1; // +1 for driver
    
    // 获取车辆性能数据
    const maxSpeed = Math.round(native.getVehicleModelEstimatedMaxSpeed(model) * 3.6); // 转换为km/h
    const acceleration = native.getVehicleModelAcceleration(model);
    const braking = native.getVehicleModelMaxBraking(model);
    const traction = native.getVehicleModelMaxTraction(model);
    
    // 获取车门数量
    const doorCount = getVehicleDoorCount(scriptID);
    
    return {
        name: vehicleName,
        class: className,
        seats: totalSeats,
        doors: doorCount,
        maxSpeed: maxSpeed,
        acceleration: Math.round(acceleration * 100),
        braking: Math.round(braking * 100),
        traction: Math.round(traction * 100)
    };
}

// 获取车辆类别名称
function getVehicleClassName(vehicleClass: number): string {
    const classNames = {
        0: '紧凑型',
        1: '轿车',
        2: 'SUV',
        3: '双门跑车',
        4: '肌肉车',
        5: '经典跑车',
        6: '跑车',
        7: '超级跑车',
        8: '摩托车',
        9: '越野车',
        10: '工业车',
        11: '多功能车',
        12: '货车',
        13: '自行车',
        14: '船只',
        15: '直升机',
        16: '飞机',
        17: '服务车',
        18: '紧急车辆',
        19: '军用车',
        20: '商用车',
        21: '火车'
    };
    return classNames[vehicleClass] || '未知';
}

// 获取车门数量
function getVehicleDoorCount(scriptID: number): number {
    let doorCount = 0;
    // 检查每个可能的车门位置
    for (let i = 0; i < 8; i++) {
        if (native.getIsDoorValid(scriptID, i)) {
            doorCount++;
        }
    }
    return doorCount;
}

// 显示车辆信息文本
function displayVehicleInfo(showroomIndex: number, vehicleInfo: any) {
    const lines = [
        `展台 ${showroomIndex + 1}`,
        `${vehicleInfo.name}`,
        `类别: ${vehicleInfo.class}`,
        `座位: ${vehicleInfo.seats}人 | 车门: ${vehicleInfo.doors}个`,
        `最高速度: ${vehicleInfo.maxSpeed} km/h`,
        `加速: ${vehicleInfo.acceleration}% | 刹车: ${vehicleInfo.braking}%`,
        `抓地力: ${vehicleInfo.traction}%`
    ];
    
    let yOffset = 0.75;
    for (const line of lines) {
        native.beginTextCommandDisplayText('STRING');
        native.addTextComponentSubstringPlayerName(line);
        native.setTextScale(0.35, 0.35);
        native.setTextFont(4);
        native.setTextCentre(true);
        
        // 根据内容设置不同颜色
        if (line.includes('展台')) {
            native.setTextColour(255, 255, 0, 255); // 黄色标题
        } else if (line === vehicleInfo.name) {
            native.setTextColour(0, 255, 0, 255); // 绿色车名
        } else {
            native.setTextColour(255, 255, 255, 255); // 白色信息
        }
        
        native.endTextCommandDisplayText(0.5, yOffset, 0);
        yOffset += 0.025;
    }
}

// 添加展台车辆信息显示
alt.everyTick(() => {
    if (webview.isAnyPageOpen() || messenger.isChatFocused() || alt.isConsoleOpen()) {
        return;
    }

    // 检测是否瞄准展台车辆
    const [hit, entity] = native.getEntityPlayerIsFreeAimingAt(alt.Player.local);
    if (hit && entity) {
        const vehicle = alt.Vehicle.getByScriptID(entity);
        if (vehicle && vehicle.hasStreamSyncedMeta('carshop:showroom')) {
            const showroomIndex = vehicle.getStreamSyncedMeta('carshop:showroomIndex') as number;
            
            // 获取并显示车辆详细信息
            try {
                const vehicleInfo = getVehicleInfo(vehicle);
                displayVehicleInfo(showroomIndex, vehicleInfo);
            } catch (error) {
                // 如果获取信息失败，显示基本信息
                native.beginTextCommandDisplayText('STRING');
                native.addTextComponentSubstringPlayerName(`展台 ${showroomIndex + 1}`);
                native.setTextScale(0.4, 0.4);
                native.setTextFont(4);
                native.setTextCentre(true);
                native.setTextColour(255, 255, 0, 255);
                native.endTextCommandDisplayText(0.5, 0.85, 0);
            }
        }
    }
});

