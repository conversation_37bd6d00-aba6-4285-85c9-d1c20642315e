import * as alt from 'alt-server';
import { useRebar } from '@Server/index.js';
import { Character, MarkerType } from '@Shared/types/index.js';
import { PageNames } from '@Shared/webview/index.js';
import { BlipColor } from '@Shared/types/blip.js';
import {
    PlaneFirstExamQuestions,
    HeliFirstExamQuestions,
    firstExamPos,
    secondExamVehiclePos,
    secondExamPos,
    thirdExamVehiclePos,
    thirdExamPos,
    airportconfig
} from '../shared/config.js';
import { Faction } from '@Plugins/faction/server/index.js';

const Rebar = useRebar();
const db = Rebar.database.useDatabase();
const promptbarapi = await Rebar.useApi().getAsync('promptbar-api');
const notifyapi = await Rebar.useApi().getAsync('notify-api')
const currencyapi = await Rebar.useApi().getAsync('currency-api')

const plane = new alt.Vehicle('buzzard2', new alt.Vector3(13284.5146, 10469.9209, 90.2087), new alt.Vector3(0, 0, 1.568842887878418))
plane.frozen = true
plane.streamingDistance = 1000
plane.setStreamSyncedMeta('frozenvehicle', true)

const airportinteraciton = Rebar.controllers.useInteraction(
    new alt.ColshapeCylinder(-1036.9846, -2770.8132, 20.3605, 0.8, 3),
    'player',
);

airportinteraciton.onEnter((player) => {
    promptbarapi.showPromptBar(player, '购买机票')
});

airportinteraciton.onLeave((player) => {
    promptbarapi.hidePromptBar(player)
});

airportinteraciton.on(async (player) => {
    Rebar.player.useWebview(player).show('airport', 'page', true)

    let ready = await Rebar.player.useWebview(player).isReady('airport', 'page')
    while (!ready) {
        await alt.Utils.wait(100); // 等待100毫秒
        ready = await Rebar.player.useWebview(player).isReady('airport', 'page')
    }

    Rebar.player.useWebview(player).emit('airport:sync', airportconfig)

});

Rebar.controllers.useMarkerGlobal({
    pos: new alt.Vector3(-1036.9846, -2770.8132, 20.3605),
    color: new alt.RGBA(255, 215, 0, 150),
    scale: new alt.Vector3(1, 1, 0.2),
    type: MarkerType.CYLINDER,
});

alt.onClient('takeplanetopoint', async (player: alt.Player, pos: { x: number, y: number, z: number }, price: number) => {

    const success = await currencyapi.cost({ player: player }, price)
    if (!success) {
        notifyapi.shownotify(player, '余额不足，无法购买机票', 'error');
        return;
    }

    Rebar.player.useWebview(player).hide('airport')
    Rebar.player.useWorld(player).setScreenFade(0);
    player.pos = new alt.Vector3(pos.x, pos.y, pos.z)
    alt.setTimeout(() => {
        Rebar.player.useWorld(player).clearScreenFade(1000);
    }, 500);

})

alt.onClient('airport:closePage', async (player: alt.Player) => {
    Rebar.player.useWebview(player).hide('airport')
})

alt.on('rebar:playerPageOpened', (player: alt.Player, page: string) => {
    if (page === 'airport') {
        Rebar.player.useWorld(player).disableControls();
    }
});

alt.on('rebar:playerPageClosed', (player: alt.Player, page: string) => {
    if (page === 'airport') {
        Rebar.player.useWorld(player).enableControls();
    }
});

const backinteraciton = Rebar.controllers.useInteraction(
    new alt.ColshapeCylinder(13284.2246, 10472.0967, 89.3099, 0.8, 3),
    'player',
);

backinteraciton.onEnter((player) => {
    promptbarapi.showPromptBar(player, '回城')
});

backinteraciton.onLeave((player) => {
    promptbarapi.hidePromptBar(player)
});

backinteraciton.on(async (player) => {
    Rebar.player.useWorld(player).setScreenFade(0);
    player.pos = new alt.Vector3(-1100.2285, -2847.9165, 20.3605)
    alt.setTimeout(() => {
        Rebar.player.useWorld(player).clearScreenFade(1000);
    }, 500);

});

Rebar.controllers.useMarkerGlobal({
    pos: new alt.Vector3(13284.2246, 10472.0967, 89.3099),
    color: new alt.RGBA(255, 215, 0, 150),
    scale: new alt.Vector3(1, 1, 0.2),
    type: MarkerType.CYLINDER,
});

Rebar.messenger.useMessenger().commands.register({
    name: 'customcutscene',
    desc: '自定义剪辑',
    options: { permissions: ['admin'] },
    callback: async (player: alt.Player, name: string) => {
        player.emit('customcutscene', name)

    }
})
























// 声明飞行驾照类型
declare module '@Shared/types/character.js' {
    export interface Character {
        flightLicence?: FlightLicence[];
    }
}

interface FlightLicence {
    name: FlightLicenceName;
    first: boolean;
    second: boolean;
    third: boolean;
}

type FlightLicenceName = 'Plane' | 'Helicopter';
type Step = 1 | 2;

/**
 * 在玩家创建角色后给予飞行驾照属性
 */
async function giveCharacterDefaultData(player: alt.Player) {
    const doc = Rebar.document.character.useCharacter(player);
    if (!doc) {
        return;
    }

    // 设置两种初始飞行驾照
    const defaultLicences: FlightLicence[] = [
        {
            name: 'Plane',
            first: false,
            second: false,
            third: false
        },
        {
            name: 'Helicopter',
            first: false,
            second: false,
            third: false
        }
    ];

    doc.set('flightLicence', defaultLicences);
}

// 玩家创建角色时触发
const charSelectApi = await Rebar.useApi().getAsync('character-creator-api');
charSelectApi.onCreate(giveCharacterDefaultData);


alt.on('rebar:playerCharacterBound', async (player: alt.Player, document: Character) => {
    if (!('flightLicence' in document)) {
        giveCharacterDefaultData(player);
        return;
    }
})


// 创建飞行驾校地图标记
const blip = Rebar.controllers.useBlipGlobal({
    color: BlipColor.BLUE,
    pos: new alt.Vector3(-1062.7649, -2740.6682, 20.3605),
    shortRange: true,
    sprite: 423, // 飞机图标
    text: '飞行驾校',
});

// 飞行驾校交互点
const flyschoolinteraciton = Rebar.controllers.useInteraction(
    new alt.ColshapeCylinder(-1062.7649, -2740.6682, 20.3605, 2.7, 3),
    'player',
);

flyschoolinteraciton.onEnter((player) => {
    promptbarapi.showPromptBar(player, '打开飞行驾校界面');
});

flyschoolinteraciton.onLeave((player) => {
    promptbarapi.hidePromptBar(player);
});

flyschoolinteraciton.on(async (player) => {
    Rebar.player.useWebview(player).show('flightschoolexam', 'page', true);

    const flightLicences = Rebar.document.character.useCharacter(player).getField('flightLicence');

    let ready = await Rebar.player.useWebview(player).isReady('flightschoolexam', 'page');
    while (!ready) {
        await alt.Utils.wait(100);
        ready = await Rebar.player.useWebview(player).isReady('flightschoolexam', 'page');
    }

    Rebar.player.useWebview(player).emit('flightschoolexam:sync', flightLicences);
    promptbarapi.hidePromptBar(player);
});

Rebar.controllers.useMarkerGlobal({
    pos: new alt.Vector3(-1062.7649, -2740.6682, 20.3605),
    color: new alt.RGBA(255, 215, 0, 150),
    scale: new alt.Vector3(3, 3, 0.2),
    type: MarkerType.CYLINDER,
});

// 页面控制
alt.on('rebar:playerPageOpened', (player: alt.Player, page: PageNames) => {
    if (page == 'flightschoolexam' || page == 'FlightWrittenExam') {
        Rebar.player.useWorld(player).disableControls();
    }
});

alt.on('rebar:playerPageClosed', (player: alt.Player, page: PageNames) => {
    if (page == 'flightschoolexam' || page == 'FlightWrittenExam') {
        Rebar.player.useWorld(player).enableControls();
    }
});

// 报名考试
alt.onClient('flightschoolexam:SignUp', async (player: alt.Player, licenceName: FlightLicenceName, step: Step) => {
    const currencyapi = await Rebar.useApi().getAsync('currency-api');

    // 飞行驾照价格更高
    let price = 0;
    switch (licenceName) {
        case 'Plane':
            price = 5000;
            break;
        case 'Helicopter':
            price = 3000;
            break;
    }

    // 检查玩家是否有足够的钱
    const success = await currencyapi.cost({ player: player }, price, '飞行驾校考试报名费');
    if (!success) {
        notifyapi.shownotify(player, '你没有足够的钱支付考试费用', 'error');
        return;
    }

    const trafficfaction = await db.getMany<Faction>({ name: '交管局' }, 'faction');
    const govermentfaction = await db.getMany<Faction>({ name: '市政厅' }, 'faction');

    if (trafficfaction.length == 0) {
        notifyapi.shownotify(player, '没有交管局，无法报名考试', 'error');
        return;
    }

    if (govermentfaction.length == 0) {
        notifyapi.shownotify(player, '没有市政厅，无法报名考试', 'error');
        return;
    }

    // 分配报名费
    const oldFactionfunds = trafficfaction[0].funds;
    const oldGovermentfunds = govermentfaction[0].funds;

    trafficfaction[0].funds = oldFactionfunds + Math.floor(price / 2);
    govermentfaction[0].funds = oldGovermentfunds + Math.floor(price / 2);

    const fundsHistory = {
        date: Date.now(),
        amount: Math.floor(price / 2),
        reason: `${player.getStreamSyncedMeta('name')} 报名${licenceName === 'Plane' ? '飞机' : '直升机'}科目${step}考试`,
        type: 'deposit' as 'withdraw' | 'deposit'
    };

    let oldfundsHistory = trafficfaction[0].fundsHistory;
    if (!oldfundsHistory) {
        oldfundsHistory = [];
    }
    oldfundsHistory.push(fundsHistory);

    let oldgovermentfundsHistory = govermentfaction[0].fundsHistory;
    if (!oldgovermentfundsHistory) {
        oldgovermentfundsHistory = [];
    }
    oldgovermentfundsHistory.push(fundsHistory);

    await db.update(trafficfaction[0], 'faction');
    await db.update(govermentfaction[0], 'faction');

    // 处理不同科目的报名
    if (step == 1) {
        player.setMeta('flightschool:firstexam', licenceName);
        notifyapi.shownotify(player, `前往教室进行科目一理论考试`, 'success');
    }

    if (step == 2) {
        player.setMeta('flightschool:secondexam', licenceName);

        let vehicle: alt.Vehicle;
        try {
            if (licenceName == 'Plane') {
                vehicle = new alt.Vehicle('vestra', secondExamVehiclePos[0].pos, secondExamVehiclePos[0].rot);
            } else {
                vehicle = new alt.Vehicle('buzzard2', secondExamVehiclePos[0].pos, secondExamVehiclePos[0].rot);
            }

            if (!vehicle || !vehicle.valid) {
                notifyapi.shownotify(player, '创建考试飞机失败，请重试', 'error');
                return;
            }

            player.dimension = player.id + 1;

            // 将玩家生成在飞机旁边
            const spawnPos = new alt.Vector3(
                secondExamVehiclePos[0].pos.x + 5,
                secondExamVehiclePos[0].pos.y,
                secondExamVehiclePos[0].pos.z
            );
            player.pos = spawnPos;

            vehicle.dimension = player.dimension;
            vehicle.setStreamSyncedMeta('flightschool:secondexam:owner', player.id);
            player.setStreamSyncedMeta('flightschool:secondexam:vehicle', vehicle.id);
        } catch (error) {
            console.error('创建科目二考试飞机时出错:', error);
            notifyapi.shownotify(player, '创建考试飞机失败，请重试', 'error');
            return;
        }

        notifyapi.shownotify(player, `前往考试飞机开始${licenceName === 'Plane' ? '飞机' : '直升机'}科目二考试`, 'success');
    }

    notifyapi.shownotify(player, `你已成功报名${licenceName === 'Plane' ? '飞机' : '直升机'}科目${step}考试`, 'success');
});

// 玩家离开飞机时的处理
alt.on('playerLeftVehicle', (player: alt.Player, vehicle: alt.Vehicle) => {
    if (!vehicle || !vehicle.valid) return;

    // 处理科目二考试
    if (player.hasMeta('flightschool:secondexam') && player.hasStreamSyncedMeta('flightschool:secondexam:vehicle')) {
        if (vehicle.hasStreamSyncedMeta('flightschool:secondexam:owner') &&
            vehicle.getStreamSyncedMeta('flightschool:secondexam:owner') == player.id &&
            player.getStreamSyncedMeta('flightschool:secondexam:vehicle') == vehicle.id) {

            const examType = player.getMeta('flightschool:secondexam') as FlightLicenceName;
            player.deleteMeta('flightschool:secondexam');
            player.deleteStreamSyncedMeta('flightschool:secondexam:vehicle');
            player.dimension = 0;
            player.pos = new alt.Vector3(-1062.7649, -2740.6682, 20.3605);

            alt.setTimeout(() => {
                if (vehicle && vehicle.valid) {
                    try {
                        vehicle.destroy();
                    } catch (error) {
                        console.error('销毁科目二考试飞机时出错:', error);
                    }
                }
            }, 100);

            notifyapi.shownotify(player, `你已放弃${examType === 'Plane' ? '飞机' : '直升机'}科目二考试`, 'error');
        }
    }
});

// 玩家进入飞机时的处理
alt.on('playerEnteringVehicle', (player: alt.Player, vehicle: alt.Vehicle, seat: number) => {
    // 处理科目二考试
    if (player.hasMeta('flightschool:secondexam')) {
        const examType = player.getMeta('flightschool:secondexam') as FlightLicenceName;

        if (vehicle && vehicle.valid &&
            vehicle.hasStreamSyncedMeta('flightschool:secondexam:owner') &&
            vehicle.getStreamSyncedMeta('flightschool:secondexam:owner') == player.id &&
            player.getStreamSyncedMeta('flightschool:secondexam:vehicle') == vehicle.id) {

            notifyapi.shownotify(player, `你已进入${examType === 'Plane' ? '飞机' : '直升机'}科目二考试飞机，开始起降练习`, 'success');

            // 创建科目二考试点
            alt.setTimeout(() => {
                let currentPosIndex = 0;
                let currentMarker = null;
                let currentblip = null;

                function createNextInteraction() {
                    if (currentPosIndex >= secondExamPos.length) return;

                    const pos = secondExamPos[currentPosIndex];

                    const interaction = Rebar.controllers.useInteractionLocal(player, `flightschool:secondexam${player.id}`, 'Cylinder', [
                        pos.x,
                        pos.y,
                        pos.z - 10, // 飞行高度
                        20,
                        50,
                    ]);

                    // 创建空中标记
                    currentMarker = Rebar.controllers.useMarkerLocal(player, {
                        pos: new alt.Vector3(pos.x, pos.y, pos.z),
                        color: new alt.RGBA(255, 215, 0, 150),
                        dimension: player.dimension,
                        scale: new alt.Vector3(20, 20, 20),
                        type: MarkerType.VERTICLE_CIRCLE,
                        faceCamera: true,
                    });

                    currentblip = Rebar.controllers.useBlipLocal(player, {
                        pos: pos,
                        color: BlipColor.BLUE,
                        sprite: 1,
                        shortRange: true,
                        dimension: player.dimension,
                        text: `考试点${currentPosIndex + 1}`,
                    });

                    player.emit('setwaypoint', pos);

                    interaction.onEnter((player: alt.Player, destroy) => {
                        const examVehicle = player.vehicle;

                        if (examVehicle && examVehicle.valid && player.getStreamSyncedMeta(`flightschool:secondexam:vehicle`) == examVehicle.id) {
                            if (currentMarker) {
                                currentMarker.destroy();
                            }

                            if (currentblip) {
                                currentblip.destroy();
                            }

                            interaction.destroy();

                            currentPosIndex++;

                            if (currentPosIndex >= secondExamPos.length) {
                                const score = examVehicle && examVehicle.valid ? examVehicle.getStreamSyncedMeta('health') as number : 0;
                                const examType = player.getMeta('flightschool:secondexam') as FlightLicenceName;

                                player.deleteStreamSyncedMeta('flightschool:secondexam:vehicle');
                                player.deleteMeta('flightschool:secondexam');
                                player.dimension = 0;
                                player.pos = new alt.Vector3(-1062.7649, -2740.6682, 20.3605);

                                alt.setTimeout(() => {
                                    if (examVehicle && examVehicle.valid) {
                                        try {
                                            examVehicle.destroy();
                                        } catch (error) {
                                            console.error('销毁科目二考试飞机时出错:', error);
                                        }
                                    }
                                }, 100);

                                if (score >= 95) {
                                    notifyapi.shownotify(player, '恭喜你通过科目二考试!', 'success');

                                    const character = Rebar.document.character.useCharacter(player);
                                    const flightLicences = character.getField('flightLicence');
                                    const licenceIndex = flightLicences.findIndex(licence =>
                                        licence.name === examType
                                    );
                                    if (licenceIndex !== -1) {
                                        flightLicences[licenceIndex].second = true;
                                        character.set('flightLicence', flightLicences);
                                        
                                        // 检查是否获得完整驾照
                                        if (flightLicences[licenceIndex].first && flightLicences[licenceIndex].second) {
                                            notifyapi.shownotify(player, `恭喜你获得${examType === 'Plane' ? '飞机' : '直升机'}驾照！`, 'success');
                                        }
                                    }
                                } else {
                                    notifyapi.shownotify(player, '你未通过科目二考试，请多加练习', 'error');
                                }

                                return;
                            }

                            createNextInteraction();
                        }
                    });
                }

                createNextInteraction();
            }, 500);
        }
    }
});

// 科目一考试地点
for (let i = 0; i < firstExamPos.length; i++) {
    const pos = firstExamPos[i];
    const interaction = Rebar.controllers.useInteraction(new alt.ColshapeCylinder(pos.x, pos.y, pos.z, 0.8, 2), 'player');

    interaction.onEnter((player) => {
        if (player.hasMeta('flightschool:firstexam')) {
            promptbarapi.showPromptBar(player, '开始理论考试');
        }
    });

    interaction.onLeave((player) => {
        if (player.hasMeta('flightschool:firstexam')) {
            promptbarapi.hidePromptBar(player);
        }
    });

    interaction.on(async (player) => {
        if (player.hasMeta('flightschool:firstexam')) {
            const type = player.getMeta('flightschool:firstexam') as FlightLicenceName;

            let questions;
            switch (type) {
                case 'Plane':
                    questions = PlaneFirstExamQuestions;
                    break;
                case 'Helicopter':
                    questions = HeliFirstExamQuestions;
                    break;
            }

            Rebar.player.useWebview(player).show('FlightWrittenExam', 'page', true);

            let ready = await Rebar.player.useWebview(player).isReady('FlightWrittenExam', 'page');
            while (!ready) {
                await alt.Utils.wait(100);
                ready = await Rebar.player.useWebview(player).isReady('FlightWrittenExam', 'page');
            }

            Rebar.player.useWebview(player).emit('FlightWrittenExam:sync', questions, type);
            promptbarapi.hidePromptBar(player);
        }
    });
}

// 处理科目一考试结果
alt.onClient('FlightWrittenExam:submit', (player: alt.Player, pass: boolean) => {
    if (pass) {
        const character = Rebar.document.character.useCharacter(player);
        const flightLicences = character.getField('flightLicence');
        const licenceIndex = flightLicences.findIndex(licence =>
            licence.name === player.getMeta('flightschool:firstexam')
        );
        if (licenceIndex !== -1) {
            flightLicences[licenceIndex].first = true;
            character.set('flightLicence', flightLicences);
        }

        const type = player.getMeta('flightschool:firstexam') as FlightLicenceName;
        notifyapi.shownotify(player, `${type === 'Plane' ? '飞机' : '直升机'}科目一考试通过`, 'success');
    } else {
        const type = player.getMeta('flightschool:firstexam') as FlightLicenceName;
        notifyapi.shownotify(player, `${type === 'Plane' ? '飞机' : '直升机'}科目一考试未通过`, 'error');
    }

    player.deleteMeta('flightschool:firstexam');
});

alt.onClient('FlightWrittenExam:hide', (player: alt.Player) => {
    Rebar.player.useWebview(player).hide('FlightWrittenExam');
});

// 玩家断开连接时清理考试飞机
alt.on('real:playerDisconnect', (player: alt.Player) => {
    // 清理科目二考试飞机
    if (player.hasStreamSyncedMeta('flightschool:secondexam:vehicle')) {
        const vehicleId = player.getStreamSyncedMeta('flightschool:secondexam:vehicle');
        const vehicle = alt.Vehicle.all.find(v => v.id === vehicleId);
        if (vehicle && vehicle.valid) {
            try {
                vehicle.destroy();
            } catch (error) {
                console.error('清理断开连接玩家的科目二考试飞机时出错:', error);
            }
        }
    }
});