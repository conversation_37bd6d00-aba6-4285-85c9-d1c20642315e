import { useRebarClient } from '@Client/index.js';
import * as native from 'natives';
import * as alt from 'alt-client';
import { getDirectionFromRotation } from '@Client/utility/math/index.js';
import { drawText3D } from '@Client/screen/textlabel.js';
import './perviewobject.js'
import './adjustsit.js'
import { GlobalTimerManagerClient } from '@Plugins/animmenu/client/interval.js';


const Rebar = useRebarClient();
const webview = Rebar.webview.useWebview();
const messenger = Rebar.messenger.useMessenger();


// 摩天轮圆心坐标
const wheelCenter = new alt.Vector3(-1663.970703125, -1126.7391357421875, 30.7066707611084);

// 八个吊舱的初始位置相对于摩天轮圆心的偏移量
const ferrisCarsOffsets = [
    { radius: 15.3, angle: 0 },         // 正下方
    { radius: 15.3, angle: 45 },        // 右下
    { radius: 15.3, angle: 90 },        // 正右
    { radius: 15.3, angle: 135 },       // 右上
    { radius: 15.3, angle: 180 },       // 正上方
    { radius: 15.3, angle: 225 },       // 左上
    { radius: 15.3, angle: 270 },       // 正左
    { radius: 15.3, angle: 315 }        // 左下
];

let intervalId = null;

let rotx = 0;

let allferriscars = []
alt.onServer('ferriswheel:rotationUpdate', (data: { degreerotx: number, wheel: alt.Object, allferriscar: alt.Object[] }) => {
    // 直接接收角度，无需转换
    rotx = data.degreerotx;

    // 每秒旋转 360° / 150 = 2.4°（2.5分钟转一圈）
    const rotationPerSecond = 360 / 150;

    if (intervalId) return;

    allferriscars = data.allferriscar

    GlobalTimerManagerClient.getInstance().addTask(() => {

        rotx += rotationPerSecond / 100; // 10ms 更新一次，1秒内更新100次
        if (rotx >= 360) rotx -= 360;

        // 设置主轮旋转
        native.setEntityRotation(
            data.wheel.scriptID,
            rotx,   // 绕 X 轴旋转
            0.0,    // 假设你不旋转 Y/Z
            0.0,
            2,
            true
        );

        // 计算并更新每个吊舱的位置
        updateFerrisCarPositions(rotx);

    }, 10);
});
// 更新摩天轮吊舱的位置
function updateFerrisCarPositions(rotationDegrees) {
    if (allferriscars.length === 0 || allferriscars.length !== ferrisCarsOffsets.length) return;

    // 将角度转换为弧度
    const rotationRadians = rotationDegrees * Math.PI / 180;

    // 使用for...let循环遍历每个吊舱
    for (let index = 0; index < allferriscars.length; index++) {
        const car = allferriscars[index];
        if (!car || !car.valid) continue;

        // 获取该吊舱的偏移配置
        const carOffset = ferrisCarsOffsets[index];

        // 计算当前角度 (原始角度 - 当前旋转角度) 反转旋转方向使吊舱与主轮同方向旋转
        const currentAngle = (carOffset.angle * Math.PI / 180) - rotationRadians;

        
        // 计算新位置 (使用三角函数)
        // X轴沿着南北方向，Y轴沿着东西方向
        const newX = wheelCenter.x;
        const newY = wheelCenter.y + carOffset.radius * Math.sin(currentAngle);
        const newZ = wheelCenter.z + carOffset.radius * Math.cos(currentAngle);

        // 设置吊舱新位置
        native.setEntityCoords(
            car.scriptID,
            newX,
            newY,
            newZ,
            false, // 不碰撞检测
            false,
            false,
            false
        );

        // 保持吊舱竖直 (始终朝下)
        native.setEntityRotation(car.scriptID, 0.0, 0.0, 0.0, 2, true);
    }
}





alt.onServer('ferriswheel:stop', () => {
    if (intervalId) {
        alt.clearInterval(intervalId);
        intervalId = null;
        rotx = 0;
        allferriscars = []
    }
});




alt.on('keyup', (key) => {
    if (key === 18) { // Alt键的键码是18
        if (alt.isConsoleOpen()) {
            return
        }
        if (messenger.isChatFocused()) {
            return
        }
        if (webview.isAnyPageOpen()) {
            return
        }
        alt.emitServerRaw('ferriswheel:leave');
    }
});
