<script lang="ts" setup>
import { ref, onMounted, onUnmounted } from 'vue';
import * as Tone from 'tone';
import { useEvents } from '../../../../webview/composables/useEvents.js';

const events = useEvents();

// 电梯状态
const floors = ref<{name: string, pos: any}[]>([]);
const currentFloor = ref<string>('');
const elevatorId = ref<string>('');
const isLoading = ref(false);
const selectedFloor = ref<number | null>(null);
const isMoving = ref(false);

// 音效系统
const elevatorSounds = {
  // 按钮点击音效
  buttonClick: new Tone.Synth({
    oscillator: { type: 'sine' },
    envelope: { attack: 0.01, decay: 0.1, sustain: 0.05, release: 0.3 },
    volume: -18
  }).toDestination(),
  
  // 楼层选择音效
  floorSelect: new Tone.PolySynth(Tone.Synth, {
    oscillator: { type: 'triangle' },
    envelope: { attack: 0.02, decay: 0.2, sustain: 0.1, release: 0.5 },
    volume: -20
  }).toDestination(),
  
  // 电梯运行音效
  elevatorMoving: new Tone.Synth({
    oscillator: { type: 'sawtooth' },
    envelope: { attack: 0, decay: 0, sustain: 0, release: 0 },
    volume: -25
  }).toDestination(),
  
  // 到达音效
  arrival: new Tone.PolySynth(Tone.Synth, {
    oscillator: { type: 'sine' },
    envelope: { attack: 0.01, decay: 0.1, sustain: 0.1, release: 0.8 },
    volume: -15
  }).toDestination(),
  
  // 悬停音效
  hover: new Tone.Synth({
    oscillator: { type: 'sine' },
    envelope: { attack: 0.001, decay: 0.05, sustain: 0, release: 0.1 },
    volume: -28
  }).toDestination()
};

// 播放音效函数
const playButtonClick = () => {
  Tone.start();
  elevatorSounds.buttonClick.triggerAttackRelease('C5', 0.1);
};

const playFloorSelect = (floorIndex: number) => {
  Tone.start();
  const notes = ['C4', 'D4', 'E4', 'F4', 'G4', 'A4', 'B4', 'C5'];
  const note = notes[floorIndex % notes.length];
  elevatorSounds.floorSelect.triggerAttackRelease([note, `${note.slice(0, -1)}${parseInt(note.slice(-1)) + 1}`], 0.2);
};

const playElevatorMoving = () => {
  Tone.start();
  elevatorSounds.elevatorMoving.triggerAttackRelease('F2', 2.0);
};

const playArrival = () => {
  Tone.start();
  const now = Tone.now();
  elevatorSounds.arrival.triggerAttackRelease(['C4', 'E4', 'G4'], 0.3, now);
  elevatorSounds.arrival.triggerAttackRelease(['F4', 'A4', 'C5'], 0.4, now + 0.2);
};

const playHover = () => {
  Tone.start();
  elevatorSounds.hover.triggerAttackRelease('G5', 0.05);
};

// 函数：加载FontAwesome
const loadFontAwesome = () => {
  const link = document.createElement('link');
  link.rel = 'stylesheet';
  link.href = 'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css';
  link.integrity = 'sha512-iecdLmaskl7CVkqkXNQ/ZH/XLlvWZOJyj7Yy7tcenmpD1ypASozpmT/E0iPtmFIB46ZmdtAc9eNBvH0H/ZpiBw==';
  link.crossOrigin = 'anonymous';
  link.referrerPolicy = 'no-referrer';
  document.head.appendChild(link);
};

// 处理楼层选择
const selectFloor = async (floorIndex: number, floor: {name: string, pos: any}) => {
  if (isMoving.value || selectedFloor.value === floorIndex) return;
  
  selectedFloor.value = floorIndex;
  playFloorSelect(floorIndex);
  
  // 延迟一下，让音效播放完
  setTimeout(async () => {
    isMoving.value = true;
    playElevatorMoving();
    
    // 模拟电梯移动时间
    setTimeout(() => {
      playArrival();
      
      // 关闭界面
      setTimeout(() => {
        closePage();
        // 发送到服务器进行传送，包含elevatorId
        events.emitServer('Elevator:goToFloor', floor.pos, elevatorId.value);
      }, 500);
    }, 2000);
  }, 200);
};

// 关闭页面
const closePage = () => {
  playButtonClick();
  // 页面会自动关闭，这里不需要特殊处理
  events.emitServer('Elevator:closePage');
};

// 监听服务器事件
events.on('Elevator:inventoryUpdate', (data: {floors: {name: string, pos: any}[], currentFloor: string, systemId?: string, elevatorId?: string}) => {
  floors.value = data.floors;
  currentFloor.value = data.currentFloor;
  elevatorId.value = data.elevatorId || '';
  isLoading.value = false;
});

onMounted(async () => {
  loadFontAwesome();
  isLoading.value = true;
  await Tone.start();
  
  // 播放开启音效
  setTimeout(() => {
    playButtonClick();
  }, 300);
});

onUnmounted(() => {
  // 清理音效资源
  Object.values(elevatorSounds).forEach(sound => {
    if (sound.dispose) {
      sound.dispose();
    }
  });
});
</script>

<template>
  <div class="elevator-container">
    <div class="elevator-background">
      <!-- 电梯面板 -->
      <div class="elevator-panel">
        <!-- 标题 -->
        <div class="elevator-header">
          <div class="elevator-title">
            <i class="fas fa-elevator"></i>
            <h2>电梯控制面板</h2>
            <div class="current-floor-badge" v-if="currentFloor">
              <span>当前: {{ currentFloor }}</span>
            </div>
          </div>
          <div class="elevator-status" :class="{ 'moving': isMoving }">
            <i :class="isMoving ? 'fas fa-spinner fa-spin' : 'fas fa-circle'"></i>
            <span>{{ isMoving ? '运行中...' : '待机' }}</span>
          </div>
        </div>

        <!-- 楼层列表 -->
        <div class="floors-container">
          <div v-if="isLoading" class="loading-state">
            <i class="fas fa-spinner fa-spin"></i>
            <span>加载中...</span>
          </div>
          
          <div v-else class="floors-list">
            <div 
              v-for="(floor, index) in floors" 
              :key="index"
              class="floor-button"
              :class="{ 
                'selected': selectedFloor === index,
                'disabled': isMoving 
              }"
              @click="selectFloor(index, floor)"
              @mouseenter="playHover"
            >
              <div class="floor-number">
                {{ index + 1 }}
              </div>
              <div class="floor-info">
                <div class="floor-name">{{ floor.name }}</div>
                <div class="floor-coordinates">
                  {{ Math.round(floor.pos.x) }}, {{ Math.round(floor.pos.y) }}, {{ Math.round(floor.pos.z) }}
                </div>
              </div>
              <div class="floor-arrow">
                <i class="fas fa-chevron-right"></i>
              </div>
            </div>
          </div>
        </div>

        <!-- 底部按钮 -->
        <div class="elevator-footer">
          <button 
            class="close-button"
            @click="closePage"
            @mouseenter="playHover"
            :disabled="isMoving"
          >
            <i class="fas fa-times"></i>
            <span>关闭</span>
          </button>
        </div>
      </div>

      <!-- 移动动画覆盖层 -->
      <div v-if="isMoving" class="moving-overlay">
        <div class="moving-content">
          <div class="elevator-animation">
            <i class="fas fa-elevator"></i>
          </div>
          <h3>电梯运行中</h3>
          <p>正在前往 {{ selectedFloor !== null ? floors[selectedFloor]?.name : '' }}</p>
          <div class="progress-bar">
            <div class="progress-fill"></div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
/* CSS重置和基础样式 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

.elevator-container {
  --primary-color: #64ffda;
  --primary-dark: #1de9b6;
  --card-bg: rgba(15, 23, 42, 0.95);
  --card-bg-light: rgba(30, 41, 59, 0.8);
  --text-primary: #ffffff;
  --text-secondary: rgba(255, 255, 255, 0.7);
  --text-muted: rgba(255, 255, 255, 0.5);
  --border-color: rgba(100, 255, 218, 0.2);
  --border-hover: rgba(100, 255, 218, 0.3);
  --shadow-sm: 0 0.2vh 0.8vh rgba(0, 0, 0, 0.2);
  --shadow-md: 0 0.4vh 1.6vh rgba(0, 0, 0, 0.3);
  --shadow-lg: 0 0.8vh 3.2vh rgba(0, 0, 0, 0.4);
  --shadow-primary: 0 0.4vh 2vh rgba(100, 255, 218, 0.3);

  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  font-family: "Inter", "PingFang SC", "Microsoft YaHei", sans-serif;
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 1000;
}

.elevator-background {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
 
}

.elevator-panel {
  position: relative;
  background: var(--card-bg);
  border-radius: 1.2vh;
  box-shadow: var(--shadow-lg), 0 0 2vh rgba(100, 255, 218, 0.1);
  border: 0.1vh solid var(--border-color);
  overflow: hidden;
  width: 28vw;
  min-width: 400px;
  max-width: 500px;
  max-height: 80vh;
  animation: panelSlideIn 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes panelSlideIn {
  from {
    opacity: 0;
    transform: translateY(3vh) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.elevator-header {
  padding: 1.5vh 2vh 1.2vh;
  background: linear-gradient(135deg, rgba(100, 255, 218, 0.05) 0%, transparent 60%);
  border-bottom: 0.1vh solid var(--border-color);
}

.elevator-title {
  display: flex;
  align-items: center;
  gap: 1.2vh;
  color: var(--text-primary);
  margin-bottom: 1.5vh;
  flex-wrap: wrap;
}

.elevator-title i {
  font-size: 2.4vh;
  color: var(--primary-color);
}

.elevator-title h2 {
  font-size: 2vh;
  font-weight: 600;
  margin: 0;
}

.current-floor-badge {
  background: rgba(100, 255, 218, 0.1);
  border: 0.1vh solid var(--border-color);
  border-radius: 1.5vh;
  padding: 0.4vh 1.2vh;
  margin-left: auto;
  box-shadow: var(--shadow-sm);
}

.current-floor-badge span {
  color: var(--primary-color);
  font-size: 1.2vh;
  font-weight: 600;
}

.elevator-status {
  display: flex;
  align-items: center;
  gap: 0.8vh;
  color: var(--text-secondary);
  font-size: 1.4vh;
  font-weight: 500;
}

.elevator-status.moving {
  color: var(--primary-color);
}

.elevator-status i {
  font-size: 1.2vh;
}

.floors-container {
  padding: 1.5vh 2vh;
  max-height: 50vh;
  overflow-y: auto;
}

.loading-state {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1vh;
  color: var(--text-secondary);
  font-size: 1.6vh;
  padding: 4vh 0;
}

.loading-state i {
  font-size: 2vh;
  color: var(--primary-color);
}

.floors-list {
  display: flex;
  flex-direction: column;
  gap: 1.2vh;
}

.floor-button {
  display: flex;
  align-items: center;
  gap: 1.5vh;
  padding: 1.5vh 2vh;
  background: var(--card-bg-light);
  border: 0.1vh solid var(--border-color);
  border-radius: 1.2vh;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  box-shadow: var(--shadow-sm);
}

.floor-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(100, 255, 218, 0.1), transparent);
  transition: left 0.5s ease;
}

.floor-button:hover::before {
  left: 100%;
}

.floor-button:hover {
  background: rgba(255, 255, 255, 0.06);
  border-color: var(--border-hover);
  transform: translateY(-0.2vh);
  box-shadow: var(--shadow-primary);
}

.floor-button.selected {
  background: rgba(100, 255, 218, 0.15);
  border-color: var(--primary-color);
  box-shadow: var(--shadow-primary);
}

.floor-button.disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none !important;
}

.floor-number {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 4vh;
  height: 4vh;
  background: linear-gradient(135deg, rgba(100, 255, 218, 0.15) 0%, rgba(100, 255, 218, 0.05) 100%);
  border: 0.1vh solid var(--border-color);
  border-radius: 50%;
  color: var(--primary-color);
  font-size: 1.6vh;
  font-weight: 600;
  flex-shrink: 0;
  box-shadow: var(--shadow-sm);
}

.floor-info {
  flex: 1;
  color: var(--text-primary);
}

.floor-name {
  font-size: 1.6vh;
  font-weight: 600;
  margin-bottom: 0.4vh;
}

.floor-coordinates {
  font-size: 1.2vh;
  color: var(--text-muted);
  font-family: 'Courier New', monospace;
}

.floor-arrow {
  color: var(--text-muted);
  transition: all 0.3s ease;
  flex-shrink: 0;
}

.floor-button:hover .floor-arrow {
  color: var(--primary-color);
  transform: translateX(0.5vh);
}

.elevator-footer {
  padding: 1.5vh 2vh 1.8vh;
  border-top: 0.1vh solid var(--border-color);
  background: rgba(20, 20, 35, 0.5);
}

.close-button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.8vh;
  width: 100%;
  padding: 1.2vh 2vh;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.05) 0%, rgba(255, 255, 255, 0.02) 100%);
  border: 0.1vh solid rgba(255, 255, 255, 0.1);
  border-radius: 1vh;
  color: var(--text-secondary);
  font-size: 1.4vh;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.close-button:hover {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
  border-color: rgba(255, 255, 255, 0.2);
  transform: translateY(-0.1vh);
  box-shadow: var(--shadow-sm);
}

.close-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none !important;
}

.moving-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.moving-content {
  text-align: center;
  color: var(--text-primary);
  animation: contentSlideIn 0.5s ease;
}

@keyframes contentSlideIn {
  from {
    opacity: 0;
    transform: translateY(2vh);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.elevator-animation {
  font-size: 6vh;
  color: var(--primary-color);
  margin-bottom: 2vh;
  animation: elevatorBounce 1s ease-in-out infinite alternate;
}

@keyframes elevatorBounce {
  from { transform: translateY(-0.5vh); }
  to { transform: translateY(0.5vh); }
}

.moving-content h3 {
  font-size: 2.4vh;
  font-weight: 600;
  margin-bottom: 1vh;
}

.moving-content p {
  font-size: 1.6vh;
  color: var(--text-secondary);
  margin-bottom: 3vh;
}

.progress-bar {
  width: 20vw;
  height: 0.4vh;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 0.2vh;
  overflow: hidden;
  margin: 0 auto;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, var(--primary-color), var(--primary-dark));
  border-radius: 0.2vh;
  animation: progressFill 2s ease-in-out;
}

@keyframes progressFill {
  from { width: 0%; }
  to { width: 100%; }
}

/* 滚动条样式 */
.floors-container::-webkit-scrollbar {
  width: 0.4vh;
}

.floors-container::-webkit-scrollbar-track {
  background: rgba(100, 255, 218, 0.05);
  border-radius: 0.2vh;
}

.floors-container::-webkit-scrollbar-thumb {
  background: rgba(100, 255, 218, 0.3);
  border-radius: 0.2vh;
  transition: all 0.3s ease;
}

.floors-container::-webkit-scrollbar-thumb:hover {
  background: rgba(100, 255, 218, 0.5);
}


</style>
