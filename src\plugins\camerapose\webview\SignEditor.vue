<template>
  <div class="sign-editor-container">
    <!-- 木牌编辑器卡片 -->
    <div class="editor-card">
      <!-- 卡片头部 -->
      <div class="card-header">
        <div class="header-icon">
          <i class="fas fa-edit"></i>
        </div>
        <div class="header-content">
          <h3 class="card-title">文字牌编辑器</h3>
          <span class="card-subtitle">SIGN EDITOR</span>
        </div>
        <button class="close-btn" @click="closeEditor">
          <i class="fas fa-times"></i>
        </button>
      </div>
      
      <!-- 预览区域 -->
      <div class="preview-section">
        <div class="preview-card">
          <div class="preview-header">
            <i class="fas fa-eye"></i>
            <span>预览效果</span>
          </div>
          <div class="preview-sign">
            <div class="sign-frame">
              <div class="sign-title">{{ signData.title || '请输入标题...' }}</div>
              <div class="sign-top">{{ signData.topText || '请输入顶部文字...' }}</div>
              <div class="sign-middle">{{ signData.midText || '请输入中间文字...' }}</div>
              <div class="sign-bottom">{{ signData.bottomText || '请输入底部文字...' }}</div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 输入区域 -->
      <div class="input-section">
        <div class="input-grid">
          <div class="input-group">
            <label class="input-label">
              <i class="fas fa-heading"></i>
              <span>标题</span>
            </label>
            <input 
              v-model="signData.title" 
              type="text" 
              maxlength="30" 
              class="text-input"
              placeholder="输入标题内容..."
              @input="validateInput"
            />
            <div class="char-count">{{ signData.title.length }}/30</div>
          </div>
          
          <div class="input-group">
            <label class="input-label">
              <i class="fas fa-arrow-up"></i>
              <span>顶部文字</span>
            </label>
            <input 
              v-model="signData.topText" 
              type="text" 
              maxlength="30" 
              class="text-input"
              placeholder="输入顶部文字..."
              @input="validateInput"
            />
            <div class="char-count">{{ signData.topText.length }}/30</div>
          </div>
          
          <div class="input-group">
            <label class="input-label">
              <i class="fas fa-minus"></i>
              <span>中间文字</span>
            </label>
            <input 
              v-model="signData.midText" 
              type="text" 
              maxlength="30" 
              class="text-input"
              placeholder="输入中间文字..."
              @input="validateInput"
            />
            <div class="char-count">{{ signData.midText.length }}/30</div>
          </div>
          
          <div class="input-group">
            <label class="input-label">
              <i class="fas fa-arrow-down"></i>
              <span>底部文字</span>
            </label>
            <input 
              v-model="signData.bottomText" 
              type="text" 
              maxlength="30" 
              class="text-input"
              placeholder="输入底部文字..."
              @input="validateInput"
            />
            <div class="char-count">{{ signData.bottomText.length }}/30</div>
          </div>
        </div>
      </div>
      
      <!-- 操作按钮 -->
      <div class="card-actions">
        <button class="action-btn secondary" @click="clearAll">
          <i class="fas fa-eraser"></i>
          <span>清空内容</span>
        </button>
        <button 
          class="action-btn primary" 
          @click="displaySign"
          :disabled="!hasContent"
          :class="{ disabled: !hasContent }"
        >
          <i class="fas fa-sign"></i>
          <span>展示木牌</span>
        </button>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useEvents } from '@Composables/useEvents'

const events = useEvents()

// 签名数据
const signData = ref({
  title: '',
  topText: '',
  midText: '',
  bottomText: ''
})

// 计算属性 - 检查是否有内容
const hasContent = computed(() => {
  return signData.value.title.trim() ||
         signData.value.topText.trim() ||
         signData.value.midText.trim() ||
         signData.value.bottomText.trim()
})

// 输入验证
function validateInput() {
  // 移除危险字符
  Object.keys(signData.value).forEach(key => {
    signData.value[key] = signData.value[key]
      .replace(/<[^>]*>/g, '') // 移除HTML标签
      .replace(/[<>&"']/g, '') // 移除危险字符
  })
}

// 清空所有内容
function clearAll() {
  signData.value = {
    title: '',
    topText: '',
    midText: '',
    bottomText: ''
  }
}

// 展示木牌
function displaySign() {
  if (!hasContent.value) return
  
  // 发送数据到客户端
  events.emitClient('camerapose:displaySign', {
    title: signData.value.title.trim(),
    topText: signData.value.topText.trim(),
    midText: signData.value.midText.trim(),
    bottomText: signData.value.bottomText.trim()
  })
}

// 关闭编辑器
function closeEditor() {
  events.emitClient('camerapose:closeEditor')
}

// 键盘事件处理
function handleKeyPress(event: KeyboardEvent) {
  if (event.key === 'Escape') {
    closeEditor()
  } else if (event.key === 'Enter' && event.ctrlKey && hasContent.value) {
    displaySign()
  }
}

// 生命周期
onMounted(() => {
  document.addEventListener('keydown', handleKeyPress)
  
  // 接收来自服务器的初始数据
  events.on('camerapose:loadSignData', (data: any) => {
    if (data) {
      signData.value = {
        title: data.title || '',
        topText: data.topText || '',
        midText: data.midText || '',
        bottomText: data.bottomText || ''
      }
    }
  })
})

onUnmounted(() => {
  document.removeEventListener('keydown', handleKeyPress)
})
</script>

<style scoped>
/* CSS变量定义 - 在组件内部定义 */
.sign-editor-container {
  /* 主色调 - 青绿色系 */
  --primary-color: #64ffda;        /* 主色 */
  --primary-hover: #ffffff;        /* 主色悬停 */
  --primary-bg: #0f172a;           /* 主背景 */
  --card-bg: rgba(30, 41, 59, 0.9);     /* 卡片背景 */
  --card-border: rgba(100, 255, 218, 0.2); /* 卡片边框 */
  --text-primary: #f8fafc;         /* 主要文字 */
  --text-secondary: #94a3b8;       /* 次要文字 */
  --text-muted: #64748b;           /* 辅助文字 */
  --success: #64ffda;              /* 成功/主要操作 */
  --warning: #a78bfa;              /* 警告/次要操作 */
  --danger: #ef4444;               /* 危险操作 */

  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 75vw;
  max-width: 90vw;
  height: 85vh;
  max-height: 90vh;
  border-radius: 1.5vh;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translate(-50%, -45%); }
  to { opacity: 1; transform: translate(-50%, -50%); }
}

.editor-card {
  width: 95%;
  height: 95%;
  background: var(--card-bg);
  border: 1px solid var(--card-border);
  border-radius: 1.2vh;
  padding: 2vh;
  display: flex;
  flex-direction: column;
  gap: 2vh;
  position: relative;
}

/* 卡片头部 */
.card-header {
  display: flex;
  align-items: center;
  gap: 1.5vh;
  padding-bottom: 1.5vh;
  border-bottom: 1px solid var(--card-border);
}

.header-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 4vh;
  height: 4vh;
  background: var(--primary-color);
  border-radius: 50%;
  color: var(--primary-bg);
  font-size: 1.8vh;
  font-weight: 600;
}

.header-content {
  flex: 1;
}

.card-title {
  color: var(--text-primary);
  font-size: 2.2vh;
  font-weight: 600;
  margin: 0;
  letter-spacing: -0.01em;
}

.card-subtitle {
  font-size: 1.2vh;
  color: var(--text-muted);
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.1em;
  margin-top: 0.3vh;
  display: block;
}

.close-btn {
  width: 3.5vh;
  height: 3.5vh;
  border: none;
  border-radius: 50%;
  background: rgba(239, 68, 68, 0.1);
  color: #ef4444;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.4vh;
}

.close-btn:hover {
  background: rgba(239, 68, 68, 0.2);
  transform: scale(1.1);
}

/* 预览区域 */
.preview-section {
  flex: 0 0 auto;
}

.preview-card {
  background: var(--primary-bg);
  border: 1px solid var(--card-border);
  border-radius: 1vh;
  padding: 1.5vh;
}

.preview-header {
  display: flex;
  align-items: center;
  gap: 0.8vh;
  margin-bottom: 1.2vh;
  color: var(--primary-color);
  font-size: 1.3vh;
  font-weight: 500;
}

.preview-sign {
  display: flex;
  justify-content: center;
}

.sign-frame {
  background: linear-gradient(145deg, #2d3748, #1a202c);
  border: 2px solid var(--primary-color);
  border-radius: 1vh;
  padding: 2vh 3vh;
  min-width: 25vh;
  text-align: center;
}

.sign-title {
  font-size: 1.8vh;
  font-weight: 600;
  color: var(--primary-color);
  margin-bottom: 1vh;
}

.sign-top, .sign-middle, .sign-bottom {
  font-size: 1.4vh;
  color: var(--text-primary);
  margin: 0.8vh 0;
  line-height: 1.4;
  opacity: 0.9;
}

/* 输入区域 */
.input-section {
  flex: 1;
  overflow-y: auto;
}

.input-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 2vh;
}

.input-group {
  display: flex;
  flex-direction: column;
  gap: 0.8vh;
}

.input-label {
  display: flex;
  align-items: center;
  gap: 0.8vh;
  color: var(--text-primary);
  font-size: 1.3vh;
  font-weight: 500;
}

.input-label i {
  color: var(--primary-color);
  width: 1.5vh;
}

.text-input {
  width: 100%;
  padding: 1.2vh;
  background: var(--primary-bg);
  border: 1px solid var(--card-border);
  border-radius: 0.8vh;
  color: var(--text-primary);
  font-size: 1.3vh;
  transition: all 0.2s ease;
  box-sizing: border-box;
}

.text-input:focus {
  outline: none;
  border-color: var(--primary-color);
  background: rgba(15, 23, 42, 0.8);
}

.text-input::placeholder {
  color: var(--text-muted);
}

.char-count {
  font-size: 1vh;
  color: var(--text-muted);
  text-align: right;
  margin-top: 0.3vh;
}

/* 操作按钮 */
.card-actions {
  display: flex;
  gap: 1.5vh;
  padding-top: 1.5vh;
  border-top: 1px solid var(--card-border);
}

.action-btn {
  flex: 1;
  padding: 1.5vh 2vh;
  border-radius: 1vh;
  font-size: 1.4vh;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.8vh;
  border: 1px solid transparent;
  min-height: 4.5vh;
}

.action-btn i {
  font-size: 1.2vh;
}

.action-btn.primary {
  background: var(--primary-color);
  color: var(--primary-bg);
  font-weight: 600;
  border-color: var(--primary-color);
}

.action-btn.primary:hover:not(.disabled) {
  background: var(--primary-hover);
  color: var(--primary-color);
  transform: translateY(-0.1vh);
}

.action-btn.secondary {
  background: var(--card-bg);
  color: var(--text-secondary);
  border-color: rgba(148, 163, 184, 0.2);
}

.action-btn.secondary:hover {
  background: rgba(148, 163, 184, 0.15);
  color: var(--text-primary);
  border-color: rgba(148, 163, 184, 0.4);
  transform: translateY(-0.1vh);
}

.action-btn.disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none !important;
}

/* 滚动条样式 */
.input-section::-webkit-scrollbar {
  width: 0.4vh;
}

.input-section::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 0.2vh;
}

.input-section::-webkit-scrollbar-thumb {
  background: rgba(100, 255, 218, 0.3);
  border-radius: 0.2vh;
}

.input-section::-webkit-scrollbar-thumb:hover {
  background: rgba(100, 255, 218, 0.5);
}


</style>