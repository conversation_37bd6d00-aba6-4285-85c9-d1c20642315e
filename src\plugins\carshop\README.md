# 进口货车订单系统

## 功能概述

进口货车订单系统允许玩家订购特殊的货车，需要收集指定材料才能获得车辆。

## 系统特性

### 每周刷新
- 每周自动刷新可订购的货车模型
- 从 `special_CAR_MODEL` 数组中按顺序循环选择
- 所有玩家看到的都是同一款车

### 订单管理
- 每个玩家同时只能有一个订单
- 提交订单需要支付 $100,000 费用
- 订单信息存储在数据库中
- 支持取消订单，退还 $100,000 费用（不退还已收集的材料）

### 材料需求
- 材料需求随机生成，每个玩家都不同
- 主要材料：钢锭、铜锭、铁锭、铝锭
- 辅助材料：润滑油、塑料、玻璃、催化剂
- 材料总价值约 $500,000

### 车辆属性
- 油箱容量：120L
- 储物容量：1000kg
- 包含钥匙
- 非商业用途
- 存入 Legion Square 车库

## 使用方法

### 玩家操作
1. 前往进口货车订单点 (坐标: 1240.8396, -3322.4438, 5.0271)
2. 按 E 键交互
3. 查看当前可订购的货车
4. 点击"提交订单"支付 $100,000
5. 收集所需材料
6. 返回订单点完成订单
7. 可选择取消订单（退还 $100,000，不退还材料）

### 管理员命令
```
/truckorder - 测试货车订单系统 (需要管理员权限)
```

## 技术实现

### 服务器端
- 位置：`src/plugins/carshop/server/index.ts`
- 主要功能：
  - 每周刷新货车模型
  - 订单创建和管理
  - 材料需求生成
  - 车辆创建
  - 车辆预览控制

### 客户端
- 位置：`src/plugins/carshop/client/index.ts`
- 主要功能：
  - 界面显示控制
  - 事件处理
  - 车辆预览集成

### 界面组件
- 订单界面：`src/plugins/carshop/webview/truck-order.vue`（合并预览和详情功能）

### 车辆预览系统
- 集成现有的车辆预览系统
- 通过 `player.emit('dealershopopen')` 触发预览
- 支持货车专用相机位置和角度
- 自动预览当前可订购的货车模型

## 数据库集合

### truck_offers
存储当前货车报价信息
```typescript
{
  vehicleModel: string;
  weekIndex: number;
  lastUpdate: number;
}
```

### truck_orders
存储玩家订单信息
```typescript
{
  playerId: number;
  vehicleModel: string;
  materials: {
    steelIngot: number;
    copperIngot: number;
    ironIngot: number;
    aluminumIngot: number;
    lubricant: number;
    plastic: number;
    glass: number;
    catalyst: number;
  };
  totalCost: number;
  createdAt: number;
  completed: boolean;
}
```

## 材料价格

| 材料 | 单价 |
|------|------|
| 钢锭 | $384 |
| 铜锭 | $86 |
| 铁锭 | $74 |
| 铝锭 | $106 |
| 润滑油 | $29.5 |
| 塑料 | $113 |
| 玻璃 | $235 |
| 催化剂 | $21.5 |

## 注意事项

1. 系统会在服务器启动时自动初始化
2. 每周刷新基于从2024年1月1日开始计算的周数
3. 材料需求是随机生成的，确保总价值约 $500,000
4. 玩家必须有足够的现金才能提交订单
5. 完成订单时会自动消耗所有材料
6. 车辆创建失败时会返还材料
7. 取消订单会退还 $100,000 费用，但不会退还已收集的材料 