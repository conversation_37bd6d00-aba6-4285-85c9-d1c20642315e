<template>
  <div class="dispatch-container">
    <div class="card">
      <div class="card-header">
        <svg viewBox="0 0 24 24">
          <path d="M12,3V9H7V3H12M12,21H7V11H12V21M5,3A2,2 0 0,0 3,5V19A2,2 0 0,0 5,21H14A2,2 0 0,0 16,19V5A2,2 0 0,0 14,3H5M17,7H22V9H19V11H21.5V13H19V15H22V17H17V7Z" />
        </svg>
        <h2>调度管理</h2>
      </div>




      
      <!-- 紧急热线按钮 -->
      <div class="hotline-controls">
        <div class="hotline-status" :class="{ 
          active: factionData.isanyoneonhotline, 
          'self-active': factionData.ismeonhotline 
        }">
          <svg viewBox="0 0 24 24">
            <path d="M12,8H4A2,2 0 0,0 2,10V14A2,2 0 0,0 4,16H5V20A1,1 0 0,0 6,21H8A1,1 0 0,0 9,20V16H12L17,20V4L12,8M21.5,12C21.5,13.71 20.54,15.26 19,16V8C20.53,8.75 21.5,10.3 21.5,12Z" />
          </svg>
          <span v-if="factionData.ismeonhotline">热线已被你接管</span>
          <span v-else-if="factionData.isanyoneonhotline">热线已被接管</span>
          <span v-else>紧急热线未激活</span>
        </div>
        <button @click="handleEmergencyHotline" class="hotline-btn activate" :disabled="factionData.isanyoneonhotline">
          <svg viewBox="0 0 24 24">
            <path d="M6.62,10.79C8.06,13.62 10.38,15.94 13.21,17.38L15.41,15.18C15.69,14.9 16.08,14.82 16.43,14.93C17.55,15.3 18.75,15.5 20,15.5A1,1 0 0,1 21,16.5V20A1,1 0 0,1 20,21A17,17 0 0,1 3,4A1,1 0 0,1 4,3H7.5A1,1 0 0,1 8.5,4C8.5,5.25 8.7,6.45 9.07,7.57C9.18,7.92 9.1,8.31 8.82,8.59L6.62,10.79Z" />
          </svg>
          <span>接管紧急热线</span>
        </button>
        <button @click="stopEmergencyHotline" class="hotline-btn deactivate" :disabled="!factionData.ismeonhotline">
          <svg viewBox="0 0 24 24">
            <path d="M12,9C10.4,9 8.85,9.25 7.4,9.72V12.82C7.4,13.22 7.17,13.56 6.84,13.72C5.86,14.21 4.97,14.84 4.17,15.57C4,15.75 3.75,15.86 3.5,15.86C3.2,15.86 2.95,15.74 2.77,15.56L0.29,13.08C0.11,12.9 0,12.65 0,12.38C0,12.1 0.11,11.85 0.29,11.67C3.34,8.77 7.46,7 12,7C16.54,7 20.66,8.77 23.71,11.67C23.89,11.85 24,12.1 24,12.38C24,12.65 23.89,12.9 23.71,13.08L21.23,15.56C21.05,15.74 20.8,15.86 20.5,15.86C20.25,15.86 20,15.75 19.82,15.57C19.03,14.84 18.14,14.21 17.16,13.72C16.83,13.56 16.6,13.22 16.6,12.82V9.72C15.15,9.25 13.6,9 12,9Z" />
          </svg>
          <span>停止接管热线</span>
        </button>
      </div>





      
          <!-- 调度列表 -->
          <div class="dispatch-list-container">
        <div class="dispatch-header-info" v-if="sortedDispatch.length > 0">
          <svg viewBox="0 0 24 24">
            <path d="M9,22A1,1 0 0,1 8,21V18H4A2,2 0 0,1 2,16V4C2,2.89 2.9,2 4,2H20A2,2 0 0,1 22,4V16A2,2 0 0,1 20,18H13.9L10.2,21.71C10,21.9 9.75,22 9.5,22V22H9M15,9H5V11H15V9M15,5H5V7H15V5M17,12H19V10H17V12M17,8H19V6H17V8Z" />
          </svg>
          <span>当前共有 <b>{{ sortedDispatch.length }}</b> 个任务</span>
        </div>
        
        <div v-if="sortedDispatch.length > 0" class="dispatch-card-grid">
          <div v-for="dispatch in sortedDispatch" :key="dispatch._id" class="dispatch-card">
            <div class="card-header">
              <h3>{{ dispatch.title }}</h3>
              <div class="time-stamp">
                <svg viewBox="0 0 24 24">
                  <path d="M12,20A8,8 0 0,0 20,12A8,8 0 0,0 12,4A8,8 0 0,0 4,12A8,8 0 0,0 12,20M12,2A10,10 0 0,1 22,12A10,10 0 0,1 12,22C6.47,22 2,17.5 2,12A10,10 0 0,1 12,2M12.5,7V12.25L17,14.92L16.25,16.15L11,13V7H12.5Z" />
                </svg>
                <span>{{ new Date(dispatch.time).toLocaleString() }}</span>
              </div>
            </div>
            
            <!-- 关键信息区（优先显示） -->
            <div class="key-info">
              <div class="info-tag phone" v-if="dispatch.alerterphone">
                <svg viewBox="0 0 24 24">
                  <path d="M6.62,10.79C8.06,13.62 10.38,15.94 13.21,17.38L15.41,15.18C15.69,14.9 16.08,14.82 16.43,14.93C17.55,15.3 18.75,15.5 20,15.5A1,1 0 0,1 21,16.5V20A1,1 0 0,1 20,21A17,17 0 0,1 3,4A1,1 0 0,1 4,3H7.5A1,1 0 0,1 8.5,4C8.5,5.25 8.7,6.45 9.07,7.57C9.18,7.92 9.1,8.31 8.82,8.59L6.62,10.79Z" />
                </svg>
                <span>{{ dispatch.alerterphone }}</span>
              </div>
              <div class="info-tag name" v-if="dispatch.alertername">
                <svg viewBox="0 0 24 24">
                  <path d="M12,4A4,4 0 0,1 16,8A4,4 0 0,1 12,12A4,4 0 0,1 8,8A4,4 0 0,1 12,4M12,14C16.42,14 20,15.79 20,18V20H4V18C4,15.79 7.58,14 12,14Z" />
                </svg>
                <span>{{ dispatch.alertername }}</span>
              </div>
              <div class="info-tag prisoner" v-if="permissions.includes('admin') && dispatch.prisonerid">
                <svg viewBox="0 0 24 24">
                  <path d="M12,1L3,5V11C3,16.55 6.84,21.74 12,23C17.16,21.74 21,16.55 21,11V5L12,1M12,5A3,3 0 0,1 15,8A3,3 0 0,1 12,11A3,3 0 0,1 9,8A3,3 0 0,1 12,5M17.13,17C15.92,18.85 14.11,20.24 12,20.92C9.89,20.24 8.08,18.85 6.87,17C6.53,16.5 6.24,16 6,15.47C6,13.82 8.71,12.47 12,12.47C15.29,12.47 18,13.79 18,15.47C17.76,16 17.47,16.5 17.13,17Z" />
                </svg>
                <span>ID:{{ dispatch.prisonerid }}</span>
              </div>
            </div>
            
            <!-- 内容区（可折叠） -->
            <div 
              class="card-content" 
              :class="{ 'collapsed': !expandedDispatch[dispatch._id] }" 
              @click="toggleDispatchExpand(dispatch._id)"
            >
              {{ dispatch.content }}
            </div>
            
            <!-- 操作按钮区 -->
            <div class="card-actions">
              <button @click="acceptDispatch(dispatch._id)" class="accept-btn">
                <svg viewBox="0 0 24 24">
                  <path d="M21,7L9,19L3.5,13.5L4.91,12.09L9,16.17L19.59,5.59L21,7Z" />
                </svg>
                <span>接受</span>
              </button>
              <button @click="deleteDispatch(dispatch._id)" class="delete-btn">
                <svg viewBox="0 0 24 24">
                  <path d="M19,4H15.5L14.5,3H9.5L8.5,4H5V6H19M6,19A2,2 0 0,0 8,21H16A2,2 0 0,0 18,19V7H6V19Z" />
                </svg>
                <span>删除</span>
              </button>
            </div>
          </div>
        </div>
        
        <div v-else class="empty-state">
          <svg viewBox="0 0 24 24">
            <path d="M3,3H21V5H3V3M3,7H15V9H3V7M3,11H21V13H3V11M3,15H15V17H3V15M3,19H21V21H3V19Z" />
          </svg>
          <p>暂无调度任务</p>
        </div>
      </div>






      <!-- 创建调度表单 -->
      <div v-if="hasPermission('assign_jobs') || permissions.includes('admin')" class="dispatch-form">
        <div class="form-header">
          <svg viewBox="0 0 24 24">
            <path d="M19,13H13V19H11V13H5V11H11V5H13V11H19V13Z" />
          </svg>
          <h3>创建新调度</h3>
        </div>
        
        <div class="form-group">
          <label>调度标题</label>
          <input 
            v-model="newDispatchTitle" 
            placeholder="输入调度标题..." 
            class="form-input"
          />
        </div>
        
        <div class="form-group">
          <label>调度内容</label>
          <textarea 
            v-model="newDispatchContent" 
            placeholder="输入详细内容..." 
            class="form-textarea"
          ></textarea>
        </div>
        
        <div class="form-row">
          <div class="form-group">
            <label>报警人电话</label>
            <input 
              v-model="newDispatchAlerterPhone" 
              placeholder="电话号码" 
              class="form-input"
            />
          </div>
          
          <div class="form-group">
            <label>报警人姓名</label>
            <input 
              v-model="newDispatchAlerterName" 
              placeholder="姓名" 
              class="form-input"
            />
          </div>
        </div>
        
        <button 
          @click="createDispatch" 
          class="create-btn"
          :disabled="!newDispatchTitle.trim() || !newDispatchContent.trim()"
        >
          <svg viewBox="0 0 24 24">
            <path d="M19,13H13V19H11V13H5V11H11V5H13V11H19V13Z" />
          </svg>
          <span>创建调度</span>
        </button>
      </div>
      
  
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, PropType, reactive } from 'vue';

const props = defineProps({
  factionData: {
    type: Object,
    required: true
  },
  permissions: {
    type: Array,
    required: true
  },
  hasPermission: {
    type: Function,
    required: true
  },
  handleEmergencyHotlineAction: {
    type: Function as PropType<() => void>,
    required: true
  },
  stopEmergencyHotlineAction: {
    type: Function as PropType<() => void>,
    required: true
  },
  createDispatchAction: {
    type: Function as PropType<(data: any) => Promise<boolean>>,
    required: true
  },
  acceptDispatchAction: {
    type: Function as PropType<(id: string) => Promise<boolean>>,
    required: true
  },
  deleteDispatchAction: {
    type: Function as PropType<(id: string) => Promise<boolean>>,
    required: true
  }
});

// 调度相关状态
const newDispatchTitle = ref('');
const newDispatchContent = ref('');
const newDispatchAlerterPhone = ref('');
const newDispatchAlerterName = ref('');

// 跟踪每个调度是否展开
const expandedDispatch = reactive({});

// 切换调度内容的展开/折叠状态
function toggleDispatchExpand(dispatchId) {
  expandedDispatch[dispatchId] = !expandedDispatch[dispatchId];
}

// 排序调度，最新的在顶部
const sortedDispatch = computed(() => {
  if (!props.factionData?.dispatch) return [];
  return [...props.factionData.dispatch].sort((a, b) => b.time - a.time);
});

function handleEmergencyHotline() {
  props.handleEmergencyHotlineAction();
}

function stopEmergencyHotline() {
  props.stopEmergencyHotlineAction();
}

async function createDispatch() {
  if (newDispatchTitle.value.trim() && newDispatchContent.value.trim()) {
    const dispatchData = {
      title: newDispatchTitle.value,
      content: newDispatchContent.value,
      alerterPhone: newDispatchAlerterPhone.value,
      alerterName: newDispatchAlerterName.value
    };

    const success = await props.createDispatchAction(dispatchData);
    if (success) {
      newDispatchTitle.value = '';
      newDispatchContent.value = '';
      newDispatchAlerterPhone.value = '';
      newDispatchAlerterName.value = '';
    }
  }
}

function acceptDispatch(dispatchId) {
  return props.acceptDispatchAction(dispatchId);
}

function deleteDispatch(dispatchId) {
  return props.deleteDispatchAction(dispatchId);
}
</script>

<style scoped>
.dispatch-container {
  height: auto;
  width: 100%;
}

.card {
  background: rgba(34, 42, 53, 0.7);
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  flex-direction: column;
}

.card-header {
  display: flex;
  align-items: center;
  padding: 1.8vh 2vh;
  background: rgba(26, 32, 44, 0.5);
  border-bottom: 1px solid rgba(255, 255, 255, 0.08);
}

.card-header svg {
  width: 2vh;
  height: 2vh;
  margin-right: 1vh;
  fill: #a995f4;
}

.card-header h2 {
  font-size: 1.6vh;
  font-weight: 600;
  color: #e2e8f0;
  margin: 0;
}

/* 紧急热线控制 */
.hotline-controls {
  display: flex;
  flex-wrap: wrap;
  gap: 1vh;
  padding: 1.5vh;
  background: rgba(26, 32, 44, 0.3);
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

.hotline-status {
  display: flex;
  align-items: center;
  padding: 0.8vh 1.2vh;
  border-radius: 6px;
  font-size: 1.3vh;
  font-weight: 500;
  border: 1px solid rgba(255, 255, 255, 0.1);
  background: rgba(26, 32, 44, 0.4);
  color: #a0aec0;
  margin-right: auto;
  position: relative;
  overflow: hidden;
}

.hotline-status::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 4px;
  background: #a0aec0;
}

.hotline-status svg {
  width: 1.6vh;
  height: 1.6vh;
  margin-right: 0.8vh;
  fill: #a0aec0;
}

.hotline-status.active {
  background: rgba(72, 187, 120, 0.1);
  color: #68d391;
  border-color: rgba(104, 211, 145, 0.2);
  animation: pulse 2s infinite;
}

.hotline-status.active::before {
  background: #68d391;
}

.hotline-status.active svg {
  fill: #68d391;
}

.hotline-status.self-active {
  background: rgba(66, 153, 225, 0.15);
  color: #63b3ed;
  border-color: rgba(99, 179, 237, 0.3);
  animation: pulse-blue 2s infinite;
}

.hotline-status.self-active::before {
  background: #63b3ed;
}

.hotline-status.self-active svg {
  fill: #63b3ed;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(104, 211, 145, 0.4);
  }
  70% {
    box-shadow: 0 0 0 5px rgba(104, 211, 145, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(104, 211, 145, 0);
  }
}

@keyframes pulse-blue {
  0% {
    box-shadow: 0 0 0 0 rgba(99, 179, 237, 0.4);
  }
  70% {
    box-shadow: 0 0 0 5px rgba(99, 179, 237, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(99, 179, 237, 0);
  }
}

.hotline-btn {
  display: flex;
  align-items: center;
  padding: 1vh 1.5vh;
  border-radius: 6px;
  font-size: 1.3vh;
  font-weight: 500;
  border: none;
  cursor: pointer;
  transition: all 0.2s;
}

.hotline-btn svg {
  width: 1.8vh;
  height: 1.8vh;
  margin-right: 0.8vh;
}

.hotline-btn.activate {
  background: rgba(72, 187, 120, 0.2);
  color: #68d391;
}

.hotline-btn.activate:hover {
  background: rgba(72, 187, 120, 0.3);
}

.hotline-btn.activate svg {
  fill: #68d391;
}

.hotline-btn.deactivate {
  background: rgba(229, 62, 62, 0.1);
  color: #fc8181;
}

.hotline-btn.deactivate:hover {
  background: rgba(229, 62, 62, 0.2);
}

.hotline-btn.deactivate svg {
  fill: #fc8181;
}

.hotline-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  filter: grayscale(100%);
}

/* 创建调度表单 */
.dispatch-form {
  padding: 1.5vh;
  background: rgba(26, 32, 44, 0.2);
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

.form-header {
  display: flex;
  align-items: center;
  margin-bottom: 1.5vh;
}

.form-header svg {
  width: 1.8vh;
  height: 1.8vh;
  margin-right: 0.8vh;
  fill: #a995f4;
}

.form-header h3 {
  font-size: 1.4vh;
  font-weight: 600;
  color: #e2e8f0;
  margin: 0;
}

.form-group {
  margin-bottom: 1.2vh;
}

.form-row {
  display: flex;
  gap: 1.5vh;
  margin-bottom: 1.2vh;
}

.form-row .form-group {
  flex: 1;
}

.form-group label {
  display: block;
  font-size: 1.2vh;
  color: #a0aec0;
  margin-bottom: 0.5vh;
}

.form-input, .form-textarea {
  width: 100%;
  background: rgba(26, 32, 44, 0.5);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 6px;
  color: #e2e8f0;
  padding: 1vh 1.2vh;
  font-size: 1.3vh;
  transition: border-color 0.2s;
}

.form-input {
  height: 4vh;
}

.form-textarea {
  min-height: 8vh;
  resize: vertical;
  max-height: 15vh;
}

.form-input:focus, .form-textarea:focus {
  outline: none;
  border-color: #a995f4;
  box-shadow: 0 0 0 1px rgba(169, 149, 244, 0.3);
}

.form-input::placeholder, .form-textarea::placeholder {
  color: rgba(160, 174, 192, 0.5);
}

.create-btn {
  display: flex;
  align-items: center;
  background: #a995f4;
  color: #1a202c;
  border: none;
  border-radius: 6px;
  padding: 1vh 2vh;
  font-size: 1.3vh;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s;
  margin-left: auto;
  margin-top: 1vh;
}

.create-btn:hover:not(:disabled) {
  background: #9179e7;
}

.create-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.create-btn svg {
  width: 1.8vh;
  height: 1.8vh;
  margin-right: 0.8vh;
  fill: #1a202c;
}

/* 调度列表 */
.dispatch-list-container {
  padding: 1vh;
}

/* 卡片网格布局 */
.dispatch-card-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(30vh, 1fr));
  gap: 1.5vh;
  max-height: 70vh;
  overflow-y: auto;
  padding-right: 0.5vh;
}

.dispatch-card-grid::-webkit-scrollbar {
  width: 6px;
}

.dispatch-card-grid::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 3px;
}

.dispatch-card-grid::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 3px;
}

.dispatch-card-grid::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.3);
}

.dispatch-card {
  background: rgba(26, 32, 44, 0.3);
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  transition: transform 0.2s, box-shadow 0.2s;
}

.dispatch-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 1vh;
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

.card-header h3 {
  font-size: 1.4vh;
  font-weight: 600;
  color: #e2e8f0;
  margin: 0;
  max-width: 65%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.time-stamp {
  display: flex;
  align-items: center;
  font-size: 1vh;
  color: #a0aec0;
}

.time-stamp svg {
  width: 1.3vh;
  height: 1.3vh;
  margin-right: 0.4vh;
  fill: #a0aec0;
}

.time-stamp span {
  white-space: nowrap;
}

/* 关键信息标签 */
.key-info {
  display: flex;
  flex-wrap: wrap;
  gap: 0.8vh;
  padding: 1vh;
  background: rgba(26, 32, 44, 0.2);
}

.info-tag {
  display: flex;
  align-items: center;
  padding: 0.5vh 0.8vh;
  border-radius: 4px;
  font-size: 1.2vh;
  font-weight: 500;
  margin-right: 0.6vh;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: fit-content;
}

.info-tag svg {
  width: 1.4vh;
  height: 1.4vh;
  margin-right: 0.5vh;
}

.info-tag.phone {
  background: rgba(66, 153, 225, 0.2);
  color: #63b3ed;
  font-weight: 600;
  font-size: 1.3vh;
}

.info-tag.phone svg {
  fill: #63b3ed;
}

.info-tag.name {
  background: rgba(72, 187, 120, 0.15);
  color: #68d391;
  font-weight: 600;
  font-size: 1.3vh;
}

.info-tag.name svg {
  fill: #68d391;
}

.info-tag.prisoner {
  background: rgba(221, 107, 32, 0.1);
  color: #ed8936;
}

.info-tag.prisoner svg {
  fill: #ed8936;
}

/* 内容区域 */
.card-content {
  font-size: 1.3vh;
  color: #e2e8f0;
  line-height: 1.5;
  padding: 1vh;
  white-space: pre-wrap;
  word-break: break-word;
  flex-grow: 1;
  max-height: 12vh;
  overflow-y: auto;
  cursor: pointer;
  background: rgba(26, 32, 44, 0.15);
}

.card-content::-webkit-scrollbar {
  width: 4px;
}

.card-content::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.1);
}

.card-content::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 2px;
}

.card-content.collapsed {
  max-height: 6vh;
  overflow: hidden;
  position: relative;
}

.card-content.collapsed:after {
  content: '...';
  position: absolute;
  bottom: 0;
  right: 0;
  padding-left: 1vh;
  background: linear-gradient(to right, transparent, rgba(26, 32, 44, 0.8) 40%);
}

.card-content:hover {
  background: rgba(255, 255, 255, 0.05);
}

/* 卡片操作区 */
.card-actions {
  display: flex;
  padding: 1vh;
  border-top: 1px solid rgba(255, 255, 255, 0.05);
  justify-content: space-between;
}

.accept-btn, .delete-btn {
  display: flex;
  align-items: center;
  padding: 0.7vh 1vh;
  border-radius: 4px;
  font-size: 1.1vh;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  border: none;
  flex: 1;
  justify-content: center;
}

.accept-btn {
  background: rgba(72, 187, 120, 0.2);
  color: #68d391;
  margin-right: 0.5vh;
}

.accept-btn:hover {
  background: rgba(72, 187, 120, 0.3);
}

.accept-btn svg {
  width: 1.6vh;
  height: 1.6vh;
  margin-right: 0.4vh;
  fill: #68d391;
}

.delete-btn {
  background: rgba(229, 62, 62, 0.1);
  color: #fc8181;
}

.delete-btn:hover {
  background: rgba(229, 62, 62, 0.2);
}

.delete-btn svg {
  width: 1.6vh;
  height: 1.6vh;
  margin-right: 0.4vh;
  fill: #fc8181;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4vh 0;
  color: #a0aec0;
  height: 100%;
  min-height: 20vh;
}

.empty-state svg {
  width: 5vh;
  height: 5vh;
  fill: #a0aec0;
  margin-bottom: 1vh;
  opacity: 0.7;
}

.empty-state p {
  font-size: 1.4vh;
  margin: 0;
}

.dispatch-header-info {
  display: flex;
  align-items: center;
  margin-bottom: 1.5vh;
  padding: 0.8vh 1vh;
  background: rgba(26, 32, 44, 0.4);
  border-radius: 6px;
  font-size: 1.2vh;
  color: #e2e8f0;
}

.dispatch-header-info svg {
  width: 1.6vh;
  height: 1.6vh;
  margin-right: 0.8vh;
  fill: #a995f4;
}

.dispatch-header-info b {
  color: #a995f4;
  font-weight: 600;
}


</style> 