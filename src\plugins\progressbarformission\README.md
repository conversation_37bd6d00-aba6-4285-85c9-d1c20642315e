# 任务进度条系统

## 功能

- 显示任务进度条
- 支持多个任务目标
- 实时更新进度
- 支持纯文本和带进度的目标

## 服务器端使用

```typescript
import { useRebar } from '@Server/index.js';

const Rebar = useRebar();
const missionApi = await Rebar.useApi().getAsync('progressbarformission-api');

// 显示任务进度
missionApi.showMissionProgress(player, '任务标题', [
    { text: '目标1', current: 0, total: 10 },
    { text: '目标2', current: 0, total: 5 },
    { text: '目标3' } // 纯文本目标
]);

// 更新进度
missionApi.updateMissionProgress(player, 0, 5); // 更新第一个目标

// 隐藏任务进度
missionApi.hideMissionProgress(player);
```

## 客户端使用

```typescript
import { useRebarClient } from '@Client/index.js';

const Rebar = useRebarClient();
const missionApi = Rebar.useClientApi().get('progressbarformission-client-api');

// 显示任务进度
missionApi.showMissionProgress('任务标题', [
    { text: '目标1', current: 0, total: 10 },
    { text: '目标2' }
]);

// 更新进度
missionApi.updateMissionProgress(0, 5);

// 隐藏任务进度
missionApi.hideMissionProgress();
```

## 使用示例

```typescript
// 服务器端 - 狩猎任务
missionApi.showMissionProgress(player, '哥布林狩猎', [
    { text: '击杀哥布林', current: 0, total: 15 },
    { text: '收集哥布林耳朵', current: 0, total: 10 }
]);

// 更新击杀进度
missionApi.updateMissionProgress(player, 0, 5);

// 客户端 - 主线任务
missionApi.showMissionProgress('主线任务', [
    { text: '前往村庄找村长对话' },
    { text: '调查废弃的矿洞' },
    { text: '击败守卫', current: 0, total: 3 }
]);
``` 