<template>
  <Transition name="slide-fade">
    <div v-if="isPromptVisible" class="prompt-station">
      <div class="prompt-panel">
        <div class="key-icon">
          <span>{{ keycode1 }}</span>
        </div>
        <div class="prompt-text">{{ message }}</div>
      </div>
    </div>
  </Transition>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { useEvents } from '@Composables/useEvents.js';

const events = useEvents();
const isPromptVisible = ref(false);
const message = ref('Press E to interact');
const keycode1 = ref('E');

// 模拟数据用于测试
const mockPrompts = [
  { message: '按 E 键进入车辆', keycode: 'E' },
  { message: '按 F 键打开车门', keycode: 'F' },
  { message: '按 G 键启动引擎', keycode: 'G' },
  { message: '按 H 键打开后备箱', keycode: 'H' },
  { message: '按 I 键查看物品', keycode: 'I' },
  { message: '按 J 键使用物品', keycode: 'J' },
  { message: '按 K 键与NPC对话', keycode: 'K' },
  { message: '按 L 键坐下休息', keycode: 'L' },
  { message: '按 M 键打开地图', keycode: 'M' },
  { message: '按 N 键切换视角', keycode: 'N' }
];

let mockIndex = 0;

// 模拟提示循环显示
const startMockCycle = () => {
  setInterval(() => {
    if (!isPromptVisible.value) {
      const mockPrompt = mockPrompts[mockIndex];
      isPromptVisible.value = true;
      message.value = mockPrompt.message;
      keycode1.value = mockPrompt.keycode;
      
      // 3秒后隐藏
      setTimeout(() => {
        isPromptVisible.value = false;
      }, 3000);
      
      // 切换到下一个模拟数据
      mockIndex = (mockIndex + 1) % mockPrompts.length;
    }
  }, 4000); // 每4秒显示一次
};

events.on('showpromptbar', (message1: string, keycode?: string) => {
  isPromptVisible.value = true;
  message.value = message1;
  if (keycode) {
    keycode1.value = keycode;
  }
});

events.on('hidepromptbar', () => {
  isPromptVisible.value = false;
  keycode1.value = 'E';
});

onMounted(() => {
  // 启动模拟数据循环（仅用于测试）
 // startMockCycle();
});
</script>

<style scoped>
/* CSS变量定义 - 在组件内部定义，不使用:root */
.prompt-station {
  --primary-color: #64ffda;
  --primary-dark: #1de9b6;
  --card-bg: rgba(15, 23, 42, 0.95);
  --card-bg-light: rgba(30, 41, 59, 0.8);
  --text-primary: #ffffff;
  --text-secondary: rgba(255, 255, 255, 0.7);
  --text-muted: rgba(255, 255, 255, 0.5);
  --border-color: rgba(100, 255, 218, 0.2);
  --border-hover: rgba(100, 255, 218, 0.3);
  --shadow-sm: 0 0.2vh 0.8vh rgba(0, 0, 0, 0.2);
  --shadow-md: 0 0.4vh 1.6vh rgba(0, 0, 0, 0.3);
  --shadow-lg: 0 0.8vh 3.2vh rgba(0, 0, 0, 0.4);
  --shadow-primary: 0 0.4vh 2vh rgba(100, 255, 218, 0.3);

  position: fixed;
  bottom: 35vh;
  left: 58%;
  transform: translateX(-50%);
  z-index: 9999;
}

.prompt-panel {
  display: flex;
  align-items: center;
  gap: 1vw;
  padding: 1.2vh 1.5vw;
  background: var(--card-bg);
  border: 1px solid var(--border-color);
  border-radius: 1.6vh;
  box-shadow: var(--shadow-lg);
}

.key-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 3.2vh;
  height: 3.2vh;
  background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
  border: 1px solid var(--primary-color);
  border-radius: 50%;
  color: #0f172a;
  font-size: 1.6vh;
  font-weight: 700;
}

.prompt-text {
  color: var(--text-primary);
  font-size: 1.4vh;
  font-weight: 600;
  letter-spacing: 0.05em;
}

/* Animations */
.slide-fade-enter-active,
.slide-fade-leave-active {
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.slide-fade-enter-from,
.slide-fade-leave-to {
  opacity: 0;
  transform: translate(-50%, 2vh);
}


</style>