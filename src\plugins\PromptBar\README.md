# 按键提示条系统

## 功能

- 显示按键提示
- 自定义按键和文本
- 动画效果
- 居中显示

## 服务器端使用

```typescript
import { useRebar } from '@Server/index.js';

const Rebar = useRebar();
const promptApi = await Rebar.useApi().getAsync('promptbar-api');

// 显示按键提示
promptApi.showPromptBar(player, '按 E 键进入车辆', 'E');

// 隐藏按键提示
promptApi.hidePromptBar(player);
```

## 客户端使用

```typescript
import { useRebarClient } from '@Client/index.js';

const Rebar = useRebarClient();
const promptApi = Rebar.useClientApi().get('promptbar-client-api');

// 显示按键提示
promptApi.showPromptBar('按 F 键打开车门', 'F');

// 隐藏按键提示
promptApi.hidePromptBar();
```

## 使用示例

```typescript
// 服务器端 - 车辆交互
promptApi.showPromptBar(player, '按 E 键进入车辆', 'E');
promptApi.showPromptBar(player, '按 F 键打开车门', 'F');

// 客户端 - 物品交互
promptApi.showPromptBar('按 G 键拾取物品', 'G');
promptApi.showPromptBar('按 H 键使用物品', 'H');

// 隐藏提示
promptApi.hidePromptBar();
``` 