


/**
 * 德州扑克牌型判断器
 *
 * 牌表示: { suit: 'H'/'D'/'C'/'S', rank: 'A'/'K'/'Q'/'J'/'T'/'9'/'8'/'7'/'6'/'5'/'4'/'3'/'2' }
 * H: Hearts (红桃), D: Diamonds (方块), C: Clubs (梅花), S: Spades (黑桃)
 * A: Ace, K: King, Q: Queen, J: Jack, T: Ten
 *
 * 返回对象结构示例:
 * {
 *   name: "Full House", // 牌型名称
 *   rank: 7,            // 牌型等级 (越高越好)
 *   values: [10, 10, 10, 9, 9], // 构成牌型的主要牌的点数值 (用于同牌型比较)
 *   handCards: [{suit:'H', rank:'T'}, ..., {}] // 构成最佳牌型的5张牌
 * }
 */

const HandRankings = {
    HIGH_CARD: { rank: 1, name: "高牌" },
    ONE_PAIR: { rank: 2, name: "一对" },
    TWO_PAIR: { rank: 3, name: "两对" },
    THREE_OF_A_KIND: { rank: 4, name: "三条" },
    STRAIGHT: { rank: 5, name: "顺子" },
    FLUSH: { rank: 6, name: "同花" },
    FULL_HOUSE: { rank: 7, name: "葫芦" },
    FOUR_OF_A_KIND: { rank: 8, name: "四条" },
    STRAIGHT_FLUSH: { rank: 9, name: "同花顺" },
    ROYAL_FLUSH: { rank: 10, name: "皇家同花顺" },
};

const RankValues = {
    '2': 2, '3': 3, '4': 4, '5': 5, '6': 6, '7': 7, '8': 8, '9': 9,
    'T': 10, 'J': 11, 'Q': 12, 'K': 13, 'A': 14
};

// --- 辅助函数 ---

function getNumericValue(rank) {
    return RankValues[rank];
}

function sortCardsByRank(cards, ascending = false) {
    return [...cards].sort((a, b) => {
        const valA = getNumericValue(a.rank);
        const valB = getNumericValue(b.rank);
        return ascending ? valA - valB : valB - valA;
    });
}

function getRankCounts(cards) {
    const counts = {};
    for (const card of cards) {
        counts[card.rank] = (counts[card.rank] || 0) + 1;
    }
    return counts;
}

function getSuitCounts(cards) {
    const counts = {};
    for (const card of cards) {
        counts[card.suit] = (counts[card.suit] || 0) + 1;
    }
    return counts;
}

// --- 5张牌的牌型判断函数 ---
// 这些函数都假设输入的是已经排序好的5张牌 (通常是降序)

function checkStraightFlushAndRoyal(fiveCards) {
    const sortedCards = sortCardsByRank(fiveCards, false); // 确保降序
    const isFlush = sortedCards.every(c => c.suit === sortedCards[0].suit);
    if (!isFlush) return null;

    // 检查顺子 (包括 A-5)
    const ranks = sortedCards.map(c => getNumericValue(c.rank));
    let isStraight = true;
    for (let i = 0; i < ranks.length - 1; i++) {
        if (ranks[i] !== ranks[i+1] + 1) {
            isStraight = false;
            break;
        }
    }
    // 特殊处理 A, 5, 4, 3, 2 顺子
    if (!isStraight && ranks[0] === 14 && ranks[1] === 5 && ranks[2] === 4 && ranks[3] === 3 && ranks[4] === 2) {
        isStraight = true;
        // 对于A2345顺子，A的值按5算，方便比较 (A K Q J T vs A 2 3 4 5)
        // 但在values中，我们通常还是会记录A的原始值14，或者用[5,4,3,2,14]表示其结构
        const aceLowValues = [5, 4, 3, 2, 1]; // A被视为1
        return {
            ...HandRankings.STRAIGHT_FLUSH,
            values: aceLowValues,
            handCards: sortedCards // 牌还是原始的A,5,4,3,2
        };
    }

    if (isStraight) {
        if (ranks[0] === 14 && ranks[1] === 13) { // A, K, Q, J, T
            return { ...HandRankings.ROYAL_FLUSH, values: ranks, handCards: sortedCards };
        }
        return { ...HandRankings.STRAIGHT_FLUSH, values: ranks, handCards: sortedCards };
    }
    return null;
}

function checkFourOfAKind(fiveCards) {
    const rankCounts = getRankCounts(fiveCards);
    let quadRank = null;
    let kicker = null;

    for (const rank in rankCounts) {
        if (rankCounts[rank] === 4) {
            quadRank = getNumericValue(rank);
            break;
        }
    }

    if (quadRank) {
        const kickerCard = fiveCards.find(c => getNumericValue(c.rank) !== quadRank);
        kicker = getNumericValue(kickerCard.rank);
        return {
            ...HandRankings.FOUR_OF_A_KIND,
            values: [quadRank, quadRank, quadRank, quadRank, kicker],
            handCards: fiveCards
        };
    }
    return null;
}

function checkFullHouse(fiveCards) {
    const rankCounts = getRankCounts(fiveCards);
    let threeRank = null;
    let pairRank = null;

    const ranksInHand = Object.keys(rankCounts).map(r => ({ rank: r, count: rankCounts[r] }));
    ranksInHand.sort((a, b) => getNumericValue(b.rank) - getNumericValue(a.rank)); // 高点数优先

    for (const item of ranksInHand) {
        if (item.count === 3) {
            threeRank = getNumericValue(item.rank);
        } else if (item.count === 2) {
            pairRank = getNumericValue(item.rank);
        }
    }
    
    // 确保先找到三条，再找对子 (对于有两个三条的情况，取点数大的做三条)
    // 例如 AAATT, A是三条, T是对子.  AAA KKK, A是三条, K是对子
    const sortedNumericRanks = Object.entries(rankCounts)
                                .map(([rank, count]) => ({ val: getNumericValue(rank), count: count as number }))
                                .sort((a, b) => b.val - a.val); // 按点数降序

    let bestThreeRank = null;
    let bestPairRank = null;

    for (const {val, count} of sortedNumericRanks) {
        if (count >= 3 && bestThreeRank === null) {
            bestThreeRank = val;
        } else if (count >= 2 && bestPairRank === null && val !== bestThreeRank) {
            bestPairRank = val;
        }
    }

    if (bestThreeRank !== null && bestPairRank !== null) {
         return {
            ...HandRankings.FULL_HOUSE,
            values: [bestThreeRank, bestThreeRank, bestThreeRank, bestPairRank, bestPairRank],
            handCards: fiveCards
        };
    }
    return null;
}

function checkFlush(fiveCards) {
    // 这个函数在checkStraightFlushAndRoyal之前被调用是不合理的
    // 通常是先判断SF，如果不是SF，再判断单独的Flush
    // 这里假设已经排除了SF
    const sortedCards = sortCardsByRank(fiveCards, false);
    const isFlush = sortedCards.every(c => c.suit === sortedCards[0].suit);
    if (isFlush) {
        return {
            ...HandRankings.FLUSH,
            values: sortedCards.map(c => getNumericValue(c.rank)),
            handCards: sortedCards
        };
    }
    return null;
}

function checkStraight(fiveCards) {
    // 假设已经排除了SF和Flush
    const sortedCards = sortCardsByRank(fiveCards, false);
    const ranks = sortedCards.map(c => getNumericValue(c.rank));
    const uniqueRanks = [...new Set(ranks)].sort((a,b) => b-a); // 确保没有重复且降序

    if (uniqueRanks.length < 5) return null; // 不够5张不同的牌形成顺子

    let isStraight = true;
    for (let i = 0; i < 4; i++) { // 检查前4张牌，因为是5张牌的顺子
        if (uniqueRanks[i] !== uniqueRanks[i+1] + 1) {
            isStraight = false;
            break;
        }
    }

    // 特殊处理 A, 5, 4, 3, 2 顺子 (Wheel)
    // 此时 uniqueRanks 会是 [14, 5, 4, 3, 2]
    if (!isStraight && uniqueRanks[0] === 14 && uniqueRanks[1] === 5 && uniqueRanks[2] === 4 && uniqueRanks[3] === 3 && uniqueRanks[4] === 2) {
        isStraight = true;
         // 对于A2345顺子，A的值按5算，方便比较 (A K Q J T vs A 2 3 4 5)
        // 但在values中，我们通常还是会记录A的原始值14，或者用[5,4,3,2,14]表示其结构
        // 为了比较一致性，A2345的顺子，其最大牌是5
        return {
            ...HandRankings.STRAIGHT,
            values: [5, 4, 3, 2, 1], // 用1代表A，使5为最高牌
            handCards: sortedCards // 卡牌本身还是A,5,4,3,2
        };
    }

    if (isStraight) {
        return {
            ...HandRankings.STRAIGHT,
            values: uniqueRanks.slice(0, 5), // 取前5个，因为uniqueRanks可能多于5个
            handCards: sortedCards
        };
    }
    return null;
}


function checkThreeOfAKind(fiveCards) {
    const rankCounts = getRankCounts(fiveCards);
    let threeRank = null;
    const kickers = [];

    for (const rank in rankCounts) {
        if (rankCounts[rank] === 3) {
            threeRank = getNumericValue(rank);
            break;
        }
    }

    if (threeRank) {
        const sortedRemaining = sortCardsByRank(fiveCards.filter(c => getNumericValue(c.rank) !== threeRank), false);
        kickers.push(...sortedRemaining.slice(0, 2).map(c => getNumericValue(c.rank)));
        return {
            ...HandRankings.THREE_OF_A_KIND,
            values: [threeRank, threeRank, threeRank, ...kickers],
            handCards: fiveCards
        };
    }
    return null;
}

function checkTwoPair(fiveCards) {
    const rankCounts = getRankCounts(fiveCards);
    const pairs = [];
    for (const rank in rankCounts) {
        if (rankCounts[rank] === 2) {
            pairs.push(getNumericValue(rank));
        }
    }

    if (pairs.length >= 2) {
        pairs.sort((a, b) => b - a); // 高对在前
        const highPair = pairs[0];
        const lowPair = pairs[1];
        const kickerCard = fiveCards.find(c => getNumericValue(c.rank) !== highPair && getNumericValue(c.rank) !== lowPair);
        const kicker = getNumericValue(kickerCard.rank);
        return {
            ...HandRankings.TWO_PAIR,
            values: [highPair, highPair, lowPair, lowPair, kicker],
            handCards: fiveCards
        };
    }
    return null;
}

function checkOnePair(fiveCards) {
    const rankCounts = getRankCounts(fiveCards);
    let pairRank = null;
    const kickers = [];

    for (const rank in rankCounts) {
        if (rankCounts[rank] === 2) {
            pairRank = getNumericValue(rank);
            break;
        }
    }

    if (pairRank) {
        const sortedRemaining = sortCardsByRank(fiveCards.filter(c => getNumericValue(c.rank) !== pairRank), false);
        kickers.push(...sortedRemaining.slice(0, 3).map(c => getNumericValue(c.rank)));
        return {
            ...HandRankings.ONE_PAIR,
            values: [pairRank, pairRank, ...kickers],
            handCards: fiveCards
        };
    }
    return null;
}

function checkHighCard(fiveCards) {
    const sortedCards = sortCardsByRank(fiveCards, false);
    return {
        ...HandRankings.HIGH_CARD,
        values: sortedCards.map(c => getNumericValue(c.rank)),
        handCards: sortedCards
    };
}

// --- 组合生成器 ---
function getCombinations(array, k) {
    const result = [];
    function combine(startIndex, currentCombination) {
        if (currentCombination.length === k) {
            result.push([...currentCombination]);
            return;
        }
        if (startIndex >= array.length) {
            return;
        }
        // Include current element
        currentCombination.push(array[startIndex]);
        combine(startIndex + 1, currentCombination);
        // Exclude current element
        currentCombination.pop();
        combine(startIndex + 1, currentCombination);
    }
    combine(0, []);
    return result;
}


// --- 主评估函数 (评估5张牌) ---                 
function evaluate5CardHand(fiveCards) {
    // 必须按此顺序检查，从最好到最差
    const handChecks = [
        checkStraightFlushAndRoyal, // 这个同时检查皇家同花顺和普通同花顺
        checkFourOfAKind,
        checkFullHouse,
        checkFlush,
        checkStraight,
        checkThreeOfAKind,
        checkTwoPair,
        checkOnePair,
        checkHighCard
    ];

    for (const check of handChecks) {
        const result = check(fiveCards);
        if (result) {
            return result;
        }
    }
    // 理论上 checkHighCard 总会返回结果
    return checkHighCard(fiveCards); // Fallback, should be caught by the loop
}


// --- 比较两个已评估的牌型 ---
function compareHandResults(handA, handB) {
    if (!handA) return handB; // handA 为 null，B 更好
    if (!handB) return handA; // handB 为 null，A 更好

    if (handA.rank !== handB.rank) {
        return handA.rank > handB.rank ? handA : handB;
    }
    // 牌型等级相同，比较 values
    for (let i = 0; i < handA.values.length; i++) {
        if (handA.values[i] !== handB.values[i]) {
            return handA.values[i] > handB.values[i] ? handA : handB;
        }
    }
    return handA; // 完全相同 (平局)
}

// --- 最终的7张牌评估函数 ---
function evaluate7Cards(all7Cards) {
    if (!all7Cards || all7Cards.length !== 7) {
        throw new Error("需要7张牌进行评估");
    }

    const fiveCardCombinations = getCombinations(all7Cards, 5);
    let bestHandResult = null;

    for (const fiveCardCombo of fiveCardCombinations) {
        const currentHandResult = evaluate5CardHand(sortCardsByRank(fiveCardCombo, false)); // 评估前排序
        bestHandResult = compareHandResults(currentHandResult, bestHandResult);
    }

    return bestHandResult;
}


// --- 测试示例 ---
function runTests() {
    
    const testCases = [
        {
            name: "皇家同花顺",
            cards: [
                { suit: 'H', rank: 'A' }, { suit: 'H', rank: 'K' }, { suit: 'H', rank: 'Q' },
                { suit: 'H', rank: 'J' }, { suit: 'H', rank: 'T' }, { suit: 'D', rank: '2' },
                { suit: 'C', rank: '3' }
            ],
            expectedRank: HandRankings.ROYAL_FLUSH.rank
        },
        {
            name: "同花顺 (K高)",
            cards: [
                { suit: 'S', rank: 'K' }, { suit: 'S', rank: 'Q' }, { suit: 'S', rank: 'J' },
                { suit: 'S', rank: 'T' }, { suit: 'S', rank: '9' }, { suit: 'H', rank: 'A' },
                { suit: 'D', rank: '8' }
            ],
            expectedRank: HandRankings.STRAIGHT_FLUSH.rank
        },
        {
            name: "同花顺 (A5432)",
            cards: [
                { suit: 'C', rank: 'A' }, { suit: 'C', rank: '2' }, { suit: 'C', rank: '3' },
                { suit: 'C', rank: '4' }, { suit: 'C', rank: '5' }, { suit: 'H', rank: 'K' },
                { suit: 'D', rank: 'Q' }
            ],
            expectedRank: HandRankings.STRAIGHT_FLUSH.rank,
            expectedValues: [5,4,3,2,1]
        },
        {
            name: "四条 (四个A)",
            cards: [
                { suit: 'H', rank: 'A' }, { suit: 'D', rank: 'A' }, { suit: 'C', rank: 'A' },
                { suit: 'S', rank: 'A' }, { suit: 'H', rank: 'K' }, { suit: 'D', rank: 'Q' },
                { suit: 'C', rank: 'J' }
            ],
            expectedRank: HandRankings.FOUR_OF_A_KIND.rank,
            expectedValuesPrefix: [14,14,14,14,13]
        },
        {
            name: "葫芦 (三个K，两个Q)",
            cards: [
                { suit: 'H', rank: 'K' }, { suit: 'D', rank: 'K' }, { suit: 'C', rank: 'K' },
                { suit: 'S', rank: 'Q' }, { suit: 'H', rank: 'Q' }, { suit: 'D', rank: 'J' },
                { suit: 'C', rank: 'T' }
            ],
            expectedRank: HandRankings.FULL_HOUSE.rank,
            expectedValues: [13,13,13,12,12]
        },
        {
            name: "同花 (A高)",
            cards: [
                { suit: 'H', rank: 'A' }, { suit: 'H', rank: 'K' }, { suit: 'H', rank: 'Q' },
                { suit: 'H', rank: 'J' }, { suit: 'H', rank: '9' }, { suit: 'D', rank: 'T' }, // 5张红桃
                { suit: 'C', rank: '8' }
            ],
            expectedRank: HandRankings.FLUSH.rank,
            expectedValuesPrefix: [14,13,12,11,9] // A K Q J 9
        },
         {
            name: "同花 (7张同花，取最高的5张)",
            cards: [
                { suit: 'D', rank: 'K' }, { suit: 'D', rank: 'Q' }, { suit: 'D', rank: 'J' },
                { suit: 'D', rank: 'T' }, { suit: 'D', rank: '2' }, { suit: 'D', rank: '3' },
                { suit: 'D', rank: 'A' } // 7 diamonds
            ],
            expectedRank: HandRankings.FLUSH.rank,
            expectedValuesPrefix: [14,13,12,11,10] // A K Q J T
        },
        {
            name: "顺子 (A高)",
            cards: [
                { suit: 'H', rank: 'A' }, { suit: 'D', rank: 'K' }, { suit: 'C', rank: 'Q' },
                { suit: 'S', rank: 'J' }, { suit: 'H', rank: 'T' }, { suit: 'D', rank: '2' },
                { suit: 'C', rank: '3' }
            ],
            expectedRank: HandRankings.STRAIGHT.rank,
            expectedValuesPrefix: [14,13,12,11,10]
        },
        {
            name: "顺子 (5高 - A2345)",
            cards: [
                { suit: 'H', rank: 'A' }, { suit: 'D', rank: '2' }, { suit: 'C', rank: '3' },
                { suit: 'S', rank: '4' }, { suit: 'H', rank: '5' }, { suit: 'D', rank: 'K' },
                { suit: 'C', rank: 'Q' }
            ],
            expectedRank: HandRankings.STRAIGHT.rank,
            expectedValues: [5,4,3,2,1] // A2345顺子，A算1，5是最高牌
        },
        {
            name: "三条 (三个A)",
            cards: [
                { suit: 'H', rank: 'A' }, { suit: 'D', rank: 'A' }, { suit: 'C', rank: 'A' },
                { suit: 'S', rank: 'K' }, { suit: 'H', rank: 'Q' }, { suit: 'D', rank: 'J' },
                { suit: 'C', rank: '2' }
            ],
            expectedRank: HandRankings.THREE_OF_A_KIND.rank,
            expectedValuesPrefix: [14,14,14,13,12] // AAAKQ
        },
        {
            name: "两对 (AA KK)",
            cards: [
                { suit: 'H', rank: 'A' }, { suit: 'D', rank: 'A' }, { suit: 'C', rank: 'K' },
                { suit: 'S', rank: 'K' }, { suit: 'H', rank: 'Q' }, { suit: 'D', rank: 'J' },
                { suit: 'C', rank: '2' }
            ],
            expectedRank: HandRankings.TWO_PAIR.rank,
            expectedValuesPrefix: [14,14,13,13,12] // AAKKQ
        },
        {
            name: "两对 (QQ JJ from 7 cards with 3 pairs: QQ, JJ, TT, highest Q)",
            cards: [
                { suit: 'H', rank: 'Q' }, { suit: 'D', rank: 'Q' }, // QQ
                { suit: 'C', rank: 'J' }, { suit: 'S', rank: 'J' }, // JJ
                { suit: 'H', rank: 'T' }, { suit: 'D', rank: 'T' }, // TT
                { suit: 'C', rank: 'A' }  // Kicker A
            ],
            expectedRank: HandRankings.TWO_PAIR.rank,
            expectedValuesPrefix: [12,12,11,11,14] // QQJJA
        },
        {
            name: "一对 (AA)",
            cards: [
                { suit: 'H', rank: 'A' }, { suit: 'D', rank: 'A' }, { suit: 'C', rank: 'K' },
                { suit: 'S', rank: 'Q' }, { suit: 'H', rank: 'J' }, { suit: 'D', rank: '2' },
                { suit: 'C', rank: '3' }
            ],
            expectedRank: HandRankings.ONE_PAIR.rank,
            expectedValuesPrefix: [14,14,13,12,11] // AAKQJ
        },
        {
            name: "高牌 (AKQJ9)",
            cards: [
                { suit: 'H', rank: 'A' }, { suit: 'D', rank: 'K' }, { suit: 'C', rank: 'Q' },
                { suit: 'S', rank: 'J' }, { suit: 'H', rank: '9' }, { suit: 'D', rank: '2' },
                { suit: 'S', rank: '3' }
            ],
            expectedRank: HandRankings.HIGH_CARD.rank,
            expectedValuesPrefix: [14,13,12,11,9]
        },
         { // 确保葫芦优先于三条和一对，即使三条和一对的点数更高
            name: "葫芦 (TTT99) vs AAKKQ (葫芦胜)",
            cards: [
                { suit: 'H', rank: 'T' }, { suit: 'D', rank: 'T' }, { suit: 'C', rank: 'T' }, // TTT
                { suit: 'S', rank: '9' }, { suit: 'H', rank: '9' }, // 99
                { suit: 'D', rank: 'A' }, { suit: 'C', rank: 'K' }
            ],
            expectedRank: HandRankings.FULL_HOUSE.rank,
            expectedValues: [10,10,10,9,9]
        },
    ];

    let allTestsPassed = true;
    testCases.forEach(tc => {
        const result = evaluate7Cards(tc.cards);
        let pass = result && result.rank === tc.expectedRank;
        if (pass && tc.expectedValues) { // 检查values数组
            pass = tc.expectedValues.every((val, index) => result.values[index] === val);
        }
        if (pass && tc.expectedValuesPrefix) { // 检查values数组前缀
            pass = tc.expectedValuesPrefix.every((val, index) => result.values[index] === val);
        }

                        if (result) {
                    } else {
                    }
                if (tc.expectedValues)         if (tc.expectedValuesPrefix)                         if (!pass) {
            allTestsPassed = false;
        }
    });
    }




//使用方法：

/*
const my7Cards = [
    { suit: 'H', rank: 'A' }, { suit: 'D', rank: 'A' }, // AA
    { suit: 'C', rank: 'K' }, { suit: 'S', rank: 'K' }, // KK
    { suit: 'H', rank: 'Q' }, // Q
    { suit: 'S', rank: '2' }, { suit: 'C', rank: '7' }
];

const bestHand = evaluate7Cards(my7Cards);

if (bestHand) {
     // "两对"
     // 3
     // [14, 14, 13, 13, 12] (AAKKQ)
    }*/

// 导出主要函数
export { evaluate7Cards, compareHandResults };