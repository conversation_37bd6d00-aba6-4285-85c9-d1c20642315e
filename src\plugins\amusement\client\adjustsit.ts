import { useRebarClient } from '@Client/index.js';
import * as native from 'natives';
import * as alt from 'alt-client';
import { screenToWorld } from '../../raycast/client/raycasttick.js';

const Rebar = useRebarClient();

let localPed: alt.LocalPed | null = null;
let tick: number | null = null;
let pos: alt.Vector3 | null = null;
let entity: number | null = null;
let isRightMouseDown = false;
let heading: number = 0;
let isAdjusting = false;

// 注册命令来开始调整ped位置
alt.onServer('adjustped:start', () => {
    if (isAdjusting) {
        console.log('已经在调整ped位置中');
        return;
    }
    
    startAdjustPed();
});

function startAdjustPed() {
    isAdjusting = true;
    alt.showCursor(true);
    
    Rebar.player.useControls().setCameraControlsDisabled(true);
    Rebar.player.useControls().setAttackControlsDisabled(true);
    
    let cursorpos = alt.getCursorPos();
    let currentpos = alt.screenToWorld(cursorpos.x, cursorpos.y);
    
    // 创建一个男性ped模型
    const pedModel = alt.hash('mp_m_freemode_01');
    localPed = new alt.LocalPed(pedModel, 0, currentpos, new alt.Vector3(0, 0, 0));

    // 设置ped为半透明
    native.setEntityAlpha(localPed.scriptID, 128, false);
    native.setEntityCollision(localPed.scriptID, false, false);
    
    // 播放坐姿动画
    native.requestAnimDict('timetable@ron@ig_5_p3');
    alt.setTimeout(() => {
        if (localPed) {
            native.taskPlayAnim(localPed.scriptID, 'timetable@ron@ig_5_p3', 'ig_5_p3_base', 8.0, 8.0, -1, 9 | 4 | 1, 0, false, false, false);
        }
    }, 100);
    
    heading = native.getEntityHeading(localPed.scriptID);
    
    tick = alt.everyTick(() => {
        if (!localPed) return;
        
        const newCursorPos = alt.getCursorPos();
        const deltaX = newCursorPos.x - cursorpos.x;
        cursorpos = newCursorPos;
        
        if (isRightMouseDown) {
            // 右键按下时旋转ped
            localPed.frozen = true;
            const newHeading = heading + deltaX * 0.5;
            heading = newHeading;
            native.setEntityHeading(localPed.scriptID, heading);
        } else {
            // 正常模式下跟随鼠标移动
            localPed.frozen = false;
            const result = screenToWorld(1 | 2 | 4 | 8 | 16 | 32 | 64 | 128 | 256, -1);
            if (result[0]) {
                const [hit, success, hitPosition, coords, entity1] = result;
                pos = hitPosition;
                entity = entity1;
            }
            
            if (pos) {
                native.setEntityCoords(localPed.scriptID, pos.x, pos.y, pos.z, false, false, false, false);
                native.setEntityHeading(localPed.scriptID, heading);
            }
        }
    });
}

function cleanupAdjustPed() {
    isAdjusting = false;
    alt.showCursor(false);
    Rebar.player.useControls().setCameraControlsDisabled(false);
    Rebar.player.useControls().setAttackControlsDisabled(false);
    
    if (localPed) {
        localPed.destroy();
        localPed = null;
    }
    if (tick) {
        alt.clearEveryTick(tick);
        tick = null;
    }
    
    isRightMouseDown = false;
}

alt.on('keydown', (key) => {
    if (!isAdjusting || !localPed) return;
    
    if (key === 0x01) { // 鼠标左键 - 确认位置并输出坐标
        const finalPos = localPed.pos;
        const finalRot = localPed.rot;
        
        console.log(`=== PED位置信息 ===`);
        console.log(`位置: x=${finalPos.x.toFixed(4)}, y=${finalPos.y.toFixed(4)}, z=${finalPos.z.toFixed(4)}`);
        console.log(`旋转: x=${finalRot.x.toFixed(4)}, y=${finalRot.y.toFixed(4)}, z=${finalRot.z.toFixed(4)}`);
        console.log(`朝向: ${heading.toFixed(4)}`);
        
        cleanupAdjustPed();
        
    } else if (key === 0x02) { // 鼠标右键 - 开始旋转
        isRightMouseDown = true;
        
    } else if (key === 0x04) { // 鼠标中键 - 取消操作
        console.log('取消ped调整操作');
        cleanupAdjustPed();
    }
});

alt.on('keyup', (key) => {
    if (!isAdjusting || !localPed) return;
    
    if (key === 0x02) { // 鼠标右键释放
        isRightMouseDown = false;
        
        // 更新鼠标位置到ped当前位置
        const newCursorPos = alt.worldToScreen(localPed.pos.x, localPed.pos.y, localPed.pos.z);
        if (newCursorPos) {
            alt.setCursorPos(newCursorPos);
        }
    }
}); 