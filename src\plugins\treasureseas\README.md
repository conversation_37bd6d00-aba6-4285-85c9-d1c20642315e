# 海底寻宝插件 - 宝藏展示功能

## 功能概述

这个插件为海底寻宝系统添加了一个华丽的宝藏展示前端界面，当玩家成功开启海底宝箱后，会显示一个精美的动画界面展示获得的宝藏。

## 新增功能

### 🎁 宝藏展示界面

- **开箱动画**: 包含宝箱掉落和开启的 3D 动画效果
- **奖励展示**: 以卡片形式展示获得的金条和珍稀物品
- **稀有度系统**: 不同稀有度的物品有不同的颜色和效果
- **海洋主题**: 海洋波浪、气泡动画等背景效果
- **响应式设计**: 支持不同屏幕尺寸

### 📦 支持的宝藏物品

| 物品名称 | 稀有度 | 获取深度 |
|---------|--------|----------|
| 古代金币 | 史诗 | 所有深度 |
| 海洋珍珠 | 稀有 | 所有深度 |
| 蓝宝石 | 史诗 | 中深层 |
| 祖母绿 | 史诗 | 中深层 |
| 钻石 | 传奇 | 深层为主 |
| 古代文物 | 传奇 | 深层为主 |
| 沉船宝箱钥匙 | 传奇 | 深层为主 |

### 🌊 深度等级

- **浅层** (-40m 到 -80m): 基础奖励
- **中层** (-80m 到 -120m): 中等奖励  
- **深层** (-120m 到 -160m): 高级奖励

## 文件结构

```
src/plugins/treasureseas/
├── server/
│   └── index.ts           # 服务端逻辑（已更新）
├── client/
│   └── index.ts           # 客户端逻辑（已更新）
├── shared/
│   └── types.ts           # 共享类型定义（新增）
├── webview/
│   ├── TreasureDisplay.vue # 宝藏展示组件（新增）
│   └── index.ts           # Webview 索引（新增）
└── README.md              # 文档（本文件）
```

## 技术实现

### 前端技术
- **Vue 3**: 使用 Composition API
- **CSS3**: 复杂动画和过渡效果
- **TypeScript**: 类型安全的开发

### 动画效果
- 宝箱掉落和开启动画
- 物品卡片从右侧滑入
- 光芒闪烁效果
- 海洋波浪和气泡动画
- 悬停和点击交互效果

### 事件流程
1. 玩家开锁成功 → 服务端处理奖励
2. 服务端发送展示数据到客户端
3. 客户端显示宝藏展示界面
4. 播放开箱动画序列
5. 展示获得的物品
6. 玩家点击关闭按钮

## 使用说明

### 玩家体验
1. 潜水到海底找到宝箱
2. 使用开锁工具成功开启宝箱
3. 自动显示宝藏展示界面
4. 观看开箱动画
5. 查看获得的宝藏
6. 点击"继续探索"关闭界面

### 开发者配置
如需自定义物品稀有度或添加新物品，请编辑 `shared/types.ts` 文件中的配置。

## 依赖项

- Rebar 框架
- inventory 插件（用于物品图片）
- Vue 3 + TypeScript
- alt:V 客户端/服务端 API

## 注意事项

- 图片资源位于 `src/plugins/inventory/images/` 目录
- 确保物品图片文件存在且路径正确
- 界面会自动适配不同的屏幕尺寸
- 开发环境下会显示测试数据

## 更新日志

### v1.0.0 (当前版本)
- ✨ 新增宝藏展示前端界面
- ✨ 实现开箱动画效果
- ✨ 添加物品稀有度系统
- ✨ 支持所有宝藏物品展示
- ✨ 响应式设计支持
- 🔧 更新服务端和客户端逻辑
- 📚 完善文档和类型定义 