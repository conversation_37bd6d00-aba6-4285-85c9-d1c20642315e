<template>
  <div class="lottery-overlay">
    <div class="lottery-container">
      <div class="scratch-ticket">
        <!-- 顶部标识 -->
        <div class="ticket-header">
          <div class="state-info">
            <div class="state-name">蛋城州立彩票</div>
            <div class="game-info">
              <span class="game-name">幸运数字</span>
            </div>
          </div>
        </div>
      
        <!-- 游戏标题 -->
        <div class="game-title">
          <div class="title-text">幸运数字</div>
          <div class="prize-text">最高奖金 $50,000!</div>
        </div>
        
        <!-- 左侧信息区 -->
        <div class="info-section">
          <!-- 游戏规则和奖金表 -->
          <div class="info-card">
            <div class="card-title">游戏规则 & 奖金表</div>
            <div class="card-content">
              <div class="game-instructions">
                刮开下方银色区域。如果"你的号码"中有任何一个与"中奖号码"匹配，即可获得对应奖金！
              </div>
              <div class="prize-list">
                <div class="prize-item">
                  <span class="match">匹配1个</span>
                  <span class="amount">$10</span>
                </div>
                <div class="prize-item">
                  <span class="match">匹配2个</span>
                  <span class="amount">$50</span>
                </div>
                <div class="prize-item">
                  <span class="match">匹配3个</span>
                  <span class="amount">$200</span>
                </div>
                <div class="prize-item">
                  <span class="match">匹配4个</span>
                  <span class="amount">$1,000</span>
                </div>
                <div class="prize-item">
                  <span class="match">匹配5个</span>
                  <span class="amount">$5,000</span>
                </div>
                <div class="prize-item">
                  <span class="match">匹配6个</span>
                  <span class="amount">$50,000</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 右侧游戏区 -->
        <div class="game-section">
          <!-- 刮刮区域 -->
          <div class="scratch-area">
            <canvas 
              ref="mainCanvas"
              class="scratch-canvas"
              @mousedown="startScratch"
              @mousemove="scratch"
              @mouseup="stopScratch"
              @mouseleave="stopScratch"
            ></canvas>
            
            <!-- 刮开后显示的内容 -->
            <div class="game-numbers">
              <!-- 中奖号码 -->
              <div class="winning-section">
                <div class="section-title">中奖号码</div>
                <div class="numbers-grid">
                  <div 
                    v-for="(number, index) in winningNumbers" 
                    :key="index" 
                    class="number-ball winning"
                  >
                    {{ number }}
                  </div>
                </div>
              </div>

              <!-- 你的号码 -->
              <div class="player-section">
                <div class="section-title">你的号码</div>
                <div class="numbers-grid player-grid">
                  <div 
                    v-for="(item, index) in playerNumbers" 
                    :key="index" 
                    class="number-ball"
                    :class="{ 'match': winningNumbers.includes(item.number) }"
                  >
                    {{ item.number }}
                  </div>
                </div>
              </div>

              <!-- 倍数 -->
              <div class="multiplier-section">
                <div class="section-title">奖励倍数</div>
                <div class="multiplier-ball">{{ multiplier.value }}</div>
              </div>
            </div>
          </div>

          <!-- 游戏状态 -->
          <div class="game-status">
            <div class="progress-info">
              <span>刮开进度: {{ Math.round(scratchProgress) }}%</span>
              <div class="progress-bar">
                <div class="progress-fill" :style="{ width: scratchProgress + '%' }"></div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 附加控制区域 -->
      <div class="additional-section">
        <!-- 中奖结果 -->
        <div v-if="gameResult" class="result-display">
          <div v-if="gameResult.prizeAmount > 0" class="win-info">
            <div class="result-text">
              🎉 匹配 {{ gameResult.matchCount }} 个号码: {{ gameResult.matchingNumbers.join(', ') }}
            </div>
            <div v-if="gameResult.multiplier.multiplier > 1" class="multiplier-text">
              💫 倍数: {{ gameResult.multiplier.value }}
            </div>
            <div class="prize-display">
              💰 奖金: <span class="prize-amount">${{ gameResult.prizeAmount }}</span>
            </div>
          </div>
          <div v-else class="no-win-info">
            😔 很遗憾，这次没有中奖
          </div>
        </div>

        <!-- 控制按钮 -->
        <div class="game-controls">
          <button 
            v-if="scratchProgress > 20 && scratchProgress < 90 && !gameResult"
            class="control-btn reveal-btn" 
            @click="revealAll"
          >
            全部揭示
          </button>
          
          <button 
            v-if="gameResult"
            class="control-btn finish-btn" 
            @click="finishGame"
          >
            {{ gameResult.prizeAmount > 0 ? '领取奖金' : '再来一次' }}
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, nextTick } from 'vue'
import { useEvents } from '@Composables/useEvents.js'

const events = useEvents()

// 游戏数据结构
interface PlayerNumber {
  number: number
  isWinner: boolean
  prizeValue: number
}

interface Multiplier {
  value: string
  multiplier: number
}

// DOM引用
const mainCanvas = ref<HTMLCanvasElement | null>(null)

// 游戏状态
const winningNumbers = ref<number[]>([])
const playerNumbers = ref<PlayerNumber[]>([])
const multiplier = ref<Multiplier>({ value: '1X', multiplier: 1 })
const gameResult = ref<any>(null)
const scratchProgress = ref<number>(0)

// 控制状态
let isScratching = false
let isSettled = false  // 添加结算状态标志

// 票据信息
const ticketNumber = ref('EG' + Date.now().toString().slice(-8))
const validationCode = ref('VAL-' + Math.random().toString(36).substring(2, 8).toUpperCase())

// Canvas配置
const canvasWidth = 600
const canvasHeight = 350
const brushRadius = 30

onMounted(async () => {
  await nextTick()
  
  events.on('scratch:initialize', (data: any) => {
    generateGameData(data)
    gameResult.value = null
    scratchProgress.value = 0
    nextTick(() => {
      initializeCanvas()
    })
  })
  
  events.on('scratch:settled', (result: any) => {
    gameResult.value = result
  })
})

function generateGameData(data: any) {
  if (data.winningNumbers && data.winningNumbers.length === 6) {
    winningNumbers.value = data.winningNumbers
  }
  
  if (data.playerNumbers && data.playerNumbers.length === 15) {
    playerNumbers.value = data.playerNumbers
  }
  
  if (data.multiplier) {
    multiplier.value = data.multiplier
  }
  
  if (data.ticketNumber) {
    ticketNumber.value = data.ticketNumber
  }
  if (data.validationCode) {
    validationCode.value = data.validationCode
  }
  
  // 重置结算状态
  isSettled = false
}

function initializeCanvas() {
  const canvas = mainCanvas.value
  if (!canvas) return
  
  canvas.width = canvasWidth
  canvas.height = canvasHeight
  
  const ctx = canvas.getContext('2d')
  if (!ctx) return
  
  drawScratchCoating(ctx)
}

function drawScratchCoating(ctx: CanvasRenderingContext2D) {
  // 创建银色涂层
  const gradient = ctx.createLinearGradient(0, 0, canvasWidth, canvasHeight)
  gradient.addColorStop(0, '#E0E0E0')
  gradient.addColorStop(0.5, '#C0C0C0')
  gradient.addColorStop(1, '#A0A0A0')
  
  ctx.fillStyle = gradient
  ctx.fillRect(0, 0, canvasWidth, canvasHeight)
  
  // 添加提示文字
  ctx.fillStyle = '#666666'
  ctx.font = 'bold 1.8vh Arial'
  ctx.textAlign = 'center'
  ctx.textBaseline = 'middle'
  ctx.fillText('刮开此区域', canvasWidth/2, canvasHeight/2 - 10)
  ctx.font = '1.2vh Arial'
  ctx.fillText('查看你的号码', canvasWidth/2, canvasHeight/2 + 15)
}

// 刮刮功能
function startScratch(e: MouseEvent) {
  e.preventDefault()
  isScratching = true
  performScratchAction(e)
}

function scratch(e: MouseEvent) {
  if (!isScratching) return
  e.preventDefault()
  performScratchAction(e)
}

function stopScratch() {
  isScratching = false
}

function performScratchAction(e: MouseEvent) {
  const canvas = mainCanvas.value
  if (!canvas) return
  
  const ctx = canvas.getContext('2d')
  if (!ctx) return
  
  const rect = canvas.getBoundingClientRect()
  const scaleX = canvas.width / rect.width
  const scaleY = canvas.height / rect.height
  
  const x = (e.clientX - rect.left) * scaleX
  const y = (e.clientY - rect.top) * scaleY
  
  scratchAt(ctx, x, y)
}

function scratchAt(ctx: CanvasRenderingContext2D, x: number, y: number) {
  ctx.globalCompositeOperation = 'destination-out'
  ctx.beginPath()
  ctx.arc(x, y, brushRadius, 0, 2 * Math.PI)
  ctx.fill()
  ctx.globalCompositeOperation = 'source-over'
  
  calculateScratchProgress(ctx)
}

function calculateScratchProgress(ctx: CanvasRenderingContext2D) {
  const imageData = ctx.getImageData(0, 0, canvasWidth, canvasHeight)
  const pixels = imageData.data
  let clearedPixels = 0
  
  for (let i = 3; i < pixels.length; i += 4) {
    if (pixels[i] === 0) clearedPixels++
  }
  
  const totalPixels = canvasWidth * canvasHeight
  const progress = (clearedPixels / totalPixels) * 100
  scratchProgress.value = progress
  
  if (progress >= 70 && !isSettled) {  // 添加 isSettled 检查
    isSettled = true  // 设置为已结算
    setTimeout(() => {
      events.emitServer('scratch:settle')
    }, 1000)
  }
}

function revealAll() {
  if (isSettled) return  // 如果已经结算过，直接返回
  
  scratchProgress.value = 100
  isSettled = true  // 设置为已结算
  
  const canvas = mainCanvas.value
  if (canvas) {
    const ctx = canvas.getContext('2d')
    if (ctx) {
      ctx.clearRect(0, 0, canvasWidth, canvasHeight)
    }
  }
  
  setTimeout(() => {
    events.emitServer('scratch:settle')
  }, 600)
}

function finishGame() {
  events.emitServer('scratch:close')
}
</script>

<style scoped>
.lottery-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  font-family: 'Arial', sans-serif;
  animation: slideIn 0.4s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: scale(0.9) translateY(-3vh);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

.lottery-container {
  width: 70vw;
  height: 95vh;
  display: flex;
  flex-direction: column;
  gap: 1vh;
}

.scratch-ticket {
  width: 100%;
  height: 85vh;
  background: linear-gradient(to bottom, #ffffff, #f8f8f8);
  border: 0.3vh solid #000000;
  border-radius: 1vh;
  box-shadow: 0 1vh 2vh rgba(0, 0, 0, 0.3);
  display: grid;
  grid-template-columns: 1fr 2.5fr;
  grid-template-rows: minmax(0, auto) minmax(0, auto) 1fr;
  grid-template-areas: 
    "header header"
    "title title"
    "info game";
  gap: 1.2vh;
  padding: 1.8vh;
  font-weight: bold;
}

/* 顶部标识 */
.ticket-header {
  grid-area: header;
  background: linear-gradient(135deg, #1e3c72, #2a5298);
  color: white;
  padding: 1.2vh 2vw;
  border-radius: 0.8vh;
  border: 0.2vh solid #000000;
}

.state-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.state-name {
  font-size: 2.4vh;
  font-weight: 900;
  letter-spacing: 0.1vw;
}

.game-info {
  display: flex;
  align-items: center;
}

.game-name {
  font-size: 2.2vh;
  font-weight: 900;
  background: rgba(255, 255, 255, 0.2);
  padding: 0.5vh 1.5vw;
  border-radius: 0.5vh;
}

/* 游戏标题 */
.game-title {
  grid-area: title;
  background: linear-gradient(45deg, #FF6B35, #F7931E);
  color: white;
  padding: 1.5vh 2vw;
  text-align: center;
  border-radius: 0.8vh;
  border: 0.2vh solid #000000;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.title-text {
  font-size: 3.5vh;
  font-weight: 900;
  text-shadow: 0.2vh 0.2vh 0 #000000;
}

.prize-text {
  font-size: 1.6vh;
  font-weight: 900;
  background: #000000;
  color: #FFD700;
  padding: 0.6vh 1.8vw;
  border-radius: 0.8vh;
  display: inline-block;
}

/* 左侧信息区 */
.info-section {
  grid-area: info;
  display: flex;
  flex-direction: column;
}

.info-card {
  background: #f8f9fa;
  border: 0.2vh solid #000000;
  border-radius: 0.8vh;
  overflow: hidden;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.card-title {
  background: #FFD700;
  color: #000000;
  padding: 1vh 1.5vw;
  font-size: 1.4vh;
  font-weight: 900;
  text-align: center;
}

.card-content {
  padding: 1.2vh 1.5vw;
  font-size: 1.2vh;
  line-height: 1.3;
  color: #333333;
  font-weight: normal;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.game-instructions {
  background: rgba(255, 215, 0, 0.1);
  border: 0.1vh solid #FFD700;
  border-radius: 0.5vh;
  padding: 0.8vh 1.2vw;
  margin-bottom: 1.2vh;
  font-size: 1.1vh;
  color: #000000;
  font-weight: 600;
}

.prize-list {
  display: flex;
  flex-direction: column;
  gap: 0.6vh;
  flex: 1;
}

.prize-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: white;
  padding: 0.5vh 0.8vw;
  border: 0.1vh solid #000000;
  border-radius: 0.4vh;
}

.match {
  font-size: 1.1vh;
  font-weight: 900;
  color: #000000;
}

.amount {
  font-size: 1.2vh;
  font-weight: 900;
  color: #28a745;
}

/* 右侧游戏区 */
.game-section {
  grid-area: game;
  display: flex;
  flex-direction: column;
  gap: 1.5vh;
}

.scratch-area {
  position: relative;
  background: #ffffff;
  border: 0.2vh solid #000000;
  border-radius: 0.8vh;
  overflow: hidden;
  flex: 1;
}

.scratch-canvas {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  cursor: crosshair;
  z-index: 2;
}

.game-numbers {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-evenly;
  align-items: center;
  padding: 2vh;
  background: linear-gradient(135deg, #FFFFFF, #F0F0F0);
  z-index: 1;
}

.section-title {
  background: #007bff;
  color: white;
  padding: 0.8vh 1.8vw;
  font-size: 1.6vh;
  font-weight: 900;
  text-align: center;
  border-radius: 0.6vh;
  margin-bottom: 1.2vh;
}

.winning-section .section-title {
  background: #28a745;
}

.multiplier-section .section-title {
  background: #6c757d;
}

.numbers-grid {
  display: flex;
  justify-content: center;
  gap: 1vw;
  flex-wrap: wrap;
}

.player-grid {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  grid-template-rows: repeat(3, 1fr);
  gap: 1vh 1.5vw;
  justify-items: center;
  max-width: 35vw;
}

.number-ball {
  background: linear-gradient(135deg, #FFFFFF, #F0F0F0);
  color: #000000;
  font-size: 1.8vh;
  font-weight: 900;
  width: 4.5vh;
  height: 4.5vh;
  border: 0.2vh solid #000000;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 0.4vh 0.8vh rgba(0, 0, 0, 0.25);
}

.number-ball.winning {
  background: linear-gradient(135deg, #28a745, #20c997);
  color: white;
}

.number-ball.match {
  background: linear-gradient(135deg, #FFD700, #FFA500);
  transform: scale(1.1);
  border-color: #FF6B35;
}

.multiplier-ball {
  background: linear-gradient(135deg, #FFF3CD, #FFE69C);
  color: #000000;
  font-size: 2.2vh;
  font-weight: 900;
  width: 7vh;
  height: 4vh;
  border: 0.2vh solid #000000;
  border-radius: 1vh;
  display: flex;
  align-items: center;
  justify-content: center;
}

.game-status {
  background: #f8f9fa;
  border: 0.2vh solid #000000;
  border-radius: 0.8vh;
  padding: 2vh 2.5vw;
}

.progress-info {
  font-size: 1.6vh;
  font-weight: 900;
  color: #000000;
  text-align: center;
  margin-bottom: 1vh;
}

.progress-bar {
  width: 100%;
  height: 0.8vh;
  background: #000000;
  border-radius: 0.4vh;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(to right, #FF6B35, #F7931E);
  border-radius: 0.4vh;
  transition: width 0.3s ease;
}

/* 附加控制区域 */
.additional-section {
  width: 100%;
  height: 9vh;
  display: flex;
  flex-direction: column;
  gap: 0.8vh;
  justify-content: center;
}

.result-display {
  background: #e3f2fd;
  border: 0.2vh solid #2196f3;
  border-radius: 0.8vh;
  padding: 0.8vh 2vw;
  text-align: center;
  margin-bottom: 0.5vh;
}

.win-info {
  color: #1976d2;
}

.result-text {
  font-size: 1.4vh;
  font-weight: bold;
  margin-bottom: 0.4vh;
}

.multiplier-text {
  background: #f3e5f5;
  padding: 0.4vh 1.2vw;
  margin: 0.4vh 0;
  border-radius: 0.4vh;
  color: #7b1fa2;
  font-weight: bold;
  font-size: 1.1vh;
}

.prize-display {
  font-size: 1.5vh;
  font-weight: bold;
  margin-top: 0.4vh;
}

.prize-amount {
  color: #28a745;
  font-size: 1.7vh;
}

.no-win-info {
  color: #666666;
  font-style: italic;
  font-size: 1.3vh;
}

.game-controls {
  display: flex;
  justify-content: center;
  gap: 2vw;
}

.control-btn {
  padding: 1.2vh 3vw;
  border: 0.2vh solid #000000;
  border-radius: 0.8vh;
  font-size: 1.6vh;
  font-weight: 900;
  cursor: pointer;
  transition: all 0.2s;
  background: #007bff;
  color: white;
}

.control-btn:hover {
  background: #0056b3;
  transform: translateY(-0.1vh);
}

.finish-btn {
  background: #28a745;
  font-size: 1.8vh;
  padding: 1.4vh 3.5vw;
}

.finish-btn:hover {
  background: #1e7e34;
}
</style> 