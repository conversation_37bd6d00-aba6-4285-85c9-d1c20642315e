import { useRebarClient } from '@Client/index.js';
import * as alt from 'alt-client';

const Rebar = useRebarClient();
const webview = Rebar.webview.useWebview();
const messenger = Rebar.messenger.useMessenger();

// 创建一个全局计时器管理器，用于统一管理所有客户端计时器任务
export class GlobalTimerManagerClient {
    private static instance: GlobalTimerManagerClient;
    private tasks: Map<string, { callback: Function, time: number, currentTick: number }>;
    private interval: number | null = null;

    private constructor() {
        this.tasks = new Map();
    }

    public static getInstance(): GlobalTimerManagerClient {
        if (!GlobalTimerManagerClient.instance) {
            GlobalTimerManagerClient.instance = new GlobalTimerManagerClient();
        }
        return GlobalTimerManagerClient.instance;
    }

    /**
     * 生成唯一ID
     * @returns 生成的唯一ID字符串
     */
    private generateUniqueId(): string {
        const timestamp = Date.now().toString(36);
        const randomStr = Math.random().toString(36).substring(2, 10);
        let uniqueId = `task_${timestamp}_${randomStr}`;
        
        // 确保ID不重复
        while (this.tasks.has(uniqueId)) {
            uniqueId = `task_${Date.now().toString(36)}_${Math.random().toString(36).substring(2, 10)}`;
        }
        
        return uniqueId;
    }

    /**
     * 初始化全局计时器（如果尚未初始化）
     */
    private initTimer() {
        if (this.interval === null) {
            this.interval = alt.setInterval(() => {
                // 执行所有注册的任务
                for (const [id, taskInfo] of this.tasks.entries()) {
                    try {
                        // 增加当前计时器的tick
                        taskInfo.currentTick++;
                        
                        // 当累计的tick达到设定的时间时，执行回调并重置计数器
                        if (taskInfo.currentTick >= taskInfo.time) {
                            taskInfo.callback();
                            taskInfo.currentTick = 0; // 重置计数器
                        }
                    } catch (error) {
                        console.error('客户端全局计时器任务执行错误:', error);
                    }
                }
            }, 1);
        }
    }

    /**
     * 添加一个任务到全局计时器（兼容旧接口）
     * @param idOrCallback 任务ID或回调函数
     * @param callbackOrTime 回调函数或执行间隔
     * @param timeOrUndefined 可选的执行间隔
     * @returns 任务ID或null
     */
    public addTask(idOrCallback: string | Function, callbackOrTime?: Function | number, timeOrUndefined?: number): string | null {
        // 新接口: addTask(callback, time?, id?)
        if (typeof idOrCallback === 'function') {
            const callback = idOrCallback;
            const time = typeof callbackOrTime === 'number' ? callbackOrTime : 1;
            const id = typeof callbackOrTime === 'string' ? callbackOrTime : undefined;
            
            return this.addTaskInternal(callback, time, id);
        } 
        // 旧接口: addTask(id, callback, time?)
        else if (typeof idOrCallback === 'string' && typeof callbackOrTime === 'function') {
            const id = idOrCallback;
            const callback = callbackOrTime;
            const time = typeof timeOrUndefined === 'number' ? timeOrUndefined : 1;
            
            return this.addTaskInternal(callback, time, id);
        }
        
        return null;
    }
    
    /**
     * 内部添加任务的实现
     */
    private addTaskInternal(callback: Function, time: number = 1, id?: string): string {
        // 如果没有提供ID，则生成一个唯一ID
        const taskId = id || this.generateUniqueId();
        
        // 如果ID已存在，且是手动指定的ID，则返回null表示添加失败
        if (id && this.tasks.has(taskId)) {
            return null;
        }

        this.tasks.set(taskId, {
            callback,
            time,
            currentTick: 0
        });
        
        this.initTimer(); // 确保计时器已启动
        return taskId;
    }

    /**
     * 移除指定的任务
     * @param id 任务唯一标识
     * @returns 是否成功移除
     */
    public removeTask(id: string): boolean {
        const result = this.tasks.delete(id);
        
        // 如果没有任务了，清除计时器
        if (this.tasks.size === 0 && this.interval !== null) {
            alt.clearInterval(this.interval);
            this.interval = null;
        }
        
        return result;
    }

    /**
     * 检查任务是否存在
     * @param id 任务唯一标识
     * @returns 任务是否存在
     */
    public hasTask(id: string): boolean {
        return this.tasks.has(id);
    }

    /**
     * 获取所有任务的ID
     * @returns 任务ID数组
     */
    public getAllTaskIds(): string[] {
        return Array.from(this.tasks.keys());
    }
    
    /**
     * 重置指定任务的计时器
     * @param id 任务唯一标识
     * @returns 是否成功重置
     */
    public resetTaskTick(id: string): boolean {
        if (!this.tasks.has(id)) {
            return false;
        }
        
        const taskInfo = this.tasks.get(id);
        taskInfo.currentTick = 0;
        return true;
    }
    
    /**
     * 修改任务的执行间隔
     * @param id 任务唯一标识
     * @param newTime 新的执行间隔（秒）
     * @returns 是否成功修改
     */
    public updateTaskTime(id: string, newTime: number): boolean {
        if (!this.tasks.has(id)) {
            return false;
        }
        
        const taskInfo = this.tasks.get(id);
        taskInfo.time = newTime;
        return true;
    }
}

// 导出单例实例，方便外部使用
export const globalTimer = GlobalTimerManagerClient.getInstance();


