import { useRebarClient } from '@Client/index.js';
import { useWebview } from '@Client/webview/index.js';
import { getDirectionFromRotation } from '@Client/utility/math/index.js';
import { drawText2D, drawText3D } from '@Client/screen/textlabel.js';
import * as natives from 'natives';
import * as alt from 'alt-client';

const Rebar = useRebarClient();
const webview = Rebar.webview.useWebview();
const messenger = Rebar.messenger.useMessenger();

alt.onServer('customcutscene', (name: string) => {
    playCutscene(name)
})



async function playCutscene(name: string) {
    const playerId = natives.playerPedId();

    // ① 画面淡出 + 冻结玩家
    natives.doScreenFadeOut(500);
    natives.freezeEntityPosition(alt.Player.local.scriptID, true);
    await alt.Utils.wait(600);

    // ② 根据角色性别申请 cutscene 资源
    if (natives.isPedMale(playerId)) {
        natives.requestCutsceneWithPlaybackList(name, 31, 8);
    } else {
        natives.requestCutsceneWithPlaybackList(name, 103, 8);
    }
    
    // 等待 cutscene 加载完成
    while (!natives.hasThisCutsceneLoaded(name)) {
        await alt.Utils.wait(10);
    }

    // ③ 播放 cutscene
    natives.startCutscene(4); // 使用参数4而不是0
    natives.doScreenFadeIn(1000);

    // ④ 等待 cutscene 结束并清理资源
    const checkCutsceneEnd = alt.setInterval(() => {
        if (!natives.isCutscenePlaying()) {
            // Cutscene 已结束，清理资源
            natives.removeCutscene();
            natives.freezeEntityPosition(alt.Player.local.scriptID, false);
            alt.clearInterval(checkCutsceneEnd);
            
            // 确保画面正常显示
            if (natives.isScreenFadedOut()) {
                natives.doScreenFadeIn(1000);
            }
        }
    }, 100); // 每100ms检查一次

    // 设置超时保护，防止无限等待
    alt.setTimeout(() => {
        if (natives.isCutscenePlaying()) {
            natives.stopCutsceneImmediately();
            natives.removeCutscene();
            natives.freezeEntityPosition(alt.Player.local.scriptID, false);
            natives.doScreenFadeIn(1000);
            alt.clearInterval(checkCutsceneEnd);
                  }
    }, 60000); // 60秒超时
}
