<template>
  <div class="activity-logs-container">
    <div class="card">
      <div class="card-header">
        <svg viewBox="0 0 24 24">
          <path d="M19,3H14.82C14.4,1.84 13.3,1 12,1C10.7,1 9.6,1.84 9.18,3H5A2,2 0 0,0 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19V5A2,2 0 0,0 19,3M12,3A1,1 0 0,1 13,4A1,1 0 0,1 12,5A1,1 0 0,1 11,4A1,1 0 0,1 12,3M7,7H17V5H19V19H5V5H7V7M17,11H7V9H17V11M15,15H7V13H15V15Z" />
        </svg>
        <h2>活动日志</h2>
      </div>
      
      <div class="logs-container">
        <div v-if="reversedLogs.length > 0" class="logs-list">
          <div v-for="log in reversedLogs" :key="log.timestamp" class="log-item">
            <div class="log-time">
              <svg viewBox="0 0 24 24">
                <path d="M12,20A8,8 0 0,0 20,12A8,8 0 0,0 12,4A8,8 0 0,0 4,12A8,8 0 0,0 12,20M12,2A10,10 0 0,1 22,12A10,10 0 0,1 12,22C6.47,22 2,17.5 2,12A10,10 0 0,1 12,2M12.5,7V12.25L17,14.92L16.25,16.15L11,13V7H12.5Z" />
              </svg>
              <span>{{ formatDate(log.timestamp) }}</span>
            </div>
            
            <div class="log-content">
              <div class="log-action">{{ log.action }}</div>
              <div class="log-description">{{ log.description }}</div>
            </div>
          </div>
        </div>
        
        <div v-else class="empty-state">
          <svg viewBox="0 0 24 24">
            <path d="M19,3H14.82C14.4,1.84 13.3,1 12,1C10.7,1 9.6,1.84 9.18,3H5A2,2 0 0,0 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19V5A2,2 0 0,0 19,3M12,3A1,1 0 0,1 13,4A1,1 0 0,1 12,5A1,1 0 0,1 11,4A1,1 0 0,1 12,3M7,7H17V5H19V19H5V5H7V7Z" />
          </svg>
          <p>暂无活动记录</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { computed, PropType } from 'vue';

const props = defineProps({
  factionData: {
    type: Object,
    required: true
  },
  permissions: {
    type: Array,
    required: true
  },
  hasPermission: {
    type: Function,
    required: true
  },
  formatDate: {
    type: Function,
    required: true
  }
});

// 反转日志顺序，最新的显示在前面
const reversedLogs = computed(() => {
  if (!props.factionData?.activityLogs) return [];
  return [...props.factionData.activityLogs].reverse();
});
</script>

<style scoped>
.activity-logs-container {
  height: 100%;
  width: 100%;
}

.card {
  background: rgba(34, 42, 53, 0.7);
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  flex-direction: column;
  height: 100%;
}

.card-header {
  display: flex;
  align-items: center;
  padding: 1.8vh 2vh;
  background: rgba(26, 32, 44, 0.5);
  border-bottom: 1px solid rgba(255, 255, 255, 0.08);
}

.card-header svg {
  width: 2vh;
  height: 2vh;
  margin-right: 1vh;
  fill: #a995f4;
}

.card-header h2 {
  font-size: 1.6vh;
  font-weight: 600;
  color: #e2e8f0;
  margin: 0;
}

.logs-container {
  flex: 1;
  overflow: hidden;
  padding: 1vh;
}

.logs-list {
  max-height: 80vh;
  overflow-y: auto;
  padding-right: 0.5vh;
}

.logs-list::-webkit-scrollbar {
  width: 6px;
}

.logs-list::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 3px;
}

.logs-list::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 3px;
}

.logs-list::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.3);
}

.log-item {
  display: flex;
  flex-direction: column;
  padding: 1.5vh;
  margin-bottom: 1vh;
  background: rgba(26, 32, 44, 0.3);
  border-radius: 8px;
  border-left: 3px solid #a995f4;
}

.log-item:last-child {
  margin-bottom: 0;
}

.log-time {
  display: flex;
  align-items: center;
  font-size: 1.2vh;
  color: #a0aec0;
  margin-bottom: 1vh;
}

.log-time svg {
  width: 1.6vh;
  height: 1.6vh;
  fill: #a0aec0;
  margin-right: 0.5vh;
}

.log-content {
  display: flex;
  flex-direction: column;
}

.log-action {
  font-size: 1.5vh;
  font-weight: 600;
  color: #e2e8f0;
  margin-bottom: 0.5vh;
}

.log-description {
  font-size: 1.3vh;
  color: #cbd5e0;
  line-height: 1.5;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4vh 0;
  color: #a0aec0;
  height: 100%;
  min-height: 20vh;
}

.empty-state svg {
  width: 5vh;
  height: 5vh;
  fill: #a0aec0;
  margin-bottom: 1vh;
  opacity: 0.7;
}

.empty-state p {
  font-size: 1.4vh;
  margin: 0;
}


</style> 