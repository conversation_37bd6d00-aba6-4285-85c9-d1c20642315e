import { useRebarClient } from '@Client/index.js';
import { useWebview } from '@Client/webview/index.js';
import * as native from 'natives';
import * as alt from 'alt-client';
import { getDirectionFromRotation } from '@Client/utility/math/index.js';
import { getForwardVector } from '@Shared/utility/vector.js';

const Rebar = useRebarClient();
const webview = Rebar.webview.useWebview();
const messenger = Rebar.messenger.useMessenger();

// 注册命令处理钻头场景动画
alt.onServer('startDrillScene', async () => {
    await startDrillScene();
});

// 注册命令处理黄金抢夺场景动画
alt.onServer('startGoldGrabScene', async () => {
    await startGoldGrabScene();
});

// 注册命令处理海滩场景动画
alt.onServer('startBeachScene', async () => {
    await startBeachScene();
});

async function startDrillScene() {
    try {
        const dict = 'anim_heist@hs3f@ig9_vault_drill@drill@';

        // 加载动画字典
        await alt.Utils.requestAnimDict(dict);

        // 加载模型
        await alt.Utils.requestModel('hei_prop_heist_drill');
        await alt.Utils.requestModel('hei_p_m_bag_var22_arm_s');

        const playerPed = alt.Player.local.scriptID;
        const playerPos = alt.Player.local.pos;
        const playerRotation = native.getEntityRotation(playerPed, 2);

        // 创建钻头和背包对象
        const drill = native.createObject(
            native.getHashKey('hei_prop_heist_drill'),
            playerPos.x,
            playerPos.y,
            playerPos.z,
            true,
            true,
            true
        );

        const bag = native.createObject(
            native.getHashKey('hei_p_m_bag_var22_arm_s'),
            playerPos.x,
            playerPos.y,
            playerPos.z,
            true,
            true,
            true
        );

        // 创建网络同步场景
        // 注意：添加0.17到z坐标以防止玩家进入地下
        const scene = native.networkCreateSynchronisedScene(
            playerPos.x,
            playerPos.y,
            playerPos.z + 0.17,
            playerRotation.x,
            playerRotation.y,
            playerRotation.z,
            2,
            false,
            false,
            -1,
            0,
            1.0
        );

        // 添加玩家到同步场景
        native.networkAddPedToSynchronisedScene(
            playerPed,
            scene,
            dict,
            'intro',
            1.5,
            -4.0,
            1,
            16,
            1148846080,
            0
        );

        // 添加钻头到同步场景
        native.networkAddEntityToSynchronisedScene(
            drill,
            scene,
            dict,
            'intro_drill_bit',
            1.0,
            1.0,
            1
        );

        // 添加背包到同步场景
        native.networkAddEntityToSynchronisedScene(
            bag,
            scene,
            dict,
            'bag_intro',
            1.0,
            1.0,
            1
        );

        // 添加场景摄像头
        native.networkAddSynchronisedSceneCamera(scene, dict, 'intro_cam');

        // 开始同步场景
        native.networkStartSynchronisedScene(scene);

        // 等待动画完成
        const animDuration = native.getAnimDuration(dict, 'intro') * 1000;
        await alt.Utils.wait(animDuration);

        // 停止场景并清理资源
        native.networkStopSynchronisedScene(scene);
        native.deleteObject(drill);
        native.deleteObject(bag);

        // 清理玩家任务
        native.clearPedTasksImmediately(playerPed);

    } catch (error) {
        console.error('钻头场景动画执行失败:', error);
    }
}

async function startGoldGrabScene() {
    try {
        const dict = 'anim@scripted@heist@ig1_table_grab@gold@male@';

        // 加载动画字典
        await alt.Utils.requestAnimDict(dict);

        // 加载模型
        await alt.Utils.requestModel('hei_p_m_bag_var22_arm_s');
        await alt.Utils.requestModel('h4_prop_h4_gold_stack_01a');

        const playerPed = alt.Player.local.scriptID;
        const playerPos = alt.Player.local.pos;
        const playerRotation = native.getEntityRotation(playerPed, 2);

        // 创建背包和黄金对象
        const bag = native.createObject(
            native.getHashKey('hei_p_m_bag_var22_arm_s'),
            playerPos.x,
            playerPos.y,
            playerPos.z,
            true,
            true,
            true
        );

        const gold = native.createObject(
            native.getHashKey('h4_prop_h4_gold_stack_01a'),
            playerPos.x,
            playerPos.y,
            playerPos.z,
            true,
            true,
            true
        );

        // 第一阶段：进入场景
        const introScene = native.networkCreateSynchronisedScene(
            playerPos.x,
            playerPos.y,
            playerPos.z - 0.05,
            playerRotation.x,
            playerRotation.y,
            playerRotation.z,
            2,
            true,
            false,
            -1,
            0,
            1.0
        );

        native.networkAddPedToSynchronisedScene(
            playerPed,
            introScene,
            dict,
            'enter',
            1.5,
            -4.0,
            1,
            16,
            1148846080,
            0
        );

        native.networkAddEntityToSynchronisedScene(
            bag,
            introScene,
            dict,
            'enter_bag',
            1.0,
            1.0,
            1
        );

        // 设置玩家服装组件（移除背包）
        native.setPedComponentVariation(playerPed, 5, 0, 0, 0);

        // 开始进入动画
        native.networkStartSynchronisedScene(introScene);
        const enterDuration = native.getAnimDuration(dict, 'enter') * 1000;
        await alt.Utils.wait(enterDuration);
        native.networkStopSynchronisedScene(introScene);

        // 第二阶段：抓取黄金场景
        const grabScene = native.networkCreateSynchronisedScene(
            playerPos.x,
            playerPos.y,
            playerPos.z - 0.05,
            playerRotation.x,
            playerRotation.y,
            playerRotation.z,
            2,
            true,
            false,
            -1,
            0,
            1.0
        );

        native.networkAddPedToSynchronisedScene(
            playerPed,
            grabScene,
            dict,
            'grab',
            1.5,
            -4.0,
            1,
            16,
            1148846080,
            0
        );

        native.networkAddEntityToSynchronisedScene(
            bag,
            grabScene,
            dict,
            'grab_bag',
            1.0,
            1.0,
            1
        );

        native.networkAddEntityToSynchronisedScene(
            gold,
            grabScene,
            dict,
            'grab_gold',
            1.0,
            1.0,
            1
        );

        // 开始抓取动画
        native.networkStartSynchronisedScene(grabScene);
        const grabDuration = native.getAnimDuration(dict, 'grab') * 1000;
        await alt.Utils.wait(grabDuration);
        native.networkStopSynchronisedScene(grabScene);

        // 删除黄金对象（已被抓取）
        native.deleteObject(gold);

        // 第三阶段：离开场景
        const exitScene = native.networkCreateSynchronisedScene(
            playerPos.x,
            playerPos.y,
            playerPos.z - 0.05,
            playerRotation.x,
            playerRotation.y,
            playerRotation.z,
            2,
            true,
            false,
            -1,
            0,
            1.0
        );

        native.networkAddPedToSynchronisedScene(
            playerPed,
            exitScene,
            dict,
            'exit',
            1.5,
            -4.0,
            1,
            16,
            1148846080,
            0
        );

        native.networkAddEntityToSynchronisedScene(
            bag,
            exitScene,
            dict,
            'exit_bag',
            1.0,
            1.0,
            1
        );

        // 开始离开动画
        native.networkStartSynchronisedScene(exitScene);
        const exitDuration = native.getAnimDuration(dict, 'exit') * 1000;
        await alt.Utils.wait(exitDuration);
        native.networkStopSynchronisedScene(exitScene);

        // 清理背包对象
        native.deleteObject(bag);

        // 清理玩家任务
        native.clearPedTasksImmediately(playerPed);

    } catch (error) {
        console.error('黄金抢夺场景动画执行失败:', error);
    }
}

async function startBeachScene() {
    try {

        const dict = 'anim@scripted@heist@ig25_beach@male@';

        // 加载动画字典
        await alt.Utils.requestAnimDict(dict);

        const playerPed = alt.Player.local.scriptID;
        const playerPos = new alt.Vector3(alt.Player.local.pos.x, alt.Player.local.pos.y, alt.Player.local.pos.z - 1);
        const playerHeading = native.getEntityHeading(playerPed);

        // 创建网络同步场景
        const scene = native.networkCreateSynchronisedScene(
            playerPos.x,
            playerPos.y,
            playerPos.z,
            0.0,
            0.0,
            playerHeading,
            2,
            false,
            false,
            8.0,
            1000.0,
            1.0
        );

        // 添加玩家到同步场景
        native.networkAddPedToSynchronisedScene(
            playerPed,
            scene,
            dict,
            'action',
            1000.0,
            8.0,
            0,
            0,
            1000.0,
            8192
        );

        // 添加同步场景摄像头
        native.networkAddSynchronisedSceneCamera(scene, dict, 'action_camera');

        // 开始同步场景
        native.networkStartSynchronisedScene(scene);

        // 等待动画完成
        const animDuration = native.getAnimDuration(dict, 'action') * 1000;
        await alt.Utils.wait(animDuration);

        // 停止场景
        native.networkStopSynchronisedScene(scene);

        // 清理玩家任务
        native.clearPedTasksImmediately(playerPed);

    } catch (error) {
        console.error('海滩场景动画执行失败:', error);
    }
}

// 导出函数供其他模块使用
export { startDrillScene, startGoldGrabScene, startBeachScene };