<template>
  <div class="announcements-container">
    <div class="card">
      <div class="card-header">
        <svg viewBox="0 0 24 24">
          <path d="M20,2H4A2,2 0 0,0 2,4V22L6,18H20A2,2 0 0,0 22,16V4A2,2 0 0,0 20,2M20,16H5.17L4,17.17V4H20V16M11,5H13V11H11V5M11,13H13V15H11V13Z" />
        </svg>
        <h2>公告栏</h2>
      </div>
      
      <!-- 添加公告表单 -->
      <div v-if="hasPermission('manage_members') || permissions.includes('admin')" class="announcement-form">
        <textarea 
          v-model="announcementContent" 
          placeholder="输入公告内容..." 
          class="announcement-textarea"
        ></textarea>
        <button 
          @click="postAnnouncement" 
          class="post-btn"
          :disabled="!announcementContent.trim()"
        >
          <svg viewBox="0 0 24 24">
            <path d="M2,21L23,12L2,3V10L17,12L2,14V21Z" />
          </svg>
          <span>发布公告</span>
        </button>
      </div>
      
      <!-- 公告列表 -->
      <div class="announcements-list-container">
        <div v-if="sortedAnnouncements.length > 0" class="announcements-list">
          <div v-for="announcement in sortedAnnouncements" :key="announcement.id" class="announcement-item">
            <div class="announcement-content">{{ announcement.content }}</div>
            
            <div class="announcement-footer">
              <div class="announcement-info">
                <div class="announcement-author">
                  <svg viewBox="0 0 24 24">
                    <path d="M12,4A4,4 0 0,1 16,8A4,4 0 0,1 12,12A4,4 0 0,1 8,8A4,4 0 0,1 12,4M12,14C16.42,14 20,15.79 20,18V20H4V18C4,15.79 7.58,14 12,14Z" />
                  </svg>
                  <span>{{ announcement.author }}</span>
                </div>
                <div class="announcement-date">
                  <svg viewBox="0 0 24 24">
                    <path d="M12,20A8,8 0 0,0 20,12A8,8 0 0,0 12,4A8,8 0 0,0 4,12A8,8 0 0,0 12,20M12,2A10,10 0 0,1 22,12A10,10 0 0,1 12,22C6.47,22 2,17.5 2,12A10,10 0 0,1 12,2M12.5,7V12.25L17,14.92L16.25,16.15L11,13V7H12.5Z" />
                  </svg>
                  <span>{{ new Date(announcement.date).toLocaleString() }}</span>
                </div>
              </div>
              
              <button 
                v-if="hasPermission('manage_members') || permissions.includes('admin')"
                @click="deleteAnnouncement(announcement.id)" 
                class="delete-btn"
              >
                <svg viewBox="0 0 24 24">
                  <path d="M19,4H15.5L14.5,3H9.5L8.5,4H5V6H19M6,19A2,2 0 0,0 8,21H16A2,2 0 0,0 18,19V7H6V19Z" />
                </svg>
                <span>删除</span>
              </button>
            </div>
          </div>
        </div>
        
        <div v-else class="empty-state">
          <svg viewBox="0 0 24 24">
            <path d="M20,2H4A2,2 0 0,0 2,4V22L6,18H20A2,2 0 0,0 22,16V4A2,2 0 0,0 20,2M20,16H5.17L4,17.17V4H20V16M11,5H13V11H11V5M11,13H13V15H11V13Z" />
          </svg>
          <p>暂无公告内容</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, PropType } from 'vue';

const props = defineProps({
  factionData: {
    type: Object,
    required: true
  },
  permissions: {
    type: Array,
    required: true
  },
  hasPermission: {
    type: Function,
    required: true
  },
  postAnnouncementAction: {
    type: Function as PropType<(content: string) => Promise<boolean>>,
    required: true
  },
  deleteAnnouncementAction: {
    type: Function as PropType<(id: string) => Promise<boolean>>,
    required: true
  }
});

const announcementContent = ref('');

// 对公告按时间排序，最新的在前面
const sortedAnnouncements = computed(() => {
  if (!props.factionData?.announcements) return [];
  return [...props.factionData.announcements].sort((a, b) => b.date - a.date);
});

async function postAnnouncement() {
  if (!announcementContent.value.trim()) return;
  
  const success = await props.postAnnouncementAction(announcementContent.value);
  if (success) {
    announcementContent.value = '';
  }
}

function deleteAnnouncement(id) {
  return props.deleteAnnouncementAction(id);
}
</script>

<style scoped>
.announcements-container {
  height: 100%;
  width: 100%;
}

.card {
  background: rgba(34, 42, 53, 0.7);
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  flex-direction: column;
  height: 100%;
}

.card-header {
  display: flex;
  align-items: center;
  padding: 1.8vh 2vh;
  background: rgba(26, 32, 44, 0.5);
  border-bottom: 1px solid rgba(255, 255, 255, 0.08);
}

.card-header svg {
  width: 2vh;
  height: 2vh;
  margin-right: 1vh;
  fill: #a995f4;
}

.card-header h2 {
  font-size: 1.6vh;
  font-weight: 600;
  color: #e2e8f0;
  margin: 0;
}

/* 公告表单 */
.announcement-form {
  padding: 1.5vh;
  background: rgba(26, 32, 44, 0.3);
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
  display: flex;
  flex-direction: column;
}

.announcement-textarea {
  background: rgba(26, 32, 44, 0.5);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  color: #e2e8f0;
  font-size: 1.4vh;
  padding: 1.5vh;
  min-height: 10vh;
  resize: vertical;
  margin-bottom: 1vh;
  transition: border-color 0.2s;
}

.announcement-textarea:focus {
  outline: none;
  border-color: #a995f4;
  box-shadow: 0 0 0 1px rgba(169, 149, 244, 0.3);
}

.announcement-textarea::placeholder {
  color: #a0aec0;
  opacity: 0.7;
}

.post-btn {
  align-self: flex-end;
  display: flex;
  align-items: center;
  background: rgba(72, 187, 120, 0.2);
  color: #68d391;
  border: none;
  border-radius: 6px;
  padding: 1vh 2vh;
  font-size: 1.3vh;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.post-btn:hover:not(:disabled) {
  background: rgba(72, 187, 120, 0.3);
}

.post-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.post-btn svg {
  width: 1.6vh;
  height: 1.6vh;
  margin-right: 0.8vh;
  fill: #68d391;
}

/* 公告列表 */
.announcements-list-container {
  flex: 1;
  overflow: hidden;
  padding: 1vh;
}

.announcements-list {
  max-height: 60vh;
  overflow-y: auto;
  padding-right: 0.5vh;
}

.announcements-list::-webkit-scrollbar {
  width: 6px;
}

.announcements-list::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 3px;
}

.announcements-list::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 3px;
}

.announcements-list::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.3);
}

.announcement-item {
  padding: 1.8vh;
  margin-bottom: 1vh;
  background: rgba(26, 32, 44, 0.3);
  border-radius: 8px;
  border-left: 3px solid #68d391;
}

.announcement-item:last-child {
  margin-bottom: 0;
}

.announcement-content {
  font-size: 1.4vh;
  color: #e2e8f0;
  line-height: 1.6;
  white-space: pre-wrap;
  margin-bottom: 1.5vh;
}

.announcement-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.announcement-info {
  display: flex;
  flex-wrap: wrap;
  gap: 1.5vh;
}

.announcement-author, .announcement-date {
  display: flex;
  align-items: center;
  font-size: 1.1vh;
  color: #a0aec0;
}

.announcement-author svg, .announcement-date svg {
  width: 1.5vh;
  height: 1.5vh;
  margin-right: 0.5vh;
  fill: #a0aec0;
}

.delete-btn {
  display: flex;
  align-items: center;
  background: rgba(229, 62, 62, 0.1);
  color: #fc8181;
  border: none;
  border-radius: 6px;
  padding: 0.7vh 1.2vh;
  font-size: 1.2vh;
  cursor: pointer;
  transition: all 0.2s;
}

.delete-btn:hover {
  background: rgba(229, 62, 62, 0.2);
}

.delete-btn svg {
  width: 1.5vh;
  height: 1.5vh;
  margin-right: 0.5vh;
  fill: #fc8181;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4vh 0;
  color: #a0aec0;
  height: 100%;
  min-height: 20vh;
}

.empty-state svg {
  width: 5vh;
  height: 5vh;
  fill: #a0aec0;
  margin-bottom: 1vh;
  opacity: 0.7;
}

.empty-state p {
  font-size: 1.4vh;
  margin: 0;
}


</style> 