# 车辆转让系统更新

## 特殊车辆转让费用

### 功能说明
从现在开始，转让`addvehicles`列表中的特殊车辆需要额外支付**20信用点**。

### 特殊车辆列表
特殊车辆包括但不限于：
- 所有在 `src/plugins/veh/shared/addvehicle.ts` 中定义的车辆模型
- 这些车辆通常为限量版或特殊版本车辆

### 转让流程

1. **普通车辆转让**：
   - 只需支付正常的过户手续费（车辆价值的1%）
   - 交管局员工免费过户

2. **特殊车辆转让**：
   - 需要支付正常的过户手续费
   - **额外支付20信用点**
   - 交管局员工仍需支付信用点费用

### 界面显示
- 在转让对话框中，特殊车辆会显示金色星标图标
- 明确标注"特殊车辆转让费用: 20 信用点"
- 信用点不足时无法进行转让

### 注意事项
- 信用点从玩家账户扣除，不可退还
- 转让失败时信用点不会被扣除
- 建议在转让前确认信用点余额充足

### 技术实现
- 服务器端和客户端均使用`alt.hash()`将车辆模型名称转换为hash值进行对比
- 支持车辆模型字段为字符串或数字类型的情况
- 前端实时显示特殊车辆信息和费用
- 确保原子性操作，避免重复扣费

### 技术细节
由于addvehicles列表存储的是字符串形式的车辆模型名称（如'03sierra'），而车辆对象中的model字段通常是hash值（数字），因此需要：
1. 将addvehicles列表中的所有字符串转换为hash值
2. 统一车辆模型字段的类型处理
3. 使用hash值进行精确匹配 