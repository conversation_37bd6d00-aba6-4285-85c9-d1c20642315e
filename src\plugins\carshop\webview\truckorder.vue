<template>
  <div class="truck-order-sidebar">
    <!-- Right Sidebar -->
    <div :class="['sidebar', 'right-sidebar', { 'collapsed': !isMenuOpen }]">
      <button @click="toggleMenu" class="toggle-btn right">
        <i :class="['fas', isMenuOpen ? 'fa-chevron-right' : 'fa-chevron-left']"></i>
      </button>

      <div class="sidebar-content">
        <!-- 标题栏 -->
        <div class="sidebar-header">
          <div class="header-icon">
            <i class="fas fa-truck"></i>
          </div>
          <div class="title-info">
            <h1>进口货车订单</h1>
            <span class="subtitle">特殊货车进口服务</span>
          </div>
        </div>

        <!-- 当前周货车展示 -->
        <div v-if="!hasActiveOrder && currentTruck" class="truck-preview-card">
          <div class="card-header">
            <h4 class="truck-title">本周特供货车</h4>
          </div>
          
          <div class="truck-info">
            <div class="truck-model">{{ currentTruck.currentTruckModel }}</div>
            <div class="truck-details">
              <div class="detail-item">
                <i class="fas fa-gas-pump"></i>
                <span>油箱: 120L</span>
              </div>
              <div class="detail-item">
                <i class="fas fa-boxes"></i>
                <span>存储: 1000</span>
              </div>
              <div class="detail-item">
                <i class="fas fa-key"></i>
                <span>包含钥匙</span>
              </div>
            </div>
          </div>
          
          <div class="price-section">
            <div class="price">¥100,000</div>
            <div class="price-label">订单费用</div>
            <button @click="previewTruck" class="preview-btn">
              <i class="fas fa-eye"></i>
              预览车辆
            </button>
          </div>

          <!-- 颜色配置 -->
          <div class="color-card">
            <div class="color-card-header">
              <div class="header-title">
                <i class="fas fa-palette"></i>
                <h5>颜色配置</h5>
              </div>
            </div>
            
            <div class="color-card-content">
              <div class="color-picker">
                <h6>主要颜色</h6>
                <div class="color-grid">
                  <div 
                    v-for="color in colors" 
                    :key="color.id" 
                    class="color-option"
                    :class="{ 'selected': primaryColor.id === color.id }" 
                    :style="{ backgroundColor: color.color }"
                    @click="selectPrimaryColor(color)" 
                    :title="color.name">
                  </div>
                </div>
              </div>

              <div class="color-picker">
                <h6>次要颜色</h6>
                <div class="color-grid">
                  <div 
                    v-for="color in colors" 
                    :key="color.id" 
                    class="color-option"
                    :class="{ 'selected': secondaryColor.id === color.id }" 
                    :style="{ backgroundColor: color.color }"
                    @click="selectSecondaryColor(color)" 
                    :title="color.name">
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="order-actions">
            <button @click="confirmOrder" class="order-btn primary" :disabled="submitting">
              <i class="fas fa-shopping-cart"></i>
              <span v-if="!submitting">提交订单</span>
              <span v-else>处理中...</span>
            </button>
            <div class="order-info">
              <p>订单提交后需要补齐材料才能获得车辆</p>
              <p>每个玩家同一时间只能有一个订单</p>
            </div>
          </div>
        </div>

        <!-- 当前订单状态 -->
        <div v-if="hasActiveOrder && currentOrder" class="order-status-card">
          <div class="card-header">
            <h4 class="status-title">当前订单</h4>
          </div>
          
          <div class="truck-model-display">{{ currentOrder.truckModel }}</div>

          <!-- 材料进度 -->
          <div class="materials-section">
            <div class="section-header">
              <h5 class="section-title">材料需求进度</h5>
              <button @click="submitAllMaterials" :disabled="!canSubmitAll || submittingAll" class="quick-submit-btn">
                <i class="fas fa-rocket"></i>
                <span v-if="!submittingAll">一键提交</span>
                <span v-else>提交中...</span>
              </button>
            </div>
            
            <div class="materials-list">
              <div v-for="(required, materialName) in currentOrder.materials" :key="materialName" class="material-card">
                <div class="material-header">
                  <div class="material-icon-container">
                    <img :src="getMaterialIcon(String(materialName))" :alt="String(materialName)" class="material-icon" loading="lazy" />
                  </div>
                  <div class="material-info">
                    <div class="material-name">{{ String(materialName) }}</div>
                    <div class="material-progress-text">
                      {{ getSubmittedAmount(String(materialName)) }} / {{ required }}
                    </div>
                    <div class="material-owned-text">
                      拥有: {{ playerMaterials[String(materialName)] || 0 }}
                    </div>
                  </div>
                </div>

                <div class="progress-bar">
                  <div class="progress-fill" :style="{ width: getProgressPercent(String(materialName)) + '%' }"></div>
                </div>

                <div class="material-actions">
                  <input 
                    v-model.number="submitAmounts[String(materialName)]" 
                    type="number" 
                    :min="1" 
                    :max="getMaxSubmittable(String(materialName))" 
                    :placeholder="getInputPlaceholder(String(materialName))" 
                    class="amount-input" 
                  />
                  <button 
                    @click="submitMaterial(String(materialName))" 
                    :disabled="!canSubmitMaterial(String(materialName))" 
                    class="submit-btn"
                  >
                    <i class="fas fa-plus"></i>
                    提交
                  </button>
                </div>
              </div>
            </div>

            <!-- 总体进度 -->
            <div class="overall-progress">
              <div class="progress-info">
                <span class="progress-label">总体进度</span>
                <span class="progress-text">{{ overallProgress.toFixed(1) }}%</span>
              </div>
              <div class="progress-bar large">
                <div class="progress-fill" :style="{ width: overallProgress + '%' }"></div>
              </div>
            </div>

            <!-- 订单操作 -->
            <div class="order-final-actions">
              <button v-if="isOrderComplete" @click="claimTruck" class="action-btn primary" :disabled="claiming">
                <i class="fas fa-key"></i>
                <span v-if="!claiming">获得货车</span>
                <span v-else>生成中...</span>
              </button>
              <button @click="cancelOrder" class="action-btn danger" :disabled="cancelling">
                <i class="fas fa-trash"></i>
                <span v-if="!cancelling">取消订单</span>
                <span v-else>取消中...</span>
              </button>
            </div>

            <div class="cancel-info">
              <p>取消订单会退还订单费用但不退还已提交的材料</p>
            </div>
          </div>
        </div>

        <!-- 无当前货车时的状态 -->
        <div v-if="!currentTruck && !hasActiveOrder" class="no-truck-section">
          <div class="no-truck-card">
            <div class="no-truck-icon">
              <i class="fas fa-truck"></i>
            </div>
            <h4>暂无可用货车</h4>
            <p>请等待新的进口货车到货</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 关闭按钮 -->
    <button @click="closePanel" class="close-btn">
      <i class="fas fa-times"></i>
    </button>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted, onUnmounted, onBeforeUnmount, watch } from 'vue'
import { useEvents } from '@Composables/useEvents.js'
import { findIcon } from '../../inventory/shared/findicon.js';

const events = useEvents()

// 颜色选项数据（从dealshop.vue复制）
const colors = [
  { id: 0, name: 'Metallic Black', color: '#0d1116' },
  { id: 5, name: 'Metallic Blue Silver', color: '#c2c4c6' },
  { id: 27, name: 'Metallic Red', color: '#c00e1a' },
  { id: 37, name: 'Metallic Classic Gold', color: '#c2944f' },
  { id: 38, name: 'Metallic Orange', color: '#f78616' },
  { id: 53, name: 'Metallic Green', color: '#155c2d' },
  { id: 55, name: 'Matte Lime Green', color: '#66b81f' },
  { id: 60, name: 'Worn Sea Wash', color: '#65867f' },
  { id: 64, name: 'Metallic Blue', color: '#47578f' },
  { id: 70, name: 'Metallic Bright Blue', color: '#0b9cf1' },
  { id: 101, name: 'Metallic Biston Brown', color: '#402e2b' },
  { id: 106, name: 'Metallic Sun Bleeched Sand', color: '#dfd5b2' },
  { id: 137, name: 'Metallic Vermillion Pink', color: '#df5891' },
  { id: 145, name: 'Metallic Purple', color: '#621276' },
]

// 材料图标映射表
const materialIcons: { [key: string]: string } = {
  '钢锭': 'steelplate.webp',
  '铜锭': 'copper.webp',
  '铁锭': 'iron.webp',
  '铝锭': 'aluminum.webp',
  '润滑油': 'lubricant.webp',
  '塑料': 'plastic.webp',
  '玻璃': 'glassmesh.webp',
  '催化剂': 'catalyst.webp'
}

// 获取材料图标路径
function getMaterialIcon(materialName: string): string {
  const iconName = materialIcons[materialName]
  return iconName ? findIcon(iconName) : findIcon('default.webp')
}

// 响应式数据
const currentTruck = ref<any>(null)
const currentOrder = ref<any>(null)
const hasActiveOrder = ref(false)
const playerMaterials = ref<{ [key: string]: number }>({})
const submitAmounts = ref<{ [key: string]: number }>({})
const submitting = ref(false)
const claiming = ref(false)
const cancelling = ref(false)
const submittingAll = ref(false)
const isMenuOpen = ref(true) // 控制侧边栏展开/收起

// 颜色选择相关
const primaryColor = ref(colors[0])
const secondaryColor = ref(colors[0])

// 计算属性
const overallProgress = computed(() => {
  if (!currentOrder.value) return 0

  const materials = currentOrder.value.materials
  const submitted = currentOrder.value.materialsSubmitted || {}

  let totalProgress = 0
  let materialCount = 0

  for (const [material, required] of Object.entries(materials)) {
    const submittedAmount = submitted[material] || 0
    const progress = Math.min((submittedAmount / (required as number)) * 100, 100)
    totalProgress += progress
    materialCount++
  }

  return materialCount > 0 ? totalProgress / materialCount : 0
})

const isOrderComplete = computed(() => {
  if (!currentOrder.value) return false

  const materials = currentOrder.value.materials
  const submitted = currentOrder.value.materialsSubmitted || {}

  for (const [material, required] of Object.entries(materials)) {
    if ((submitted[material] || 0) < (required as number)) {
      return false
    }
  }

  return true
})

// 计算是否可以一键提交
const canSubmitAll = computed(() => {
  if (!currentOrder.value || !playerMaterials.value) return false

  const materials = currentOrder.value.materials
  const submitted = currentOrder.value.materialsSubmitted || {}

  // 检查是否有任何材料可以提交
  for (const [material, required] of Object.entries(materials)) {
    const alreadySubmitted = submitted[material] || 0
    const stillNeeded = (required as number) - alreadySubmitted
    const playerHas = playerMaterials.value[material] || 0
    
    if (stillNeeded > 0 && playerHas > 0) {
      return true // 至少有一种材料可以提交
    }
  }

  return false
})

// 方法
function getSubmittedAmount(materialName: string): number {
  if (!currentOrder.value) return 0
  return currentOrder.value.materialsSubmitted?.[materialName] || 0
}

function getProgressPercent(materialName: string): number {
  if (!currentOrder.value) return 0
  const required = currentOrder.value.materials[materialName]
  const submitted = getSubmittedAmount(materialName)
  return Math.min((submitted / required) * 100, 100)
}

function getMaxSubmittable(materialName: string): number {
  if (!currentOrder.value) return 0
  const required = currentOrder.value.materials[materialName]
  const submitted = getSubmittedAmount(materialName)
  const remaining = required - submitted
  
  // 获取玩家背包中的材料数量
  const playerAmount = playerMaterials.value[materialName] || 0
  
  // 返回玩家拥有的数量和还需要的数量中的较小值
  return Math.max(0, Math.min(remaining, playerAmount))
}

function getInputPlaceholder(materialName: string): string {
  const maxAmount = getMaxSubmittable(materialName)
  const playerAmount = playerMaterials.value[materialName] || 0
  
  if (playerAmount === 0) {
    return '背包中无此材料'
  } else if (maxAmount === 0) {
    return '已完成'
  } else {
    return `最多 ${maxAmount}`
  }
}

function canSubmitMaterial(materialName: string): boolean {
  const amount = submitAmounts.value[materialName]
  return amount > 0 && amount <= getMaxSubmittable(materialName)
}

function previewTruck() {
  if (currentTruck.value) {
    events.emitClient('previewvehicle', currentTruck.value.currentTruckModel, primaryColor.value.id, secondaryColor.value.id)
  }
}


function confirmOrder() {
  submitting.value = true
  events.emitServer('truck:confirmOrder')
}

function submitMaterial(materialName: string) {
  const amount = submitAmounts.value[materialName]
  if (amount > 0) {
    events.emitServer('truck:submitMaterials', materialName, amount)
    submitAmounts.value[materialName] = 0
  }
}

// 一键提交所有可提交的材料
function submitAllMaterials() {
  if (!currentOrder.value || !playerMaterials.value) return
  
  submittingAll.value = true
  
  const materials = currentOrder.value.materials
  const submitted = currentOrder.value.materialsSubmitted || {}
  const submissionList: { material: string, amount: number }[] = []
  
  // 计算每种材料可以提交的数量
  for (const [material, required] of Object.entries(materials)) {
    const alreadySubmitted = submitted[material] || 0
    const stillNeeded = (required as number) - alreadySubmitted
    const playerHas = playerMaterials.value[material] || 0
    
    if (stillNeeded > 0 && playerHas > 0) {
      const canSubmit = Math.min(stillNeeded, playerHas)
      if (canSubmit > 0) {
        submissionList.push({ material, amount: canSubmit })
      }
    }
  }
  
  // 发送批量提交请求到服务器
  if (submissionList.length > 0) {
    events.emitServer('truck:submitAllMaterials', submissionList)
  } else {
    submittingAll.value = false
  }
}

function claimTruck() {
  claiming.value = true
  events.emitServer('truck:claimVehicle')
}

function cancelOrder() {
  cancelling.value = true
  events.emitServer('truck:cancelOrder')
}

function closePanel() {
  // 关闭预览
  events.emitServer('truck:closePanel')
}

function toggleMenu() {
  isMenuOpen.value = !isMenuOpen.value
}

function selectPrimaryColor(color: any) {
  primaryColor.value = color
}

function selectSecondaryColor(color: any) {
  secondaryColor.value = color
}

// 监听颜色变化，实时更新预览
watch([primaryColor, secondaryColor], () => {
  // 如果有当前货车可预览
  if (currentTruck.value) {
    events.emitClient('previewvehicle', currentTruck.value.currentTruckModel, primaryColor.value.id, secondaryColor.value.id)
  }
  // 如果有当前订单的货车可预览
  else if (currentOrder.value) {
    events.emitClient('previewvehicle', currentOrder.value.truckModel, primaryColor.value.id, secondaryColor.value.id)
  }
})

// 事件监听
events.on('truck:updateData', (data: any) => {
  currentTruck.value = data.currentTruck
  currentOrder.value = data.currentOrder
  hasActiveOrder.value = !!data.currentOrder
  playerMaterials.value = data.playerMaterials || {}
  submitting.value = false
  claiming.value = false
  cancelling.value = false
  submittingAll.value = false
})

events.on('truck:orderSubmitted', () => {
  submitting.value = false
})

events.on('truck:materialSubmitted', () => {
  // 重新请求数据以更新材料数量
  events.emitServer('truck:requestData')
})

events.on('truck:allMaterialsSubmitted', () => {
  submittingAll.value = false
  // 重新请求数据以更新材料数量
  events.emitServer('truck:requestData')
})

events.on('truck:vehicleClaimed', () => {
  claiming.value = false
  hasActiveOrder.value = false
  currentOrder.value = null
})

events.on('truck:orderCancelled', () => {
  cancelling.value = false
  hasActiveOrder.value = false
  currentOrder.value = null
})

onMounted(() => {
  // 请求初始数据
  events.emitServer('truck:requestData')

  // 初始化提交数量
  submitAmounts.value = {}
})
</script>

<style scoped>
/* CSS变量定义 */
.truck-order-sidebar {
  --primary-color: #64ffda;
  --primary-hover: #ffffff;
  --primary-bg: #0f172a;
  --card-bg: rgba(30, 41, 59, 0.9);
  --card-bg-light: rgba(51, 65, 85, 0.8);
  --border-color: rgba(100, 255, 218, 0.2);
  --text-primary: #f8fafc;
  --text-secondary: #94a3b8;
  --text-muted: #64748b;
  --success: #64ffda;
  --warning: #a78bfa;
  --danger: #ef4444;
  --shadow-primary: 0 0 1vh rgba(100, 255, 218, 0.3);
  --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.1);

  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 100;
  pointer-events: none;
}

/* 侧边栏主体 */
.sidebar {
  position: fixed;
  top: 2vh;
  bottom: 2vh;
  width: 30vw;
  background: var(--card-bg);
  border: 1px solid var(--border-color);
  box-shadow: var(--shadow-primary);
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
  pointer-events: all;
}

.right-sidebar {
  right: 2vh;
  border-radius: 20px 0 0 20px;
}

.right-sidebar.collapsed {
  transform: translateX(30vw);
}

/* 切换按钮 */
.toggle-btn {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  width: 3vh;
  height: 12vh;
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--success) 100%);
  border: 1px solid var(--border-color);
  color: var(--primary-bg);
  font-size: 1.4vh;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: var(--shadow-primary);
  z-index: 10;
}

.toggle-btn:hover {
  background: linear-gradient(135deg, var(--primary-hover) 0%, var(--primary-color) 100%);
  box-shadow: var(--shadow-primary), 0 0 1vh rgba(100, 255, 218, 0.5);
  transform: translateY(-50%) scale(1.05);
}

.right-sidebar .toggle-btn {
  left: -3vh;
  border-radius: 12px 0 0 12px;
}

/* 侧边栏内容 */
.sidebar-content {
  height: 100%;
  padding: 1.5vh;
  overflow-y: auto;
  scrollbar-width: thin;
  scrollbar-color: var(--primary-color) rgba(255, 255, 255, 0.1);
  display: flex;
  flex-direction: column;
  gap: 1.5vh;
}

.sidebar-content::-webkit-scrollbar {
  width: 0.8vh;
}

.sidebar-content::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 0.4vh;
  margin: 0.4vh 0;
}

.sidebar-content::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--success) 100%);
  border-radius: 0.4vh;
  transition: background 0.3s ease;
}

/* 侧边栏标题 */
.sidebar-header {
  display: flex;
  align-items: center;
  gap: 1.2vh;
  padding: 1.5vh;
  background: linear-gradient(135deg, rgba(100, 255, 218, 0.05) 0%, transparent 60%);
  border: 1px solid var(--border-color);
  border-radius: 1.2vh;
  position: relative;
  overflow: hidden;
  flex-shrink: 0;
}

.header-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 4vh;
  height: 4vh;
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--success) 100%);
  border: 1px solid var(--border-color);
  border-radius: 50%;
  color: var(--primary-bg);
  font-size: 1.8vh;
  position: relative;
  z-index: 1;
  box-shadow: var(--shadow-sm);
}

.title-info {
  position: relative;
  z-index: 1;
}

.title-info h1 {
  color: var(--primary-color);
  font-size: 1.8vh;
  font-weight: 700;
  letter-spacing: 0.05em;
  margin: 0 0 0.2vh 0;
}

.subtitle {
  font-size: 1vh;
  color: var(--text-secondary);
  font-weight: 400;
  text-transform: uppercase;
  letter-spacing: 0.1em;
}

/* 卡片样式 */
.truck-preview-card,
.order-status-card {
  background: var(--card-bg-light);
  border: 1px solid var(--border-color);
  border-radius: 1.2vh;
  padding: 1.5vh;
  margin-bottom: 1vh;
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(1vh);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5vh;
  padding-bottom: 1vh;
  border-bottom: 1px solid var(--border-color);
}

.card-header h4,
.truck-title {
  margin: 0;
  font-size: 1.6vh;
  color: var(--text-primary);
  font-weight: 600;
}

/* 货车信息 */
.truck-info {
  margin-bottom: 1.5vh;
}

.truck-model {
  color: var(--primary-color);
  font-size: 2vh;
  font-weight: 700;
  margin-bottom: 1vh;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.truck-model-display {
  color: var(--primary-color);
  font-size: 2vh;
  font-weight: 700;
  margin-bottom: 1.5vh;
  text-transform: uppercase;
  letter-spacing: 1px;
  text-align: center;
}

.truck-details {
  display: flex;
  flex-direction: column;
  gap: 0.8vh;
}

.detail-item {
  display: flex;
  align-items: center;
  gap: 1vh;
  padding: 0.8vh 1vh;
  background: rgba(30, 41, 59, 0.5);
  border-radius: 0.8vh;
  border: 1px solid var(--border-color);
}

.detail-item i {
  color: var(--primary-color);
  font-size: 1.2vh;
  width: 1.8vh;
  text-align: center;
}

.detail-item span {
  color: var(--text-primary);
  font-size: 1.2vh;
  font-weight: 500;
}

/* 价格区域 */
.price-section {
  text-align: center;
  margin-bottom: 1.5vh;
  padding: 1.5vh;
  background: rgba(100, 255, 218, 0.05);
  border: 1px solid rgba(100, 255, 218, 0.1);
  border-radius: 1vh;
}

.price {
  color: var(--success);
  font-size: 2.5vh;
  font-weight: 700;
  margin-bottom: 0.5vh;
}

.price-label {
  color: var(--text-secondary);
  font-size: 1.2vh;
  margin-bottom: 1vh;
}

/* 按钮样式 */
.preview-btn,
.order-btn,
.action-btn {
  padding: 0.8vh 1.5vh;
  border-radius: 0.8vh;
  font-size: 1.2vh;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5vh;
  border: 1px solid transparent;
}

.preview-btn {
  background: rgba(100, 255, 218, 0.1);
  color: var(--primary-color);
  border-color: rgba(100, 255, 218, 0.3);
  width: 100%;
}

.preview-btn:hover {
  background: rgba(100, 255, 218, 0.2);
  border-color: var(--primary-color);
}

.order-btn.primary,
.action-btn.primary {
  background: var(--primary-color);
  color: var(--primary-bg);
  border-color: var(--primary-color);
  width: 100%;
}

.order-btn.primary:hover:not(:disabled),
.action-btn.primary:hover:not(:disabled) {
  background: var(--primary-hover);
  color: var(--primary-color);
  transform: translateY(-0.1vh);
}

.action-btn.danger {
  background: rgba(239, 68, 68, 0.1);
  color: var(--danger);
  border-color: rgba(239, 68, 68, 0.3);
  width: 100%;
}

.action-btn.danger:hover:not(:disabled) {
  background: rgba(239, 68, 68, 0.2);
  border-color: var(--danger);
  transform: translateY(-0.1vh);
}

.order-btn:disabled,
.action-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none !important;
}

/* 订单操作 */
.order-actions {
  display: flex;
  flex-direction: column;
  gap: 1vh;
}

.order-final-actions {
  display: flex;
  flex-direction: column;
  gap: 1vh;
  margin-bottom: 1.5vh;
}

.order-info {
  margin-top: 1vh;
  padding: 1vh;
  background: rgba(167, 139, 250, 0.1);
  border: 1px solid rgba(167, 139, 250, 0.2);
  border-radius: 0.8vh;
  color: var(--text-secondary);
}

.order-info p {
  margin: 0.5vh 0;
  font-size: 1.1vh;
  line-height: 1.4;
}

/* 材料相关 */
.materials-section {
  background: var(--card-bg);
  border-radius: 1vh;
  padding: 1.5vh;
  border: 1px solid var(--border-color);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1vh;
}

.section-header h5 {
  color: var(--text-primary);
  font-size: 1.4vh;
  font-weight: 600;
  margin: 0;
}

.quick-submit-btn {
  background: linear-gradient(135deg, var(--primary-color), #4facfe);
  color: var(--primary-bg);
  border: none;
  padding: 0.6vh 1vh;
  border-radius: 0.6vh;
  font-size: 1vh;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.4vh;
}

.quick-submit-btn:hover:not(:disabled) {
  background: linear-gradient(135deg, var(--primary-hover), #ffffff);
  color: var(--primary-color);
}

.quick-submit-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.materials-list {
  display: flex;
  flex-direction: column;
  gap: 1vh;
  margin-bottom: 1.5vh;
}

.material-card {
  background: rgba(30, 41, 59, 0.5);
  border: 1px solid var(--border-color);
  border-radius: 0.8vh;
  padding: 1vh;
  transition: all 0.3s ease;
}

.material-card:hover {
  border-color: rgba(100, 255, 218, 0.3);
  background: rgba(30, 41, 59, 0.7);
}

.material-header {
  display: flex;
  align-items: center;
  gap: 0.8vh;
  margin-bottom: 0.8vh;
}

.material-icon-container {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 3vh;
  height: 3vh;
  background: rgba(100, 255, 218, 0.1);
  border: 1px solid rgba(100, 255, 218, 0.2);
  border-radius: 0.6vh;
  flex-shrink: 0;
}

.material-icon {
  width: 2.5vh;
  height: 2.5vh;
  object-fit: contain;
}

.material-info {
  flex: 1;
}

.material-name {
  color: var(--text-primary);
  font-size: 1.2vh;
  font-weight: 600;
  margin-bottom: 0.2vh;
}

.material-progress-text {
  color: var(--text-secondary);
  font-size: 1vh;
  font-weight: 500;
}

.material-owned-text {
  color: var(--primary-color);
  font-size: 1vh;
  font-weight: 600;
  margin-top: 0.1vh;
}

.progress-bar {
  background: rgba(30, 41, 59, 0.8);
  border-radius: 0.4vh;
  height: 0.6vh;
  margin-bottom: 0.8vh;
  overflow: hidden;
}

.progress-bar.large {
  height: 1vh;
  margin-bottom: 0;
}

.progress-fill {
  background: linear-gradient(90deg, var(--primary-color), var(--success));
  height: 100%;
  transition: width 0.3s ease;
  border-radius: 0.4vh;
}

.material-actions {
  display: flex;
  gap: 0.8vh;
  align-items: center;
}

.amount-input {
  background: var(--primary-bg);
  border: 1px solid var(--border-color);
  color: var(--text-primary);
  padding: 0.6vh 0.8vh;
  border-radius: 0.6vh;
  width: 8vh;
  font-size: 1vh;
  transition: all 0.2s ease;
}

.amount-input:focus {
  outline: none;
  border-color: var(--primary-color);
  background: rgba(100, 255, 218, 0.05);
}

.submit-btn {
  background: var(--primary-color);
  color: var(--primary-bg);
  border: none;
  padding: 0.6vh 1vh;
  border-radius: 0.6vh;
  font-size: 1vh;
  font-weight: 600;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.4vh;
  transition: all 0.2s ease;
  min-width: 6vh;
}

.submit-btn:hover:not(:disabled) {
  background: var(--primary-hover);
  color: var(--primary-color);
}

.submit-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* 总体进度 */
.overall-progress {
  margin-bottom: 1.5vh;
  padding: 1vh;
  background: rgba(100, 255, 218, 0.05);
  border: 1px solid rgba(100, 255, 218, 0.1);
  border-radius: 0.8vh;
}

.progress-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.8vh;
}

.progress-label {
  color: var(--text-primary);
  font-size: 1.2vh;
  font-weight: 600;
}

.progress-text {
  color: var(--primary-color);
  font-size: 1.4vh;
  font-weight: 700;
}

.cancel-info {
  color: var(--text-muted);
  font-size: 1vh;
  line-height: 1.4;
}

.cancel-info p {
  margin: 0.5vh 0;
}

/* 无货车状态 */
.no-truck-section {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 20vh;
}

.no-truck-card {
  text-align: center;
  padding: 2vh;
}

.no-truck-icon {
  font-size: 4vh;
  color: var(--text-muted);
  margin-bottom: 1vh;
}

.no-truck-card h4 {
  color: var(--text-primary);
  font-size: 1.6vh;
  font-weight: 600;
  margin: 0 0 0.8vh 0;
}

.no-truck-card p {
  color: var(--text-secondary);
  font-size: 1.2vh;
  margin: 0;
  line-height: 1.4;
}

/* 关闭按钮 */
.close-btn {
  position: fixed;
  top: 2vh;
  right: 2vh;
  width: 4vh;
  height: 4vh;
  background: var(--card-bg);
  border: 1px solid var(--border-color);
  border-radius: 50%;
  color: var(--text-secondary);
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.4vh;
  z-index: 200;
  pointer-events: all;
}

.close-btn:hover {
  background: var(--danger);
  color: var(--text-primary);
  border-color: var(--danger);
}

/* 颜色选择器样式 */
.color-card {
  background: var(--card-bg);
  border: 1px solid var(--border-color);
  border-radius: 1vh;
  padding: 1.5vh;
  margin-top: 1.5vh;
  margin-bottom: 1.5vh;
}

.color-card-header {
  display: flex;
  align-items: center;
  gap: 0.8vh;
  margin-bottom: 1.5vh;
  padding-bottom: 1vh;
  border-bottom: 1px solid var(--border-color);
}

.header-title {
  display: flex;
  align-items: center;
  gap: 0.5vh;
}

.header-title h5 {
  color: var(--text-primary);
  font-size: 1.4vh;
  font-weight: 600;
  margin: 0;
}

.color-card-content {
  display: flex;
  flex-direction: column;
  gap: 1.5vh;
}

.color-picker {
  margin-bottom: 1.5vh;
}

.color-picker h6 {
  color: var(--text-primary);
  font-size: 1.2vh;
  font-weight: 600;
  margin-bottom: 0.8vh;
}

.color-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(3vh, 1fr));
  gap: 0.8vh;
}

.color-option {
  width: 3vh;
  height: 3vh;
  border-radius: 50%;
  cursor: pointer;
  border: 1px solid transparent;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: var(--shadow-sm);
}

.color-option:hover:not(.selected) {
  transform: scale(1.1);
  border-color: var(--primary-color);
}

.color-option.selected {
  border-color: var(--primary-color);
  box-shadow: var(--shadow-primary);
}
</style>