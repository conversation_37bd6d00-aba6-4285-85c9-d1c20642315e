:: --------------------------------------------------------------
::	CloudflareSpeedTest Auto Update Hosts
::	Version: 1.0.4 (Modified)
::	Author: XIU2
::	Project: https://github.com/XIU2/CloudflareSpeedTest
:: --------------------------------------------------------------
@echo off
chcp 936 >nul
Setlocal Enabledelayedexpansion

::检查管理员权限

>nul 2>&1 "%SYSTEMROOT%\system32\cacls.exe" "%SYSTEMROOT%\system32\config\system" 

if '%errorlevel%' NEQ '0' (  
    goto UACPrompt  
) else ( goto gotAdmin )  

::以管理员身份运行脚本

:UACPrompt  
    echo Set UAC = CreateObject^("Shell.Application"^) > "%temp%\getadmin.vbs" 
    echo UAC.ShellExecute "%~s0", "", "", "runas", 1 >> "%temp%\getadmin.vbs" 
    "%temp%\getadmin.vbs" 
    exit /B  

:gotAdmin  
    if exist "%temp%\getadmin.vbs" ( del "%temp%\getadmin.vbs" )  
    pushd "%CD%" 
    CD /D "%~dp0" 

echo 开始测速...

:RESET

:: 运行CloudflareST测速
echo.|CloudflareST.exe -o "result_hosts.txt"

:: 检查结果文件
if not exist result_hosts.txt (
    echo.
    echo 测速失败，请检查网络后重试...
    goto :STOP
)

:: 获取最快IP
for /f "tokens=1 delims=," %%i in (result_hosts.txt) do (
    SET /a n+=1 
    If !n!==2 (
        SET bestip=%%i
        goto :END
    )
)
:END

:: 检查IP是否有效
if "%bestip%"=="" (
    echo.
    echo 未获取到有效IP，请检查网络后重试...
    goto :STOP
)

echo.
echo 最快IP: %bestip%

CD /d "C:\Windows\System32\drivers\etc"
echo.
echo 备份hosts文件...
copy hosts hosts_backup >nul
echo.
echo 更新hosts文件...
(
    echo # Cloudflare CDN IP - Auto updated by CloudflareST
    echo %bestip% cloudflare.com
    echo %bestip% www.cloudflare.com
    echo %bestip% api.cloudflare.com
    echo %bestip% dash.cloudflare.com
    echo %bestip% workers.cloudflare.com
    echo %bestip% blog.cloudflare.com
    echo %bestip% support.cloudflare.com
    echo %bestip% community.cloudflare.com
    echo %bestip% developers.cloudflare.com
    echo %bestip% registry.npmjs.org
    echo %bestip% www.npmjs.com
    echo %bestip% npmjs.com
    echo.
    echo # Default Windows hosts entries
    echo 127.0.0.1       localhost
    echo ::1             localhost
)>hosts

echo 完成！
echo.
:STOP
pause 