# 道德值与撞鬼系统

这是一个基于道德值的撞鬼系统，玩家的道德值和周围环境会影响遇到超自然事件的概率。

## 功能特性

### 📊 道德值系统
- **道德值等级**：圣人(1601-2000) → 善人(1201-1600) → 好人(801-1200) → 普通人(601-800) → 坏人(401-600) → 恶人(201-400) → 极恶(0-200)
- **默认道德值**：700（普通人）
- **影响因素**：伤害他人、击杀他人会降低道德值

### 👻 撞鬼机制
撞鬼概率由以下因素决定：
- **基础概率**：0.1%（每分钟检查一次）
- **道德值影响**：道德值越低，遇鬼概率越高（极恶15倍，圣人不遇鬼）
- **时间影响**：夜晚3倍，黄昏2倍，黎明1.5倍，白天0.5倍
- **玩家数量影响**：40米范围内玩家越少概率越高
  - 独自一人：10倍概率
  - 附近1人：5倍概率  
  - 附近2人：2倍概率
  - 附近3人：正常概率
  - 附近4人：0.5倍概率
  - 附近5人：0.2倍概率
  - 附近6人及以上：0.1倍概率

### 🛡️ 防护道具系统
**三种防护道具可以抵挡撞鬼事件**：

#### 符箓 📜
- **描述**：道教符箓，可以抵挡一次撞鬼事件
- **优先级**：最高（优先消耗）
- **效果提示**："你的符箓开始自己燃烧了，保护了你免受邪灵侵扰"
- **重量**：0.1
- **最大堆叠**：10个

#### 十字架 ✝️
- **描述**：神圣的十字架，可以抵挡一次撞鬼事件
- **优先级**：中等
- **效果提示**："你的十字架断开了，但保护了你免受邪灵侵扰"
- **重量**：0.3
- **最大堆叠**：10个

#### 圣水 💧
- **描述**：神圣的水，可以抵挡一次撞鬼事件
- **优先级**：最低
- **效果提示**："你的圣水蒸发了，但保护了你免受邪灵侵扰"
- **重量**：0.5
- **最大堆叠**：10个

## 命令列表

### 玩家命令
- `/morality [playerId]` - 查看道德值
- `/ghostChance [playerId]` - 查看撞鬼概率和防护道具数量

### 管理员命令
- `/spawnGhost [playerId]` - 手动生成幽灵
- `/forceGhost [playerId]` - 强制触发撞鬼（忽略冷却）
- `/setMorality <playerId> <value>` - 设置道德值
- `/giveProtection <playerId> <itemName> [quantity]` - 给予防护道具
  - 可用道具：符箓, 十字架, 圣水

## 游戏机制

### 撞鬼流程
1. **第一次遇鬼**：幽灵在玩家背后生成，开始向玩家移动
2. **玩家转头**：幽灵会消失
3. **持续注视**：幽灵会不断接近，太近时自动消失
4. **第二次遇鬼**：幽灵在正前方生成，进入jumpscare模式
5. **jumpscare模式**：玩家无法控制，必须看着幽灵接近，直到触发jumpscare效果

### 防护机制
- 当撞鬼事件被触发时，系统会自动检查玩家是否拥有防护道具
- 按优先级顺序消耗：符箓 > 十字架 > 圣水
- 消耗一个防护道具后，撞鬼事件被阻止，但冷却时间仍然生效
- 玩家会收到相应的提示消息

### 冷却机制
- **冷却时间**：10分钟
- **冷却规则**：无论是正常撞鬼还是被防护道具阻止，都会触发冷却

## 技术实现

### 文件结构
```
src/plugins/morality/
├── client/index.ts          # 客户端幽灵生成和移动逻辑
├── server/index.ts          # 服务端道德值和撞鬼系统
├── shared/types.ts          # 共享类型定义
├── webview/ghostcatch.vue   # Jumpscare界面
├── sounds/                  # 音效文件
│   ├── ghostrun.ogg        # 追逐音效
│   ├── ghostyall.ogg       # 消失音效
│   └── ghostcatch.ogg      # 被抓音效
└── README.md               # 本文档
```

### API接口
- **道德值API**：MoralityAPI.getMorality(), setMorality(), addMorality()
- **背包API**：inventoryApi.hasitem(), subItem(), addItem()
- **防护道具配置**：PROTECTION_ITEMS数组，支持动态添加新道具

## 平衡性说明

### 道德值影响
- 极恶玩家（0-200）：15倍撞鬼概率，高风险高刺激
- 普通玩家（601-800）：正常概率，平衡体验
- 善良玩家（1201+）：极低概率，奖励良好行为
- 圣人玩家（1601+）：完全免疫，终极奖励

### 防护道具设计
- **稀缺性**：需要通过特定途径获得，不能随意刷取
- **选择性**：三种道具提供不同的RP体验
- **平衡性**：重量和堆叠限制防止过度囤积

### 社交机制
- 鼓励玩家聚集：人多时撞鬼概率显著降低
- 独狼惩罚：独自行动风险更高
- 团队合作：共同面对超自然威胁 