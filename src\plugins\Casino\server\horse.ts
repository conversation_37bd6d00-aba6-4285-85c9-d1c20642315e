import * as alt from 'alt-server';
import { useRebar } from '@Server/index.js';
import { PageNames } from '@Shared/webview/index.js';

const Rebar = useRebar();
const notifyapi = await Rebar.useApi().getAsync('notify-api')
const promptbarapi = await Rebar.useApi().getAsync('promptbar-api')
const inventoryapi = await Rebar.useApi().getAsync('inventory-api')


// 马匹数据接口
interface Horse {
    id: number;
    name: string;
    odds: number;
    weight: number;
}

// 生成基础马匹数据
function generateHorseData(): Horse[] {
    return [
        { id: 1, name: '雷霆闪电', odds: 2.5, weight: 35 },
        { id: 2, name: '烈焰之火', odds: 3.2, weight: 25 },
        { id: 3, name: '疾风骏马', odds: 4.1, weight: 20 },
        { id: 4, name: '风暴追逐者', odds: 5.5, weight: 15 },
        { id: 5, name: '北极风暴', odds: 6.8, weight: 10 }
    ];
}

// 赛马位置
const vectorarray = [
    new alt.Vector3(1097.98681640625, 257.24835205078125, -52.245361328125),
    new alt.Vector3(1098.6856689453125, 258, -52.245361328125),
    new alt.Vector3(1099.4901123046875, 258.73846435546875, -52.245361328125),
    new alt.Vector3(1103.037353515625, 262.20660400390625, -52.245361328125),
    new alt.Vector3(1094.7164306640625, 253.92527770996094, -52.245361328125)
]

// 创建交互点
vectorarray.forEach((vector, index) => {
    const interaction = Rebar.controllers.useInteraction(new alt.ColshapeCylinder(vector.x, vector.y, vector.z , 0.8, 2), 'player');
    
    interaction.onEnter((player) => {
        promptbarapi.showPromptBar(player, '赛马');
    });
    
    interaction.onLeave((player) => {
        promptbarapi.hidePromptBar(player);
    });
    
    interaction.on(async (player) => {
        await openHorseGame(player);
    });

    interaction.addMarker({
        pos: vector,
        color: new alt.RGBA(255, 255, 0, 75),
        type: 1,
        scale: new alt.Vector3(1, 1, 0.2),
    })
});

// 打开赛马游戏
async function openHorseGame(player: alt.Player) {
    try {
        // 显示webview
        Rebar.player.useWebview(player).show('horse', 'page', true);

        // 等待页面加载完成
        let ready = await Rebar.player.useWebview(player).isReady('horse', 'page')
        while (!ready) {
            await alt.Utils.wait(100);
            ready = await Rebar.player.useWebview(player).isReady('horse', 'page')
        }

        // 隐藏提示条
        promptbarapi.hidePromptBar(player);

        // 获取玩家筹码数量
        const playerChips = await inventoryapi.getitemquantity('筹码', { player: player });
        
        // 发送初始游戏数据
        Rebar.player.useWebview(player).emit('horse:gameData', {
            chips: playerChips,
            horses: generateHorseData() // 发送基础马匹数据
        });

    } catch (error) {
        console.error('打开赛马游戏失败:', error);
        notifyapi.shownotify(player, '打开游戏失败', 'error');
    }
}

// 处理下注
alt.onClient('horse:placeBet', async (player: alt.Player, bet: { horse: number, amount: number }) => {
    try {
        // 防止频繁下注
        const lastBetTime = Number(player.getStreamSyncedMeta('lastHorseBetTime')) || 0;
        const currentTime = Date.now();
        if (currentTime - lastBetTime < 3000) { // 3秒限制
            notifyapi.shownotify(player, '下注过于频繁，请稍后再试', 'error');
            return;
        }

        // 检查是否在游戏中
        if (player.getStreamSyncedMeta('isHorseGaming')) {
            notifyapi.shownotify(player, '正在进行比赛', 'error');
            return;
        }

        // 验证下注数据
        if (!bet || typeof bet.horse !== 'number' || typeof bet.amount !== 'number') {
            console.error(`赛马：无效的下注数据类型 - 玩家: ${player.getStreamSyncedMeta('name')}, 数据: ${JSON.stringify(bet)}`);
            notifyapi.shownotify(player, '无效的下注数据', 'error');
            return;
        }

        // 严格验证输入
        if (!Number.isFinite(bet.amount) || !Number.isInteger(bet.amount) || bet.amount <= 0) {
            console.error(`赛马：无效的下注金额 - 玩家: ${player.getStreamSyncedMeta('name')}, 金额: ${bet.amount}`);
            notifyapi.shownotify(player, '下注金额必须是有效的正整数', 'error');
            return;
        }

        if (!Number.isFinite(bet.horse) || !Number.isInteger(bet.horse) || bet.horse < 1 || bet.horse > 5) {
            console.error(`赛马：无效的马匹编号 - 玩家: ${player.getStreamSyncedMeta('name')}, 马匹: ${bet.horse}`);
            notifyapi.shownotify(player, '无效的马匹编号', 'error');
            return;
        }

        if (bet.amount < 50 || bet.amount > 50000) {
            notifyapi.shownotify(player, '下注金额必须在50-50000之间', 'error');
            return;
        }

        // 设置游戏状态
        player.setStreamSyncedMeta('isHorseGaming', true);
        player.setStreamSyncedMeta('lastHorseBetTime', currentTime);

        try {
            // 检查玩家是否有足够的筹码
            const hasEnoughChips = await inventoryapi.hasitem('筹码', bet.amount, { player: player });
            if (!hasEnoughChips) {
                notifyapi.shownotify(player, '筹码不足', 'error');
                const currentChips = await inventoryapi.getitemquantity('筹码', { player: player });
                Rebar.player.useWebview(player).emit('horse:updateChips', currentChips);
                return;
            }

            // 原子性操作：扣除筹码
            const [success, message] = await inventoryapi.subItem('筹码', bet.amount, { player: player });
            if (!success) {
                notifyapi.shownotify(player, message || '扣除筹码失败', 'error');
                return;
            }

            // 获取更新后的筹码数量
            const newChips = await inventoryapi.getitemquantity('筹码', { player: player });

            // 发送下注成功响应
            Rebar.player.useWebview(player).emit('horse:betPlaced', {
                success: true,
                bet: bet,
                newChips: newChips
            });

            notifyapi.shownotify(player, `成功下注${bet.amount}筹码在${bet.horse}号马`, 'success');
            //console.log(`赛马下注成功 - 玩家: ${player.getStreamSyncedMeta('name')}, 下注: ${bet.amount}筹码在${bet.horse}号马`);

            // 开始比赛倒计时
            setTimeout(async () => {
                await startRace(player, bet);
            }, 3000);

        } finally {
            // 延迟清理状态
            setTimeout(() => {
                if (player && player.valid) {
                    player.deleteStreamSyncedMeta('isHorseGaming');
                }
            }, 15000); // 比赛约需要10秒，加上缓冲时间
        }

    } catch (error) {
      //  console.error('赛马下注处理失败:', error);
        notifyapi.shownotify(player, '下注失败', 'error');
        player.deleteStreamSyncedMeta('isHorseGaming');
    }
});

// 开始比赛
async function startRace(player: alt.Player, bet: { horse: number, amount: number }) {
    try {
        const horses = generateHorseData();
        
        // 通知前端开始比赛
        Rebar.player.useWebview(player).emit('horse:raceStart', {
            horses: horses
        });

        const raceTime = 10000; // 固定10秒比赛时间
        const updateInterval = 500; // 0.5秒更新一次
        const totalUpdates = raceTime / updateInterval;
        
        // 随机决定获胜者，根据权重
        const totalWeight = horses.reduce((sum, horse) => sum + horse.weight, 0);
        const random = Math.random() * totalWeight;
        
        let currentWeight = 0;
        let winner = 1;
        
        for (const horse of horses) {
            currentWeight += horse.weight;
            if (random <= currentWeight) {
                winner = horse.id;
                break;
            }
        }

        // 模拟比赛进程
        let raceProgress = 0;
        
        const raceInterval = setInterval(() => {
            raceProgress++;
            // 计算每匹马的位置
            const horsePositions = horses.map(horse => {
                let baseProgress = (raceProgress / totalUpdates) * 100;
                
                // 获胜马在最后阶段会领先更多
                if (horse.id === winner) {
                    if (raceProgress > totalUpdates * 0.7) {
                        baseProgress += Math.random() * 15;
                    } else {
                        baseProgress += Math.random() * 8;
                    }
                } else {
                    baseProgress += Math.random() * 10 - 3;
                }
                
                // 确保不超过100%
                if (raceProgress === totalUpdates) {
                    if (horse.id === winner) {
                        return 100;
                    } else {
                        return Math.min(baseProgress, 98);
                    }
                }
                
                return Math.min(Math.max(baseProgress, 0), 99);
            });

            // 发送位置更新到前端
            Rebar.player.useWebview(player).emit('horse:positionUpdate', horsePositions);

            if (raceProgress >= totalUpdates) {
                clearInterval(raceInterval);
                finishRace(player, bet, winner, horses);
            }
        }, updateInterval);

    } catch (error) {
        console.error('比赛过程出错:', error);
        notifyapi.shownotify(player, '比赛出现错误', 'error');
    }
}

// 结束比赛
async function finishRace(player: alt.Player, bet: { horse: number, amount: number }, winner: number, horses: Horse[]) {
    try {
        // 验证输入数据
        if (!Number.isFinite(winner) || winner < 1 || winner > 5) {
            console.error(`赛马：无效的获胜马匹 - 玩家: ${player.getStreamSyncedMeta('name')}, 获胜马: ${winner}`);
            notifyapi.shownotify(player, '比赛结果异常', 'error');
            return;
        }

        // 计算奖励
        let winAmount = 0;
        let resultMessage = '';
        
        if (bet.horse === winner) {
            // 获胜
            const winHorse = horses.find(h => h.id === winner);
            if (!winHorse) {
                console.error(`赛马：找不到获胜马匹信息 - 玩家: ${player.getStreamSyncedMeta('name')}, 马匹ID: ${winner}`);
                notifyapi.shownotify(player, '比赛结果异常', 'error');
                return;
            }

            winAmount = Math.floor(bet.amount * (winHorse.odds || 2));
            
            // 验证奖励金额
            if (!Number.isFinite(winAmount) || winAmount <= 0 || winAmount > 1000000) {
                console.error(`赛马：无效的奖励金额 - 玩家: ${player.getStreamSyncedMeta('name')}, 奖励: ${winAmount}`);
                notifyapi.shownotify(player, '奖励计算异常', 'error');
                return;
            }
            
            // 安全地给玩家奖励
            const [addSuccess, addError] = await inventoryapi.addItem('筹码', winAmount, { player: player });
            if (!addSuccess) {
                console.error(`赛马：奖励发放失败 - 玩家: ${player.getStreamSyncedMeta('name')}, 奖励: ${winAmount}, 错误: ${addError}`);
                notifyapi.shownotify(player, '奖励发放失败，请联系管理员', 'error');
                return;
            }
            
            resultMessage = `恭喜！${winner}号马获胜，您赢得了${winAmount}筹码`;
            notifyapi.shownotify(player, resultMessage, 'success');
        } else {
            // 失败
            resultMessage = `${winner}号马获胜，很遗憾您没有中奖`;
            notifyapi.shownotify(player, resultMessage, 'error');
        }

        // 获取更新后的筹码
        const newChips = await inventoryapi.getitemquantity('筹码', { player: player });

        // 发送比赛结果到前端
        Rebar.player.useWebview(player).emit('horse:raceFinished', {
            winner: winner,
            playerBet: bet,
            winAmount: winAmount,
            newChips: newChips,
            horses: horses
        });

    } catch (error) {
        console.error('赛马结束比赛失败:', error);
        notifyapi.shownotify(player, '比赛结果处理出现错误', 'error');
    }
}

// 处理开始新游戏
alt.onClient('horse:startNewGame', async (player: alt.Player) => {
    try {
        // 获取当前筹码
        const playerChips = await inventoryapi.getitemquantity('筹码', { player: player });
        
        // 发送更新的数据
        Rebar.player.useWebview(player).emit('horse:newGameData', {
            chips: playerChips,
            horses: generateHorseData()
        });
        
    } catch (error) {
        console.error('开始新游戏失败:', error);
    }
});

// 处理关闭游戏
alt.onClient('horse:close', (player: alt.Player) => {
    try {
        Rebar.player.useWebview(player).hide('horse');
        Rebar.player.useWorld(player).enableControls();
        
    } catch (error) {
        console.error('关闭游戏失败:', error);
    }
});

// 处理比赛结果
alt.onClient('horse:raceResult', async (player: alt.Player, result: any) => {
    try {
        // 这里可以添加额外的结果处理逻辑
        // 比如记录游戏历史、统计等
        console.log(`玩家 ${player.name} 的赛马结果:`, result);
        
    } catch (error) {
        console.error('处理比赛结果失败:', error);
    }
});

// 玩家断开连接时的清理（如果有其他需要清理的资源）
alt.on('real:playerDisconnect', (player: alt.Player) => {
    // 预留清理函数，目前没有需要清理的会话数据
});





alt.on('rebar:playerPageOpened', (player: alt.Player, page: PageNames) => {
    if (page === 'horse') {
        Rebar.player.useWorld(player).disableControls();
    }
});

alt.on('rebar:playerPageClosed', (player: alt.Player, page: PageNames) => {
    if (page === 'horse') {
        Rebar.player.useWorld(player).enableControls();
    }
});
