import * as alt from 'alt-shared'

export interface FloorData {
    name: string,
    pos: alt.Vector3,
    dimension?: number,
}

export interface ElevatorSystem {
    id: string, // 电梯系统唯一标识
    floors: FloorData[], // 该系统的所有楼层
}

// 统一配置：添加新的电梯系统就在这里加一个对象
export const elevatorSystems: ElevatorSystem[] = [
    // 第一个电梯系统
    {
        id: 'mall_elevator_1',
        floors: [
            {name: '地下层', pos: new alt.Vector3(-661.3055, 326.0967, 77.1106)},
            {name: '1F', pos: new alt.Vector3(-661.3055, 326.0967, 82.0813)},
            {name: '2F', pos: new alt.Vector3(-661.3055, 326.0967, 87.0183)},
            {name: '3F', pos: new alt.Vector3(-661.3055, 326.0967, 91.7363)},
        ]
    },
    // 第二个电梯系统
    {
        id: 'mall_elevator_2',
        floors: [
            {name: '地下层', pos: new alt.Vector3(-664.2462, 326.3209, 77.1106)},
            {name: '1F', pos: new alt.Vector3(-664.2462, 326.3209, 82.0813)},
            {name: '2F', pos: new alt.Vector3(-664.2462, 326.3209, 87.0183)},
            {name: '3F', pos: new alt.Vector3(-664.2462, 326.3209, 91.7363)},
        ]
    },
    // 第三个电梯系统
    {
        id: 'mall_elevator_3',
        floors: [
            {name: '地下层', pos: new alt.Vector3(-667.1473, 326.4000, 77.1106)},
            {name: '1F', pos: new alt.Vector3(-667.1473, 326.4000, 82.0813)},
            {name: '2F', pos: new alt.Vector3(-667.1473, 326.4000, 87.0183)},
            {name: '3F', pos: new alt.Vector3(-667.1473, 326.4000, 91.7363)},
        ]
    },

    // 第四个电梯系统
    {
        id: 'mall_elevator_4',
        floors: [
            {name: '地下层', pos: new alt.Vector3(380.5187, -15.1648, 81.9971)},
            {name: '2F', pos: new alt.Vector3(414.9495, -15.3494, 98.6447)},
        ]
    },
    
    
    // 想添加新的电梯系统？在这里加一个对象即可：
    // {
    //     id: 'hospital_elevator',
    //     floors: [
    //         {name: '1F', pos: new alt.Vector3(x, y, z)},
    //         {name: '2F', pos: new alt.Vector3(x, y, z)},
    //         // ... 更多楼层
    //     ]
    // },
];

