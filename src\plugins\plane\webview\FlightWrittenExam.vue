<template>
  <div class="exam-container">
    <div class="exam-header">
      <div class="exam-title">{{ examType === 'Plane' ? '飞机' : '直升机' }}驾照 - 科目一理论考试</div>
      <div class="exam-info">
        <div class="exam-progress">
          <span class="progress-text">进度：{{ currentIndex + 1 }} / {{ questions.length }}</span>
          <div class="progress-bar">
            <div class="progress-fill" :style="{ width: progressPercent + '%' }"></div>
          </div>
        </div>
        <div class="exam-score">正确：{{ correctCount }} | 错误：{{ wrongCount }}</div>
      </div>
    </div>

    <div class="question-container" v-if="!isFinished">
      <div class="question-number">第 {{ currentIndex + 1 }} 题</div>
      <div class="question-text">{{ currentQuestion.question }}</div>
      
      <div class="options-list">
        <button 
          v-for="(option, index) in currentQuestion.options" 
          :key="index"
          @click="selectAnswer(option[0])"
          :class="getOptionClass(option[0])"
          :disabled="selectedAnswer !== null"
          class="option-btn"
        >
          <span class="option-label">{{ option[0] }}</span>
          <span class="option-text">{{ option.substring(2) }}</span>
        </button>
      </div>

      <div class="feedback" v-if="selectedAnswer">
        <div v-if="isCorrect" class="feedback-correct">
          ✅ 回答正确！
        </div>
        <div v-else class="feedback-wrong">
          ❌ 回答错误！正确答案是：{{ currentQuestion.answer }}
        </div>
      </div>

      <button 
        v-if="selectedAnswer"
        @click="nextQuestion"
        class="next-btn"
      >
        {{ currentIndex < questions.length - 1 ? '下一题' : '查看结果' }}
      </button>
    </div>

    <div class="result-container" v-else>
      <div class="result-title">考试结束</div>
      <div class="result-score" :class="{ 'pass': passed, 'fail': !passed }">
        <div class="score-number">{{ scorePercent }}%</div>
        <div class="score-text">{{ passed ? '恭喜通过！' : '很遗憾，未通过' }}</div>
      </div>
      
      <div class="result-details">
        <div class="detail-item">
          <span class="detail-label">总题数：</span>
          <span class="detail-value">{{ questions.length }}</span>
        </div>
        <div class="detail-item">
          <span class="detail-label">正确数：</span>
          <span class="detail-value correct">{{ correctCount }}</span>
        </div>
        <div class="detail-item">
          <span class="detail-label">错误数：</span>
          <span class="detail-value wrong">{{ wrongCount }}</span>
        </div>
        <div class="detail-item">
          <span class="detail-label">及格线：</span>
          <span class="detail-value">90%</span>
        </div>
      </div>

      <button @click="submitResult" class="submit-btn">
        确认并退出
      </button>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted } from 'vue';
import { useEvents } from '@Composables/useEvents.js';

const events = useEvents();

interface Question {
  question: string;
  options: string[];
  answer: string;
}

const questions = ref<Question[]>([]);
const examType = ref<string>('');
const currentIndex = ref(0);
const selectedAnswer = ref<string | null>(null);
const userAnswers = ref<string[]>([]);
const isFinished = ref(false);

const currentQuestion = computed(() => questions.value[currentIndex.value]);
const correctCount = computed(() => {
  return userAnswers.value.filter((answer, index) => 
    answer === questions.value[index]?.answer
  ).length;
});
const wrongCount = computed(() => userAnswers.value.length - correctCount.value);
const scorePercent = computed(() => 
  Math.round((correctCount.value / questions.value.length) * 100)
);
const passed = computed(() => scorePercent.value >= 90);
const progressPercent = computed(() => 
  ((currentIndex.value + 1) / questions.value.length) * 100
);
const isCorrect = computed(() => 
  selectedAnswer.value === currentQuestion.value?.answer
);

// 接收考试数据
events.on('FlightWrittenExam:sync', (questionsData: Question[], type: string) => {
  questions.value = questionsData;
  examType.value = type;
  // 随机打乱题目顺序
  questions.value.sort(() => Math.random() - 0.5);
  // 只取前20题
  questions.value = questions.value.slice(0, 20);
});

const selectAnswer = (answer: string) => {
  if (selectedAnswer.value) return;
  selectedAnswer.value = answer;
  userAnswers.value.push(answer);
};

const nextQuestion = () => {
  if (currentIndex.value < questions.value.length - 1) {
    currentIndex.value++;
    selectedAnswer.value = null;
  } else {
    isFinished.value = true;
  }
};

const submitResult = () => {
  events.emitServer('FlightWrittenExam:submit', passed.value);
  events.emitServer('FlightWrittenExam:hide');
};

const getOptionClass = (option: string) => {
  if (!selectedAnswer.value) return '';
  if (option === currentQuestion.value?.answer) return 'correct';
  if (option === selectedAnswer.value && !isCorrect.value) return 'wrong';
  return '';
};

// 键盘快捷键
onMounted(() => {
  const handleKeypress = (e: KeyboardEvent) => {
    if (selectedAnswer.value) {
      if (e.key === 'Enter' || e.key === ' ') {
        nextQuestion();
      }
    } else {
      // A-D 选择答案
      const key = e.key.toUpperCase();
      if (['A', 'B', 'C', 'D'].includes(key)) {
        selectAnswer(key);
      }
    }
  };

  window.addEventListener('keydown', handleKeypress);
  
  return () => {
    window.removeEventListener('keydown', handleKeypress);
  };
});
</script>

<style scoped>
.exam-container {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 70vw;
  max-height: 85vh;
  background: linear-gradient(135deg, rgba(10, 20, 40, 0.95), rgba(0, 0, 0, 0.98));
  border-radius: 1.5vw;
  padding: 3vh 3vw;
  color: white;
  box-shadow: 0 1vh 3vh rgba(0, 0, 0, 0.5);
  border: 1px solid rgba(100, 150, 255, 0.2);
  overflow-y: auto;
  animation: fadeIn 0.3s ease-out;
}

.exam-header {
  margin-bottom: 3vh;
  border-bottom: 1px solid rgba(100, 150, 255, 0.2);
  padding-bottom: 2vh;
}

.exam-title {
  font-size: 2.2vw;
  font-weight: bold;
  text-align: center;
  background: linear-gradient(45deg, #00a8ff, #0078ff);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  margin-bottom: 1.5vh;
}

.exam-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.exam-progress {
  flex: 1;
  margin-right: 2vw;
}

.progress-text {
  font-size: 1vw;
  color: rgba(255, 255, 255, 0.7);
  margin-bottom: 0.5vh;
  display: block;
}

.progress-bar {
  width: 100%;
  height: 0.8vh;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 0.4vh;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #0078ff, #00a8ff);
  transition: width 0.3s ease;
}

.exam-score {
  font-size: 1.1vw;
  color: rgba(255, 255, 255, 0.8);
}

.question-container {
  animation: slideIn 0.3s ease-out;
}

.question-number {
  font-size: 1vw;
  color: #00a8ff;
  margin-bottom: 1vh;
  font-weight: 600;
}

.question-text {
  font-size: 1.4vw;
  line-height: 1.6;
  margin-bottom: 3vh;
  color: white;
}

.options-list {
  display: flex;
  flex-direction: column;
  gap: 1.5vh;
  margin-bottom: 3vh;
}

.option-btn {
  display: flex;
  align-items: center;
  padding: 1.5vh 2vw;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(100, 150, 255, 0.2);
  border-radius: 0.8vw;
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 1.1vw;
  text-align: left;
}

.option-btn:hover:not(:disabled) {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(100, 150, 255, 0.4);
  transform: translateX(0.5vw);
}

.option-btn:disabled {
  cursor: default;
}

.option-btn.correct {
  background: rgba(0, 200, 81, 0.2);
  border-color: #00c851;
}

.option-btn.wrong {
  background: rgba(255, 0, 0, 0.2);
  border-color: #ff0000;
}

.option-label {
  display: inline-block;
  width: 2.5vw;
  height: 2.5vw;
  background: rgba(100, 150, 255, 0.2);
  border-radius: 50%;
  text-align: center;
  line-height: 2.5vw;
  margin-right: 1.5vw;
  font-weight: bold;
}

.option-text {
  flex: 1;
}

.feedback {
  padding: 1.5vh 2vw;
  border-radius: 0.8vw;
  margin-bottom: 2vh;
  font-size: 1.1vw;
  animation: fadeIn 0.3s ease-out;
}

.feedback-correct {
  background: rgba(0, 200, 81, 0.2);
  border: 1px solid #00c851;
  color: #00ff00;
}

.feedback-wrong {
  background: rgba(255, 0, 0, 0.2);
  border: 1px solid #ff0000;
  color: #ff6666;
}

.next-btn {
  display: block;
  margin: 0 auto;
  padding: 1.2vh 3vw;
  background: linear-gradient(45deg, #0078ff, #00a8ff);
  border: none;
  border-radius: 0.8vw;
  color: white;
  font-size: 1.2vw;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 0.3vh 0.8vh rgba(0, 120, 255, 0.3);
}

.next-btn:hover {
  background: linear-gradient(45deg, #00a8ff, #0078ff);
  transform: scale(1.05);
  box-shadow: 0 0.5vh 1.2vh rgba(0, 168, 255, 0.4);
}

.result-container {
  text-align: center;
  animation: fadeIn 0.5s ease-out;
}

.result-title {
  font-size: 2.5vw;
  font-weight: bold;
  margin-bottom: 3vh;
  color: white;
}

.result-score {
  margin-bottom: 4vh;
}

.score-number {
  font-size: 5vw;
  font-weight: bold;
  margin-bottom: 1vh;
}

.result-score.pass .score-number {
  color: #00ff00;
  text-shadow: 0 0 2vh rgba(0, 255, 0, 0.5);
}

.result-score.fail .score-number {
  color: #ff0000;
  text-shadow: 0 0 2vh rgba(255, 0, 0, 0.5);
}

.score-text {
  font-size: 1.8vw;
  margin-bottom: 3vh;
}

.result-details {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1.5vh 3vw;
  max-width: 40vw;
  margin: 0 auto 4vh;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  padding: 1vh 1.5vw;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 0.5vw;
  font-size: 1.1vw;
}

.detail-label {
  color: rgba(255, 255, 255, 0.7);
}

.detail-value {
  font-weight: 600;
}

.detail-value.correct {
  color: #00ff00;
}

.detail-value.wrong {
  color: #ff6666;
}

.submit-btn {
  padding: 1.5vh 4vw;
  background: linear-gradient(45deg, #0078ff, #00a8ff);
  border: none;
  border-radius: 0.8vw;
  color: white;
  font-size: 1.3vw;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 0.3vh 0.8vh rgba(0, 120, 255, 0.3);
}

.submit-btn:hover {
  background: linear-gradient(45deg, #00a8ff, #0078ff);
  transform: scale(1.05);
  box-shadow: 0 0.5vh 1.2vh rgba(0, 168, 255, 0.4);
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(-2vw);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* 响应式设计 */
@media screen and (max-width: 768px) {
  .exam-container {
    width: 90vw;
    padding: 2vh 4vw;
  }
  
  .exam-title {
    font-size: 4vw;
  }
  
  .question-text {
    font-size: 3vw;
  }
  
  .option-btn {
    font-size: 2.5vw;
    padding: 2vh 3vw;
  }
  
  .option-label {
    width: 5vw;
    height: 5vw;
    line-height: 5vw;
  }
  
  .result-details {
    grid-template-columns: 1fr;
    max-width: 80vw;
  }
  
  .detail-item {
    font-size: 2.5vw;
  }
  
  .score-number {
    font-size: 10vw;
  }
  
  .score-text {
    font-size: 3.5vw;
  }
}
</style>