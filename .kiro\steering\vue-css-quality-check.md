# Vue CSS 编写质量检查规则

在编写Vue组件的CSS样式时，必须严格遵循以下检查规则，避免低级格式错误：

## CSS 语法检查清单

### 基础语法检查
- [ ] 每个CSS规则都有完整的开始和结束大括号 `{}`
- [ ] 每个属性声明都以分号 `;` 结尾
- [ ] 属性名和属性值之间有冒号 `:` 分隔
- [ ] 选择器语法正确，没有多余的字符
- [ ] 注释格式正确 `/* 注释内容 */`

### 常见错误模式

#### ❌ 错误示例
```css
/* 缺少大括号 */
.class-name
  color: red;
  background: blue;

/* 缺少分号 */
.class-name {
  color: red
  background: blue;
}

/* 属性名错误 */
.class-name {
  widt: 100px;  /* 应该是 width */
  heigh: 50px;  /* 应该是 height */
}

/* 大括号不匹配 */
.class-name {
  color: red;
  .nested {
    background: blue;
  /* 缺少闭合大括号 */

/* 属性值格式错误 */
.class-name {
  margin: 10px 20px 30px;  /* 缺少第四个值或使用简写 */
  border: 1px solid;        /* 缺少颜色值 */
}
```

#### ✅ 正确示例
```css
/* 完整的CSS规则 */
.class-name {
  color: red;
  background: blue;
}

/* 正确的嵌套 */
.parent {
  color: red;
}

.parent .child {
  background: blue;
}

/* 完整的属性值 */
.class-name {
  margin: 10px 20px 30px 40px;
  border: 1px solid #000000;
  width: 100px;
  height: 50px;
}
```

## Vue 特定检查

### `<style scoped>` 标签检查
- [ ] `<style scoped>` 标签正确闭合
- [ ] 标签之间有适当的换行分隔
- [ ] 没有标签名称被意外分割

#### ❌ 错误示例
```vue
</script><sty
le scoped>
/* CSS内容 */
</style>
```

#### ✅ 正确示例
```vue
</script>

<style scoped>
/* CSS内容 */
</style>
```

### CSS 类名规范
- [ ] 类名使用 kebab-case 格式（如 `.my-class-name`）
- [ ] 避免使用驼峰命名（如 `.myClassName`）
- [ ] 类名语义化，易于理解

## 编写流程检查

### 1. 写入前检查
在使用 `fsWrite`、`strReplace`、`fsAppend` 写入CSS代码前：
- 检查每个CSS规则的完整性
- 验证所有大括号是否配对
- 确认所有属性都有分号结尾
- 检查属性名拼写是否正确

### 2. 常见属性名检查
确保以下常用属性名拼写正确：
- `width` (不是 `widt`)
- `height` (不是 `heigh`)
- `margin` (不是 `marin`)
- `padding` (不是 `pading`)
- `background` (不是 `backgrond`)
- `border` (不是 `boder`)
- `position` (不是 `postion`)
- `display` (不是 `dispaly`)

### 3. 值格式检查
- 颜色值格式正确：`#ffffff`、`rgba(255, 255, 255, 0.5)`
- 尺寸单位完整：`10px`、`1rem`、`100%`
- 字体名称用引号包围：`font-family: 'Arial', sans-serif`

## 修复策略

### 发现错误时
1. **立即停止**：不要继续写入有错误的CSS
2. **逐行检查**：仔细检查每一行CSS语法
3. **分段验证**：将大段CSS分成小段分别验证
4. **使用工具**：可以使用CSS验证工具检查语法

### 预防措施
1. **慢速编写**：宁可写得慢一些，也要确保语法正确
2. **分段提交**：不要一次性写入大量CSS，分段进行
3. **重复检查**：写完后再检查一遍语法
4. **模板使用**：对于常用的CSS模式，使用已验证的模板

## 执行原则

- **质量优于速度**：确保每次输出的CSS都是语法正确的
- **用户体验优先**：避免因CSS错误导致界面显示异常
- **持续改进**：从每次错误中学习，避免重复犯错

这个规则必须在每次编写Vue组件CSS时严格执行，确保代码质量和用户体验。