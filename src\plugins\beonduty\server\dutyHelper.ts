import * as alt from 'alt-server';
import { useRebar } from '@Server/index.js';
import { beOnDutytrue, endbeondutytrue } from '@Plugins/faction/server/index.js';

const Rebar = useRebar();

const factionUniformApi = await Rebar.useApi().getAsync('faction-uniforms-api');

const notifyapi = await Rebar.useApi().getAsync('notify-api');






/**
 * 将动态制服配置转换为beonduty界面需要的格式
 */
function convertFactionUniformsToBeOnDutyFormat(factionUniforms: any[], playerGender: number) {
    const converted = {};

    for (const uniform of factionUniforms) {
        // 检查uniformcomponents的格式
        let components = uniform.uniformcomponents;
        
        // 如果uniformcomponents是对象（key-value格式），转换为数组
        if (components && typeof components === 'object' && !Array.isArray(components)) {
            components = Object.values(components).flat();
        }
        
        // 确保components是数组
        if (!Array.isArray(components)) {
            console.warn(`制服 "${uniform.uniformname}" 的uniformcomponents格式不正确，跳过`);
            continue;
        }

        // 根据性别过滤组件
        const filteredComponents = components.filter((comp: any) => comp.sex === playerGender);

        // 如果没有找到对应性别的制服组件，尝试使用默认性别(1-男性)的制服
        if (filteredComponents.length === 0 && playerGender === 0) {
            console.log(`警告: 制服 "${uniform.uniformname}" 没有女性版本，使用男性版本作为fallback`);
            const fallbackComponents = components.filter((comp: any) => comp.sex === 1);

            if (fallbackComponents.length > 0) {
                converted[uniform.uniformname] = {
                    uid: uniform.uid,
                    uniformname: uniform.uniformname,
                    uniformimage: uniform.uniformimage || "",
                    uniformcomponents: fallbackComponents
                };
            }
        } else if (filteredComponents.length > 0) {
            converted[uniform.uniformname] = {
                uid: uniform.uid,
                uniformname: uniform.uniformname,
                uniformimage: uniform.uniformimage || "",
                uniformcomponents: filteredComponents
            };
        }
    }

    return converted;
}

/**
 * 检查派系是否有制服配置（带详细日志）
 * @param factionName 派系名称
 * @param playerRank 玩家等级（可选）
 * @returns 是否有制服配置
 */
export async function checkFactionHasUniforms(factionName: string, playerRank?: number): Promise<boolean> {
    console.log(`[checkFactionHasUniforms] 开始检查派系: ${factionName}, playerRank: ${playerRank}`);

    // 检查动态制服配置

    try {
        // 检查API是否可用
        if (!factionUniformApi || typeof factionUniformApi.getFactionUniforms !== 'function') {
            console.log(`[checkFactionHasUniforms] 动态制服API不可用或未正确加载`);
        } else {
            // 使用factionName作为factionId，因为动态制服API的getFactionUniforms方法需要factionId参数
            const dynamicUniforms = await factionUniformApi.getFactionUniforms(factionName, playerRank);
            console.log(`[checkFactionHasUniforms] 动态制服API返回:`, dynamicUniforms);
            if (dynamicUniforms && dynamicUniforms.length > 0) {
                console.log(`[checkFactionHasUniforms] 派系 ${factionName} 存在动态制服配置，共${dynamicUniforms.length}套`);
                return true;
            } else {
                console.log(`[checkFactionHasUniforms] 派系 ${factionName} 没有动态制服配置`);
            }
        }
    } catch (error) {
        console.error(`[checkFactionHasUniforms] 检查派系 ${factionName} 动态制服配置时出错:`, error);
    }


    // 只使用动态制服配置
    return false;
}

/**
 * 获取派系制服配置（仅动态配置）
 */
export async function getFactionUniforms(factionName: string, playerGender: number, playerRank?: number) {
    // 只使用动态制服API
    if (factionUniformApi && factionUniformApi.getFactionUniforms) {
        try {
            const dynamicUniforms = await factionUniformApi.getFactionUniforms(factionName, playerRank);

            if (dynamicUniforms && dynamicUniforms.length > 0) {
                console.log(`为派系 ${factionName} 使用动态制服配置，共 ${dynamicUniforms.length} 套`);
                return convertFactionUniformsToBeOnDutyFormat(dynamicUniforms, playerGender);
            } else {
                console.log(`派系 ${factionName} 没有动态制服配置`);
            }
        } catch (error) {
            console.error('获取动态制服配置时出错:', error);
        }
    }

    console.log(`派系 ${factionName} 没有制服配置`);
    return {};
}

/**
 * 处理上班逻辑的通用函数
 * @param player 玩家对象
 * @param factionName 派系名称
 * @param islowestrank 是否为最低等级
 * @param ishighestrank 是否为最高等级
 * @param onNoUniforms 没有制服时的回调函数（可选）
 */
export async function handleDutyLogic(
    player: alt.Player,
    factionName: string,
    islowestrank: boolean = true,
    ishighestrank: boolean = true,
    onNoUniforms?: () => void
) {
    // 验证factionName参数
    if (!factionName || factionName.trim() === '') {
        console.error('[handleDutyLogic] factionName参数无效:', factionName);
        notifyapi.shownotify(player, '派系名称无效', 'error');
        return;
    }

    const playerdata = Rebar.document.character.useCharacter(player).get();

    // 检查玩家是否已经上班
    const isOnDuty = playerdata.job.beOnDuty;

    // 获取玩家性别和等级
    const playerGender = playerdata.appearance?.sex || 0;
    const playerRank = playerdata.job?.rank || 0;

    // 检查是否有制服配置
    const hasUniforms = await checkFactionHasUniforms(factionName, playerRank);

    if (isOnDuty && hasUniforms) {
        // 已经上班且有制服配置，允许更换制服
        const cloth = await getFactionUniforms(factionName, playerGender, playerRank);
        
        if (Object.keys(cloth).length > 0) {
            console.log(`玩家 ${playerdata.name} 在上班状态下请求更换制服`);
            
            Rebar.player.useWebview(player).show('beonduty', 'page', true);

            let ready = await Rebar.player.useWebview(player).isReady('beonduty', 'page');
            while (!ready) {
                await alt.Utils.wait(100);
                ready = await Rebar.player.useWebview(player).isReady('beonduty', 'page');
            }

            Rebar.player.useWebview(player).emit('beonduty:sync', cloth, islowestrank, ishighestrank, playerdata.job);
            return;
        }
    }

    if (isOnDuty) {
        // 已经上班但没有制服配置，直接下班
        endbeondutytrue(player);
        alt.emitRaw('leavebeonduty', player);
        notifyapi.shownotify(player, `${factionName}下班成功`, 'success');
        return;
    }

    if (!hasUniforms) {
        // 没有制服配置
        if (onNoUniforms) {
            onNoUniforms();
        } else {
            // 默认行为：直接上班
            beOnDutytrue(player);
            notifyapi.shownotify(player, `上班成功`, 'success');
        }
        return;
    }

    // 获取制服配置
    const cloth = await getFactionUniforms(factionName, playerGender, playerRank);

    if (Object.keys(cloth).length === 0) {
        // 制服配置为空
        if (onNoUniforms) {
            onNoUniforms();
        } else {
            // 默认 行为：直接上班
            beOnDutytrue(player);
            notifyapi.shownotify(player, `上班成功`, 'success');
        }
        return;
    }

    // 有制服配置，显示制服选择界面
    console.log(`玩家 ${playerdata.name} (性别: ${playerGender === 0 ? '女' : '男'}, 等级: ${playerRank}) 请求 ${factionName} 制服，可用制服: ${Object.keys(cloth).length} 套`);

    Rebar.player.useWebview(player).show('beonduty', 'page', true);

    let ready = await Rebar.player.useWebview(player).isReady('beonduty', 'page');
    while (!ready) {
        await alt.Utils.wait(100);
        ready = await Rebar.player.useWebview(player).isReady('beonduty', 'page');
    }

    Rebar.player.useWebview(player).emit('beonduty:sync', cloth, islowestrank, ishighestrank, playerdata.job);
}

