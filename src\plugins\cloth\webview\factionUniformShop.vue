<template>
  <div :class="['uniform-creator-container', { 'dragging': isDragging }]">
    <!-- 右侧切换按钮 -->
    <button :class="['toggle-btn', { 'closed': !isMenuOpen }]" @click="toggleMenu">
      <i :class="['fas', isMenuOpen ? 'fa-chevron-right' : 'fa-chevron-left']"></i>
    </button>
    
    <!-- 右侧主面板 -->
    <div :class="['uniform-panel', { 'closed': !isMenuOpen }]">
      <div class="panel-content">
        <!-- 标题区域 -->
        <div class="panel-header">
          <div class="header-icon">
            <i class="fas fa-tshirt"></i>
          </div>
          <div class="title-info">
            <h1>制服设计师</h1>
          </div>
        </div>

        <!-- 控制区域 -->
        <div class="controls-section">
          <!-- 控制面板 -->
          <div class="control-panels">
            <!-- 角色控制 -->
            <div class="control-group">
              <div class="group-header">
                <i class="fas fa-user-cog"></i>
                <span>角色控制</span>
              </div>
              <div class="control-row">
                <!-- 旋转提示 -->
                <div class="rotation-display">
                  <i class="fas fa-sync-alt"></i>
                  <span class="rotation-angle">{{ Math.round(rotationY) }}°</span>
                  <button @click="resetRotation" class="reset-btn" title="重置角色朝向">
                    <i class="fas fa-undo"></i>
                  </button>
                </div>
                
                <!-- 躯体调整 -->
                <div class="body-controls">
                  <span class="control-label">躯体</span>
                  <button @click="adjustBody('down')" class="control-btn">
                    <i class="fas fa-minus"></i>
                  </button>
                  <span class="body-value">{{ currentBodyValue }}</span>
                  <button @click="adjustBody('up')" class="control-btn">
                    <i class="fas fa-plus"></i>
                  </button>
                </div>
              </div>
            </div>
            
            <!-- 视角控制 -->
            <div class="control-group">
              <div class="group-header">
                <i class="fas fa-video"></i>
                <span>视角控制</span>
                <button @click="toggleAutoCamera" :class="['auto-toggle', { 'active': isAutoCamera }]">
                  <i :class="isAutoCamera ? 'fas fa-eye' : 'fas fa-eye-slash'"></i>
                  <span>{{ isAutoCamera ? '自动' : '手动' }}</span>
                </button>
              </div>
              <div class="camera-buttons" v-show="!isAutoCamera">
                <button 
                  v-for="(_, category) in CAMERA_POSITIONS" 
                  :key="category"
                  @click="setManualCameraPosition(category)"
                  :class="['camera-btn', { 'active': currentFocusedCategory === category }]"
                  :title="`查看${getCategoryName(category)}`"
                >
                  <i :class="getCameraIcon(category)"></i>
                </button>
              </div>
            </div>
          </div>
        </div>

        <!-- 数据源选择区域 -->
        <div class="data-source-section">
          <div class="section-header">
            <h3>服装来源</h3>
          </div>
          <div class="data-source-grid">
            <button class="source-tab active">
              <i class="fas fa-layer-group"></i>
              <span>全部服装</span>
            </button>
          </div>
        </div>

        <!-- 服装选择区域 - 仿照clothshop样式 -->
        <div class="clothes-section">
          <!-- 分类导航 -->
          <div class="category-nav">
            <button 
              v-for="category in availableCategories" 
              :key="category"
              @click="selectedCategory = category"
              :class="['category-tab', { 'active': selectedCategory === category }]"
            >
              <i :class="getCategoryIcon(getCategoryIndex(category), false)"></i>
              <span>{{ getCategoryDisplayName(category) }}</span>
            </button>
          </div>

          <!-- 服装选择器 -->
          <div v-if="selectedCategory && currentClothes?.length > 0" class="clothes-selector">
            <!-- 服装信息 -->
            <div class="clothes-info">
              <h3 class="clothes-name">{{ getCurrentClothesItem()?.name || '未选择' }}</h3>
              <div class="clothes-source" :class="getCurrentClothesItem()?.dlcname === 'boyuniform_01' ? 'uniform-source' : 'regular-source'">
                {{ getCurrentClothesItem()?.dlcname === 'boyuniform_01' ? '制服专用' : '常规服装' }}
              </div>
            </div>
            
            <!-- 导航控制 -->
            <div class="clothes-nav">
              <button @click="prevClothes" class="nav-btn prev" :disabled="currentClothesIndex <= 0">
                <i class="fas fa-chevron-left"></i>
              </button>
              
              <div class="nav-info">
                <input 
                  type="number" 
                  :value="currentClothesIndex + 1" 
                  @input="jumpToClothes($event)" 
                  :min="1" 
                  :max="currentClothes.length"
                  class="page-input"
                />
                <span class="nav-separator">/</span>
                <span class="total-count">{{ currentClothes.length }}</span>
              </div>

              <button @click="nextClothes" class="nav-btn next" :disabled="currentClothesIndex >= currentClothes.length - 1">
                <i class="fas fa-chevron-right"></i>
              </button>
            </div>

            <!-- 进度条 -->
            <div class="progress-container">
              <input 
                type="range" 
                :min="0" 
                :max="currentClothes.length - 1"
                :value="currentClothesIndex"
                @input="currentClothesIndex = parseInt(($event.target as HTMLInputElement).value)"
                class="clothes-slider"
              />
            </div>

            <!-- 操作按钮 -->
            <div class="clothes-actions">
              <button 
                @click="selectCurrentClothes" 
                class="action-btn primary"
                :disabled="!getCurrentClothesItem()"
              >
                <i class="fas fa-plus"></i>
                <span>添加到制服</span>
              </button>
              
              <button 
                @click="previewCurrentClothes" 
                class="action-btn secondary"
                :disabled="!getCurrentClothesItem()"
              >
                <i class="fas fa-eye"></i>
                <span>预览</span>
              </button>
            </div>
          </div>
          
          <div v-else class="empty-state">
            <i class="fas fa-tshirt"></i>
            <p>暂无可用服装</p>
          </div>
        </div>

        <!-- 已选组件和制服信息 -->
        <div class="uniform-section" :class="{ 'has-selection': selectedComponents.length > 0 }">
          <div class="section-header">
            <h3>当前制服搭配</h3>
            <div class="component-count" v-if="selectedComponents.length > 0">
              {{ selectedComponents.length }} 个组件
            </div>
          </div>
          
          <!-- 已选组件列表 -->
          <div class="selected-components">
            <div 
              v-for="(component, index) in selectedComponents" 
              :key="index"
              class="selected-item"
            >
              <div class="component-info">
                <i :class="getCategoryIcon(component.component, component.prop)"></i>
                <div class="component-details">
                  <span class="component-name">{{ getComponentTypeName(component.component, component.prop) }}</span>
                  <span class="component-specs">{{ component.drawable }}-{{ component.texture }}</span>
                </div>
              </div>
              <button @click="removeComponent(index)" class="remove-btn">
                <i class="fas fa-times"></i>
              </button>
            </div>
            
            <!-- 空状态 -->
            <div v-if="selectedComponents.length === 0" class="empty-selection">
              <i class="fas fa-tshirt"></i>
              <p>还未选择任何服装组件</p>
            </div>
          </div>

          <!-- 制服信息输入 -->
          <div class="uniform-info" v-if="selectedComponents.length > 0">
            <div class="form-group">
              <label>制服名称</label>
              <input 
                v-model="uniformName" 
                placeholder="输入制服名称..." 
                class="uniform-name-input"
                maxlength="50"
              />
            </div>

            <div class="form-row">
              <div class="form-group">
                <label>最低等级</label>
                <select v-model="uniformRequiredRank" class="rank-select">
                  <option :value="undefined">无要求</option>
                  <option v-for="job in factionJobs" :key="job.rank" :value="job.rank">
                    等级 {{ job.rank }} - {{ job.name }}
                  </option>
                </select>
              </div>
              
              <div class="form-group">
                <label>最高等级</label>
                <select v-model="uniformMaxRank" class="rank-select">
                  <option :value="undefined">无限制</option>
                  <option v-for="job in factionJobs" :key="job.rank" :value="job.rank">
                    等级 {{ job.rank }} - {{ job.name }}
                  </option>
                </select>
              </div>
            </div>

            <!-- 操作按钮 -->
            <div class="action-buttons">
              <button @click="resetSelection" class="action-btn secondary">
                <i class="fas fa-undo"></i>
                <span>重置</span>
              </button>
              <button @click="saveUniform" class="action-btn primary" :disabled="!uniformName.trim()">
                <i class="fas fa-save"></i>
                <span>保存制服</span>
              </button>
            </div>
            
            <!-- 辅助操作按钮 -->
            <div class="utility-buttons">
              <button @click="removeAllClothes" class="utility-btn remove-all" title="脱下所有服装">
                <i class="fas fa-tshirt"></i>
                <span>全脱</span>
              </button>
              <button @click="wearOriginalClothes" class="utility-btn restore" title="恢复原装服装">
                <i class="fas fa-undo"></i>
                <span>原装</span>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, watch } from 'vue';
import { useEvents } from '@Composables/useEvents.js';
import { femalecloth, malecloth } from '../shared/config.js';

// 类型定义
interface ClothingItem {
  component: number;
  drawable: number;
  texture: number;
  dlcname: string | number;
  isProp: boolean;
  sex: number;
  name?: string;
  id: number;
  dlc: string;
  price?: number;
}

interface FactionUniformComponent {
  component: number;
  drawable: number;
  texture: number;
  dlcname: string | number;
  prop: boolean;
  sex: number;
}

interface FactionJob {
  rank: number;
  name: string;
  permissions: string[];
}

const events = useEvents();

// 状态管理
const isMenuOpen = ref(true);
const isDragging = ref(false);
const selectedCategory = ref('tops');
const selectedComponents = ref<FactionUniformComponent[]>([]);
const uniformName = ref('');
const uniformRequiredRank = ref<number | undefined>(undefined);
const uniformMaxRank = ref<number | undefined>(undefined);
const factionJobs = ref<FactionJob[]>([]);

// 服装选择状态
const currentClothesIndex = ref(0);

// 服装数据
const regularClothes = ref<ClothingItem[]>([]);
const uniformClothes = ref<ClothingItem[]>([]);
const playerSex = ref<number>(0);

// 计算属性：当前可用的分类
const availableCategories = computed(() => {
  const categories = new Set<string>();
  [...regularClothes.value, ...uniformClothes.value].forEach(item => {
    const category = getCategoryByComponent(item.id, item.isProp);
    categories.add(category);
  });
  return Array.from(categories);
});

// 计算属性：当前分类的服装
const currentClothes = computed(() => {
  if (!selectedCategory.value) return [];
  
  const allClothes = [...uniformClothes.value, ...regularClothes.value];
  return allClothes.filter(item => 
    getCategoryByComponent(item.id, item.isProp) === selectedCategory.value
  );
});

// 获取当前服装项
const getCurrentClothesItem = () => {
  return currentClothes.value[currentClothesIndex.value] || null;
};


// 摄像机和角色控制相关状态
const rotationY = ref(0);
const currentBodyValue = ref(0);
const isAutoCamera = ref(true);
const currentFocusedCategory = ref<CameraCategory>('overview');

// 定义摄像机视角类型
type CameraCategory = 'overview' | 'tops' | 'legs' | 'shoes' | 'under' | 'hat' | 'glasses' | 'ears' | 'watch' | 'bracelet' | 'mask' | 'body';

// 定义不同部位的摄像机参数
const CAMERA_POSITIONS = ref({
  overview: { zpos: 0.0, fov: 90 },
  tops: { zpos: 0.19, fov: 49 },
  legs: { zpos: -0.47, fov: 59 },
  shoes: { zpos: -0.71, fov: 53 },
  under: { zpos: 0.19, fov: 49 },
  hat: { zpos: 0.6, fov: 33 },
  glasses: { zpos: 0.61, fov: 29 },
  ears: { zpos: 0.62, fov: 29 },
  watch: { zpos: -0.1, fov: 45 },
  bracelet: { zpos: -0.1, fov: 45 },
  mask: { zpos: 0.6, fov: 33 },
  body: { zpos: 0.0, fov: 90 }
});




// 服装导航方法
const nextClothes = () => {
  if (currentClothesIndex.value < currentClothes.value.length - 1) {
    currentClothesIndex.value++;
    previewCurrentClothes();
  }
};

const prevClothes = () => {
  if (currentClothesIndex.value > 0) {
    currentClothesIndex.value--;
    previewCurrentClothes();
  }
};

const jumpToClothes = (event: Event) => {
  const target = event.target as HTMLInputElement;
  const index = parseInt(target.value) - 1;
  if (index >= 0 && index < currentClothes.value.length) {
    currentClothesIndex.value = index;
    previewCurrentClothes();
  }
};

const selectCurrentClothes = () => {
  const item = getCurrentClothesItem();
  if (item) {
    selectClothingItem(item);
  }
};

const previewCurrentClothes = () => {
  const item = getCurrentClothesItem();
  if (item) {
    const component: FactionUniformComponent = {
      component: item.id,
      drawable: item.drawable,
      texture: item.texture,
      dlcname: item.dlc,
      prop: item.isProp,
      sex: item.sex
    };
    previewItem(component);
  }
};

// 方法
const toggleMenu = () => {
  isMenuOpen.value = !isMenuOpen.value;
};

const selectClothingItem = (item: ClothingItem) => {
  const component: FactionUniformComponent = {
    component: item.id,
    drawable: item.drawable,
    texture: item.texture,
    dlcname: item.dlc,
    prop: item.isProp,
    sex: item.sex
  };

  // 检查是否已选择相同组件部位，如果有则替换
  const existingIndex = selectedComponents.value.findIndex(comp => 
    comp.component === component.component && comp.prop === component.prop
  );
  
  if (existingIndex !== -1) {
    selectedComponents.value[existingIndex] = component;
  } else {
    selectedComponents.value.push(component);
  }
  
  // 自动切换视角到对应部位
  if (isAutoCamera.value) {
    const category = getCategoryByComponent(item.id, item.isProp) as CameraCategory;
    setCameraPosition(category);
  }
  
  // 发送预览请求
  previewItem(component);
};








const removeComponent = (index: number) => {
  selectedComponents.value.splice(index, 1);
  // 重新预览当前搭配
  previewCurrentSelection();
};

const resetSelection = () => {
  selectedComponents.value = [];
  uniformName.value = '';
  uniformRequiredRank.value = undefined;
  uniformMaxRank.value = undefined;
  // 恢复原始外观
  events.emitServer('clothshop:wearOriginalClothes');
};

// 脱下所有衣服
const removeAllClothes = () => {
  events.emitServer('clothshop:removeAllClothes');
};

// 穿上原来的衣服
const wearOriginalClothes = () => {
  events.emitServer('clothshop:wearOriginalClothes');
};

const saveUniform = async () => {
  if (!uniformName.value.trim() || selectedComponents.value.length === 0) {
    return;
  }

  const uniformData = {
    uid: Date.now().toString(), // 简单的ID生成
    uniformname: uniformName.value.trim(),
    uniformcomponents: selectedComponents.value,
    requiredRank: uniformRequiredRank.value,
    maxRank: uniformMaxRank.value
  };

  try {
    const success = await events.emitServerRpc('factionUniformShop:createUniform', uniformData);
    if (success) {
      // 保存成功，重置状态
      resetSelection();
      console.log('制服创建成功');
    } else {
      console.error('制服创建失败');
    }
  } catch (error) {
    console.error('保存制服失败:', error);
  }
};

const previewItem = (component: FactionUniformComponent) => {
  events.emitServer('clothshop:preview', 
    component.dlcname, 
    component.drawable, 
    component.component, 
    component.texture, 
    component.prop
  );
};

const previewCurrentSelection = () => {
  // 先恢复原始外观，再穿上所有选中的组件
  events.emitServer('clothshop:wearOriginalClothes');
  setTimeout(() => {
    selectedComponents.value.forEach(component => {
      previewItem(component);
    });
  }, 100);
};

// 摄像机控制相关方法
const setCameraPosition = (category: CameraCategory, forceChange: boolean = false) => {
  const position = CAMERA_POSITIONS.value[category];
  if (position && (currentFocusedCategory.value !== category || forceChange)) {
    currentFocusedCategory.value = category;
    events.emitClient("CAMERA_SET_POSITION", position.zpos, position.fov);
  }
};

const setManualCameraPosition = (category: CameraCategory) => {
  setCameraPosition(category, true);
};

const toggleAutoCamera = () => {
  isAutoCamera.value = !isAutoCamera.value;
};

const getCameraIcon = (category: CameraCategory) => {
  const icons: Record<CameraCategory, string> = {
    overview: 'fas fa-expand-arrows-alt',
    tops: 'fas fa-tshirt',
    legs: 'fas fa-socks',
    shoes: 'fas fa-shoe-prints',
    under: 'fas fa-vest',
    hat: 'fas fa-hat-cowboy',
    glasses: 'fas fa-glasses',
    ears: 'fas fa-gem',
    watch: 'fas fa-clock',
    bracelet: 'fas fa-ring',
    mask: 'fas fa-mask',
    body: 'fas fa-male'
  };
  return icons[category] || 'fas fa-eye';
};

const getCategoryDisplayName = (category: string): string => {
  const categoryNames: Record<string, string> = {
    'hat': '帽子',
    'mask': '面具',
    'tops': '上衣',
    'under': '内衬',
    'legs': '裤子',
    'shoes': '鞋子',
    'glasses': '眼镜',
    'ears': '耳环',
    'watch': '手表',
    'bracelet': '手环',
    'accessories': '配饰'
  };
  return categoryNames[category] || '未知';
};

const getCategoryName = (category: CameraCategory): string => {
  const categoryNames: Record<CameraCategory, string> = {
    overview: '全景',
    tops: '上衣',
    legs: '裤子',
    shoes: '鞋子',
    under: '内衬',
    hat: '帽子',
    glasses: '眼镜',
    ears: '耳环',
    watch: '手表',
    bracelet: '手环',
    mask: '面具',
    body: '躯体'
  };
  return categoryNames[category];
};

// 角色旋转控制
const handleMouseDown = (e: MouseEvent) => {
  if (e.button === 2) {
    isDragging.value = true;
    e.preventDefault();
  }
};

const handleMouseMove = (e: MouseEvent) => {
  if (isDragging.value) {
    const deltaX = e.movementX;
    const rotationSpeed = 0.3;
    
    let newRotation = rotationY.value + deltaX * rotationSpeed;
    
    if (newRotation > 180) {
      newRotation = newRotation - 360;
    } else if (newRotation < -180) {
      newRotation = newRotation + 360;
    }
    
    rotationY.value = newRotation;
    events.emitClient("PLAYER_ROTATE", rotationY.value);
    e.preventDefault();
  }
};

const handleMouseUp = (e: MouseEvent) => {
  if (e.button === 2) {
    isDragging.value = false;
    e.preventDefault();
  }
};

const resetRotation = () => {
  rotationY.value = 0;
  events.emitClient("PLAYER_ROTATE", 0);
};

const adjustBody = (direction: 'up' | 'down') => {
  if (direction === 'up') {
    currentBodyValue.value++;
  } else if (direction === 'down') {
    currentBodyValue.value--;
  }
  
  events.emitServer('clothshop:adjustBody', currentBodyValue.value);
  setCameraPosition('overview');
};

// 防止右键菜单弹出
const handleContextMenu = (e: Event) => {
  if (isDragging.value) {
    e.preventDefault();
  }
};

// 辅助函数
const getCategoryByComponent = (componentId: number, isProp: boolean = false): string => {
  if (isProp) {
    // Props 映射到前端分类
    const propCategoryMap: Record<number, string> = {
      0: 'hat',       // 帽子 
      1: 'glasses', // 眼镜  
      2: 'ears', // 耳环 
      6: 'watch', // 手表 
      7: 'bracelet', // 手镯 
    };
    return propCategoryMap[componentId] || 'accessories';
  } else {
    // Clothing 映射到前端分类
    const clothingCategoryMap: Record<number, string> = {
      0: 'hat',    // head -> 帽子分类
      1: 'mask',   // mask -> 面具分类
      3: 'tops',   // torso -> 上衣分类
      4: 'legs',   // legs -> 裤子分类
      6: 'shoes',  // shoes -> 鞋子分类
      7: 'accessories', // accessories -> 配饰分类
      8: 'under',  // undershirts -> 内衬分类
      11: 'tops',  // tops -> 上衣分类
    };
    return clothingCategoryMap[componentId] || 'tops';
  }
};

const getCategoryIndex = (category: string): number => {
  const categoryMap: Record<string, number> = {
    'hat': 0,
    'mask': 1,
    'tops': 11,
    'under': 8,
    'legs': 4,
    'shoes': 6,
    'glasses': 1,
    'ears': 2,
    'watch': 6,
    'bracelet': 7,
    'accessories': 7
  };
  return categoryMap[category] || 11;
};

const getCategoryIcon = (componentId: number, isProp: boolean = false): string => {
  if (isProp) {
    // Props 图标映射
    const propIconMap: Record<number, string> = {
      0: 'fas fa-hat-cowboy', // 帽子
      1: 'fas fa-glasses',    // 眼镜
      2: 'fas fa-gem',        // 耳环
      6: 'fas fa-clock',      // 手表
      7: 'fas fa-gem',        // 手镯
    };
    return propIconMap[componentId] || 'fas fa-gem';
  } else {
    // Clothing 图标映射
    const clothingIconMap: Record<number, string> = {
      0: 'fas fa-user',         // 发型
      1: 'fas fa-mask',         // 面具
      3: 'fas fa-tshirt',       // 上衣
      4: 'fas fa-socks',        // 裤子
      6: 'fas fa-shoe-prints',  // 鞋子
      7: 'fas fa-gem',          // 配饰
      8: 'fas fa-vest',         // 内衬
      11: 'fas fa-tshirt',      // 上衣
    };
    return clothingIconMap[componentId] || 'fas fa-tshirt';
  }
};

const getComponentTypeName = (componentId: number, isProp: boolean = false): string => {
  if (isProp) {
    // Props 名称映射 - 直接用具体名称
    const propNameMap: Record<number, string> = {
      0: '帽子',   // hat
      1: '眼镜',   // glasses
      2: '耳环',   // ears
      6: '手表',   // watch
      7: '手环',   // bracelet
    };
    return propNameMap[componentId] || '道具';
  } else {
    // Clothing 名称映射 - 直接用具体名称
    const clothingNameMap: Record<number, string> = {
      0: '发型',   // head
      1: '面具',   // mask
      3: '躯干',   // torso
      4: '裤子',   // legs
      5: '背包',   // bag
      6: '鞋子',   // shoes
      7: '饰品',   // accessories
      8: '内衬',   // undershirts
      9: '护甲',   // bodyarmor
      10: '贴纸',  // decals
      11: '上衣',  // tops
    };
    return clothingNameMap[componentId] || '服装';
  }
};


// 存储派系信息
const factionInfo = ref<{
  factionId?: string;
  factionName?: string;
  existingUniforms?: any[];
}>({});

// 服务端事件监听
events.on('factionUniformShop:setConfig', async (configData: any) => {
  console.log('[派系制服] 收到派系制服配置:', configData);
  factionInfo.value = configData;
  
  if (configData.playerSex !== undefined) {
    playerSex.value = configData.playerSex;
    console.log(`[派系制服] 从配置获取玩家性别: ${configData.playerSex}`);
    
    try {
      // 加载制服数据
      const { ModClothes } = await import('../shared/addclothes.js');
      console.log(`[派系制服] ModClothes 加载完成，数量: ${ModClothes.length}`);
      
      const uniformItems = ModClothes.filter(cloth => 
        cloth.dlc === 'boyuniform_01' && cloth.sex === configData.playerSex
      ).map(cloth => ({
        id: cloth.id,
        drawable: cloth.drawable,
        texture: cloth.texture,
        dlc: cloth.dlc,
        isProp: cloth.isProp,
        sex: cloth.sex,
        name: (cloth as any).name || getComponentTypeName(cloth.id, cloth.isProp),
        component: cloth.id,
        dlcname: cloth.dlc
      }));
      
      uniformClothes.value = uniformItems || [];
      
      // 加载常规服装数据
      const clothData = configData.playerSex === 0 ? malecloth : femalecloth;
      const regularItems: ClothingItem[] = [];
      
      // 遍历所有分类
      Object.entries(clothData).forEach(([, items]) => {
        if (Array.isArray(items)) {
          items.forEach(item => {
            regularItems.push({
              id: item.id,
              drawable: item.drawable,
              texture: item.texture,
              dlc: item.dlc,
              isProp: item.isProp,
              sex: item.sex,
              name: item.name,
              price: item.price,
              component: item.id,
              dlcname: item.dlc
            });
          });
        }
      });
      
      
      regularClothes.value = regularItems;
      
      console.log(`[派系制服] 制服数据: ${uniformClothes.value.length} 件`);
      console.log(`[派系制服] 常规服装: ${regularClothes.value.length} 件`);
      
      // 获取派系职位数据
      const jobsData = await events.emitServerRpc('factionUniformShop:getFactionJobs');
      factionJobs.value = jobsData || [];
      
    } catch (error) {
      console.error('[派系制服] 处理配置数据失败:', error);
    }
  }
});

events.on('factionUniformShop:saveResult', (result: { success: boolean }) => {
  if (result.success) {
    console.log('制服保存成功');
  } else {
    console.log('制服保存失败');
  }
});

events.on('factionUniformShop:uniformRemoved', (uniformUid: string) => {
  console.log('制服已删除:', uniformUid);
});

// 生命周期
onMounted(() => {
  console.log('[派系制服] onMounted 启动，初始化界面...');
  
  // 添加鼠标事件监听器
  console.log('[派系制服] 发送 clothshopopendisablecontrols 事件');
  events.emitClient('clothshopopendisablecontrols');
  
  document.addEventListener('mousedown', handleMouseDown);
  document.addEventListener('mousemove', handleMouseMove);
  document.addEventListener('mouseup', handleMouseUp);
  document.addEventListener('contextmenu', handleContextMenu);
  
  document.addEventListener('selectstart', (e) => {
    if (isDragging.value) {
      e.preventDefault();
      console.log('[派系制服] 阻止拖拽时的文本选择');
    }
  });

  // 初始化摄像机位置
  console.log('[派系制服] 初始化摄像机位置为 overview');
  setCameraPosition('overview');
  resetRotation();
  console.log('[派系制服] onMounted 完成，等待服务端配置数据...');
});

// 清理监听器
onUnmounted(() => {
  document.removeEventListener('mousedown', handleMouseDown);
  document.removeEventListener('mousemove', handleMouseMove);
  document.removeEventListener('mouseup', handleMouseUp);
  document.removeEventListener('contextmenu', handleContextMenu);
  
  // 关闭界面时恢复原装服装
  events.emitServer('clothshop:wearOriginalClothes');
});

// 监听分类变化，重置服装索引
watch(selectedCategory, () => {
  currentClothesIndex.value = 0;
});

// 监听当前服装索引变化，自动预览
watch(currentClothesIndex, () => {
  previewCurrentClothes();
});

// 监听搭配变化，自动预览
watch(selectedComponents, () => {
  if (selectedComponents.value.length > 0) {
    previewCurrentSelection();
  }
}, { deep: true });

</script>

<style scoped>
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

/* Reset & Base */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

/* Main Container */
.uniform-creator-container {
  position: fixed;
  top: 0;
  right: 0;
  height: 100vh;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
  z-index: 1000;
  animation: slideIn 0.3s ease-out;
}

.uniform-creator-container.dragging,
.uniform-creator-container.dragging * {
  cursor: grabbing !important;
  user-select: none;
}

@keyframes slideIn {
  from { 
    opacity: 0; 
    transform: translateX(100%);
  }
  to { 
    opacity: 1; 
    transform: translateX(0);
  }
}

/* Panel */
.uniform-panel {
  position: fixed;
  top: 0;
  right: 0;
  width: min(35vw, 450px);
  height: 100vh;
  background: rgba(15, 23, 42, 0.95);
  border-left: 1px solid rgba(100, 255, 218, 0.1);
  transition: transform 0.3s ease;
  overflow: hidden;
  box-shadow: -0.8vh 0 3.2vh rgba(0, 0, 0, 0.3);
}

.uniform-panel.closed {
  transform: translateX(100%);
}

/* Toggle Button */
.toggle-btn {
  position: fixed;
  right: min(35vw, 450px);
  top: 50%;
  transform: translateY(-50%);
  width: 2.4vh;
  height: 8vh;
  background: #64ffda;
  border: none;
  border-radius: 0.8vh 0 0 0.8vh;
  color: #0f172a;
  font-size: 1.4vh;
  cursor: pointer;
  z-index: 1001;
  transition: all 0.3s ease;
  box-shadow: -0.4vh 0 1.6vh rgba(0, 0, 0, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
}

.toggle-btn:hover {
  background: #ffffff;
  color: #64ffda;
  transform: translateY(-50%) translateX(-0.2vh);
}

.toggle-btn.closed { 
  right: 0;
  transform: translateY(-50%);
}

/* Content */
.panel-content {
  height: 100%;
  padding: 1vh;
  display: flex;
  flex-direction: column;
  gap: 0.8vh;
  overflow-y: auto;
  scrollbar-width: thin;
  scrollbar-color: #64ffda transparent;
}

.panel-content::-webkit-scrollbar { 
  width: 0.3vh; 
}
.panel-content::-webkit-scrollbar-track { 
  background: transparent; 
}
.panel-content::-webkit-scrollbar-thumb {
  background: #64ffda;
  border-radius: 0.3vh;
}

/* Header */
.panel-header {
  display: flex;
  align-items: center;
  gap: 0.8vh;
  padding: 0.8vh;
  background: rgba(30, 41, 59, 0.8);
  border-radius: 0.8vh;
  border: 1px solid rgba(100, 255, 218, 0.1);
}

.header-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2.4vh;
  height: 2.4vh;
  background: #64ffda;
  border-radius: 50%;
  color: #0f172a;
  font-size: 1.2vh;
  font-weight: 600;
}

.title-info h1 {
  color: #f8fafc;
  font-size: 1.4vh;
  font-weight: 600;
  margin: 0;
  letter-spacing: -0.025em;
}

/* Controls Section */
.controls-section {
  display: flex;
  flex-direction: column;
  gap: 0.8vh;
}

/* 移除搜索相关样式 */

/* Control Panels */
.control-panels {
  display: flex;
  flex-direction: column;
  gap: 0.4vh;
}

.control-group {
  background: rgba(30, 41, 59, 0.8);
  border: 1px solid rgba(100, 255, 218, 0.08);
  border-radius: 0.8vh;
  padding: 0.8vh;
}

.group-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 0.8vh;
  color: #f8fafc;
  font-size: 1.3vh;
  font-weight: 500;
}

.group-header i {
  color: #64ffda;
  margin-right: 0.8vh;
  font-size: 1.2vh;
}

.control-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 1.2vh;
}

/* Rotation Display */
.rotation-display {
  display: flex;
  align-items: center;
  gap: 0.8vh;
  color: #94a3b8;
  font-size: 1.2vh;
}

.rotation-angle {
  background: rgba(30, 41, 59, 0.8);
  border: 1px solid rgba(100, 255, 218, 0.2);
  border-radius: 0.8vh;
  padding: 0.4vh 0.8vh;
  font-weight: 600;
  color: #64ffda;
  font-size: 1.1vh;
}

.reset-btn {
  background: rgba(30, 41, 59, 0.8);
  border: 1px solid rgba(167, 139, 250, 0.2);
  border-radius: 50%;
  width: 2.4vh;
  height: 2.4vh;
  color: #a78bfa;
  font-size: 1.1vh;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.reset-btn:hover {
  background: #a78bfa;
  color: #fff;
  border-color: #a78bfa;
  transform: rotate(90deg);
}

/* Body Controls */
.body-controls {
  display: flex;
  align-items: center;
  gap: 0.8vh;
}

.control-label {
  color: #94a3b8;
  font-size: 1.2vh;
  min-width: 3.2vh;
}

.control-btn {
  background: rgba(30, 41, 59, 0.8);
  border: 1px solid rgba(100, 255, 218, 0.2);
  border-radius: 50%;
  width: 2.4vh;
  height: 2.4vh;
  color: #64ffda;
  font-size: 1.1vh;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.control-btn:hover {
  background: #64ffda;
  color: #0f172a;
  border-color: #64ffda;
}

.body-value {
  font-size: 1.3vh;
  font-weight: 600;
  color: #f8fafc;
  min-width: 2.4vh;
  text-align: center;
}

/* Auto Toggle */
.auto-toggle {
  background: rgba(30, 41, 59, 0.8);
  border: 1px solid rgba(168, 85, 247, 0.2);
  border-radius: 2vh;
  padding: 0.6vh 1.2vh;
  color: #a78bfa;
  font-size: 1.2vh;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.6vh;
  transition: all 0.2s ease;
}

.auto-toggle:hover,
.auto-toggle.active {
  background: #a78bfa;
  color: #fff;
  border-color: #a78bfa;
}

/* Camera Buttons */
.camera-buttons {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  gap: 0.4vh;
  flex-wrap: wrap;
}

.camera-btn {
  background: rgba(30, 41, 59, 0.8);
  border: 1px solid rgba(168, 85, 247, 0.2);
  border-radius: 0.8vh;
  color: #a78bfa;
  font-size: 1.1vh;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2.8vh;
  height: 2.8vh;
}

.camera-btn:hover,
.camera-btn.active {
  background: #a78bfa;
  color: #fff;
  border-color: #a78bfa;
}

/* Category Navigation */
.category-nav {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 0.4vh;
  margin-bottom: 1vh;
}

.category-tab {
  background: rgba(30, 41, 59, 0.8);
  border: 1px solid rgba(100, 255, 218, 0.1);
  border-radius: 0.8vh;
  color: #94a3b8;
  padding: 0.6vh;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 0.3vh;
  font-size: 1vh;
  font-weight: 500;
  min-height: 5vh;
  text-align: center;
}

.category-tab:hover {
  background: rgba(100, 255, 218, 0.1);
  color: #64ffda;
  border-color: rgba(100, 255, 218, 0.3);
}

.category-tab.active {
  background: #64ffda;
  color: #0f172a;
  border-color: #64ffda;
  font-weight: 600;
}

.category-tab i {
  font-size: 1.5vh;
}

/* Clothes Selector */
.clothes-selector {
  background: rgba(30, 41, 59, 0.8);
  border: 1px solid rgba(100, 255, 218, 0.08);
  border-radius: 0.8vh;
  padding: 0.8vh;
  margin-bottom: 1vh;
}

.clothes-info {
  text-align: center;
  margin-bottom: 1vh;
}

.clothes-name {
  color: #f8fafc;
  font-size: 1.4vh;
  font-weight: 600;
  margin: 0 0 0.5vh 0;
}

.clothes-source {
  font-size: 0.9vh;
  padding: 0.2vh 0.6vh;
  border-radius: 0.4vh;
  font-weight: 500;
  display: inline-block;
}

.uniform-source {
  background: rgba(255, 107, 107, 0.2);
  color: #ff6b6b;
}

.regular-source {
  background: rgba(78, 205, 196, 0.2);
  color: #4ecdc4;
}

/* Navigation Controls */
.clothes-nav {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1vh;
  margin-bottom: 1vh;
}

.nav-btn {
  background: rgba(30, 41, 59, 0.8);
  border: 1px solid rgba(100, 255, 218, 0.2);
  border-radius: 50%;
  width: 3vh;
  height: 3vh;
  color: #64ffda;
  font-size: 1.2vh;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.nav-btn:hover:not(:disabled) {
  background: #64ffda;
  color: #0f172a;
  border-color: #64ffda;
}

.nav-btn:disabled {
  opacity: 0.3;
  cursor: not-allowed;
}

.nav-info {
  display: flex;
  align-items: center;
  gap: 0.4vh;
}

.page-input {
  width: 4vh;
  padding: 0.4vh;
  background: rgba(15, 23, 42, 0.8);
  border: 1px solid rgba(100, 255, 218, 0.2);
  border-radius: 0.4vh;
  color: #f8fafc;
  font-size: 1.1vh;
  text-align: center;
  outline: none;
}

.page-input:focus {
  border-color: #64ffda;
  box-shadow: 0 0 0 0.2vh rgba(100, 255, 218, 0.1);
}

.nav-separator {
  color: #94a3b8;
  font-size: 1.1vh;
}

.total-count {
  color: #94a3b8;
  font-size: 1.1vh;
  font-weight: 500;
}

/* Progress Container */
.progress-container {
  margin-bottom: 1vh;
}

.clothes-slider {
  width: 100%;
  height: 0.4vh;
  background: rgba(148, 163, 184, 0.2);
  border-radius: 0.2vh;
  outline: none;
  -webkit-appearance: none;
  appearance: none;
}

.clothes-slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 1.6vh;
  height: 1.6vh;
  background: #64ffda;
  border-radius: 50%;
  cursor: pointer;
}

.clothes-slider::-moz-range-thumb {
  width: 1.6vh;
  height: 1.6vh;
  background: #64ffda;
  border-radius: 50%;
  cursor: pointer;
  border: none;
}

/* Clothes Actions */
.clothes-actions {
  display: flex;
  gap: 0.4vh;
}

.action-btn {
  flex: 1;
  padding: 0.8vh 1.2vh;
  border-radius: 0.8vh;
  font-size: 1.1vh;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.3vh;
  border: 1px solid transparent;
  min-height: 3vh;
}

.action-btn.primary {
  background: #64ffda;
  color: #0f172a;
  font-weight: 600;
  border-color: #64ffda;
}

.action-btn.primary:hover:not(:disabled) {
  background: #ffffff;
  color: #64ffda;
  transform: translateY(-0.1vh);
}

.action-btn.primary:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.action-btn.secondary {
  background: rgba(30, 41, 59, 0.8);
  color: #94a3b8;
  border-color: rgba(148, 163, 184, 0.2);
}

.action-btn.secondary:hover:not(:disabled) {
  background: rgba(148, 163, 184, 0.15);
  color: #f8fafc;
  border-color: rgba(148, 163, 184, 0.4);
  transform: translateY(-0.1vh);
}

/* Empty State */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3vh;
  color: #94a3b8;
  text-align: center;
}

.empty-state i {
  font-size: 4vh;
  margin-bottom: 1vh;
  opacity: 0.5;
}

.empty-state p {
  font-size: 1.2vh;
  margin: 0;
  opacity: 0.8;
}




/* Uniform Section */
.uniform-section {
  margin-top: 0.8vh;
  flex-shrink: 0;
  min-height: 22vh;
  background: rgba(30, 41, 59, 0.6);
  border: 1px solid rgba(100, 255, 218, 0.15);
  border-radius: 0.8vh;
  padding: 0.8vh;
}

.uniform-section .section-header {
  margin-bottom: 0.6vh;
}

.uniform-section .section-header h3 {
  color: #f8fafc;
  font-size: 1.4vh;
  font-weight: 600;
  margin: 0;
}

.uniform-section .component-count {
  color: #64ffda;
  font-size: 1vh;
  font-weight: 600;
  background: rgba(100, 255, 218, 0.15);
  padding: 0.2vh 0.6vh;
  border-radius: 0.4vh;
}

.selected-components {
  background: rgba(30, 41, 59, 0.8);
  border: 1px solid rgba(100, 255, 218, 0.08);
  border-radius: 0.8vh;
  padding: 0.6vh;
  max-height: 10vh;
  overflow-y: auto;
  margin-bottom: 0.8vh;
}

.selected-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.8vh;
  background: rgba(15, 23, 42, 0.8);
  border: 1px solid rgba(100, 255, 218, 0.1);
  border-radius: 0.8vh;
  margin-bottom: 0.8vh;
  transition: all 0.2s ease;
}

.selected-item:hover {
  background: rgba(15, 23, 42, 0.9);
  border-color: rgba(100, 255, 218, 0.3);
}

.component-info {
  display: flex;
  align-items: center;
  gap: 0.8vh;
}

.component-info i {
  width: 2.4vh;
  height: 2.4vh;
  background: #64ffda;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #0f172a;
  font-size: 1vh;
}

.component-details {
  display: flex;
  flex-direction: column;
  gap: 0.1vh;
}

.component-name {
  color: #f8fafc;
  font-weight: 600;
  font-size: 1.1vh;
}

.component-specs {
  color: #94a3b8;
  font-size: 1vh;
}

.remove-btn {
  background: rgba(239, 68, 68, 0.2);
  border: 1px solid rgba(239, 68, 68, 0.3);
  border-radius: 50%;
  width: 2vh;
  height: 2vh;
  color: #fca5a5;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.9vh;
}

.remove-btn:hover {
  background: #ef4444;
  color: white;
  border-color: #ef4444;
}

.empty-selection {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2vh;
  color: #94a3b8;
  text-align: center;
}

.empty-selection i {
  font-size: 3vh;
  margin-bottom: 1vh;
  opacity: 0.5;
}

.empty-selection p {
  font-size: 1.2vh;
  margin: 0.3vh 0;
  opacity: 0.8;
}

/* Uniform Info */
.uniform-info {
  background: rgba(30, 41, 59, 0.8);
  border: 1px solid rgba(100, 255, 218, 0.08);
  border-radius: 0.8vh;
  padding: 0.6vh;
}

.form-group {
  margin-bottom: 0.6vh;
}

.form-group label {
  display: block;
  color: #94a3b8;
  font-size: 1vh;
  font-weight: 500;
  margin-bottom: 0.3vh;
}

.uniform-name-input,
.rank-select {
  width: 100%;
  padding: 0.6vh;
  background: rgba(15, 23, 42, 0.8);
  border: 1px solid rgba(100, 255, 218, 0.2);
  border-radius: 0.6vh;
  color: #f8fafc;
  font-size: 1.1vh;
  outline: none;
  transition: all 0.2s ease;
}

.uniform-name-input:focus,
.rank-select:focus {
  border-color: #64ffda;
  box-shadow: 0 0 0 0.2vh rgba(100, 255, 218, 0.1);
}

.form-row {
  display: flex;
  gap: 0.6vh;
}

.form-row .form-group {
  flex: 1;
}

/* Action Buttons */
.action-buttons {
  display: flex;
  gap: 0.4vh;
  margin-top: 0.6vh;
}

.action-btn {
  flex: 1;
  padding: 0.8vh 1.2vh;
  border-radius: 1vh;
  font-size: 1.1vh;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.3vh;
  border: 1px solid transparent;
  min-height: 3vh;
}

.action-btn.primary {
  background: #64ffda;
  color: #0f172a;
  font-weight: 600;
  border-color: #64ffda;
}

.action-btn.primary:hover:not(:disabled) {
  background: #ffffff;
  color: #64ffda;
  transform: translateY(-0.1vh);
}

.action-btn.primary:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.action-btn.secondary {
  background: rgba(30, 41, 59, 0.8);
  color: #94a3b8;
  border-color: rgba(148, 163, 184, 0.2);
}

.action-btn.secondary:hover {
  background: rgba(148, 163, 184, 0.15);
  color: #f8fafc;
  border-color: rgba(148, 163, 184, 0.4);
  transform: translateY(-0.1vh);
}

/* Utility Buttons */
.utility-buttons {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 0.4vh;
  margin-top: 0.6vh;
}

.utility-btn {
  padding: 0.8vh;
  border-radius: 0.8vh;
  font-size: 1vh;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 0.2vh;
  border: 1px solid transparent;
  min-height: 4vh;
}

.utility-btn.remove-all {
  background: rgba(239, 68, 68, 0.15);
  color: #fca5a5;
  border-color: rgba(239, 68, 68, 0.2);
}

.utility-btn.remove-all:hover {
  background: rgba(239, 68, 68, 0.25);
  color: #ef4444;
  border-color: rgba(239, 68, 68, 0.4);
  transform: translateY(-0.1vh);
}

.utility-btn.restore {
  background: rgba(34, 197, 94, 0.15);
  color: #86efac;
  border-color: rgba(34, 197, 94, 0.2);
}

.utility-btn.restore:hover {
  background: rgba(34, 197, 94, 0.25);
  color: #22c55e;
  border-color: rgba(34, 197, 94, 0.4);
  transform: translateY(-0.1vh);
}

.utility-btn i {
  font-size: 1.2vh;
}

/* Scrollbar */
.selected-components::-webkit-scrollbar {
  width: 0.3vh;
}

.selected-components::-webkit-scrollbar-track {
  background: transparent;
}

.selected-components::-webkit-scrollbar-thumb {
  background: #64ffda;
  border-radius: 0.3vh;
}

/* 数据源选择器样式 */
.data-source-section {
  margin-bottom: 0.8vh;
}

.data-source-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 0.4vh;
  margin-bottom: 0.6vh;
}

.source-tab {
  background: rgba(30, 41, 59, 0.8);
  border: 1px solid rgba(100, 255, 218, 0.1);
  border-radius: 0.8vh;
  color: #94a3b8;
  padding: 0.6vh;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 0.3vh;
  font-size: 1vh;
  font-weight: 500;
  min-height: 4vh;
  text-align: center;
}

.source-tab:hover {
  background: rgba(100, 255, 218, 0.1);
  color: #64ffda;
  border-color: rgba(100, 255, 218, 0.3);
}

.source-tab.active {
  background: #64ffda;
  color: #0f172a;
  border-color: #64ffda;
  font-weight: 600;
}

/* 紧凑服装网格样式 */
.section-controls {
  display: flex;
  align-items: center;
  gap: 1vh;
}

.item-count {
  color: #64ffda;
  font-size: 1vh;
  font-weight: 500;
  background: rgba(100, 255, 218, 0.1);
  padding: 0.2vh 0.6vh;
  border-radius: 0.4vh;
}

.view-toggle {
  display: flex;
  gap: 0.2vh;
}

.view-btn {
  background: rgba(30, 41, 59, 0.8);
  border: 1px solid rgba(100, 255, 218, 0.2);
  border-radius: 0.4vh;
  color: #94a3b8;
  width: 2vh;
  height: 2vh;
  font-size: 0.9vh;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.view-btn:hover {
  background: rgba(100, 255, 218, 0.1);
  color: #64ffda;
}

.view-btn.active {
  background: #64ffda;
  color: #0f172a;
}

.clothes-grid {
  display: grid;
  gap: 0.4vh;
  max-height: 40vh;
  overflow-y: auto;
}

.clothes-grid.list {
  grid-template-columns: 1fr;
}

.clothes-grid.compact {
  grid-template-columns: repeat(2, 1fr);
}

.clothes-item {
  background: rgba(30, 41, 59, 0.8);
  border: 1px solid rgba(100, 255, 218, 0.08);
  border-radius: 0.8vh;
  padding: 0.6vh;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 0.6vh;
  position: relative;
}

.clothes-item:hover {
  background: rgba(100, 255, 218, 0.1);
  border-color: rgba(100, 255, 218, 0.3);
  transform: translateY(-0.1vh);
}

.clothes-item.selected {
  background: rgba(100, 255, 218, 0.15);
  border-color: #64ffda;
}

.clothes-item.uniform {
  border-left: 2px solid #ff6b6b;
}

.clothes-item.regular {
  border-left: 2px solid #4ecdc4;
}

.item-preview {
  width: 3vh;
  height: 3vh;
  background: rgba(100, 255, 218, 0.1);
  border: 1px solid rgba(100, 255, 218, 0.2);
  border-radius: 0.6vh;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #64ffda;
  font-size: 1.2vh;
  flex-shrink: 0;
}

.item-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 0.1vh;
  min-width: 0;
}

.item-name {
  color: #f8fafc;
  font-size: 1vh;
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.item-id {
  color: #94a3b8;
  font-size: 0.8vh;
  font-family: monospace;
}

.item-source {
  font-size: 0.7vh;
  padding: 0.1vh 0.3vh;
  border-radius: 0.2vh;
  font-weight: 500;
  width: fit-content;
}

.uniform-source {
  background: rgba(255, 107, 107, 0.2);
  color: #ff6b6b;
}

.regular-source {
  background: rgba(78, 205, 196, 0.2);
  color: #4ecdc4;
}

.item-actions {
  display: flex;
  gap: 0.2vh;
  flex-shrink: 0;
}

.preview-btn,
.select-btn-compact {
  width: 1.8vh;
  height: 1.8vh;
  border: none;
  border-radius: 0.4vh;
  font-size: 0.8vh;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.preview-btn {
  background: rgba(168, 85, 247, 0.2);
  color: #a78bfa;
}

.preview-btn:hover {
  background: #a78bfa;
  color: #fff;
}

.select-btn-compact {
  background: rgba(100, 255, 218, 0.2);
  color: #64ffda;
}

.select-btn-compact:hover,
.select-btn-compact.active {
  background: #64ffda;
  color: #0f172a;
}

/* 滚动条样式 */
.clothes-grid::-webkit-scrollbar {
  width: 0.2vh;
}

.clothes-grid::-webkit-scrollbar-track {
  background: transparent;
}

.clothes-grid::-webkit-scrollbar-thumb {
  background: #64ffda;
  border-radius: 0.1vh;
}

/* Variables */
:root {
  --primary-color: #64ffda;
  --primary-dark: #4fd1c7;
}
</style>