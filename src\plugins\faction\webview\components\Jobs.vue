<template>
  <div class="jobs-container">
    <!-- 职位列表卡片 -->
    <div class="card jobs-list-card">
      <div class="card-header">
        <svg viewBox="0 0 24 24">
          <path
            d="M20,6H16V4A2,2 0 0,0 14,2H10A2,2 0 0,0 8,4V6H4C2.89,6 2,6.89 2,8V19A2,2 0 0,0 4,21H20A2,2 0 0,0 22,19V8C22,6.89 21.1,6 20,6M10,4H14V6H10V4M12,9A2.5,2.5 0 0,1 14.5,11.5A2.5,2.5 0 0,1 12,14A2.5,2.5 0 0,1 9.5,11.5A2.5,2.5 0 0,1 12,9M17,19H7V17.75C7,16.37 9.24,15.25 12,15.25C14.76,15.25 17,16.37 17,17.75V19Z" />
        </svg>
        <h2>职位管理</h2>

        <button v-if="(hasPermission('manage_members') || permissions.includes('admin'))"
          @click="showAddJobModal = true" class="add-job-btn">
          <svg viewBox="0 0 24 24">
            <path d="M19,13H13V19H11V13H5V11H11V5H13V11H19V13Z" />
          </svg>
          <span>添加职位</span>
        </button>
      </div>

      <div class="jobs-list">
        <div v-for="job in factionData?.jobs" :key="job.name" class="job-item">
          <div class="job-info">
            <div class="job-title-section">
              <div class="job-icon-wrapper">
                <svg viewBox="0 0 24 24">
                  <path
                    d="M12,15C7.58,15 4,16.79 4,19V21H20V19C20,16.79 16.42,15 12,15M8,9A4,4 0 0,0 12,13A4,4 0 0,0 16,9M11.5,2C11.2,2 11,2.21 11,2.5V5.5H10V3C10,3 7.75,3.86 7.75,6.75C7.75,6.75 7,6.89 7,8H17C16.95,6.89 16.25,6.75 16.25,6.75C16.25,3.86 14,3 14,3V5.5H13V2.5C13,2.21 12.81,2 12.5,2H11.5Z" />
                </svg>
              </div>
              <div>
                <h3>{{ job.name }}</h3>
                <div class="job-meta">
                  <span class="job-rank">等级 {{ job.rank }}</span>
                  <span class="job-salary">${{ (job.salary ?? 0).toLocaleString() }}</span>
                </div>
              </div>
            </div>

            <div class="job-permissions">
              <div class="permissions-label">权限：</div>
              <div class="permissions-tags">
                <span v-for="(p, idx) in job.permissions" :key="idx" class="permission-tag">
                  {{ p === 'manage_vehicles' ? '管理车辆' :
                    p === 'manage_funds' ? '管理资金' :
                      p === 'manage_members' ? '管理成员' :
                        p === 'assign_jobs' ? '分配工作' :
                          p === 'dissolve_faction' ? '解散派系' : p }}
                </span>
                <span v-if="!job.permissions.length" class="no-permissions">无权限</span>
              </div>
            </div>
          </div>

          <button v-if="(hasPermission('manage_members') || permissions.includes('admin'))"
            @click="openEditJobModal(job)" class="edit-job-btn">
            <svg viewBox="0 0 24 24">
              <path
                d="M20.71,7.04C21.1,6.65 21.1,6 20.71,5.63L18.37,3.29C18,2.9 17.35,2.9 16.96,3.29L15.12,5.12L18.87,8.87M3,17.25V21H6.75L17.81,9.93L14.06,6.18L3,17.25Z" />
            </svg>
            <span>编辑</span>
          </button>
        </div>
      </div>
    </div>

    <!-- 申请卡片 -->
    <div class="card applications-card">
      <div class="tabs">
        <div :class="['tab', { active: activeTab === 'jobApplication' }]" @click="activeTab = 'jobApplication'">
          职位申请
        </div>
        <div :class="['tab', { active: activeTab === 'jobApproval' }]" @click="activeTab = 'jobApproval'"
          v-if="hasPermission('manage_members') || permissions.includes('admin')">
          申请审批
        </div>
        <div :class="['tab', { active: activeTab === 'resignApproval' }]" @click="activeTab = 'resignApproval'"
          v-if="hasPermission('manage_members') || permissions.includes('admin')">
          离职审批
        </div>
      </div>

      <div class="tab-content">
        <!-- 职位申请面板 -->
        <div v-if="activeTab === 'jobApplication'" class="apply-panel">
          <div class="apply-intro">
            <svg viewBox="0 0 24 24">
              <path
                d="M20,20H4A2,2 0 0,1 2,18V6A2,2 0 0,1 4,4H20A2,2 0 0,1 22,6V18A2,2 0 0,1 20,20M4,6V18H20V6H4M6,9H18V11H6V9M6,13H16V15H6V13Z" />
            </svg>
            <div class="apply-text">
              <h3>申请晋升</h3>
              <p>您可以申请晋升到更高级别的职位，申请将由管理员审核。</p>
            </div>
          </div>

          <button @click="openJobApplicationModal" class="apply-btn">
            申请新职位
          </button>
        </div>

        <!-- 职位申请审核面板 -->
        <div v-if="activeTab === 'jobApproval' && (hasPermission('manage_members') || permissions.includes('admin'))"
          class="approval-panel">
          <div v-if="(factionData.jobApplications || []).length > 0" class="applications-list">
            <div
              v-for="application in [...(factionData.jobApplications || [])].sort((a, b) => b.timestamp - a.timestamp)"
              :key="application.id" class="application-item">
              <div class="application-info">
                <div class="applicant-name">{{ application.memberName }}</div>
                <div class="application-details">
                  <div>申请 <span class="highlight">{{ application.jobName }}</span></div>
                  <div class="job-meta">
                    <span>等级: {{ getJobRank(application.jobName) }}</span>
                    <span>薪水: ${{ getJobSalary(application.jobName) }}</span>
                  </div>
                  <div class="application-date">申请时间: {{ formatDate(new Date(application.timestamp)) }}</div>
                </div>
              </div>

              <div class="action-buttons">
                <button @click="handleJobApplication(application.id, true)" class="approve-btn">
                  <svg viewBox="0 0 24 24">
                    <path d="M21,7L9,19L3.5,13.5L4.91,12.09L9,16.17L19.59,5.59L21,7Z" />
                  </svg>
                  <span>通过</span>
                </button>
                <button @click="handleJobApplication(application.id, false)" class="reject-btn">
                  <svg viewBox="0 0 24 24">
                    <path
                      d="M19,6.41L17.59,5L12,10.59L6.41,5L5,6.41L10.59,12L5,17.59L6.41,19L12,13.41L17.59,19L19,17.59L13.41,12L19,6.41Z" />
                  </svg>
                  <span>拒绝</span>
                </button>
              </div>
            </div>
          </div>

          <div v-else class="empty-state">
            <svg viewBox="0 0 24 24">
              <path d="M20,2H4A2,2 0 0,0 2,4V22L6,18H20A2,2 0 0,0 22,16V4A2,2 0 0,0 20,2M20,16H6L4,18V4H20V16Z" />
            </svg>
            <p>暂无待处理的职位申请</p>
          </div>
        </div>

        <!-- 离职申请审核面板 -->
        <div v-if="activeTab === 'resignApproval' && (hasPermission('manage_members') || permissions.includes('admin'))"
          class="approval-panel">
          <div v-if="(factionData.resignationApplications || []).length > 0" class="applications-list">
            <div
              v-for="application in [...(factionData.resignationApplications || [])].sort((a, b) => b.timestamp - a.timestamp)"
              :key="application.memberId" class="application-item">
              <div class="application-info">
                <div class="applicant-name">{{ application.memberName }}</div>
                <div class="application-details">
                  <div>申请 <span class="highlight-warning">离职</span></div>
                  <div class="resignation-reason">
                    <div class="reason-label">离职原因:</div>
                    <div class="reason-text">{{ application.reason }}</div>
                  </div>
                </div>
              </div>

              <div class="action-buttons">
                <button @click="handleResignationApplication(application.memberId, true)" class="approve-btn">
                  <svg viewBox="0 0 24 24">
                    <path d="M21,7L9,19L3.5,13.5L4.91,12.09L9,16.17L19.59,5.59L21,7Z" />
                  </svg>
                  <span>通过</span>
                </button>
                <button @click="handleResignationApplication(application.memberId, false)" class="reject-btn">
                  <svg viewBox="0 0 24 24">
                    <path
                      d="M19,6.41L17.59,5L12,10.59L6.41,5L5,6.41L10.59,12L5,17.59L6.41,19L12,13.41L17.59,19L19,17.59L13.41,12L19,6.41Z" />
                  </svg>
                  <span>拒绝</span>
                </button>
              </div>
            </div>
          </div>

          <div v-else class="empty-state">
            <svg viewBox="0 0 24 24">
              <path d="M20,2H4A2,2 0 0,0 2,4V22L6,18H20A2,2 0 0,0 22,16V4A2,2 0 0,0 20,2M20,16H6L4,18V4H20V16Z" />
            </svg>
            <p>暂无待处理的离职申请</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 编辑职位模态框 -->
    <div v-if="showEditJobModal" class="modal-backdrop" @click="showEditJobModal = false"></div>
    <div v-if="showEditJobModal" class="modal">
      <div class="modal-card" @click.stop>
        <div class="modal-header">
          <h3>编辑职位</h3>
          <button @click="showEditJobModal = false" class="close-btn">
            <svg viewBox="0 0 24 24">
              <path
                d="M19,6.41L17.59,5L12,10.59L6.41,5L5,6.41L10.59,12L5,17.59L6.41,19L12,13.41L17.59,19L19,17.59L13.41,12L19,6.41Z" />
            </svg>
          </button>
        </div>

        <div class="modal-body">
          <div class="form-group">
            <label>职位名称</label>
            <input v-model="editingJob.name" placeholder="职位名称" class="form-input">
          </div>

          <div class="form-row">
            <div class="form-group">
              <label>职位等级</label>
              <input v-model.number="editingJob.rank" type="number" placeholder="职位等级" class="form-input">
            </div>

            <div class="form-group">
              <label>薪资</label>
              <input v-model.number="editingJob.salary" type="number" placeholder="职位薪水" class="form-input">
            </div>
          </div>

          <div class="form-group">
            <label>权限设置</label>
            <div class="permissions-checkboxes">
              <label class="checkbox-label" v-for="perm in permissionsList" :key="perm.value">
                <input type="checkbox" v-model="editingJob.permissions" :value="perm.value">
                <span class="checkbox-text">{{ perm.label }}</span>
              </label>
            </div>
          </div>
        </div>

        <div class="modal-footer">
          <button @click="showEditJobModal = false" class="cancel-btn">取消</button>
          <button @click="saveEditedJob" class="save-btn">保存</button>
        </div>
      </div>
    </div>

    <!-- 添加职位模态框 -->
    <div v-if="showAddJobModal" class="modal-backdrop" @click="showAddJobModal = false"></div>
    <div v-if="showAddJobModal" class="modal">
      <div class="modal-card" @click.stop>
        <div class="modal-header">
          <h3>添加新职位</h3>
          <button @click="showAddJobModal = false" class="close-btn">
            <svg viewBox="0 0 24 24">
              <path
                d="M19,6.41L17.59,5L12,10.59L6.41,5L5,6.41L10.59,12L5,17.59L6.41,19L12,13.41L17.59,19L19,17.59L13.41,12L19,6.41Z" />
            </svg>
          </button>
        </div>

        <div class="modal-body">
          <div class="form-group">
            <label>职位名称</label>
            <input v-model="newJobName" placeholder="职位名称" class="form-input">
          </div>

          <div class="form-row">
            <div class="form-group">
              <label>职位等级</label>
              <input v-model.number="newJobRank" type="number" placeholder="职位等级" class="form-input">
            </div>

            <div class="form-group">
              <label>薪资</label>
              <input v-model.number="newJobSalary" type="number" placeholder="职位薪水" class="form-input">
            </div>
          </div>

          <div class="form-group">
            <label>权限设置</label>
            <div class="permissions-checkboxes">
              <label class="checkbox-label" v-for="perm in permissionsList" :key="perm.value">
                <input type="checkbox" v-model="newJobPermissions" :value="perm.value">
                <span class="checkbox-text">{{ perm.label }}</span>
              </label>
            </div>
          </div>
        </div>

        <div class="modal-footer">
          <button @click="showAddJobModal = false" class="cancel-btn">取消</button>
          <button @click="addNewJob" class="save-btn">添加</button>
        </div>
      </div>
    </div>

    <!-- 职位申请模态框 -->
    <div v-if="showJobApplicationModal" class="modal-backdrop" @click="showJobApplicationModal = false"></div>
    <div v-if="showJobApplicationModal" class="modal">
      <div class="modal-card" @click.stop>
        <div class="modal-header">
          <h3>申请新职位</h3>
          <button @click="showJobApplicationModal = false" class="close-btn">
            <svg viewBox="0 0 24 24">
              <path
                d="M19,6.41L17.59,5L12,10.59L6.41,5L5,6.41L10.59,12L5,17.59L6.41,19L12,13.41L17.59,19L19,17.59L13.41,12L19,6.41Z" />
            </svg>
          </button>
        </div>

        <div class="modal-body">
          <template v-if="availableJobsToApply.length > 0">
            <p class="modal-text">请选择你想申请的职位：</p>

            <div class="job-selection">
              <div v-for="job in availableJobsToApply" :key="job.name"
                :class="['job-option', { selected: selectedJobToApply === job }]" @click="selectedJobToApply = job">
                <div class="job-option-title">{{ job.name }}</div>
                <div class="job-option-meta">
                  <span>等级: {{ job.rank }}</span>
                  <span>薪水: ${{ job.salary }}</span>
                </div>
                <svg v-if="selectedJobToApply === job" class="selected-check" viewBox="0 0 24 24">
                  <path d="M21,7L9,19L3.5,13.5L4.91,12.09L9,16.17L19.59,5.59L21,7Z" />
                </svg>
              </div>
            </div>
          </template>

          <div v-else class="empty-state">
            <svg viewBox="0 0 24 24">
              <path
                d="M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M12,4A8,8 0 0,1 20,12A8,8 0 0,1 12,20A8,8 0 0,1 4,12A8,8 0 0,1 12,4M11,16.5L18,9.5L16.59,8.09L11,13.67L7.91,10.59L6.5,12L11,16.5Z" />
            </svg>
            <p>暂无可申请的职位</p>
          </div>
        </div>

        <div class="modal-footer">
          <button @click="showJobApplicationModal = false" class="cancel-btn">关闭</button>
          <button v-if="availableJobsToApply.length > 0" @click="submitJobApplication" :disabled="!selectedJobToApply"
            class="save-btn">
            提交申请
          </button>
        </div>
      </div>
    </div>

  </div>
</template>

<script lang="ts" setup>
import { ref, computed, PropType } from 'vue';

const props = defineProps({
  factionData: {
    type: Object,
    required: true
  },
  job: {
    type: Object,
    required: true
  },
  permissions: {
    type: Array,
    required: true
  },
  hasPermission: {
    type: Function,
    required: true
  },
  formatDate: {
    type: Function,
    required: true
  },
  saveEditedJobAction: {
    type: Function as PropType<(job: any, oldName: string) => Promise<boolean>>,
    required: true
  },
  addNewJobAction: {
    type: Function as PropType<(job: any) => Promise<boolean>>,
    required: true
  },
  submitJobApplicationAction: {
    type: Function as PropType<(jobName: string) => Promise<boolean>>,
    required: true
  },
  handleJobApplicationAction: {
    type: Function as PropType<(applicationId: number, isAccepted: boolean) => Promise<boolean>>,
    required: true
  },
  handleResignationApplicationAction: {
    type: Function as PropType<(memberId: number, isAccepted: boolean) => Promise<boolean>>,
    required: true
  }
});

// 标签页切换
const activeTab = ref('jobApplication');

// 职位申请相关
const showJobApplicationModal = ref(false);
const selectedJobToApply = ref(null);

// 获取可申请的职位列表
const availableJobsToApply = computed(() => {
  if (!props.factionData?.jobs || !props.job) {
    return [];
  }
  const playerJobRank = props.factionData.jobs.find(j => j.name === props.job.name)?.rank || 0;
  // 只返回比当前玩家职位等级低一级的职位
  return props.factionData.jobs.filter(j => j.rank === playerJobRank - 1);
});

// 添加和编辑职位相关
const showAddJobModal = ref(false);
const newJobName = ref('');
const newJobRank = ref(0);
const newJobSalary = ref(0);
const newJobPermissions = ref([]);
const editingJob = ref(null);
const showEditJobModal = ref(false);
const oldJobName = ref('');

// 权限列表
const permissionsList = [
  { value: 'manage_vehicles', label: '管理车辆' },
  { value: 'manage_funds', label: '管理资金' },
  { value: 'manage_members', label: '管理成员' },
  { value: 'assign_jobs', label: '分配工作' },
  { value: 'dissolve_faction', label: '解散派系' }
];

// 判断是否可以添加新职位
const canAddNewJob = computed(() => {
  if (!props.factionData?.jobs) return false;
  // 通常0级是最高职位，且一般只能有一个
  const highestRankJob = props.factionData.jobs.find(j => j.rank === 0);
  return !highestRankJob;
});

function getJobRank(jobName) {
  const job = props.factionData?.jobs.find(j => j.name === jobName);
  return job ? job.rank : '未知';
}

function getJobSalary(jobName) {
  const job = props.factionData?.jobs.find(j => j.name === jobName);
  return job ? job.salary : '未知';
}

function openEditJobModal(job) {
  editingJob.value = { ...job };
  oldJobName.value = job.name;
  showEditJobModal.value = true;
}

async function saveEditedJob() {
  if (editingJob.value) {
    const success = await props.saveEditedJobAction(editingJob.value, oldJobName.value);
    if (success) {
      showEditJobModal.value = false;
      editingJob.value = null;
      oldJobName.value = '';
    }
  }
}

function openJobApplicationModal() {
  showJobApplicationModal.value = true;
}

async function submitJobApplication() {
  if (!selectedJobToApply.value) return;

  await props.submitJobApplicationAction(selectedJobToApply.value.name);
  showJobApplicationModal.value = false;
  selectedJobToApply.value = null;
}

async function addNewJob() {
  if (newJobName.value && newJobRank.value >= 0 && newJobSalary.value >= 0) {
    const newJob = {
      name: newJobName.value,
      rank: newJobRank.value,
      salary: newJobSalary.value,
      permissions: newJobPermissions.value || []
    };

    const success = await props.addNewJobAction(newJob);
    if (success) {
      newJobName.value = '';
      newJobRank.value = 0;
      newJobSalary.value = 0;
      newJobPermissions.value = [];
      showAddJobModal.value = false;
    }
  }
}

function handleJobApplication(applicationId, isAccepted) {
  return props.handleJobApplicationAction(applicationId, isAccepted);
}

function handleResignationApplication(memberId, isAccepted) {
  return props.handleResignationApplicationAction(memberId, isAccepted);
}

// 计算属性：pending job applications count
const pendingJobApplicationsCount = computed(() => {
  return (props.factionData?.jobApplications || []).length;
});

// 计算属性：pending resignation applications count
const pendingResignationApplicationsCount = computed(() => {
  return (props.factionData?.resignationApplications || []).length;
});
</script>

<style scoped>
/* 主容器 */
.jobs-container {
  display: grid;
  height: 100%;
  grid-template-columns: 1fr 1fr;
  grid-template-rows: auto;
  gap: 2vh;
}

/* 卡片通用样式 */
.card {
  background: rgba(34, 42, 53, 0.7);
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.card-header {
  display: flex;
  align-items: center;
  padding: 1.8vh 2vh;
  background: rgba(26, 32, 44, 0.5);
  border-bottom: 1px solid rgba(255, 255, 255, 0.08);
}

.card-header svg {
  width: 2vh;
  height: 2vh;
  margin-right: 1vh;
  fill: #a995f4;
}

.card-header h2 {
  font-size: 1.6vh;
  font-weight: 600;
  color: #e2e8f0;
  margin: 0;
  flex: 1;
}

/* 职位列表 */
.jobs-list {
  padding: 1vh;
  max-height: 50vh;
  overflow-y: auto;
}

.jobs-list::-webkit-scrollbar {
  width: 6px;
}

.jobs-list::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 3px;
}

.jobs-list::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 3px;
}

.jobs-list::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.3);
}

.job-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1.8vh;
  margin-bottom: 1vh;
  background: rgba(26, 32, 44, 0.3);
  border-radius: 8px;
  transition: all 0.2s;
}

.job-item:hover {
  background: rgba(26, 32, 44, 0.5);
}

.job-item:last-child {
  margin-bottom: 0;
}

.job-info {
  flex: 1;
}

.job-title-section {
  display: flex;
  align-items: center;
  margin-bottom: 1vh;
}

.job-icon-wrapper {
  width: 3.5vh;
  height: 3.5vh;
  border-radius: 50%;
  background: rgba(135, 116, 225, 0.15);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 1.5vh;
}

.job-icon-wrapper svg {
  width: 2vh;
  height: 2vh;
  fill: #a995f4;
}

.job-title-section h3 {
  font-size: 1.6vh;
  font-weight: 600;
  color: #e2e8f0;
  margin: 0;
  margin-bottom: 0.4vh;
}

.job-meta {
  display: flex;
  gap: 1.5vh;
  font-size: 1.2vh;
  color: #a0aec0;
}

.job-rank {
  color: #a995f4;
}

.job-salary {
  color: #68d391;
}

.job-permissions {
  display: flex;
  align-items: flex-start;
  gap: 1vh;
}

.permissions-label {
  font-size: 1.2vh;
  color: #a0aec0;
  white-space: nowrap;
}

.permissions-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 0.8vh;
}

.permission-tag {
  font-size: 1vh;
  padding: 0.4vh 0.8vh;
  background: rgba(135, 116, 225, 0.1);
  color: #a995f4;
  border-radius: 3vh;
  white-space: nowrap;
}

.no-permissions {
  font-size: 1vh;
  color: #a0aec0;
  font-style: italic;
}

/* 按钮样式 */
.add-job-btn {
  display: flex;
  align-items: center;
  padding: 0.8vh 1.5vh;
  background: rgba(135, 116, 225, 0.15);
  color: #a995f4;
  border-radius: 6px;
  font-size: 1.2vh;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  border: none;
}

.add-job-btn:hover {
  background: rgba(135, 116, 225, 0.3);
}

.add-job-btn svg {
  width: 1.6vh;
  height: 1.6vh;
  margin-right: 0.5vh;
  fill: #a995f4;
}

.edit-job-btn {
  display: flex;
  align-items: center;
  padding: 0.8vh 1.5vh;
  background: rgba(66, 153, 225, 0.1);
  color: #63b3ed;
  border-radius: 6px;
  font-size: 1.2vh;
  cursor: pointer;
  transition: all 0.2s;
  border: none;
  margin-left: 1vh;
}

.edit-job-btn:hover {
  background: rgba(66, 153, 225, 0.2);
}

.edit-job-btn svg {
  width: 1.6vh;
  height: 1.6vh;
  margin-right: 0.5vh;
  fill: #63b3ed;
}

/* 申请卡片样式 */
.applications-card {
  display: flex;
  flex-direction: column;
  grid-column: 2;
  grid-row: 1;
}

/* 选项卡样式 */
.tabs {
  display: flex;
  background: rgba(26, 32, 44, 0.5);
  border-bottom: 1px solid rgba(255, 255, 255, 0.08);
}

.tab {
  padding: 1.5vh 2vh;
  font-size: 1.4vh;
  cursor: pointer;
  color: #a0aec0;
  transition: all 0.2s;
  position: relative;
}

.tab:hover {
  color: #e2e8f0;
}

.tab.active {
  color: #a995f4;
}

.tab.active::after {
  content: '';
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  height: 2px;
  background: #a995f4;
}

.tab-content {
  padding: 2vh;
  max-height: 40vh;
  overflow-y: auto;
}

/* 申请面板 */
.apply-panel {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2vh;
}

.apply-intro {
  display: flex;
  align-items: center;
  padding: 2vh;
  background: rgba(26, 32, 44, 0.3);
  border-radius: 8px;
  width: 100%;
}

.apply-intro svg {
  width: 4vh;
  height: 4vh;
  fill: #a995f4;
  margin-right: 2vh;
}

.apply-text h3 {
  font-size: 1.6vh;
  font-weight: 600;
  color: #e2e8f0;
  margin-bottom: 0.6vh;
}

.apply-text p {
  font-size: 1.2vh;
  color: #a0aec0;
  margin: 0;
}

.apply-btn {
  padding: 1vh 3vh;
  background: #8774e1;
  color: white;
  border-radius: 6px;
  font-size: 1.4vh;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  border: none;
}

.apply-btn:hover {
  background: #6856b9;
  transform: translateY(-2px);
}

/* 申请审核面板 */
.approval-panel {
  padding: 0;
}

.applications-list {
  display: flex;
  flex-direction: column;
  gap: 1vh;
}

.application-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5vh;
  background: rgba(26, 32, 44, 0.3);
  border-radius: 8px;
}

.application-info {
  flex: 1;
}

.applicant-name {
  font-size: 1.5vh;
  font-weight: 600;
  color: #e2e8f0;
  margin-bottom: 0.5vh;
}

.application-details {
  font-size: 1.2vh;
  color: #a0aec0;
}

.highlight {
  color: #a995f4;
  font-weight: 500;
}

.highlight-warning {
  color: #f6ad55;
  font-weight: 500;
}

.application-date {
  font-size: 1.1vh;
  color: #718096;
  margin-top: 0.5vh;
}

.resignation-reason {
  display: flex;
  margin-top: 0.5vh;
}

.reason-label {
  color: #718096;
  margin-right: 0.5vh;
}

.reason-text {
  color: #e2e8f0;
}

.action-buttons {
  display: flex;
  gap: 1vh;
}

.approve-btn,
.reject-btn {
  display: flex;
  align-items: center;
  padding: 0.8vh 1.5vh;
  border-radius: 6px;
  font-size: 1.2vh;
  cursor: pointer;
  transition: all 0.2s;
  border: none;
}

.approve-btn {
  background: rgba(72, 187, 120, 0.1);
  color: #68d391;
}

.approve-btn:hover {
  background: rgba(72, 187, 120, 0.2);
}

.approve-btn svg {
  width: 1.6vh;
  height: 1.6vh;
  margin-right: 0.5vh;
  fill: #68d391;
}

.reject-btn {
  background: rgba(229, 62, 62, 0.1);
  color: #fc8181;
}

.reject-btn:hover {
  background: rgba(229, 62, 62, 0.2);
}

.reject-btn svg {
  width: 1.6vh;
  height: 1.6vh;
  margin-right: 0.5vh;
  fill: #fc8181;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4vh 0;
  color: #a0aec0;
}

.empty-state svg {
  width: 5vh;
  height: 5vh;
  fill: #a0aec0;
  margin-bottom: 1vh;
  opacity: 0.7;
}

.empty-state p {
  font-size: 1.4vh;
  margin: 0;
}

/* 模态框样式 */
.modal-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 100;
  animation: fadeIn 0.2s ease forwards;
}

.modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 101;
}

.modal-card {
  background: rgba(34, 42, 53, 0.95);
  width: 50vh;
  border-radius: 12px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.5);
  overflow: hidden;
  max-height: 90vh;
  display: flex;
  flex-direction: column;
  animation: scaleIn 0.2s ease forwards;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }

  to {
    opacity: 1;
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }

  to {
    opacity: 1;
    transform: scale(1);
  }
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 2vh;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.modal-header h3 {
  font-size: 1.8vh;
  font-weight: 500;
  color: #e2e8f0;
  margin: 0;
}

.close-btn {
  background: transparent;
  padding: 0.5vh;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s;
  border: none;
}

.close-btn:hover {
  background: rgba(255, 255, 255, 0.1);
}

.close-btn svg {
  width: 2vh;
  height: 2vh;
  fill: rgba(255, 255, 255, 0.5);
}

.modal-body {
  padding: 2vh;
  overflow-y: auto;
  flex: 1;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  gap: 1vh;
  padding: 1.5vh 2vh;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

/* 表单样式 */
.form-group {
  margin-bottom: 1.5vh;
}

.form-group:last-child {
  margin-bottom: 0;
}

.form-row {
  display: flex;
  gap: 1.5vh;
}

.form-row .form-group {
  flex: 1;
}

.form-group label {
  display: block;
  font-size: 1.2vh;
  color: #a0aec0;
  margin-bottom: 0.8vh;
}

.form-input {
  width: 100%;
  height: 4.5vh;
  padding: 0 1.5vh;
  background: rgba(26, 32, 44, 0.6);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 6px;
  color: #e2e8f0;
  font-size: 1.4vh;
}

.form-input:focus {
  outline: none;
  border-color: rgba(135, 116, 225, 0.5);
  box-shadow: 0 0 0 1px rgba(135, 116, 225, 0.2);
}

.permissions-checkboxes {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1vh;
}

.checkbox-label {
  display: flex;
  align-items: center;
  font-size: 1.3vh;
  color: #e2e8f0;
  cursor: pointer;
}

.checkbox-label input[type="checkbox"] {
  margin-right: 0.8vh;
}

.modal-text {
  font-size: 1.4vh;
  color: #a0aec0;
  margin-bottom: 2vh;
}

/* 职位选择 */
.job-selection {
  display: flex;
  flex-direction: column;
  gap: 1vh;
}

.job-option {
  padding: 1.5vh;
  background: rgba(26, 32, 44, 0.4);
  border: 1px solid rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s;
  position: relative;
}

.job-option:hover {
  background: rgba(26, 32, 44, 0.6);
  border-color: rgba(255, 255, 255, 0.1);
}

.job-option.selected {
  background: rgba(135, 116, 225, 0.1);
  border-color: rgba(135, 116, 225, 0.3);
}

.job-option-title {
  font-size: 1.5vh;
  font-weight: 500;
  color: #e2e8f0;
  margin-bottom: 0.8vh;
}

.job-option-meta {
  display: flex;
  gap: 1.5vh;
  font-size: 1.2vh;
  color: #a0aec0;
}

.selected-check {
  position: absolute;
  top: 1.5vh;
  right: 1.5vh;
  width: 2vh;
  height: 2vh;
  fill: #a995f4;
}

/* 按钮样式 */
.cancel-btn,
.save-btn {
  padding: 1vh 2vh;
  border-radius: 6px;
  font-size: 1.4vh;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  border: none;
}

.cancel-btn {
  background: rgba(255, 255, 255, 0.1);
  color: #e2e8f0;
}

.cancel-btn:hover {
  background: rgba(255, 255, 255, 0.15);
}

.save-btn {
  background: #8774e1;
  color: white;
}

.save-btn:hover {
  background: #6856b9;
}

.save-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}



.jobs-list-card {
  grid-column: 1;
  grid-row: 1;
}
</style>