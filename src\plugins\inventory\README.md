# 物品栏系统

## 功能

- 物品管理和操作
- 背包容量和重量系统
- 物品堆叠和分类
- 武器装备和弹药管理
- 存储箱系统
- 物品丢弃和拾取
- 衣服装备系统

## 服务器端使用

```typescript
import { useRebar } from '@Server/index.js';

const Rebar = useRebar();
const inventoryApi = await Rebar.useApi().getAsync('inventory-api');
const itemApi = await Rebar.useApi().getAsync('dbitem-api');

// 添加物品到玩家背包
const [success, message] = await inventoryApi.addItem('手枪', 1, { player });

// 从玩家背包移除物品
const [success, message] = await inventoryApi.subItem('手枪', 1, { player });

// 检查玩家是否有物品
const hasItem = await inventoryApi.hasitem('手枪', 1, { player });

// 获取物品数量
const quantity = await inventoryApi.getitemquantity('手枪', { player });

// 获取玩家背包
const inventory = await inventoryApi.getInventory({ player });

// 更新背包
await inventoryApi.updateInventory(inventory, { player });
```

## 物品管理

```typescript
// 创建新物品
await itemApi.createitem({
    name: '新物品',
    icon: 'item_icon',
    desc: '物品描述',
    weight: 1.0,
    type: '工具',
    maxStack: 10
});

// 获取物品信息
const item = await itemApi.getitem('手枪');

// 更新物品信息
await itemApi.updateitem('手枪', { weight: 2.0 });

// 删除物品
await itemApi.deleteitem('手枪');

// 获取所有物品
const allItems = await itemApi.getallitems();
```

## 存储箱系统

```typescript
// 创建存储箱
await inventoryApi.createstorge('我的箱子', 100, pos, {
    characterid: player.getStreamSyncedMeta('id')
});

// 添加物品到存储箱
await inventoryApi.addItem('手枪', 1, { storage: '我的箱子' });

// 从存储箱移除物品
await inventoryApi.subItem('手枪', 1, { storage: '我的箱子' });

// 获取存储箱内容
const storageItems = await inventoryApi.getInventory({ storage: '我的箱子' });
```

## 武器系统

```typescript
// 装备武器
await EquipWeapon(player, slot);

// 卸下武器
await UnequipWeapon(player);

// 装备弹药
await EquipBullet(player, slot);
```

## 使用示例

```typescript
// 玩家购买物品
const buyItems = [
    { name: '手枪', quantity: 1 },
    { name: '子弹', quantity: 30 }
];

if (await inventoryApi.canTheseItemsAddInBag(player, buyItems)) {
    for (const item of buyItems) {
        await inventoryApi.addItem(item.name, item.quantity, { player });
    }
    notifyApi.shownotify(player, '购买成功', 'success');
} else {
    notifyApi.shownotify(player, '背包空间不足', 'error');
}

// 玩家使用物品
const inventory = await inventoryApi.getInventory({ player });
const item = inventory.find(i => i.name === '医疗包');
if (item) {
    // 使用物品逻辑
    await inventoryApi.subItem('医疗包', 1, { player });
    // 恢复玩家血量
    player.health = 200;
}

// 创建掉落物品
await discardItem(player.pos, '手枪', 1, 'prop_cs_cardbox_01');
```

## 离线玩家操作

```typescript
// 使用角色ID操作离线玩家
await inventoryApi.addItem('手枪', 1, { characterId: 123 });
await inventoryApi.subItem('子弹', 10, { characterId: 123 });

// 获取离线玩家背包
const inventory = await inventoryApi.getInventory({ characterId: 123 });
```

## 物品类型

```typescript
type ItemType = '武器' | '消耗品' | '材料' | '工具' | '钥匙' | '衣服' | '食物';
```

## 客户端快捷键

- **I键** - 打开/关闭背包
- **1-5键** - 快速使用工具栏物品

## 物品效果注册

```typescript
import { registerItemEffect } from './server/itemEffects.js';

// 注册食物效果
registerItemEffect('foodapple', (player, item) => {
    player.health += 10;
    // 其他效果...
});

// 注册饮料效果
registerItemEffect('drinkwater', (player, item) => {
    player.health += 5;
    // 其他效果...
});

// 注册医疗包效果
registerItemEffect('medkit', (player, item) => {
    player.health = 200;
    player.armour = 100;
    // 其他效果...
});

// 注册武器效果
registerItemEffect('weapon_pistol', (player, item) => {
    // 装备武器逻辑
    player.giveWeapon(item.hash, 0);
});

// 注册自定义物品效果
registerItemEffect('custom_item', (player, item) => {
    // 自定义逻辑
    console.log(`玩家 ${player.name} 使用了 ${item.name}`);
    // 可以访问 item.customData 获取自定义数据
});
``` 