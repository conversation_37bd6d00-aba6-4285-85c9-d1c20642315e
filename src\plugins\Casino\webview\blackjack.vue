<template>
  <div class="blackjack-overlay">
    <div class="blackjack-container">
      <!-- 装饰性顶部霓虹灯效果 -->
      <div class="neon-border-top"></div>

      <!-- 顶部信息 -->
      <div class="header-section">
        <div class="casino-logo">
          <div class="logo-icon">♠♥♣♦</div>
          <div class="casino-info">
            <span class="casino-name">皇家赌场 · 21点</span>
            <span class="table-limits">
              <span class="limit-icon">💎</span>
              下注限额: ¥50 - ¥50,000
            </span>
          </div>
        </div>
        <button @click="closeGame" class="exit-btn">
          <span class="exit-icon">🚪</span>
          <span>离开赌桌</span>
        </button>
      </div>

      <!-- 游戏主区域 -->
      <div class="game-area">
        <!-- 庄家区域 -->
        <div class="dealer-area">
          <div class="area-header">
            <div class="dealer-badge">
              <span class="badge-icon">👤</span>
              <span class="area-title">庄家</span>
            </div>
            <div class="score-display">
              <span class="score-label">点数</span>
              <span class="score-value">{{ gameState.isPlayerTurn ? '?' : dealerHandValue }}</span>
            </div>
          </div>
          <div class="table-felt">
            <div class="cards-container">
              <div v-for="(card, index) in dealerCards" :key="index" class="playing-card"
                :class="{ 'face-down': card.hidden }" :style="{ '--card-index': index }">
                <div v-if="!card.hidden" class="card-face">
                  <div class="card-top">
                    <span class="card-value">{{ card.displayValue }}</span>
                    <span class="card-suit" :class="{ 'red': card.suit === '♥' || card.suit === '♦' }">
                      {{ card.suit }}
                    </span>
                  </div>
                  <div class="card-center">
                    <span class="center-suit" :class="{ 'red': card.suit === '♥' || card.suit === '♦' }">
                      {{ card.suit }}
                    </span>
                  </div>
                  <div class="card-bottom">
                    <span class="card-value">{{ card.displayValue }}</span>
                    <span class="card-suit" :class="{ 'red': card.suit === '♥' || card.suit === '♦' }">
                      {{ card.suit }}
                    </span>
                  </div>
                </div>
                <div v-else class="card-back">
                  <div class="back-pattern">
                    <div class="pattern-inner"></div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 分隔装饰线 -->
        <div class="table-divider">
          <div class="divider-ornament">♦ ♦ ♦</div>
        </div>

        <!-- 玩家区域 -->
        <div class="player-area">
          <div class="area-header">
            <div class="player-badge">
              <span class="badge-icon">🎩</span>
              <span class="area-title">玩家</span>
            </div>
            <div class="score-display">
              <span class="score-label">点数</span>
              <span class="score-value" :class="{ 'perfect': playerHandValue === 21, 'bust': playerHandValue > 21 }">
                {{ playerHandValue }}
              </span>
            </div>
          </div>
          <div class="table-felt">
            <div class="cards-container">
              <div v-for="(card, index) in playerCards" :key="index" class="playing-card"
                :style="{ '--card-index': index }">
                <div class="card-face">
                  <div class="card-top">
                    <span class="card-value">{{ card.displayValue }}</span>
                    <span class="card-suit" :class="{ 'red': card.suit === '♥' || card.suit === '♦' }">
                      {{ card.suit }}
                    </span>
                  </div>
                  <div class="card-center">
                    <span class="center-suit" :class="{ 'red': card.suit === '♥' || card.suit === '♦' }">
                      {{ card.suit }}
                    </span>
                  </div>
                  <div class="card-bottom">
                    <span class="card-value">{{ card.displayValue }}</span>
                    <span class="card-suit" :class="{ 'red': card.suit === '♥' || card.suit === '♦' }">
                      {{ card.suit }}
                    </span>
                  </div>
                </div>
              </div>
            </div>
            <!-- 下注区域 -->
            <div v-if="currentBet > 0" class="bet-area">
              <div class="chip-stack">
                <div v-for="n in Math.min(5, Math.ceil(currentBet / 1000))" :key="n" class="poker-chip"
                  :style="{ '--chip-index': n }">
                  <div class="chip-inner">
                    <span class="chip-value">{{ currentBet >= 5000 ? '5K' : currentBet >= 1000 ? '1K' : '100' }}</span>
                  </div>
                </div>
              </div>
              <div class="bet-display">
                <span class="bet-label">当前下注</span>
                <span class="bet-amount">¥{{ currentBet.toLocaleString() }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 侧边信息区 -->
      <div class="info-section">
        <!-- 玩家状态 -->
        <div class="player-status">
          <div class="status-header">
            <span class="header-icon">💰</span>
            <span>账户信息</span>
          </div>
          <div class="status-content">
            <div class="balance-display">
              <div class="balance-value">¥{{ playerChips.toLocaleString() }}</div>
              <div class="balance-label">可用筹码</div>
            </div>
            <div v-if="gameStats.gamesPlayed > 0" class="stats-grid">
              <div class="stat-card">
                <div class="stat-value">{{ gameStats.gamesPlayed }}</div>
                <div class="stat-label">总局数</div>
              </div>
              <div class="stat-card">
                <div class="stat-value">{{ (gameStats.wins / gameStats.gamesPlayed * 100).toFixed(0) }}%</div>
                <div class="stat-label">胜率</div>
              </div>
            </div>
          </div>
        </div>

        <!-- 游戏规则 -->
        <div class="rules-card">
          <div class="card-header">
            <span class="header-icon">📋</span>
            <span>游戏规则</span>
          </div>
          <div class="card-content">
            <div class="rule-item">
              <span class="rule-icon">🎯</span>
              <span>目标：接近21点但不爆牌</span>
            </div>
            <div class="rule-item">
              <span class="rule-icon">🃏</span>
              <span>J、Q、K = 10点</span>
            </div>
            <div class="rule-item">
              <span class="rule-icon">A</span>
              <span>A = 1点或11点</span>
            </div>
          </div>
        </div>

        <!-- 装饰元素 -->
        <div class="decoration-card">
          <div class="lucky-symbols">
            <span>🍀</span>
            <span>🎰</span>
            <span>💎</span>
          </div>
          <div class="luck-text">祝您好运！</div>
        </div>
      </div>

      <!-- 控制区域 -->
      <div class="control-section">
        <!-- 下注阶段 -->
        <div v-if="!gameState.gameStarted" class="betting-controls">
          <div class="betting-title">
            <span class="title-icon">💰</span>
            <span>选择下注金额</span>
          </div>
          <div class="chip-selection">
            <button v-for="amount in [100, 500, 1000, 5000]" :key="amount" @click="setBetAmount(amount)"
              class="chip-btn" :class="{ active: betAmount === amount }">
              <div class="chip-design">
                <div class="chip-edge"></div>
                <div class="chip-center">
                  <span class="chip-amount">{{ amount >= 1000 ? amount / 1000 + 'K' : amount }}</span>
                </div>
              </div>
            </button>
          </div>
          <div class="bet-input-area">
            <div class="input-wrapper">
              <span class="currency-symbol">¥</span>
              <input v-model.number="betAmount" type="number" min="50" max="50000" class="bet-input"
                placeholder="自定义金额">
            </div>
            <button @click="startGame" class="start-btn" :disabled="!canStartGame">
              <span class="btn-icon">🃏</span>
              <span>发牌</span>
            </button>
          </div>
        </div>

        <!-- 游戏操作 -->
        <div v-else-if="gameState.isPlayerTurn && !gameState.isGameOver" class="action-controls">
          <button @click="hit" class="action-btn hit" :disabled="playerHandValue >= 21">
            <div class="btn-content">
              <span class="btn-icon">👆</span>
              <span class="btn-text">要牌</span>
              <span class="btn-subtext">HIT</span>
            </div>
          </button>
          <button @click="stand" class="action-btn stand">
            <div class="btn-content">
              <span class="btn-icon">✋</span>
              <span class="btn-text">停牌</span>
              <span class="btn-subtext">STAND</span>
            </div>
          </button>
          <button v-if="canDouble" @click="doubleDown" class="action-btn double">
            <div class="btn-content">
              <span class="btn-icon">⚡</span>
              <span class="btn-text">加倍</span>
              <span class="btn-subtext">DOUBLE</span>
            </div>
          </button>
        </div>

        <!-- 游戏结果 -->
        <div v-else-if="gameState.isGameOver" class="result-display">
          <div class="result-card" :class="gameState.result">
            <div class="result-glow"></div>
            <div class="result-content">
              <div class="result-icon">
                {{ gameState.result === 'win' ? '🏆' : gameState.result === 'lose' ? '💔' : '🤝' }}
              </div>
              <div class="result-text">{{ getResultMessage() }}</div>
              <div class="result-detail">
                <div class="score-comparison">
                  <span>玩家: {{ playerHandValue }}</span>
                  <span class="vs">VS</span>
                  <span>庄家: {{ dealerHandValue }}</span>
                </div>
              </div>
            </div>
            <div class="payout-info">
              <span v-if="gameState.payout > 0" class="win-amount">
                <span class="plus">+</span>¥{{ gameState.payout.toLocaleString() }}
              </span>
              <span v-else class="loss-amount">
                <span class="minus">-</span>¥{{ currentBet.toLocaleString() }}
              </span>
            </div>
            <button @click="newGame" class="new-game-btn">
              <span class="btn-icon">🔄</span>
              <span>再来一局</span>
            </button>
          </div>
        </div>

        <!-- 等待提示 -->
        <div v-else class="waiting-display">
          <div class="waiting-content">
            <div class="dealer-thinking">
              <span class="thinking-icon">🎲</span>
              <span>庄家思考中</span>
            </div>
            <div class="loading-dots">
              <span></span>
              <span></span>
              <span></span>
            </div>
          </div>
        </div>
      </div>

      <!-- 底部装饰 -->
      <div class="neon-border-bottom"></div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, onBeforeUnmount } from 'vue';
import { useEvents } from '@Composables/useEvents.js';

const events = useEvents();

// 音效管理
const audioContext = ref<AudioContext | null>(null);
const audioFiles = ref({
  backgroundMusic: null as HTMLAudioElement | null,
  cardFlip: null as HTMLAudioElement | null,
  cardDeal: null as HTMLAudioElement | null,
  cardShuffle: null as HTMLAudioElement | null, // 新增洗牌音效
  chipPlace: null as HTMLAudioElement | null,
  buttonClick: null as HTMLAudioElement | null,
  win: null as HTMLAudioElement | null,
  lose: null as HTMLAudioElement | null,
  push: null as HTMLAudioElement | null,
  ambience: null as HTMLAudioElement | null
});

// 音效播放函数
const playSound = (soundName: keyof typeof audioFiles.value, volume = 0.5) => {
  try {
    const audio = audioFiles.value[soundName];
    if (audio) {
      audio.volume = volume;
      audio.currentTime = 0;
      audio.play().catch(e => console.log('音效播放失败:', e));
    }
  } catch (error) {
    console.log('音效错误:', error);
  }
};

// 创建音效元素
const createAudioElement = (frequency: number, duration: number, type: OscillatorType = 'sine'): HTMLAudioElement => {
  const audio = new Audio();
  return audio;
};

// 初始化音效
const initAudio = () => {
  // 创建Web Audio Context
  if (!audioContext.value) {
    audioContext.value = new (window.AudioContext || (window as any).webkitAudioContext)();
  }

  // 加载真实的赌场背景音乐
  setTimeout(() => {
    console.log('加载背景音乐');
    audioFiles.value.backgroundMusic = new Audio('/sounds/casion.ogg');
    audioFiles.value.backgroundMusic.loop = true;
    audioFiles.value.backgroundMusic.volume = 0.3;

  }, 500);

  // 创建各种音效
  setTimeout(() => {
    // 使用真实的发牌音效文件
    audioFiles.value.cardDeal = new Audio('../sounds/sendpork.ogg');
    audioFiles.value.cardDeal.volume = 0.8;
    audioFiles.value.cardDeal.preload = 'auto';

    // 使用真实的洗牌音效文件
    audioFiles.value.cardShuffle = new Audio('../sounds/washpork.ogg');
    audioFiles.value.cardShuffle.volume = 0.5;
    audioFiles.value.cardShuffle.preload = 'auto';
    

    // 卡片翻牌音效保持原来的生成版本
    audioFiles.value.cardFlip = createSyntheticSound([
      { freq: 800, duration: 0.05, volume: 0.3 },
      { freq: 400, duration: 0.1, volume: 0.2 },
      { freq: 200, duration: 0.05, volume: 0.1 }
    ]);

    // 筹码音效 - 金属碰撞声
    audioFiles.value.chipPlace = createSyntheticSound([
      { freq: 1200, duration: 0.05, volume: 0.4 },
      { freq: 800, duration: 0.1, volume: 0.3 },
      { freq: 600, duration: 0.05, volume: 0.2 }
    ]);

    // 按钮点击音效
    audioFiles.value.buttonClick = createSyntheticSound([
      { freq: 800, duration: 0.05, volume: 0.3 }
    ]);

    // 胜利音效 - 上升的音调
    audioFiles.value.win = createSyntheticSound([
      { freq: 523, duration: 0.2, volume: 0.5 }, // C5
      { freq: 659, duration: 0.2, volume: 0.6 }, // E5
      { freq: 784, duration: 0.3, volume: 0.7 }, // G5
      { freq: 1047, duration: 0.3, volume: 0.8 } // C6
    ]);

    // 失败音效 - 下降的音调
    audioFiles.value.lose = createSyntheticSound([
      { freq: 392, duration: 0.3, volume: 0.6 }, // G4
      { freq: 330, duration: 0.3, volume: 0.5 }, // E4
      { freq: 262, duration: 0.5, volume: 0.4 }, // C4
      { freq: 196, duration: 0.4, volume: 0.3 }  // G3
    ]);

    // 平局音效 - 中性音调
    audioFiles.value.push = createSyntheticSound([
      { freq: 440, duration: 0.3, volume: 0.5 }, // A4
      { freq: 440, duration: 0.3, volume: 0.4 }, // A4
      { freq: 440, duration: 0.2, volume: 0.3 }  // A4
    ]);

    // 保留原来的环境音效作为备用
    audioFiles.value.ambience = createAmbienceSound();
  }, 100);
};

// 创建合成音效
const createSyntheticSound = (notes: Array<{ freq: number; duration: number; volume: number }>) => {
  if (!audioContext.value) return null;

  const totalDuration = notes.reduce((sum, note) => sum + note.duration, 0);
  const sampleRate = audioContext.value.sampleRate;
  const buffer = audioContext.value.createBuffer(1, sampleRate * totalDuration, sampleRate);
  const data = buffer.getChannelData(0);

  let currentTime = 0;

  notes.forEach(note => {
    const startSample = Math.floor(currentTime * sampleRate);
    const endSample = Math.floor((currentTime + note.duration) * sampleRate);

    for (let i = startSample; i < endSample && i < data.length; i++) {
      const t = (i - startSample) / sampleRate;
      const envelope = Math.exp(-t * 3); // 指数衰减包络
      data[i] += Math.sin(2 * Math.PI * note.freq * t) * note.volume * envelope;
    }

    currentTime += note.duration;
  });

  // 创建音频元素
  const audio = new Audio();
  const audioBlob = bufferToWave(buffer, buffer.length);
  audio.src = URL.createObjectURL(audioBlob);
  return audio;
};

// 创建背景环境音效
const createAmbienceSound = () => {
  if (!audioContext.value) return null;

  const duration = 10; // 10秒循环
  const sampleRate = audioContext.value.sampleRate;
  const buffer = audioContext.value.createBuffer(1, sampleRate * duration, sampleRate);
  const data = buffer.getChannelData(0);

  // 添加多层低频音效模拟赌场环境
  for (let i = 0; i < data.length; i++) {
    const t = i / sampleRate;

    // 低频持续音
    data[i] += Math.sin(2 * Math.PI * 60 * t) * 0.05 * (1 + Math.sin(2 * Math.PI * 0.1 * t) * 0.3);

    // 轻微的噪音
    data[i] += (Math.random() - 0.5) * 0.02;

    // 偶尔的高频闪烁
    if (Math.random() < 0.001) {
      data[i] += Math.sin(2 * Math.PI * 1000 * t) * 0.03 * Math.exp(-((i % 1000) / 100));
    }
  }

  const audio = new Audio();
  const audioBlob = bufferToWave(buffer, buffer.length);
  audio.src = URL.createObjectURL(audioBlob);
  audio.loop = true;
  audio.volume = 0.15;
  return audio;
};

// 将AudioBuffer转换为WAV格式
const bufferToWave = (abuffer: AudioBuffer, len: number) => {
  const numOfChan = abuffer.numberOfChannels;
  const length = len * numOfChan * 2 + 44;
  const buffer = new ArrayBuffer(length);
  const view = new DataView(buffer);
  const channels = [];
  let sample;
  let offset = 0;
  let pos = 0;

  // WAV文件头
  setUint32(0x46464952); // "RIFF"
  setUint32(length - 8); // file length - 8
  setUint32(0x45564157); // "WAVE"
  setUint32(0x20746d66); // "fmt " chunk
  setUint32(16); // length = 16
  setUint16(1); // PCM (uncompressed)
  setUint16(numOfChan);
  setUint32(abuffer.sampleRate);
  setUint32(abuffer.sampleRate * 2 * numOfChan); // avg. bytes/sec
  setUint16(numOfChan * 2); // block-align
  setUint16(16); // 16-bit (hardcoded in this demo)
  setUint32(0x61746164); // "data" - chunk
  setUint32(length - pos - 4); // chunk length

  // 写入PCM样本
  for (let i = 0; i < abuffer.numberOfChannels; i++)
    channels.push(abuffer.getChannelData(i));

  while (pos < length) {
    for (let i = 0; i < numOfChan; i++) {
      sample = Math.max(-1, Math.min(1, channels[i][offset])); // clamp
      sample = (0.5 + sample < 0 ? sample * 32768 : sample * 32767) | 0; // scale to 16-bit signed int
      view.setInt16(pos, sample, true); // write 16-bit sample
      pos += 2;
    }
    offset++
  }

  function setUint16(data: number) {
    view.setUint16(pos, data, true);
    pos += 2;
  }

  function setUint32(data: number) {
    view.setUint32(pos, data, true);
    pos += 4;
  }

  return new Blob([buffer], { type: 'audio/wav' });
};

// 游戏状态
const gameState = ref({
  gameStarted: false,
  isPlayerTurn: true,
  isGameOver: false,
  result: '' as 'win' | 'lose' | 'push' | '',
  payout: 0
});

// 卡牌接口
interface Card {
  suit: string;
  value: number;
  displayValue: string;
  hidden?: boolean;
}

// 游戏数据
const playerCards = ref<Card[]>([]);
const dealerCards = ref<Card[]>([]);
const playerChips = ref(10000);
const betAmount = ref(100);
const currentBet = ref(0);

// 游戏统计
const gameStats = ref({
  gamesPlayed: 0,
  wins: 0
});

// 计算属性
const playerHandValue = computed(() => calculateHandValue(playerCards.value));
const dealerHandValue = computed(() => calculateHandValue(dealerCards.value.filter(card => !card.hidden)));

const canStartGame = computed(() => {
  return betAmount.value >= 50 && betAmount.value <= playerChips.value && betAmount.value <= 50000;
});

const canDouble = computed(() => {
  return playerCards.value.length === 2 && playerChips.value >= currentBet.value && playerHandValue.value < 21;
});

// 计算手牌点数
const calculateHandValue = (cards: Card[]): number => {
  let value = 0;
  let aces = 0;

  cards.forEach(card => {
    if (card.value === 1) {
      aces++;
      value += 11;
    } else if (card.value > 10) {
      value += 10;
    } else {
      value += card.value;
    }
  });

  while (value > 21 && aces > 0) {
    value -= 10;
    aces--;
  }

  return value;
};

// 游戏方法
const setBetAmount = (amount: number) => {
  if (!gameState.value.gameStarted) {
    betAmount.value = amount;
    playSound('chipPlace', 0.3);
  }
};

const startGame = () => {
  if (!canStartGame.value) return;

  currentBet.value = betAmount.value;
  playSound('chipPlace', 0.4);
  events.emitServer('blackjack:startGame', { bet: betAmount.value });
};

const hit = () => {
  if (gameState.value.isPlayerTurn && !gameState.value.isGameOver) {
    playSound('buttonClick', 0.3);
    events.emitServer('blackjack:hit');
  }
};

const stand = () => {
  if (gameState.value.isPlayerTurn && !gameState.value.isGameOver) {
    playSound('buttonClick', 0.3);
    events.emitServer('blackjack:stand');
  }
};

const doubleDown = () => {
  if (canDouble.value) {
    playSound('chipPlace', 0.4);
    events.emitServer('blackjack:double');
  }
};

const newGame = () => {
  playSound('buttonClick', 0.3);
  
  // 播放洗牌音效，表示准备新一局
  setTimeout(() => {
    playSound('cardShuffle', 0.4);
  }, 200);
  
  gameState.value = {
    gameStarted: false,
    isPlayerTurn: true,
    isGameOver: false,
    result: '',
    payout: 0
  };
  playerCards.value = [];
  dealerCards.value = [];
  currentBet.value = 0;
  events.emitServer('blackjack:newGame');
};

const closeGame = () => {
  playSound('buttonClick', 0.3);
  // 停止背景音乐和环境音效
  if (audioFiles.value.backgroundMusic) {
    audioFiles.value.backgroundMusic.pause();
    audioFiles.value.backgroundMusic.currentTime = 0;
  }
  if (audioFiles.value.ambience) {
    audioFiles.value.ambience.pause();
    audioFiles.value.ambience.currentTime = 0;
  }
  events.emitServer('blackjack:close');
};

const getResultMessage = () => {
  switch (gameState.value.result) {
    case 'win':
      return '恭喜获胜！';
    case 'lose':
      return '很遗憾，你输了';
    case 'push':
      return '平局';
    default:
      return '';
  }
};

// 事件监听
onMounted(() => {
  // 初始化音效
  initAudio();

  events.on('blackjack:gameData', (data: any) => {
    playerChips.value = data.chips;
    if (data.stats) {
      gameStats.value = data.stats;
    }
  });

  events.on('blackjack:gameStarted', (data: any) => {
    gameState.value.gameStarted = true;
    gameState.value.isPlayerTurn = true;
    gameState.value.isGameOver = false;
    playerCards.value = data.playerCards;
    dealerCards.value = data.dealerCards;
    playerChips.value = data.newChips;

    // 首先播放洗牌音效
    playSound('cardShuffle', 0.5);
    
    // 延迟播放发牌音效
    setTimeout(() => {
      playSound('cardDeal', 0.4);
    }, 1000);

    // 启动背景音乐
    setTimeout(() => {
      if (audioFiles.value.backgroundMusic) {
        audioFiles.value.backgroundMusic.play().catch(e => {
          console.log('背景音乐播放失败，尝试播放环境音效:', e);
          // 如果背景音乐播放失败，使用环境音效
          if (audioFiles.value.ambience) {
            audioFiles.value.ambience.play().catch(e2 => console.log('环境音效也播放失败:', e2));
          }
        });
      }
    }, 500);
  });

  events.on('blackjack:playerHit', (data: any) => {
    playerCards.value = data.playerCards;

    // 播放翻牌音效
    playSound('cardFlip', 0.4);

    if (data.bust) {
      gameState.value.isPlayerTurn = false;
      gameState.value.isGameOver = true;
      gameState.value.result = 'lose';
      gameState.value.payout = 0;
    }
  });

  events.on('blackjack:dealerTurn', (data: any) => {
    gameState.value.isPlayerTurn = false;
    dealerCards.value = data.dealerCards;

    // 播放庄家翻牌音效
    playSound('cardFlip', 0.3);
  });

  events.on('blackjack:dealerHit', (data: any) => {
    dealerCards.value = data.dealerCards;

    // 播放庄家要牌音效
    playSound('cardDeal', 0.3);
  });

  events.on('blackjack:gameEnd', (data: any) => {
    gameState.value.isGameOver = true;
    gameState.value.result = data.result;
    gameState.value.payout = data.payout;
    playerChips.value = data.newChips;
    dealerCards.value = data.dealerCards;

    gameStats.value.gamesPlayed++;
    if (data.result === 'win') {
      gameStats.value.wins++;
    }

    // 根据结果播放不同音效
    setTimeout(() => {
      switch (data.result) {
        case 'win':
          playSound('win', 0.7);
          break;
        case 'lose':
          playSound('lose', 0.6);
          break;
        case 'push':
          playSound('push', 0.5);
          break;
      }
    }, 500);
  });

  events.on('blackjack:updateChips', (chips: number) => {
    playerChips.value = chips;
  });
});

onBeforeUnmount(() => {
  // 组件卸载时停止所有音效
  if (audioFiles.value.backgroundMusic) {
    audioFiles.value.backgroundMusic.pause();
    audioFiles.value.backgroundMusic.currentTime = 0;
  }
  if (audioFiles.value.ambience) {
    audioFiles.value.ambience.pause();
    audioFiles.value.ambience.currentTime = 0;
  }

  // 清理所有音频资源
  Object.values(audioFiles.value).forEach(audio => {
    if (audio) {
      audio.pause();
      audio.currentTime = 0;
      // 只清理生成的blob URL，不清理真实音频文件
      if (audio.src.startsWith('blob:')) {
        URL.revokeObjectURL(audio.src);
      }
    }
  });
});
</script>

<style scoped>
.blackjack-overlay {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  align-items: center;
  justify-content: center;
  font-family: 'Georgia', 'Times New Roman', serif;
}



.blackjack-container {
  width: 80vw;
  height: 99vh;
  background:
    radial-gradient(ellipse at center, #0a5d3a 0%, #063d25 40%, #032015 100%);
  border: 0.4vh solid #d4af37;
  border-radius: 1.5vh;
  box-shadow:
    0 1.5vh 3vh rgba(0, 0, 0, 0.6),
    inset 0 0.2vh 0.5vh rgba(212, 175, 55, 0.3),
    0 0 8vh rgba(212, 175, 55, 0.15);
  display: grid;
  grid-template-columns: 2.8fr 1fr;
  grid-template-rows: auto 1fr auto;
  grid-template-areas:
    "header header"
    "game info"
    "control control";
  gap: 1.2vh;
  padding: 2vh;
  color: white;
  position: relative;
  overflow: hidden;
}

/* 霓虹灯装饰边框 */
.neon-border-top,
.neon-border-bottom {
  position: absolute;
  left: 0;
  right: 0;
  height: 0.3vh;
  background: linear-gradient(90deg,
      transparent 0%,
      #d4af37 20%,
      #ffed4e 50%,
      #d4af37 80%,
      transparent 100%);
  opacity: 0.8;
}

.neon-border-top {
  top: 0;
  box-shadow: 0 0.5vh 2vh rgba(212, 175, 55, 0.5);
}

.neon-border-bottom {
  bottom: 0;
  box-shadow: 0 -0.5vh 2vh rgba(212, 175, 55, 0.5);
}

/* 顶部信息栏 */
.header-section {
  grid-area: header;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5vh 2.5vw;
  background:
    linear-gradient(135deg, rgba(139, 69, 19, 0.4) 0%, rgba(160, 82, 45, 0.2) 100%),
    rgba(0, 0, 0, 0.4);
  border-radius: 1vh;
  border: 0.2vh solid rgba(212, 175, 55, 0.5);
  box-shadow:
    inset 0 0.2vh 0.5vh rgba(212, 175, 55, 0.2),
    0 0.5vh 1vh rgba(0, 0, 0, 0.3);
}

.casino-logo {
  display: flex;
  align-items: center;
  gap: 1.5vw;
}

.logo-icon {
  font-size: 3vh;
  color: #d4af37;
  text-shadow:
    0 0 1vh rgba(212, 175, 55, 0.8),
    0 0 2vh rgba(212, 175, 55, 0.4);
  letter-spacing: 0.3vw;
}

.casino-info {
  display: flex;
  flex-direction: column;
  gap: 0.6vh;
}

.casino-name {
  font-size: 2.4vh;
  font-weight: bold;
  color: #ffed4e;
  text-shadow:
    0.1vh 0.1vh 0.3vh rgba(0, 0, 0, 0.8),
    0 0 1vh rgba(255, 237, 78, 0.5);
  letter-spacing: 0.1vw;
}

.table-limits {
  font-size: 1.5vh;
  color: #f0e6d2;
  display: flex;
  align-items: center;
  gap: 0.5vw;
}

.limit-icon {
  font-size: 1.3vh;
}

.exit-btn {
  padding: 1.2vh 2.5vw;
  background:
    linear-gradient(135deg, #8b4513 0%, #a0522d 50%, #8b4513 100%);
  color: #f0e6d2;
  border: 0.2vh solid #d4af37;
  border-radius: 0.8vh;
  font-size: 1.6vh;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  gap: 0.8vw;
  box-shadow:
    0 0.3vh 0.8vh rgba(0, 0, 0, 0.4),
    inset 0 0.1vh 0.3vh rgba(212, 175, 55, 0.3);
}

.exit-btn:hover {
  background:
    linear-gradient(135deg, #a0522d 0%, #cd853f 50%, #a0522d 100%);
  transform: translateY(-0.1vh);
  box-shadow:
    0 0.5vh 1.2vh rgba(0, 0, 0, 0.5),
    inset 0 0.1vh 0.3vh rgba(212, 175, 55, 0.4);
}

.exit-icon {
  font-size: 1.8vh;
}

/* 游戏主区域 */
.game-area {
  grid-area: game;
  display: flex;
  flex-direction: column;
  gap: 0.8vh;
  overflow-y: auto;
  max-height: 100%;
}

.dealer-area,
.player-area {
  background:
    radial-gradient(ellipse at center, rgba(13, 79, 60, 0.3) 0%, rgba(10, 61, 46, 0.2) 100%),
    rgba(0, 0, 0, 0.3);
  border: 0.3vh solid rgba(212, 175, 55, 0.4);
  border-radius: 1.2vh;
  padding: 1.8vh;
  flex: 1;
  box-shadow:
    inset 0 0.3vh 0.8vh rgba(0, 0, 0, 0.3),
    0 0.2vh 0.5vh rgba(212, 175, 55, 0.2);
  position: relative;
}

.dealer-area::before,
.player-area::before {
  content: '';
  position: absolute;
  inset: 0;
  border-radius: 1.2vh;
  background:
    repeating-linear-gradient(45deg,
      transparent,
      transparent 2vh,
      rgba(212, 175, 55, 0.03) 2vh,
      rgba(212, 175, 55, 0.03) 2.2vh);
  pointer-events: none;
}

.area-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5vh;
  padding-bottom: 1vh;
  border-bottom: 0.2vh solid rgba(212, 175, 55, 0.3);
}

.dealer-badge,
.player-badge {
  display: flex;
  align-items: center;
  gap: 0.8vw;
  background: rgba(0, 0, 0, 0.4);
  padding: 0.8vh 1.5vw;
  border-radius: 2vh;
  border: 0.15vh solid rgba(212, 175, 55, 0.5);
}

.badge-icon {
  font-size: 2vh;
}

.area-title {
  font-size: 1.9vh;
  font-weight: bold;
  color: #ffed4e;
  text-shadow: 0 0.1vh 0.3vh rgba(0, 0, 0, 0.8);
}

.score-display {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 0.3vh;
}

.score-label {
  font-size: 1.2vh;
  color: #b8a47e;
  text-transform: uppercase;
  letter-spacing: 0.1vw;
}

.score-value {
  font-size: 2.8vh;
  font-weight: bold;
  color: #ffed4e;
  text-shadow:
    0 0.1vh 0.3vh rgba(0, 0, 0, 0.8),
    0 0 0.8vh rgba(255, 237, 78, 0.6);
}

.score-value.perfect {
  color: #90ee90;
  text-shadow:
    0 0.1vh 0.3vh rgba(0, 0, 0, 0.8),
    0 0 1vh rgba(144, 238, 144, 0.8);
}

.score-value.bust {
  color: #ff6b6b;
  text-shadow:
    0 0.1vh 0.3vh rgba(0, 0, 0, 0.8),
    0 0 1vh rgba(255, 107, 107, 0.8);
}

.table-felt {
  background:
    radial-gradient(ellipse at center, rgba(15, 95, 70, 0.4) 0%, transparent 70%);
  border-radius: 0.8vh;
  padding: 1.5vh;
  min-height: 12vh;
}

.cards-container {
  display: flex;
  gap: 1.2vw;
  flex-wrap: wrap;
  justify-content: center;
  min-height: 10vh;
  padding: 0.8vh;
}

.playing-card {
  width: 7vh;
  height: 10vh;
  background: #fff;
  border: 0.2vh solid #2c3e50;
  border-radius: 0.8vh;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow:
    0 0.4vh 0.8vh rgba(0, 0, 0, 0.4),
    0 0.1vh 0.3vh rgba(0, 0, 0, 0.3);
  transition: all 0.2s;
  position: relative;
  transform: translateY(calc(var(--card-index, 0) * -0.5vh));
}

.playing-card:hover {
  transform: translateY(calc(var(--card-index, 0) * -0.5vh - 0.5vh));
  box-shadow:
    0 0.6vh 1.2vh rgba(0, 0, 0, 0.5),
    0 0.2vh 0.4vh rgba(0, 0, 0, 0.3);
}

.playing-card.face-down {
  background: #2c3e50;
  border-color: #d4af37;
}

.card-face {
  width: 100%;
  height: 100%;
  padding: 0.8vh;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  position: relative;
}

.card-top,
.card-bottom {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.2vh;
}

.card-bottom {
  transform: rotate(180deg);
}

.card-value {
  font-size: 1.8vh;
  font-weight: bold;
  color: #2c3e50;
  line-height: 1;
}

.card-suit {
  font-size: 1.5vh;
  line-height: 1;
}

.card-suit.red {
  color: #dc143c;
}

.card-center {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.center-suit {
  font-size: 3.5vh;
  opacity: 0.15;
}

.center-suit.red {
  color: #dc143c;
}

.card-back {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
}

.back-pattern {
  width: 80%;
  height: 80%;
  background:
    repeating-linear-gradient(45deg,
      #d4af37,
      #d4af37 0.3vh,
      #b8860b 0.3vh,
      #b8860b 0.6vh);
  border-radius: 0.5vh;
  position: relative;
}

.pattern-inner {
  position: absolute;
  inset: 20%;
  background: #2c3e50;
  border-radius: 50%;
  border: 0.2vh solid #d4af37;
}

/* 分隔装饰 */
.table-divider {
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 1vh 0;
}

.divider-ornament {
  color: #d4af37;
  font-size: 1.5vh;
  letter-spacing: 1vw;
  opacity: 0.6;
}

/* 下注区域 */
.bet-area {
  margin-top: 2vh;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 2vw;
}

.chip-stack {
  display: flex;
  flex-direction: column-reverse;
  align-items: center;
}

.poker-chip {
  width: 4.5vh;
  height: 4.5vh;
  margin-top: calc(var(--chip-index, 1) * -3.5vh);
  position: relative;
  transform: rotateX(60deg);
}

.chip-inner {
  width: 100%;
  height: 100%;
  background:
    radial-gradient(circle at center, #ffd700 0%, #daa520 40%, #b8860b 100%);
  border-radius: 50%;
  border: 0.3vh solid #8b4513;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow:
    0 0.2vh 0.5vh rgba(0, 0, 0, 0.4),
    inset 0 -0.1vh 0.3vh rgba(0, 0, 0, 0.3);
}

.chip-value {
  font-size: 1.2vh;
  font-weight: bold;
  color: #2c3e50;
  text-shadow: 0 0.1vh 0.2vh rgba(255, 255, 255, 0.5);
}

.bet-display {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5vh;
}

.bet-label {
  font-size: 1.3vh;
  color: #b8a47e;
  text-transform: uppercase;
  letter-spacing: 0.1vw;
}

.bet-amount {
  font-size: 2.2vh;
  font-weight: bold;
  color: #ffd700;
  text-shadow:
    0 0.1vh 0.3vh rgba(0, 0, 0, 0.8),
    0 0 0.8vh rgba(255, 215, 0, 0.6);
}

/* 侧边信息区 */
.info-section {
  grid-area: info;
  display: flex;
  flex-direction: column;
  gap: 1.2vh;
  overflow-y: auto;
  max-height: 100%;
}

.player-status,
.rules-card,
.decoration-card {
  background:
    linear-gradient(135deg, rgba(13, 79, 60, 0.3) 0%, rgba(10, 61, 46, 0.2) 100%),
    rgba(0, 0, 0, 0.4);
  border: 0.25vh solid rgba(212, 175, 55, 0.4);
  border-radius: 1vh;
  overflow: hidden;
  box-shadow:
    inset 0 0.2vh 0.5vh rgba(0, 0, 0, 0.3),
    0 0.2vh 0.5vh rgba(212, 175, 55, 0.2);
}

.status-header,
.card-header {
  background:
    linear-gradient(135deg, rgba(212, 175, 55, 0.25) 0%, rgba(184, 134, 11, 0.15) 100%);
  padding: 1.2vh 1.5vw;
  font-size: 1.7vh;
  font-weight: bold;
  color: #ffed4e;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.8vw;
  border-bottom: 0.2vh solid rgba(212, 175, 55, 0.3);
  text-shadow: 0 0.1vh 0.3vh rgba(0, 0, 0, 0.8);
}

.header-icon {
  font-size: 1.8vh;
}

.status-content {
  padding: 2vh 1.5vw;
}

.balance-display {
  text-align: center;
  margin-bottom: 1.5vh;
}

.balance-value {
  font-size: 2.8vh;
  font-weight: bold;
  color: #ffd700;
  text-shadow:
    0 0.1vh 0.3vh rgba(0, 0, 0, 0.8),
    0 0 1vh rgba(255, 215, 0, 0.6);
  margin-bottom: 0.5vh;
}

.balance-label {
  font-size: 1.2vh;
  color: #b8a47e;
  text-transform: uppercase;
  letter-spacing: 0.1vw;
}

.stats-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1vw;
}

.stat-card {
  background: rgba(0, 0, 0, 0.3);
  border-radius: 0.8vh;
  padding: 1.5vh 1vw;
  text-align: center;
  border: 0.15vh solid rgba(212, 175, 55, 0.3);
}

.stat-value {
  font-size: 2vh;
  font-weight: bold;
  color: #ffed4e;
  margin-bottom: 0.3vh;
}

.stat-label {
  font-size: 1.1vh;
  color: #b8a47e;
  text-transform: uppercase;
  letter-spacing: 0.05vw;
}

.card-content {
  padding: 1.8vh 1.5vw;
}

.rule-item {
  display: flex;
  align-items: center;
  gap: 1vw;
  padding: 0.8vh 0;
  font-size: 1.3vh;
  color: #e0d5c7;
}

.rule-icon {
  font-size: 1.5vh;
  color: #d4af37;
}

.decoration-card {
  padding: 2vh;
  text-align: center;
}

.lucky-symbols {
  display: flex;
  justify-content: center;
  gap: 1.5vw;
  font-size: 2.5vh;
  margin-bottom: 1vh;
}

.luck-text {
  font-size: 1.4vh;
  color: #d4af37;
  font-weight: bold;
  text-shadow: 0 0.1vh 0.3vh rgba(0, 0, 0, 0.8);
}

/* 控制区域 */
.control-section {
  grid-area: control;
  background:
    linear-gradient(135deg, rgba(0, 0, 0, 0.5) 0%, rgba(0, 0, 0, 0.3) 100%);
  border: 0.3vh solid rgba(212, 175, 55, 0.4);
  border-radius: 1vh;
  padding: 1.8vh;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow:
    inset 0 0.3vh 0.8vh rgba(0, 0, 0, 0.3),
    0 0.2vh 0.5vh rgba(212, 175, 55, 0.2);
}

.betting-controls {
  display: flex;
  flex-direction: column;
  gap: 1.8vh;
  align-items: center;
  width: 100%;
}

.betting-title {
  display: flex;
  align-items: center;
  gap: 1vw;
  font-size: 2vh;
  font-weight: bold;
  color: #ffed4e;
  text-shadow: 0 0.1vh 0.3vh rgba(0, 0, 0, 0.8);
}

.title-icon {
  font-size: 2.2vh;
}

.chip-selection {
  display: flex;
  gap: 2.5vw;
}

.chip-btn {
  width: 6vh;
  height: 6vh;
  border: none;
  background: none;
  cursor: pointer;
  transition: all 0.2s;
  position: relative;
}

.chip-btn:hover {
  transform: translateY(-0.3vh);
}

.chip-btn.active {
  transform: translateY(-0.5vh) scale(1.1);
}

.chip-design {
  width: 100%;
  height: 100%;
  position: relative;
}

.chip-edge {
  width: 100%;
  height: 100%;
  background:
    radial-gradient(circle at center, #ffd700 0%, #daa520 45%, #b8860b 100%);
  border-radius: 50%;
  border: 0.4vh solid #8b4513;
  box-shadow:
    0 0.3vh 0.8vh rgba(0, 0, 0, 0.4),
    inset 0 -0.2vh 0.4vh rgba(0, 0, 0, 0.3),
    inset 0 0.2vh 0.4vh rgba(255, 255, 255, 0.3);
  position: relative;
}

.chip-btn.active .chip-edge {
  box-shadow:
    0 0.5vh 1.2vh rgba(0, 0, 0, 0.5),
    0 0 2vh rgba(255, 215, 0, 0.5),
    inset 0 -0.2vh 0.4vh rgba(0, 0, 0, 0.3),
    inset 0 0.2vh 0.4vh rgba(255, 255, 255, 0.3);
}

.chip-center {
  position: absolute;
  inset: 25%;
  background: #fff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: inset 0 0.1vh 0.3vh rgba(0, 0, 0, 0.2);
}

.chip-amount {
  font-size: 1.6vh;
  font-weight: bold;
  color: #2c3e50;
}

.bet-input-area {
  display: flex;
  gap: 2vw;
  align-items: center;
}

.input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.currency-symbol {
  position: absolute;
  left: 1.5vw;
  font-size: 1.8vh;
  color: #d4af37;
  font-weight: bold;
}

.bet-input {
  width: 18vw;
  padding: 1.5vh 1.5vw 1.5vh 3vw;
  background: rgba(0, 0, 0, 0.6);
  border: 0.25vh solid #d4af37;
  border-radius: 0.8vh;
  color: #fff;
  font-size: 1.7vh;
  font-weight: bold;
  text-align: center;
  box-shadow: inset 0 0.2vh 0.5vh rgba(0, 0, 0, 0.4);
}

.bet-input:focus {
  outline: none;
  border-color: #ffed4e;
  box-shadow:
    inset 0 0.2vh 0.5vh rgba(0, 0, 0, 0.4),
    0 0 1vh rgba(255, 237, 78, 0.4);
}

.start-btn {
  padding: 1.5vh 4vw;
  background:
    linear-gradient(135deg, #228b22 0%, #32cd32 50%, #228b22 100%);
  color: #fff;
  border: 0.25vh solid #90ee90;
  border-radius: 1vh;
  font-size: 2vh;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  gap: 1vw;
  box-shadow:
    0 0.4vh 0.8vh rgba(0, 0, 0, 0.4),
    inset 0 0.1vh 0.3vh rgba(144, 238, 144, 0.4);
  text-transform: uppercase;
  letter-spacing: 0.1vw;
}

.start-btn:hover:not(:disabled) {
  background:
    linear-gradient(135deg, #32cd32 0%, #3cb371 50%, #32cd32 100%);
  transform: translateY(-0.2vh);
  box-shadow:
    0 0.6vh 1.2vh rgba(0, 0, 0, 0.5),
    0 0 1.5vh rgba(144, 238, 144, 0.4),
    inset 0 0.1vh 0.3vh rgba(144, 238, 144, 0.5);
}

.start-btn:disabled {
  background:
    linear-gradient(135deg, #555 0%, #666 50%, #555 100%);
  border-color: #888;
  cursor: not-allowed;
  opacity: 0.7;
}

.btn-icon {
  font-size: 2.2vh;
}

.action-controls {
  display: flex;
  gap: 2.5vw;
}

.action-btn {
  padding: 1.8vh 3.5vw;
  border: 0.25vh solid;
  border-radius: 1vh;
  font-size: 1.4vh;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.2s;
  color: #fff;
  text-transform: uppercase;
  letter-spacing: 0.05vw;
  position: relative;
  overflow: hidden;
}

.action-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.action-btn:hover::before {
  left: 100%;
}

.btn-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5vh;
  position: relative;
  z-index: 1;
}

.btn-text {
  font-size: 1.8vh;
}

.btn-subtext {
  font-size: 1.1vh;
  opacity: 0.8;
}

.action-btn.hit {
  background:
    linear-gradient(135deg, #ff6347 0%, #ff7f50 50%, #ff6347 100%);
  border-color: #ffa07a;
  box-shadow:
    0 0.4vh 0.8vh rgba(0, 0, 0, 0.4),
    inset 0 0.1vh 0.3vh rgba(255, 160, 122, 0.4);
}

.action-btn.hit:hover:not(:disabled) {
  transform: translateY(-0.2vh);
  box-shadow:
    0 0.6vh 1.2vh rgba(0, 0, 0, 0.5),
    0 0 1.5vh rgba(255, 160, 122, 0.4),
    inset 0 0.1vh 0.3vh rgba(255, 160, 122, 0.5);
}

.action-btn.stand {
  background:
    linear-gradient(135deg, #4169e1 0%, #6495ed 50%, #4169e1 100%);
  border-color: #87ceeb;
  box-shadow:
    0 0.4vh 0.8vh rgba(0, 0, 0, 0.4),
    inset 0 0.1vh 0.3vh rgba(135, 206, 235, 0.4);
}

.action-btn.stand:hover {
  transform: translateY(-0.2vh);
  box-shadow:
    0 0.6vh 1.2vh rgba(0, 0, 0, 0.5),
    0 0 1.5vh rgba(135, 206, 235, 0.4),
    inset 0 0.1vh 0.3vh rgba(135, 206, 235, 0.5);
}

.action-btn.double {
  background:
    linear-gradient(135deg, #9932cc 0%, #ba55d3 50%, #9932cc 100%);
  border-color: #dda0dd;
  box-shadow:
    0 0.4vh 0.8vh rgba(0, 0, 0, 0.4),
    inset 0 0.1vh 0.3vh rgba(221, 160, 221, 0.4);
}

.action-btn.double:hover {
  transform: translateY(-0.2vh);
  box-shadow:
    0 0.6vh 1.2vh rgba(0, 0, 0, 0.5),
    0 0 1.5vh rgba(221, 160, 221, 0.4),
    inset 0 0.1vh 0.3vh rgba(221, 160, 221, 0.5);
}

.action-btn:disabled {
  background:
    linear-gradient(135deg, #555 0%, #666 50%, #555 100%);
  border-color: #888;
  cursor: not-allowed;
  opacity: 0.7;
}

.action-btn:disabled::before {
  display: none;
}

/* 游戏结果 */
.result-display {
  width: 100%;
}

.result-card {
  background:
    radial-gradient(ellipse at center, rgba(0, 0, 0, 0.8) 0%, rgba(0, 0, 0, 0.6) 100%);
  border: 0.3vh solid;
  border-radius: 1.5vh;
  padding: 2vh;
  text-align: center;
  position: relative;
  overflow: hidden;
}

.result-card.win {
  border-color: #90ee90;
  box-shadow:
    0 0 3vh rgba(144, 238, 144, 0.4),
    inset 0 0 2vh rgba(144, 238, 144, 0.1);
}

.result-card.lose {
  border-color: #ff6b6b;
  box-shadow:
    0 0 3vh rgba(255, 107, 107, 0.4),
    inset 0 0 2vh rgba(255, 107, 107, 0.1);
}

.result-card.push {
  border-color: #ffd700;
  box-shadow:
    0 0 3vh rgba(255, 215, 0, 0.4),
    inset 0 0 2vh rgba(255, 215, 0, 0.1);
}

.result-glow {
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
  animation: glow-rotate 8s linear infinite;
  pointer-events: none;
}

@keyframes glow-rotate {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}

.result-content {
  position: relative;
  z-index: 1;
}

.result-icon {
  font-size: 4vh;
  margin-bottom: 0.8vh;
  display: block;
}

.result-text {
  font-size: 2.8vh;
  font-weight: bold;
  color: #ffed4e;
  text-shadow:
    0 0.2vh 0.4vh rgba(0, 0, 0, 0.8),
    0 0 1.5vh rgba(255, 237, 78, 0.5);
  margin-bottom: 1.5vh;
}

.score-comparison {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 2vw;
  font-size: 1.8vh;
  color: #e0d5c7;
  margin-bottom: 2vh;
}

.vs {
  color: #d4af37;
  font-weight: bold;
  font-size: 1.5vh;
}

.payout-info {
  font-size: 3vh;
  font-weight: bold;
  margin-bottom: 2vh;
}

.win-amount {
  color: #90ee90;
  text-shadow:
    0 0.2vh 0.4vh rgba(0, 0, 0, 0.8),
    0 0 1.5vh rgba(144, 238, 144, 0.6);
}

.loss-amount {
  color: #ff6b6b;
  text-shadow:
    0 0.2vh 0.4vh rgba(0, 0, 0, 0.8),
    0 0 1.5vh rgba(255, 107, 107, 0.6);
}

.plus,
.minus {
  font-size: 2.5vh;
  margin-right: 0.5vw;
}

.new-game-btn {
  padding: 1.5vh 4vw;
  background:
    linear-gradient(135deg, #4169e1 0%, #6495ed 50%, #4169e1 100%);
  color: #fff;
  border: 0.25vh solid #87ceeb;
  border-radius: 1vh;
  font-size: 2vh;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.2s;
  display: inline-flex;
  align-items: center;
  gap: 1vw;
  box-shadow:
    0 0.4vh 0.8vh rgba(0, 0, 0, 0.4),
    inset 0 0.1vh 0.3vh rgba(135, 206, 235, 0.4);
  text-transform: uppercase;
  letter-spacing: 0.1vw;
}

.new-game-btn:hover {
  background:
    linear-gradient(135deg, #6495ed 0%, #7fb3ff 50%, #6495ed 100%);
  transform: translateY(-0.2vh);
  box-shadow:
    0 0.6vh 1.2vh rgba(0, 0, 0, 0.5),
    0 0 1.5vh rgba(135, 206, 235, 0.4),
    inset 0 0.1vh 0.3vh rgba(135, 206, 235, 0.5);
}

/* 等待显示 */
.waiting-display {
  text-align: center;
}

.waiting-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2vh;
}

.dealer-thinking {
  display: flex;
  align-items: center;
  gap: 1vw;
  font-size: 2.2vh;
  color: #ffed4e;
  font-weight: bold;
  text-shadow: 0 0.1vh 0.3vh rgba(0, 0, 0, 0.8);
}

.thinking-icon {
  font-size: 2.5vh;
}

.loading-dots {
  display: flex;
  gap: 0.8vw;
}

.loading-dots span {
  width: 1.2vh;
  height: 1.2vh;
  background: #d4af37;
  border-radius: 50%;
  box-shadow: 0 0 0.5vh rgba(212, 175, 55, 0.8);
  opacity: 0.3;
}

.loading-dots span:nth-child(1) {
  animation-delay: -0.32s;
}

.loading-dots span:nth-child(2) {
  animation-delay: -0.16s;
}
</style>
