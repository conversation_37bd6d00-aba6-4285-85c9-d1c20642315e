# 现代极简UI设计规范 - Rebar/Alt:V

## 🎨 设计风格概述

**关键词**: 现代、极简、神秘、成就感、暗黑风格

这是一种受现代游戏UI（Valorant、Apex Legends）和高端产品展示页面启发的设计风格，强调简约而不简单的视觉体验。

## 🌈 核心设计原则

### 1. 极简暗黑主题
- **背景**: 纯黑 `#0a0a0a` 
- **容器**: 半透明玻璃质感 `rgba(255, 255, 255, 0.02)`
- **边框**: 极细微光 `rgba(255, 255, 255, 0.05)`

### 2. 现代排版
```css
/* 字体系统 */
font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;

/* 字重使用 */
font-weight: 200;  /* 超大标题 */
font-weight: 300;  /* 大数字展示 */
font-weight: 400;  /* 正文 */
font-weight: 600;  /* 小标签 */
font-weight: 700;  /* 强调文本 */

/* 字间距 */
letter-spacing: -0.02em;  /* 大标题，更紧凑 */
letter-spacing: 0.05em;   /* 按钮文本 */
letter-spacing: 0.15em;   /* 标签，更疏松 */
letter-spacing: 0.3em;    /* 副标题，最疏松 */
```

### 3. 颜色系统
```css
/* 主色调 */
--primary: #9333ea;      /* 紫色主题 */
--primary-dark: #6b21a8; /* 深紫色 */

/* 稀有度颜色 */
--rare: #3b82f6;        /* 蓝色 */
--epic: #9333ea;        /* 紫色 */
--legendary: #f59e0b;   /* 橙色 */

/* 中性色 */
--text-primary: #ffffff;
--text-secondary: #666666;
--bg-card: rgba(255, 255, 255, 0.02);
--border: rgba(255, 255, 255, 0.05);
```

## 📐 布局规范（VH/VW单位）

### 基础容器
```css
.overlay {
  width: 100vw;
  height: 100vh;
  background: #0a0a0a;
}

.content-wrapper {
  width: 90vw;
  max-width: 80vw;
  gap: 5vh;
}
```

### 间距系统
```css
/* 垂直间距 */
margin-top: 5vh;
margin-bottom: 3vh;
padding: 2.5vh 2vw;
gap: 1vh;

/* 响应式字体 */
font-size: max(2rem, 5vw);      /* 主标题 */
font-size: max(1.25rem, 2.5vw); /* 副标题 */
font-size: max(1rem, 2vw);      /* 正文 */
font-size: max(0.875rem, 1.5vw);/* 小字 */
```

### 网格布局
```css
.grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(15vw, 1fr));
  gap: 2vw;
  width: 100%;
  max-width: 60vw;
}
```

## ✨ 动画规范

### 入场动画
```css
/* 淡入上移 */
@keyframes fadeInUp {
  0% {
    opacity: 0;
    transform: translateY(3vh);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 卡片入场 */
@keyframes cardIn {
  0% {
    opacity: 0;
    transform: translateY(3vh) scale(0.9);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* 使用延迟创造流畅感 */
animation-delay: calc(0.1s * var(--index));
```

### 背景动画
```css
/* 脉冲光晕 */
@keyframes pulse {
  0%, 100% { 
    transform: translate(-50%, -50%) scale(1); 
    opacity: 0.5; 
  }
  50% { 
    transform: translate(-50%, -50%) scale(1.1); 
    opacity: 0.3; 
  }
}

/* 网格移动 */
@keyframes grid-move {
  0% { transform: translate(0, 0); }
  100% { transform: translate(5vw, 5vh); }
}
```

## 🎯 组件样式

### 卡片组件
```css
.card {
  background: rgba(255, 255, 255, 0.02);
  border: 1px solid rgba(255, 255, 255, 0.05);
  border-radius: 1.5vh;
  padding: 2.5vh 1.5vw;
  transition: all 0.3s ease;
}

.card:hover {
  transform: translateY(-0.5vh);
  background: rgba(255, 255, 255, 0.04);
}
```

### 按钮组件
```css
.button-minimal {
  background: none;
  border: 1px solid rgba(255, 255, 255, 0.1);
  color: #ffffff;
  padding: 1vh 2vw;
  border-radius: 5vh;
  font-size: max(0.875rem, 1.5vw);
  font-weight: 500;
  letter-spacing: 0.05em;
  cursor: pointer;
  transition: all 0.3s ease;
}

.button-minimal:hover {
  background: rgba(255, 255, 255, 0.05);
  border-color: rgba(255, 255, 255, 0.2);
  transform: translateX(0.5vw);
}
```

### 徽章组件
```css
.badge {
  width: 6vw;
  height: 6vw;
  min-width: 60px;
  min-height: 60px;
  background: linear-gradient(135deg, #9333ea 0%, #6b21a8 100%);
  border-radius: 50%;
  position: relative;
  animation: rotate 20s linear infinite;
}
```

## 🔌 前后端交互规范 (Rebar/Alt:V)

### 1. Webview 基础

#### 页面类型
- **Page**: 一次性显示的页面（背包、ATM、商店）
- **Overlay**: 总是显示的 HUD 界面（血量、金钱）
- **Persistent**: 无论什么情况都显示的页面（水印、Logo）

#### CSS 框架
- 内置 TailwindCSS
- 推荐安装 Tailwind CSS IntelliSense 扩展
- 使用 `CTRL + SPACE` 自动补全 CSS 类

### 2. 事件系统 (useEvents)

```typescript
// Vue 组件中导入
import { useEvents } from '@Composables/useEvents'
const events = useEvents()

// 监听来自服务端或客户端的事件
events.on('event-from-server-or-client', (var1: string, var2: number) => {
    console.log(var1, var2)
})

// 发送到客户端（可被 useWebview().on 接收）
events.emitClient('emit-to-client-event', myVariable)

// 发送到服务端（可被 alt.onClient 接收）
events.emitServer('emit-to-server-event', myVariable)

// 发送 RPC 请求到服务端并获取结果
const result = await events.emitServerRpc('get-something', 'hello')
console.log(`Webview Result: ${result}`)

// 发送 RPC 请求到客户端
const result = await events.emitClientRpc('get-something', 'hello')
```

### 3. 服务端操作

#### 显示/隐藏页面
```typescript
import { useWebview } from '@Server/player/webview.js'

// 显示页面
function showPage(player: alt.Player) {
    const webview = useWebview(player)
    webview.show('MyPageName', 'page')
    
    // 发送数据到 webview
    webview.emit('page:data', { someData: 'value' })
}

// 隐藏页面
function hidePage(player: alt.Player) {
    const webview = useWebview(player)
    webview.hide('MyPageName')
}

// 监听来自 webview 的事件
alt.onClient('page:closePage', (player: alt.Player) => {
    const webview = useWebview(player)
    webview.hide('MyPageName')
})

// 处理 RPC 请求
alt.onRpc('get-something', (player: alt.Player, arg1: string) => {
    const result = arg1 + ' world'
    return result
})
```

### 4. 客户端操作（如需要）

```typescript
import { useWebview } from '@Client/webview/index.js'

// 显示页面
useWebview().show('Example', 'page')

// 隐藏页面
useWebview().hide('Example')

// 处理 RPC 请求
const view = useWebview()
view.onRpc('get-something', (arg1: string) => {
    return arg1 + ' world'
})
```

### 5. 本地存储 (useLocalStorage)

```typescript
import { useLocalStorage } from '@Composables/useLocalStorage'

async function example() {
    const localStorage = useLocalStorage()
    
    // 设置数据
    localStorage.set('myKey', 'someValue')

    // 获取数据
    const result = await localStorage.get('myKey')
    console.log(`Local test result: ${result}`) // 返回 'someValue'

    // 删除数据
    await localStorage.remove('myKey')
}
```

### 6. 键盘事件

```typescript
import { onMounted, onUnmounted } from 'vue'
import { useEvents } from '@Composables/useEvents'

const events = useEvents()

function doSomething() {
    console.log('hello world')
}

onUnmounted(() => {
    events.offKeyUp('inventory')
})

onMounted(() => {
    // 监听按键 'I' (73)
    events.onKeyUp('inventory', 73, doSomething)
})
```

### 7. 图片资源

```typescript
// 使用 findIcon 函数获取正确路径
import { findIcon } from '../../inventory/shared/findicon.js'

const imagePath = findIcon('filename.webp')

// 车辆图片
import { findvehicleicon } from '../../inventory/shared/findicon.js'
const vehicleImage = findvehicleicon('adder') // 返回 ../images/vehicles/adder.webp
```

### 8. 最佳实践

#### ✅ 推荐做法
- 服务端直接控制 webview 的显示/隐藏
- 使用 `events.emitServer` 与服务端通信
- 关闭页面统一使用 `pluginName:closePage` 格式
- 使用 TypeScript 确保类型安全

#### ❌ 避免做法
- 不要通过客户端中转 webview 事件（除非必要）
- 不要在 webview 中直接操作游戏逻辑
- 不要硬编码路径，使用 `findIcon` 函数

## 📝 完整组件模板

```vue
<template>
  <div v-if="visible" class="overlay">
    <div class="container">
      <!-- 背景效果 -->
      <div class="bg-effect">
        <div class="glow-orb"></div>
        <div class="grid-lines"></div>
      </div>

      <!-- 内容 -->
      <div class="content-wrapper">
        <!-- 标题区 -->
        <div class="title-section">
          <div class="badge">
            <div class="badge-inner"></div>
          </div>
          <h1 class="main-title">{{ title }}</h1>
          <div class="subtitle">{{ subtitle }}</div>
        </div>

        <!-- 内容网格 -->
        <div class="content-grid">
          <div 
            v-for="(item, index) in items" 
            :key="index"
            class="card"
            :style="{ animationDelay: `${0.1 * index}s` }"
          >
            <!-- 卡片内容 -->
          </div>
        </div>

        <!-- 操作按钮 -->
        <button @click="handleClose" class="button-minimal">
          <span>继续</span>
          <svg width="1.5vw" height="1.5vh" viewBox="0 0 24 24">
            <path d="M5 12h14m-7-7l7 7-7 7" stroke="currentColor" stroke-width="2" fill="none"/>
          </svg>
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useEvents } from '@Composables/useEvents'
import { findIcon } from '../../inventory/shared/findicon.js'

const events = useEvents()
const visible = ref(false)

// 定义数据类型
interface DisplayData {
  title: string
  subtitle: string
  items: any[]
}

const displayData = ref<DisplayData>({
  title: '',
  subtitle: '',
  items: []
})

// 监听服务端发送的显示事件
events.on('plugin:show', (data: DisplayData) => {
  displayData.value = data
  visible.value = true
})

// 关闭处理 - 通知服务端
const handleClose = () => {
  events.emitServer('plugin:closePage')
  visible.value = false
}

// 示例：获取图片路径
const getItemImage = (imageName: string) => {
  return findIcon(imageName)
}
</script>

<style scoped>
/* 引入上述所有样式规范 */
</style>
```

## 🎮 使用指南

1. **创建新组件时**，参考此文档的设计规范
2. **所有尺寸**优先使用 vh/vw 单位
3. **动画时长**保持在 0.3s-0.8s 之间
4. **颜色使用**遵循既定的颜色系统
5. **交互方式**严格按照前后端规范

## 💡 设计理念

- **少即是多**: 移除所有不必要的装饰
- **层次分明**: 通过大小、颜色、间距创造视觉层次
- **动效克制**: 动画服务于功能，不喧宾夺主
- **一致性**: 保持整体风格的统一性

---

*此设计规范适用于所有需要现代、神秘、有成就感的游戏UI界面* 