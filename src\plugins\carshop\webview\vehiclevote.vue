<template>
  <div class="vehicle-vote-sidebar">
    <!-- Right Sidebar -->
    <div :class="['sidebar', 'right-sidebar', { 'collapsed': !isMenuOpen }]">
      <button @click="toggleMenu" class="toggle-btn right">
        <i :class="['fas', isMenuOpen ? 'fa-chevron-right' : 'fa-chevron-left']"></i>
      </button>

      <div class="sidebar-content">
        <!-- 标题栏 -->
        <div class="sidebar-header">
          <div class="header-icon">
            <i class="fas fa-vote-yea"></i>
          </div>
          <div class="title-info">
            <h1>进口车辆投票系统</h1>
            <span class="subtitle">{{ currentViewMode === 'admin' ? '管理投票会话' : '参与车辆投票' }}</span>
            <span v-if="isAdmin && showAsPlayer" class="test-mode-indicator">
              <i class="fas fa-vial"></i> 测试模式
            </span>
          </div>
        </div>

        <!-- 管理员视图切换按钮 -->
        <div v-if="!isOrderMode && isAdmin" class="view-toggle-card">
          <button @click="toggleViewMode" class="toggle-btn-view" :class="{ active: showAsPlayer }">
            <i class="fas fa-exchange-alt"></i>
            <span>{{ showAsPlayer ? '切换到管理视图' : '切换到玩家视图' }}</span>
          </button>
        </div>

        <!-- 材料提交界面（有订单时显示，优先级最高） -->
        <div v-if="isOrderMode && currentOrder" class="order-material-section">
          <div class="order-header-card">
            <h4>进口车辆订单 - 材料提交</h4>
            <div class="order-vehicle">{{ currentOrder.vehicleModel }}</div>
            <button @click="previewVehicle" class="preview-vehicle-btn">
              <i class="fas fa-eye"></i>
              预览车辆
            </button>
          </div>

          <!-- 材料提交区域 -->
          <div class="materials-section">
            <div class="section-header">
              <h5>材料需求进度</h5>
              <button @click="submitAllMaterials" :disabled="!canSubmitAnyMaterial || submittingAll" class="quick-submit-btn">
                <i class="fas fa-rocket"></i>
                <span v-if="!submittingAll">一键提交</span>
                <span v-else>提交中...</span>
              </button>
            </div>

            <div class="materials-list">
              <div v-for="(required, materialName) in currentOrder.materials" :key="materialName" class="material-card">
                <div class="material-header">
                  <div class="material-icon-container">
                    <img :src="getMaterialIcon(String(materialName))" :alt="String(materialName)" class="material-icon" loading="lazy" />
                  </div>
                  <div class="material-info">
                    <div class="material-name">{{ String(materialName) }}</div>
                    <div class="material-progress-text">
                      {{ getSubmittedAmount(String(materialName)) }} / {{ required }}
                    </div>
                    <div class="material-owned-text">
                      拥有: {{ playerMaterials[String(materialName)] || 0 }}
                    </div>
                  </div>
                </div>

                <div class="progress-bar">
                  <div class="progress-fill" :style="{ width: getProgressPercent(String(materialName)) + '%' }"></div>
                </div>

                <div class="material-actions">
                  <input 
                    v-model.number="submitAmounts[String(materialName)]" 
                    type="number" 
                    :min="1"
                    :max="getMaxSubmittable(String(materialName))"
                    :placeholder="getInputPlaceholder(String(materialName))" 
                    class="amount-input"
                  />
                  <button 
                    @click="submitMaterial(String(materialName))"
                    :disabled="!canSubmitMaterial(String(materialName))" 
                    class="submit-btn"
                  >
                    <i class="fas fa-plus"></i>
                    提交
                  </button>
                </div>
              </div>
            </div>

            <!-- 总体进度 -->
            <div class="overall-progress">
              <div class="progress-info">
                <span class="progress-label">总体进度</span>
                <span class="progress-text">{{ overallProgress.toFixed(1) }}%</span>
              </div>
              <div class="progress-bar large">
                <div class="progress-fill" :style="{ width: overallProgress + '%' }"></div>
              </div>
            </div>

            <!-- 订单操作 -->
            <div class="order-actions">
              <button v-if="isOrderComplete" @click="claimVehicle" class="action-btn primary" :disabled="claiming">
                <i class="fas fa-key"></i>
                <span v-if="!claiming">获得车辆</span>
                <span v-else>生成中...</span>
              </button>
              <button @click="cancelImportOrder" class="action-btn danger" :disabled="cancelling">
                <i class="fas fa-trash"></i>
                <span v-if="!cancelling">取消订单</span>
                <span v-else>取消中...</span>
              </button>
            </div>

            <div class="cancel-info">
              <p>取消订单会退还订单费用但不退还已提交的材料</p>
            </div>
          </div>

          <!-- 颜色配置 -->
          <div class="color-card">
            <div class="color-card-header">
              <div class="header-title">
                <i class="fas fa-palette"></i>
                <h5>车辆颜色配置</h5>
              </div>
            </div>
            
            <div class="color-card-content">
              <div class="color-picker">
                <h6>主要颜色</h6>
                <div class="color-grid">
                  <div 
                    v-for="color in colors" 
                    :key="color.id" 
                    class="color-option"
                    :class="{ 'selected': primaryColor.id === color.id }" 
                    :style="{ backgroundColor: color.color }"
                    @click="selectPrimaryColor(color)" 
                    :title="color.name">
                  </div>
                </div>
              </div>

              <div class="color-picker">
                <h6>次要颜色</h6>
                <div class="color-grid">
                  <div 
                    v-for="color in colors" 
                    :key="color.id" 
                    class="color-option"
                    :class="{ 'selected': secondaryColor.id === color.id }" 
                    :style="{ backgroundColor: color.color }"
                    @click="selectSecondaryColor(color)" 
                    :title="color.name">
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 管理员界面（无订单时显示） -->
        <div v-if="!isOrderMode && currentViewMode === 'admin'" class="admin-section">
          <!-- 当前会话状态 -->
          <div v-if="currentSession" class="session-status-card">
            <div class="card-header">
              <h4>当前投票会话</h4>
              <div class="status-badge" :class="getStatusClass(currentSession.status)">
                {{ getStatusText(currentSession.status) }}
              </div>
            </div>
            
            <div class="session-info">
              <div class="info-row">
                <span class="label">会话ID:</span>
                <span class="value">{{ currentSession.sessionId }}</span>
              </div>
              <div class="info-row">
                <span class="label">开始时间:</span>
                <span class="value">{{ formatTime(currentSession.startTime) }}</span>
              </div>
              <div class="info-row">
                <span class="label">结束时间:</span>
                <span class="value">{{ formatTime(currentSession.endTime) }}</span>
              </div>
              <div v-if="currentSession.topVehicles.length > 0" class="info-row">
                <span class="label">前5名车辆:</span>
                <span class="value">{{ currentSession.topVehicles.join(', ') }}</span>
              </div>
            </div>

            <!-- 投票统计 -->
            <div v-if="voteStats" class="vote-statistics">
              <h5>投票统计</h5>
              <div class="stats-grid">
                <div class="stat-item" v-for="(count, model) in voteStats.vehicleVotes" :key="model">
                  <span class="stat-model">{{ model }}</span>
                  <span class="stat-count">{{ count }} 票</span>
                </div>
              </div>
              <div class="total-votes">总投票数: {{ voteStats.totalVotes }}</div>
            </div>
          </div>

          <!-- 创建新投票会话 -->
          <div v-if="!currentSession || currentSession.status === 'ended'" class="create-session-card">
            <div class="card-header">
              <h4>创建新投票会话</h4>
            </div>
            
            <!-- 车辆设置 -->
            <div class="vehicles-setup">
              <h5>设置投票车辆 (10个)</h5>
              <div class="vehicle-inputs">
                <div v-for="(vehicle, index) in newSessionData.vehicles" :key="index" class="vehicle-input-group">
                  <input 
                    v-model="newSessionData.vehicles[index]" 
                    :placeholder="`车辆 ${index + 1} 模型名称`"
                    class="vehicle-input"
                  />
                  <div class="price-inputs">
                    <input 
                      v-model.number="newSessionData.vehiclePrices[vehicle]" 
                      type="number"
                      placeholder="订单价格"
                      class="price-input"
                    />
                    <input 
                      v-model.number="newSessionData.materialPrices[vehicle]" 
                      type="number"
                      placeholder="材料价格"
                      class="price-input"
                    />
                  </div>
                </div>
              </div>
            </div>

            <button @click="createSession" :disabled="!canCreateSession" class="create-btn">
              <i class="fas fa-plus"></i>
              <span v-if="!isAdmin">无管理员权限</span>
              <span v-else-if="!canCreateSession">请填写完整信息</span>
              <span v-else>创建投票会话</span>
            </button>
          </div>
        </div>

        <!-- 玩家投票界面 -->
        <div v-if="!isOrderMode && currentViewMode === 'player'" class="player-section">
          <!-- 管理员测试模式提示 -->
          <div v-if="isAdmin && showAsPlayer" class="admin-test-notice">
            <div class="notice-card">
              <i class="fas fa-info-circle"></i>
              <div class="notice-content">
                <h4>管理员测试模式</h4>
                <p>您正在以玩家视角测试投票和订单功能，所有操作将真实执行。</p>
              </div>
            </div>
          </div>
          
          <!-- 投票进行中 -->
          <div v-if="currentSession && currentSession.status === 'active'" class="voting-section">
            <div class="voting-header-card">
              <h4>车辆投票进行中</h4>
              <div class="time-remaining">
                剩余时间: {{ getTimeRemaining() }}
              </div>
            </div>

            <div class="vehicles-list">
              <div 
                v-for="vehicle in currentSession.vehicles" 
                :key="vehicle"
                class="vehicle-vote-card"
                :class="{ selected: playerVote && playerVote.vehicleModel === vehicle }"
              >
                <div class="vehicle-name">{{ vehicle }}</div>
                <div class="vehicle-price">${{ formatNumber(currentSession.vehiclePrices[vehicle] || 0) }}</div>
                <div class="vehicle-actions">
                  <button @click="previewVoteVehicle(vehicle)" class="preview-btn">
                    <i class="fas fa-eye"></i>
                    预览
                  </button>
                  <button @click="vote(vehicle)" class="vote-btn" :class="{ voted: playerVote && playerVote.vehicleModel === vehicle }">
                    <i class="fas fa-vote-yea"></i>
                    {{ playerVote && playerVote.vehicleModel === vehicle ? '已投票' : '投票' }}
                  </button>
                </div>
                <div v-if="playerVote && playerVote.vehicleModel === vehicle" class="voted-indicator">
                  <i class="fas fa-check"></i>
                  已投票
                </div>
              </div>
            </div>

            <div v-if="playerVote" class="vote-status">
              <i class="fas fa-info-circle"></i>
              您已投票给: {{ playerVote.vehicleModel }}，可以重新选择其他车辆
            </div>

            <!-- 颜色配置 -->
            <div class="color-card">
              <div class="color-card-header">
                <div class="header-title">
                  <i class="fas fa-palette"></i>
                  <h5>车辆颜色配置</h5>
                </div>
              </div>
              
              <div class="color-card-content">
                <div class="color-picker">
                  <h6>主要颜色</h6>
                  <div class="color-grid">
                    <div 
                      v-for="color in colors" 
                      :key="color.id" 
                      class="color-option"
                      :class="{ 'selected': primaryColor.id === color.id }" 
                      :style="{ backgroundColor: color.color }"
                      @click="selectPrimaryColor(color)" 
                      :title="color.name">
                    </div>
                  </div>
                </div>

                <div class="color-picker">
                  <h6>次要颜色</h6>
                  <div class="color-grid">
                    <div 
                      v-for="color in colors" 
                      :key="color.id" 
                      class="color-option"
                      :class="{ 'selected': secondaryColor.id === color.id }" 
                      :style="{ backgroundColor: color.color }"
                      @click="selectSecondaryColor(color)" 
                      :title="color.name">
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 订单阶段 -->
          <div v-if="currentSession && currentSession.status === 'orders_active'" class="orders-section">
            <div class="orders-header-card">
              <h4>投票结果 - 前5名车辆可订购</h4>
              <div class="order-info">
                订单有效期: 30天
              </div>
            </div>

            <div class="top-vehicles-list">
              <div 
                v-for="(vehicle, index) in currentSession.topVehicles" 
                :key="vehicle"
                class="top-vehicle-card"
              >
                <div class="rank-badge">{{ index + 1 }}</div>
                <div class="vehicle-info">
                  <div class="vehicle-name">{{ vehicle }}</div>
                  <div class="vehicle-price">${{ formatNumber(currentSession.vehiclePrices[vehicle] || 0) }}</div>
                </div>
                <div class="vehicle-card-actions">
                  <button @click="previewVoteVehicle(vehicle)" class="preview-btn">
                    <i class="fas fa-eye"></i>
                    预览
                  </button>
                  <button @click="confirmOrder(vehicle)" class="order-btn">
                    <i class="fas fa-shopping-cart"></i>
                    订购
                  </button>
                </div>
              </div>
            </div>

            <!-- 颜色配置 -->
            <div class="color-card">
              <div class="color-card-header">
                <div class="header-title">
                  <i class="fas fa-palette"></i>
                  <h5>车辆颜色配置</h5>
                </div>
              </div>
              
              <div class="color-card-content">
                <div class="color-picker">
                  <h6>主要颜色</h6>
                  <div class="color-grid">
                    <div 
                      v-for="color in colors" 
                      :key="color.id" 
                      class="color-option"
                      :class="{ 'selected': primaryColor.id === color.id }" 
                      :style="{ backgroundColor: color.color }"
                      @click="selectPrimaryColor(color)" 
                      :title="color.name">
                    </div>
                  </div>
                </div>

                <div class="color-picker">
                  <h6>次要颜色</h6>
                  <div class="color-grid">
                    <div 
                      v-for="color in colors" 
                      :key="color.id" 
                      class="color-option"
                      :class="{ 'selected': secondaryColor.id === color.id }" 
                      :style="{ backgroundColor: color.color }"
                      @click="selectSecondaryColor(color)" 
                      :title="color.name">
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 无活动投票 -->
          <div v-if="!currentSession || currentSession.status === 'ended'" class="no-vote-section">
            <div class="no-vote-card">
              <div class="no-vote-icon">
                <i class="fas fa-vote-yea"></i>
              </div>
              <h4>暂无进行中的投票</h4>
              <p>请等待管理员开启新的车辆投票</p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 关闭按钮 -->
    <button @click="closePanel" class="close-btn">
      <i class="fas fa-times"></i>
    </button>
  </div>


</template>

<script lang="ts" setup>
import { ref, computed, onMounted, onUnmounted, watch } from 'vue';
import { usePlayerStatsEx } from '../../hud/webview/stats.js'
import { useEvents } from '@Composables/useEvents.js'
import { findIcon } from '../../inventory/shared/findicon.js';

const {permissions} = usePlayerStatsEx()
const events = useEvents()

// 颜色选项数据（从dealshop.vue复制）
const colors = [
  { id: 0, name: 'Metallic Black', color: '#0d1116' },
  { id: 5, name: 'Metallic Blue Silver', color: '#c2c4c6' },
  { id: 27, name: 'Metallic Red', color: '#c00e1a' },
  { id: 37, name: 'Metallic Classic Gold', color: '#c2944f' },
  { id: 38, name: 'Metallic Orange', color: '#f78616' },
  { id: 53, name: 'Metallic Green', color: '#155c2d' },
  { id: 55, name: 'Matte Lime Green', color: '#66b81f' },
  { id: 60, name: 'Worn Sea Wash', color: '#65867f' },
  { id: 64, name: 'Metallic Blue', color: '#47578f' },
  { id: 70, name: 'Metallic Bright Blue', color: '#0b9cf1' },
  { id: 101, name: 'Metallic Biston Brown', color: '#402e2b' },
  { id: 106, name: 'Metallic Sun Bleeched Sand', color: '#dfd5b2' },
  { id: 137, name: 'Metallic Vermillion Pink', color: '#df5891' },
  { id: 145, name: 'Metallic Purple', color: '#621276' },
]

// 材料图标映射表
const materialIcons: { [key: string]: string } = {
  '钢锭': 'steelplate.webp',
  '铜锭': 'copper.webp',
  '铁锭': 'iron.webp',
  '铝锭': 'aluminum.webp',
  '润滑油': 'lubricant.webp',
  '塑料': 'plastic.webp',
  '玻璃': 'glassmesh.webp',
  '催化剂': 'catalyst.webp'
}


// 响应式数据
const isAdmin = computed(() => permissions.value.includes('admin'));
const showAsPlayer = ref(false); // 管理员是否以玩家视图显示
const isMenuOpen = ref(true); // 侧边栏是否展开
const currentSession = ref(null);
const playerVote = ref(null);
const voteStats = ref(null);

// 订单模式相关数据
const isOrderMode = ref(false);
const currentOrder = ref(null);
const playerMaterials = ref({});
const submitAmounts = ref({});
const submittingAll = ref(false);
const claiming = ref(false);
const cancelling = ref(false);

// 颜色选择相关
const primaryColor = ref(colors[0])
const secondaryColor = ref(colors[0])

// 新会话数据
const newSessionData = ref({
  vehicles: Array(10).fill(''),
  vehiclePrices: {} as { [key: string]: number }, // 订单价格（玩家直接支付）
  materialPrices: {} as { [key: string]: number } // 材料价格（用于生成材料需求）
});

// 计算属性
const canCreateSession = computed(() => {
  return isAdmin.value &&
         newSessionData.value.vehicles.every(v => v.trim() !== '') &&
         newSessionData.value.vehicles.every(v => {
           const orderPrice = newSessionData.value.vehiclePrices[v];
           const materialPrice = newSessionData.value.materialPrices[v];
           return orderPrice > 0 && materialPrice > 0;
         });
});

// 当前显示模式（管理员可以切换为玩家视图进行测试）
const currentViewMode = computed(() => {
  if (!isAdmin.value) return 'player';
  return showAsPlayer.value ? 'player' : 'admin';
});

// 方法
function closePanel() {
  events.emitServer('webview:close');
}

function toggleViewMode() {
  showAsPlayer.value = !showAsPlayer.value;
  
  // 当切换到玩家视图时，重新请求数据以获取玩家的投票状态
  if (showAsPlayer.value) {
    events.emitServer('vehicleVote:requestData');
  }
}

function toggleMenu() {
  isMenuOpen.value = !isMenuOpen.value;
}

function getStatusClass(status: string) {
  switch (status) {
    case 'active': return 'status-active';
    case 'ended': return 'status-ended';
    case 'orders_active': return 'status-orders';
    default: return '';
  }
}

function getStatusText(status: string) {
  switch (status) {
    case 'active': return '投票进行中';
    case 'ended': return '已结束';
    case 'orders_active': return '订单开放中';
    default: return '未知状态';
  }
}

function formatTime(timestamp: number) {
  return new Date(timestamp).toLocaleString('zh-CN');
}

function formatNumber(num: number) {
  return num.toLocaleString();
}

function getTimeRemaining() {
  if (!currentSession.value) return '';
  
  const now = Date.now();
  const end = currentSession.value.endTime;
  const remaining = Math.max(0, end - now);
  
  const hours = Math.floor(remaining / (1000 * 60 * 60));
  const minutes = Math.floor((remaining % (1000 * 60 * 60)) / (1000 * 60));
  
  return `${hours}小时${minutes}分钟`;
}

function vote(vehicleModel: string) {
  events.emitServer('vehicleVote:vote', vehicleModel);
}

function createSession() {
  // 检查管理员权限
  if (!isAdmin.value) {
    console.error('无管理员权限，无法创建投票会话');
    return;
  }

  // 准备数据
  const sessionData = {
    vehicles: newSessionData.value.vehicles.filter(v => v.trim() !== ''),
    vehiclePrices: {} as { [key: string]: number },
    materialPrices: {} as { [key: string]: number }
  };

  // 设置车辆的订单价格和材料价格
  sessionData.vehicles.forEach(vehicle => {
    sessionData.vehiclePrices[vehicle] = newSessionData.value.vehiclePrices[vehicle] || 0;
    sessionData.materialPrices[vehicle] = newSessionData.value.materialPrices[vehicle] || 0;
  });

  events.emitServer('vehicleVote:createSession', sessionData);
}

function confirmOrder(vehicle: string) {
  events.emitServer('vehicleVote:createOrder', {
    vehicleModel: vehicle
  });
}

// ==================== 订单模式相关方法 ====================

// 获取材料图标路径
function getMaterialIcon(materialName: string): string {
  const iconName = materialIcons[materialName]
  return iconName ? findIcon(iconName) : findIcon('default.webp')
}

// 获取已提交的材料数量
function getSubmittedAmount(materialName: string): number {
  if (!currentOrder.value) return 0
  return currentOrder.value.materialsSubmitted?.[materialName] || 0
}

// 获取材料进度百分比
function getProgressPercent(materialName: string): number {
  if (!currentOrder.value) return 0
  const required = currentOrder.value.materials[materialName]
  const submitted = getSubmittedAmount(materialName)
  return Math.min((submitted / required) * 100, 100)
}

// 获取最大可提交数量
function getMaxSubmittable(materialName: string): number {
  if (!currentOrder.value) return 0
  const required = currentOrder.value.materials[materialName]
  const submitted = getSubmittedAmount(materialName)
  const remaining = required - submitted
  
  // 获取玩家背包中的材料数量
  const playerAmount = playerMaterials.value[materialName] || 0
  
  // 返回玩家拥有的数量和还需要的数量中的较小值
  return Math.max(0, Math.min(remaining, playerAmount))
}

// 获取输入框占位符
function getInputPlaceholder(materialName: string): string {
  const maxAmount = getMaxSubmittable(materialName)
  const playerAmount = playerMaterials.value[materialName] || 0
  
  if (playerAmount === 0) {
    return '背包中无此材料'
  } else if (maxAmount === 0) {
    return '已完成'
  } else {
    return `最多 ${maxAmount}`
  }
}

// 检查是否可以提交材料
function canSubmitMaterial(materialName: string): boolean {
  const amount = submitAmounts.value[materialName]
  return amount > 0 && amount <= getMaxSubmittable(materialName)
}

// 总体进度计算属性
const overallProgress = computed(() => {
  if (!currentOrder.value) return 0

  const materials = currentOrder.value.materials
  const submitted = currentOrder.value.materialsSubmitted || {}

  let totalProgress = 0
  let materialCount = 0

  for (const [material, required] of Object.entries(materials)) {
    const submittedAmount = submitted[material] || 0
    const progress = Math.min((submittedAmount / (required as number)) * 100, 100)
    totalProgress += progress
    materialCount++
  }

  return materialCount > 0 ? totalProgress / materialCount : 0
})

// 检查订单是否完成
const isOrderComplete = computed(() => {
  if (!currentOrder.value) return false

  const materials = currentOrder.value.materials
  const submitted = currentOrder.value.materialsSubmitted || {}

  for (const [material, required] of Object.entries(materials)) {
    if ((submitted[material] || 0) < (required as number)) {
      return false
    }
  }

  return true
})

// 检查是否可以一键提交任何材料
const canSubmitAnyMaterial = computed(() => {
  if (!currentOrder.value || !playerMaterials.value) return false

  const materials = currentOrder.value.materials
  const submitted = currentOrder.value.materialsSubmitted || {}

  // 检查是否有任何材料可以提交
  for (const [material, required] of Object.entries(materials)) {
    const alreadySubmitted = submitted[material] || 0
    const stillNeeded = (required as number) - alreadySubmitted
    const playerHas = playerMaterials.value[material] || 0
    
    if (stillNeeded > 0 && playerHas > 0) {
      return true // 至少有一种材料可以提交
    }
  }

  return false
})

// 提交单个材料
function submitMaterial(materialName: string) {
  const amount = submitAmounts.value[materialName]
  if (amount > 0) {
    events.emitServer('importOrder:submitMaterial', { materialName, amount })
    submitAmounts.value[materialName] = 0
  }
}

// 一键提交所有可提交的材料
function submitAllMaterials() {
  if (!currentOrder.value || !playerMaterials.value) return
  
  submittingAll.value = true
  
  const materials = currentOrder.value.materials
  const submitted = currentOrder.value.materialsSubmitted || {}
  const submissionList: { material: string, amount: number }[] = []
  
  // 计算每种材料可以提交的数量
  for (const [material, required] of Object.entries(materials)) {
    const alreadySubmitted = submitted[material] || 0
    const stillNeeded = (required as number) - alreadySubmitted
    const playerHas = playerMaterials.value[material] || 0
    
    if (stillNeeded > 0 && playerHas > 0) {
      const canSubmit = Math.min(stillNeeded, playerHas)
      if (canSubmit > 0) {
        submissionList.push({ material, amount: canSubmit })
      }
    }
  }
  
  // 发送批量提交请求到服务器
  if (submissionList.length > 0) {
    events.emitServer('importOrder:submitAllMaterials', submissionList)
  } else {
    submittingAll.value = false
  }
}

// 获得车辆
function claimVehicle() {
  claiming.value = true
  events.emitServer('importOrder:claimVehicle')
}

// 取消进口车辆订单
function cancelImportOrder() {
  cancelling.value = true
  events.emitServer('importOrder:cancel')
}

// 预览当前订单的车辆
function previewVehicle() {
  if (currentOrder.value) {
    events.emitClient('previewvehicle', currentOrder.value.vehicleModel, primaryColor.value.id, secondaryColor.value.id)
  }
}

// 预览投票阶段的车辆
function previewVoteVehicle(vehicleModel: string) {
  events.emitClient('previewvehicle', vehicleModel, primaryColor.value.id, secondaryColor.value.id)
}

// 监听服务端数据更新
function handleDataUpdate(event: any) {
  currentSession.value = event.currentSession;
  playerVote.value = event.playerVote;
  voteStats.value = event.voteStats;
  
  // 处理订单模式数据
  if (event.isOrderMode) {
    isOrderMode.value = true;
    currentOrder.value = event.currentOrder;
    playerMaterials.value = event.playerMaterials || {};
    // 重置提交状态
    submittingAll.value = false;
    claiming.value = false;
    cancelling.value = false;
  } else {
    isOrderMode.value = false;
    currentOrder.value = null;
    playerMaterials.value = {};
  }
}

function selectPrimaryColor(color: any) {
  primaryColor.value = color
}

function selectSecondaryColor(color: any) {
  secondaryColor.value = color
}

// 监听颜色变化，实时更新预览
watch([primaryColor, secondaryColor], () => {
  // 如果在订单模式且有订单，预览订单车辆
  if (isOrderMode.value && currentOrder.value) {
    events.emitClient('previewvehicle', currentOrder.value.vehicleModel, primaryColor.value.id, secondaryColor.value.id)
  }
  // 如果在投票模式且有玩家投票，预览投票的车辆
  else if (!isOrderMode.value && playerVote.value) {
    events.emitClient('previewvehicle', playerVote.value.vehicleModel, primaryColor.value.id, secondaryColor.value.id)
  }
})

events.on('vehicleVote:updateData', handleDataUpdate);

// 监听订单创建成功事件
events.on('vehicleVote:orderCreated', () => {
  events.emitServer('vehicleVote:requestData');
});

// 监听进口订单相关事件
events.on('importOrder:materialSubmitted', () => {
  // 材料提交成功，重新请求数据更新进度
  events.emitServer('vehicleVote:requestData');
});

events.on('importOrder:allMaterialsSubmitted', () => {
  // 批量材料提交完成
  submittingAll.value = false;
  events.emitServer('vehicleVote:requestData');
});

events.on('importOrder:vehicleClaimed', () => {
  // 车辆获取成功，清除订单状态
  claiming.value = false;
  isOrderMode.value = false;
  currentOrder.value = null;
  // 重新请求数据，可能会切换回投票界面
  events.emitServer('vehicleVote:requestData');
});

events.on('importOrder:cancelled', () => {
  // 订单取消成功，清除订单状态
  cancelling.value = false;
  isOrderMode.value = false;
  currentOrder.value = null;
  // 重新请求数据，切换回投票界面
  events.emitServer('vehicleVote:requestData');
});

onMounted(() => {
  events.emitServer('vehicleVote:requestData');
});
</script>

<style scoped>
/* CSS变量定义 */
.vehicle-vote-sidebar {
  /* 主色调 - 青绿色系 */
  --primary-color: #64ffda;
  --primary-hover: #ffffff;
  --primary-bg: #0f172a;
  --card-bg: rgba(30, 41, 59, 0.9);
  --card-bg-light: rgba(51, 65, 85, 0.8);
  --border-color: rgba(100, 255, 218, 0.2);
  --text-primary: #f8fafc;
  --text-secondary: #94a3b8;
  --text-muted: #64748b;
  --success: #64ffda;
  --warning: #a78bfa;
  --danger: #ef4444;
  --shadow-primary: 0 0 1vh rgba(100, 255, 218, 0.3);
  --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.1);

  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 100;
  pointer-events: none;
}

/* 侧边栏主体 */
.sidebar {
  position: fixed;
  top: 2vh;
  bottom: 2vh;
  width: 30vw;
  background: var(--card-bg);
  border: 1px solid var(--border-color);
  box-shadow: var(--shadow-primary);
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
  pointer-events: all;
}

.right-sidebar {
  right: 2vh;
  border-radius: 20px 0 0 20px;
}

.right-sidebar.collapsed {
  transform: translateX(30vw);
}

/* 切换按钮 */
.toggle-btn {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  width: 3vh;
  height: 12vh;
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--success) 100%);
  border: 1px solid var(--border-color);
  color: var(--primary-bg);
  font-size: 1.4vh;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: var(--shadow-primary);
  z-index: 10;
}

.toggle-btn:hover {
  background: linear-gradient(135deg, var(--primary-hover) 0%, var(--primary-color) 100%);
  box-shadow: var(--shadow-primary), 0 0 1vh rgba(100, 255, 218, 0.5);
  transform: translateY(-50%) scale(1.05);
}

.right-sidebar .toggle-btn {
  left: -3vh;
  border-radius: 12px 0 0 12px;
}

/* 侧边栏内容 */
.sidebar-content {
  height: 100%;
  padding: 1.5vh;
  overflow-y: auto;
  scrollbar-width: thin;
  scrollbar-color: var(--primary-color) rgba(255, 255, 255, 0.1);
  display: flex;
  flex-direction: column;
  gap: 1.5vh;
}

.sidebar-content::-webkit-scrollbar {
  width: 0.8vh;
}

.sidebar-content::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 0.4vh;
  margin: 0.4vh 0;
}

.sidebar-content::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--success) 100%);
  border-radius: 0.4vh;
  transition: background 0.3s ease;
}

/* 侧边栏标题 */
.sidebar-header {
  display: flex;
  align-items: center;
  gap: 1.2vh;
  padding: 1.5vh;
  background: linear-gradient(135deg, rgba(100, 255, 218, 0.05) 0%, transparent 60%);
  border: 1px solid var(--border-color);
  border-radius: 1.2vh;
  position: relative;
  overflow: hidden;
  flex-shrink: 0;
}

.header-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 4vh;
  height: 4vh;
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--success) 100%);
  border: 1px solid var(--border-color);
  border-radius: 50%;
  color: var(--primary-bg);
  font-size: 1.8vh;
  position: relative;
  z-index: 1;
  box-shadow: var(--shadow-sm);
}

.title-info {
  position: relative;
  z-index: 1;
}

.title-info h1 {
  color: var(--primary-color);
  font-size: 1.8vh;
  font-weight: 700;
  letter-spacing: 0.05em;
  margin: 0 0 0.2vh 0;
}

.subtitle {
  font-size: 1vh;
  color: var(--text-secondary);
  font-weight: 400;
  text-transform: uppercase;
  letter-spacing: 0.1em;
}

.test-mode-indicator {
  display: inline-flex;
  align-items: center;
  gap: 0.5vh;
  margin-top: 0.5vh;
  padding: 0.4vh 0.8vh;
  background: rgba(167, 139, 250, 0.2);
  color: var(--warning);
  border-radius: 1vh;
  font-size: 1vh;
  font-weight: 500;
}

/* 卡片样式 */
.view-toggle-card,
.session-status-card,
.create-session-card,
.voting-header-card,
.orders-header-card,
.order-header-card {
  background: var(--card-bg-light);
  border: 1px solid var(--border-color);
  border-radius: 1.2vh;
  padding: 1.5vh;
  margin-bottom: 1vh;
}

/* 美化标题样式 */
.order-header-card h4,
.orders-header-card h4 {
  margin: 0 0 1vh 0;
  font-size: 1.8vh;
  font-weight: 700;
  color: var(--primary-color);
  text-shadow: 0 0 1vh rgba(100, 255, 218, 0.4);
  background: linear-gradient(135deg, rgba(100, 255, 218, 0.1) 0%, rgba(100, 255, 218, 0.05) 100%);
  padding: 1vh 1.5vh;
  border-radius: 0.8vh;
  border: 1px solid rgba(100, 255, 218, 0.3);
  text-align: center;
  letter-spacing: 0.05em;
  position: relative;
  overflow: hidden;
}

.order-header-card h4::before,
.orders-header-card h4::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(100, 255, 218, 0.2), transparent);
}



/* 美化订单信息样式 */
.order-info {
  display: inline-block;
  padding: 0.8vh 1.5vh;
  background: linear-gradient(135deg, rgba(167, 139, 250, 0.15) 0%, rgba(167, 139, 250, 0.08) 100%);
  border: 1px solid rgba(167, 139, 250, 0.4);
  border-radius: 2vh;
  color: var(--warning);
  font-size: 1.2vh;
  font-weight: 600;
  text-shadow: 0 0 0.5vh rgba(167, 139, 250, 0.3);
  margin-top: 0.5vh;
  position: relative;
  box-shadow: 0 0 1vh rgba(167, 139, 250, 0.2);
}

.order-info::before {
  content: '⏰';
  margin-right: 0.5vh;
  font-size: 1.1vh;
}

/* 美化车辆名称样式 */
.order-vehicle {
  display: inline-block;
  padding: 0.8vh 1.5vh;
  background: linear-gradient(135deg, rgba(100, 255, 218, 0.15) 0%, rgba(100, 255, 218, 0.08) 100%);
  border: 1px solid rgba(100, 255, 218, 0.4);
  border-radius: 0.8vh;
  color: var(--primary-color);
  font-size: 1.4vh;
  font-weight: 700;
  text-shadow: 0 0 0.8vh rgba(100, 255, 218, 0.4);
  margin: 0.8vh 0;
  text-align: center;
  letter-spacing: 0.05em;
  position: relative;
  box-shadow: 0 0 1vh rgba(100, 255, 218, 0.2);
  text-transform: uppercase;
  min-width: 12vh;
}

.order-vehicle::before {
  content: '🚗';
  margin-right: 0.8vh;
  font-size: 1.2vh;
  filter: grayscale(1) brightness(1.5);
}

/* 美化预览车辆按钮 */
.preview-vehicle-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.8vh;
  padding: 1vh 1.5vh;
  background: linear-gradient(135deg, var(--primary-color) 0%, rgba(100, 255, 218, 0.8) 100%);
  border: 1px solid var(--primary-color);
  border-radius: 0.8vh;
  color: var(--primary-bg);
  font-size: 1.3vh;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  margin-top: 1vh;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
  box-shadow: var(--shadow-primary), 0 2px 8px rgba(100, 255, 218, 0.3);
  position: relative;
  overflow: hidden;
}

.preview-vehicle-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.5s;
}

.preview-vehicle-btn:hover::before {
  left: 100%;
}

.preview-vehicle-btn:hover {
  background: linear-gradient(135deg, var(--primary-hover) 0%, var(--primary-color) 100%);
  box-shadow: var(--shadow-primary), 0 4px 16px rgba(100, 255, 218, 0.5);
  transform: translateY(-2px) scale(1.02);
}

.preview-vehicle-btn:active {
  transform: translateY(0) scale(0.98);
}

.preview-vehicle-btn i {
  font-size: 1.4vh;
}



.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5vh;
}

.card-header h4 {
  margin: 0;
  font-size: 1.6vh;
  color: var(--text-primary);
}

/* 按钮样式 */
.toggle-btn-view {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.6vh;
  padding: 1vh 1.2vh;
  background: var(--card-bg);
  border: 1px solid var(--border-color);
  border-radius: 0.8vh;
  color: var(--text-secondary);
  cursor: pointer;
  transition: all 0.2s;
  font-size: 1.2vh;
  font-weight: 500;
}

.toggle-btn-view:hover {
  background: rgba(100, 255, 218, 0.1);
  border-color: var(--primary-color);
  color: var(--primary-color);
}

.toggle-btn-view.active {
  background: var(--primary-color);
  color: var(--primary-bg);
  border-color: var(--primary-color);
}

/* 状态徽章 */
.status-badge {
  padding: 0.5vh 1.2vh;
  border-radius: 2vh;
  font-size: 1vh;
  font-weight: 500;
}

.status-active {
  background: rgba(100, 255, 218, 0.2);
  color: var(--success);
}

.status-ended {
  background: rgba(239, 68, 68, 0.2);
  color: var(--danger);
}

.status-orders {
  background: rgba(167, 139, 250, 0.2);
  color: var(--warning);
}

/* 信息行 */
.session-info {
  display: flex;
  flex-direction: column;
  gap: 1vh;
  margin-bottom: 1.5vh;
}

.info-row {
  display: flex;
  justify-content: space-between;
  font-size: 1.1vh;
}

.label {
  color: var(--text-secondary);
}

.value {
  color: var(--text-primary);
  font-weight: 500;
}

/* 投票统计 */
.vote-statistics h5 {
  margin: 0 0 1vh 0;
  color: var(--text-primary);
  font-size: 1.4vh;
}

.stats-grid {
  display: flex;
  flex-direction: column;
  gap: 0.5vh;
  margin-bottom: 1vh;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  padding: 0.6vh 1vh;
  background: var(--primary-bg);
  border-radius: 0.6vh;
  border: 1px solid var(--border-color);
  font-size: 1.1vh;
}

.stat-model {
  color: var(--text-primary);
}

.stat-count {
  color: var(--success);
  font-weight: 600;
}

.total-votes {
  text-align: center;
  font-size: 1.3vh;
  font-weight: 600;
  color: var(--primary-color);
}

/* 车辆输入 */
.vehicles-setup h5 {
  margin: 0 0 1vh 0;
  color: var(--text-primary);
  font-size: 1.4vh;
}

.vehicle-inputs {
  display: flex;
  flex-direction: column;
  gap: 1vh;
  margin-bottom: 1.5vh;
}

.vehicle-input-group {
  display: flex;
  flex-direction: column;
  gap: 0.5vh;
}

.price-inputs {
  display: flex;
  gap: 0.5vh;
}

.vehicle-input,
.price-input {
  padding: 0.8vh;
  background: var(--primary-bg);
  border: 1px solid var(--border-color);
  border-radius: 0.6vh;
  color: var(--text-primary);
  font-size: 1.1vh;
}

.vehicle-input::placeholder,
.price-input::placeholder {
  color: var(--text-muted);
}

.vehicle-input:focus,
.price-input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 0.2vh rgba(100, 255, 218, 0.2);
}

/* 通用按钮 */
.create-btn,
.preview-btn,
.vote-btn,
.order-btn,
.action-btn {
  padding: 0.8vh 1.2vh;
  border-radius: 0.6vh;
  font-size: 1.1vh;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5vh;
  border: 1px solid transparent;
}

.create-btn {
  width: 100%;
  background: var(--primary-color);
  color: var(--primary-bg);
  font-weight: 600;
  padding: 1vh;
}

.create-btn:hover:not(:disabled) {
  background: var(--primary-hover);
  color: var(--primary-color);
}

.create-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  background: var(--text-muted);
}

.preview-btn {
  background: rgba(100, 255, 218, 0.1);
  color: var(--primary-color);
  border-color: rgba(100, 255, 218, 0.3);
  flex: 1;
  min-height: 3.5vh;
}

.preview-btn:hover {
  background: rgba(100, 255, 218, 0.2);
  border-color: var(--primary-color);
  transform: translateY(-0.1vh);
}

.vote-btn {
  background: var(--primary-color);
  color: var(--primary-bg);
  flex: 1;
  min-height: 3.5vh;
  font-weight: 600;
}

.vote-btn:hover {
  background: var(--primary-hover);
  color: var(--primary-color);
  transform: translateY(-0.1vh);
}

.vote-btn.voted {
  background: var(--success);
  color: var(--primary-bg);
}

.order-btn {
  background: var(--primary-color);
  color: var(--primary-bg);
  min-height: 3.5vh;
  font-weight: 600;
}

.order-btn:hover {
  background: var(--primary-hover);
  color: var(--primary-color);
  transform: translateY(-0.1vh);
}

/* 车辆列表 */
.vehicles-list,
.top-vehicles-list,
.materials-list {
  display: flex;
  flex-direction: column;
  gap: 1vh;
}

.vehicle-vote-card,
.top-vehicle-card {
  background: var(--card-bg);
  border: 1px solid var(--border-color);
  border-radius: 1vh;
  padding: 1.5vh;
  transition: all 0.3s;
  position: relative;
}

.vehicle-vote-card:hover,
.top-vehicle-card:hover {
  border-color: var(--primary-color);
  transform: translateY(-0.2vh);
  box-shadow: var(--shadow-primary);
}

.vehicle-vote-card.selected {
  border-color: var(--success);
  background: rgba(100, 255, 218, 0.1);
  box-shadow: var(--shadow-primary);
}

.vehicle-name {
  font-size: 1.5vh;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 0.8vh;
}

.vehicle-price {
  font-size: 1.3vh;
  color: var(--success);
  font-weight: 600;
  margin-bottom: 1vh;
}

.vehicle-actions {
  display: flex;
  gap: 0.8vh;
}

.voted-indicator {
  position: absolute;
  top: 0.5vh;
  right: 0.5vh;
  background: var(--success);
  color: var(--primary-bg);
  padding: 0.3vh 0.6vh;
  border-radius: 1vh;
  font-size: 1vh;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 0.3vh;
}

/* 排名徽章 */
.rank-badge {
  width: 3vh;
  height: 3vh;
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--warning) 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--primary-bg);
  font-weight: 600;
  font-size: 1.4vh;
  margin-right: 1vh;
}

.top-vehicle-card {
  display: flex;
  align-items: center;
}

.vehicle-info {
  flex: 1;
}

.vehicle-card-actions {
  display: flex;
  flex-direction: column;
  gap: 0.8vh;
  min-width: 10vh;
}

/* 材料相关 */
.materials-section {
  background: var(--card-bg);
  border-radius: 1vh;
  padding: 1.5vh;
  border: 1px solid var(--border-color);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1vh;
}

.section-header h5 {
  color: var(--text-primary);
  font-size: 1.4vh;
  font-weight: 600;
  margin: 0;
}

.quick-submit-btn {
  background: linear-gradient(135deg, var(--primary-color), #4facfe);
  color: var(--primary-bg);
  border: none;
  padding: 0.6vh 1vh;
  border-radius: 0.6vh;
  font-size: 1vh;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.4vh;
}

.quick-submit-btn:hover:not(:disabled) {
  background: linear-gradient(135deg, var(--primary-hover), #ffffff);
  color: var(--primary-color);
}

.quick-submit-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.material-card {
  background: rgba(30, 41, 59, 0.5);
  border: 1px solid var(--border-color);
  border-radius: 0.8vh;
  padding: 1vh;
  transition: all 0.3s ease;
}

.material-card:hover {
  border-color: rgba(100, 255, 218, 0.3);
  background: rgba(30, 41, 59, 0.7);
}

.material-header {
  display: flex;
  align-items: center;
  gap: 0.8vh;
  margin-bottom: 0.8vh;
}

.material-icon-container {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 3vh;
  height: 3vh;
  background: rgba(100, 255, 218, 0.1);
  border: 1px solid rgba(100, 255, 218, 0.2);
  border-radius: 0.6vh;
  flex-shrink: 0;
}

.material-icon {
  width: 2.5vh;
  height: 2.5vh;
  object-fit: contain;
}

.material-info {
  flex: 1;
}

.material-name {
  color: var(--text-primary);
  font-size: 1.2vh;
  font-weight: 600;
  margin-bottom: 0.2vh;
}

.material-progress-text {
  color: var(--text-secondary);
  font-size: 1vh;
  font-weight: 500;
}

.material-owned-text {
  color: var(--primary-color);
  font-size: 1vh;
  font-weight: 600;
  margin-top: 0.1vh;
}

.progress-bar {
  background: rgba(30, 41, 59, 0.8);
  border-radius: 0.4vh;
  height: 0.6vh;
  margin-bottom: 0.8vh;
  overflow: hidden;
}

.progress-bar.large {
  height: 1vh;
  margin-bottom: 0;
}

.progress-fill {
  background: linear-gradient(90deg, var(--primary-color), var(--success));
  height: 100%;
  transition: width 0.3s ease;
  border-radius: 0.4vh;
}

.material-actions {
  display: flex;
  gap: 0.8vh;
  align-items: center;
}

.amount-input {
  background: var(--primary-bg);
  border: 1px solid var(--border-color);
  color: var(--text-primary);
  padding: 0.6vh 0.8vh;
  border-radius: 0.6vh;
  width: 8vh;
  font-size: 1vh;
  transition: all 0.2s ease;
}

.amount-input:focus {
  outline: none;
  border-color: var(--primary-color);
  background: rgba(100, 255, 218, 0.05);
}

.submit-btn {
  background: var(--primary-color);
  color: var(--primary-bg);
  border: none;
  padding: 0.6vh 1vh;
  border-radius: 0.6vh;
  font-size: 1vh;
  font-weight: 600;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.4vh;
  transition: all 0.2s ease;
  min-width: 6vh;
}

.submit-btn:hover:not(:disabled) {
  background: var(--primary-hover);
  color: var(--primary-color);
}

.submit-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* 总体进度 */
.overall-progress {
  margin-bottom: 1.5vh;
  padding: 1vh;
  background: rgba(100, 255, 218, 0.05);
  border: 1px solid rgba(100, 255, 218, 0.1);
  border-radius: 0.8vh;
}

.progress-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.8vh;
}

.progress-label {
  color: var(--text-primary);
  font-size: 1.2vh;
  font-weight: 600;
}

.progress-text {
  color: var(--primary-color);
  font-size: 1.4vh;
  font-weight: 700;
}

/* 订单操作 */
.order-actions {
  display: flex;
  flex-direction: column;
  gap: 1vh;
  margin-bottom: 1vh;
}

.action-btn.primary {
  background: var(--primary-color);
  color: var(--primary-bg);
  border-color: var(--primary-color);
}

.action-btn.primary:hover:not(:disabled) {
  background: var(--primary-hover);
  color: var(--primary-color);
}

.action-btn.danger {
  background: rgba(239, 68, 68, 0.1);
  color: var(--danger);
  border-color: rgba(239, 68, 68, 0.3);
}

.action-btn.danger:hover:not(:disabled) {
  background: rgba(239, 68, 68, 0.2);
  border-color: var(--danger);
}

.action-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.cancel-info {
  color: var(--text-muted);
  font-size: 1vh;
  line-height: 1.4;
}

/* 其他状态 */
.vote-status {
  text-align: center;
  padding: 1vh;
  background: rgba(100, 255, 218, 0.1);
  border-radius: 0.6vh;
  color: var(--primary-color);
  font-size: 1.1vh;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.6vh;
}

.admin-test-notice {
  margin-bottom: 1.5vh;
}

.notice-card {
  display: flex;
  align-items: center;
  gap: 1vh;
  padding: 1vh 1.5vh;
  background: rgba(167, 139, 250, 0.1);
  border: 1px solid rgba(167, 139, 250, 0.3);
  border-radius: 0.8vh;
  color: var(--warning);
}

.notice-card i {
  font-size: 1.5vh;
  flex-shrink: 0;
}

.notice-content h4 {
  margin: 0 0 0.3vh 0;
  font-size: 1.2vh;
  font-weight: 600;
  color: var(--warning);
}

.notice-content p {
  margin: 0;
  font-size: 1vh;
  color: var(--text-secondary);
  line-height: 1.4;
}

.no-vote-section {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 20vh;
}

.no-vote-card {
  text-align: center;
  padding: 2vh;
}

.no-vote-icon {
  width: 6vh;
  height: 6vh;
  background: var(--card-bg);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 1.5vh;
  border: 1px solid var(--border-color);
}

.no-vote-icon i {
  font-size: 2.5vh;
  color: var(--text-muted);
}

.no-vote-card h4 {
  margin: 0 0 0.8vh 0;
  color: var(--text-primary);
  font-size: 1.4vh;
}

.no-vote-card p {
  color: var(--text-secondary);
  margin: 0;
  font-size: 1.1vh;
}

/* 关闭按钮 */
.close-btn {
  position: fixed;
  top: 2vh;
  right: 2vh;
  width: 4vh;
  height: 4vh;
  background: var(--card-bg);
  border: 1px solid var(--border-color);
  border-radius: 50%;
  color: var(--text-secondary);
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.4vh;
  z-index: 200;
  pointer-events: all;
}

.close-btn:hover {
  background: var(--danger);
  color: var(--text-primary);
  border-color: var(--danger);
}



/* 颜色配置卡片 */
.color-card {
  background: var(--card-bg);
  border: 1px solid var(--border-color);
  border-radius: 1vh;
  padding: 1.5vh;
  margin-top: 1.5vh;
  margin-bottom: 1.5vh;
}

.color-card-header {
  display: flex;
  align-items: center;
  gap: 0.8vh;
  margin-bottom: 1.5vh;
  padding-bottom: 1vh;
  border-bottom: 1px solid var(--border-color);
}

.header-title {
  display: flex;
  align-items: center;
  gap: 0.5vh;
}

.header-title h5 {
  color: var(--text-primary);
  font-size: 1.4vh;
  font-weight: 600;
  margin: 0;
}

.color-card-content {
  display: flex;
  flex-direction: column;
  gap: 1.5vh;
}

.color-picker {
  margin-bottom: 1.5vh;
}

.color-picker h6 {
  color: var(--text-primary);
  font-size: 1.2vh;
  font-weight: 600;
  margin-bottom: 0.8vh;
}

.color-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(3vh, 1fr));
  gap: 0.8vh;
}

.color-option {
  width: 3vh;
  height: 3vh;
  border-radius: 50%;
  cursor: pointer;
  border: 1px solid transparent;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: var(--shadow-sm);
}

.color-option:hover:not(.selected) {
  transform: scale(1.1);
  border-color: var(--primary-color);
}

.color-option.selected {
  border-color: var(--primary-color);
  box-shadow: var(--shadow-primary);
}
</style>