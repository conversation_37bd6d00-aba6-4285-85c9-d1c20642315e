<template>
  <div class="showroom-container">
    <div class="showroom-background">
      <!-- 展台管理面板 -->
      <div class="showroom-panel">
        <!-- 标题区域 -->
        <div class="showroom-header">
          <div class="showroom-title">
            <i class="fas fa-car"></i>
            <h2>汽车展台管理</h2>
            <div class="status-badge">
              <span>{{ Object.keys(currentVehicles).length }}/7 展台已使用</span>
            </div>
          </div>
          <button 
            class="close-button-header"
            @click="closePanel"
            @mouseenter="playHover"
          >
            <i class="fas fa-times"></i>
          </button>
        </div>

        <!-- 展台列表 -->
        <div class="showrooms-container">
          <div class="showrooms-list">
            <div 
              v-for="(position, index) in showroomPositions" 
              :key="index"
              class="showroom-item"
              :class="{ 'has-vehicle': currentVehicles[index] }"
            >
              <div class="showroom-number">
                {{ index + 1 }}
              </div>
              <div class="showroom-info">
                <div class="showroom-name">展台 {{ index + 1 }}</div>
                <div class="showroom-coordinates">
                  {{ position.x.toFixed(1) }}, {{ position.y.toFixed(1) }}, {{ position.z.toFixed(1) }}
                </div>
                <div v-if="currentVehicles[index]" class="current-vehicle">
                  <i class="fas fa-car"></i>
                  <span>{{ currentVehicles[index] }}</span>
                </div>
                <div v-else class="empty-showroom">
                  <i class="fas fa-plus-circle"></i>
                  <span>空闲展台</span>
                </div>
              </div>
              <div class="showroom-controls">
                <div class="input-group">
                  <input 
                    v-model="vehicleInputs[index]" 
                    placeholder="输入车辆模型" 
                    class="vehicle-input"
                    @keyup.enter="placeVehicle(index)"
                  />
                  <button 
                    @click="placeVehicle(index)" 
                    :disabled="!vehicleInputs[index]" 
                    class="place-btn"
                    @mouseenter="playHover"
                  >
                    <i class="fas fa-plus"></i>
                    <span>放置</span>
                  </button>
                </div>
                <button 
                  @click="removeVehicle(index)" 
                  class="remove-btn"
                  @mouseenter="playHover"
                  :disabled="!currentVehicles[index]"
                >
                  <i class="fas fa-trash"></i>
                  <span>移除</span>
                </button>
              </div>
            </div>
          </div>
        </div>

        <!-- 底部操作区 -->
        <div class="showroom-footer">
          <div class="footer-stats">
            <div class="stat-item">
              <i class="fas fa-info-circle"></i>
              <span>使用车辆模型名称，如: sultan, infernus, elegy2</span>
            </div>
          </div>
        
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted } from 'vue';
import { useEvents } from '../../../../webview/composables/useEvents';
import * as Tone from 'tone';

const events = useEvents();

const show = ref(true); // 直接显示
const showroomPositions = ref([]);
const vehicleInputs = ref({});
const currentVehicles = ref({});

// 音效系统
const showroomSounds = {
  hover: new Tone.Synth({
    oscillator: { type: 'sine' },
    envelope: { attack: 0.001, decay: 0.05, sustain: 0, release: 0.1 },
    volume: -28
  }).toDestination(),
  
  buttonClick: new Tone.Synth({
    oscillator: { type: 'sine' },
    envelope: { attack: 0.01, decay: 0.1, sustain: 0.05, release: 0.3 },
    volume: -18
  }).toDestination(),
  
  success: new Tone.PolySynth(Tone.Synth, {
    oscillator: { type: 'triangle' },
    envelope: { attack: 0.02, decay: 0.2, sustain: 0.1, release: 0.5 },
    volume: -20
  }).toDestination()
};

// 播放音效函数
const playHover = () => {
  Tone.start();
  showroomSounds.hover.triggerAttackRelease('G5', 0.05);
};

const playButtonClick = () => {
  Tone.start();
  showroomSounds.buttonClick.triggerAttackRelease('C5', 0.1);
};

const playSuccess = () => {
  Tone.start();
  showroomSounds.success.triggerAttackRelease(['C4', 'E4', 'G4'], 0.3);
};

// 加载FontAwesome
const loadFontAwesome = () => {
  const link = document.createElement('link');
  link.rel = 'stylesheet';
  link.href = 'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css';
  link.integrity = 'sha512-iecdLmaskl7CVkqkXNQ/ZH/XLlvWZOJyj7Yy7tcenmpD1ypASozpmT/E0iPtmFIB46ZmdtAc9eNBvH0H/ZpiBw==';
  link.crossOrigin = 'anonymous';
  link.referrerPolicy = 'no-referrer';
  document.head.appendChild(link);
};

// Mock数据用于测试
const mockShowroomData = {
  0: 'sultan',    // 展台1有斯巴鲁翼豹
  2: 'infernus',  // 展台3有兰博基尼
  4: 'elegy2',    // 展台5有宝马M3
  6: 'comet2'     // 展台7有保时捷911
};

// 初始化展台位置
const initializeShowroom = () => {
  showroomPositions.value = [
    { x: 143.3934, y: -165.1516, z: 54.8579 },
    { x: 137.5648, y: -162.5670, z: 54.8579 },
    { x: 132.1978, y: -160.9582, z: 54.8579 },
    { x: 126.6330, y: -159.0725, z: 54.8579 },
    { x: 120.8571, y: -156.7253, z: 54.8579 },
    { x: 138.4483, y: -149.6044, z: 55.0432 },
    { x: 114.6593, y: -140.0967, z: 54.7905 }
  ];
  
  // 设置mock数据
 // currentVehicles.value = mockShowroomData;
};

// 放置车辆到展台
const placeVehicle = (showroomIndex: number) => {
  const vehicleModel = vehicleInputs.value[showroomIndex];
  
  if (vehicleModel && vehicleModel.trim()) {
    playSuccess();
    // 更新本地数据
    currentVehicles.value[showroomIndex] = vehicleModel.trim();
    
    // 发送到服务器
    events.emitServer('carshop:adjustShowroom', showroomIndex, vehicleModel.trim());
    vehicleInputs.value[showroomIndex] = ''; // 清空输入框
  }
};

// 移除展台车辆
const removeVehicle = (showroomIndex: number) => {
  playButtonClick();
  // 更新本地数据
  delete currentVehicles.value[showroomIndex];
  
  // 发送到服务器
  events.emitServer('carshop:removeShowroomVehicle', showroomIndex);
};

// 关闭面板
const closePanel = () => {
  playButtonClick();
  show.value = false;
  events.emitServer('carshop:closePanel');
};

// 监听服务器更新事件
events.on('showroompanel:updateShowroom', (showroomData: any) => {
  currentVehicles.value = showroomData;
});

onMounted(async () => {
  loadFontAwesome();
  initializeShowroom();
  await Tone.start();
  
  // 播放开启音效
  setTimeout(() => {
    playButtonClick();
  }, 300);
  
 // console.log('展台管理面板已加载');
 // console.log('当前展台状态:', currentVehicles.value);
});
</script>

<style scoped>
/* CSS重置和基础样式 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

.showroom-container {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100vw;
  height: 100vh;
  overflow: hidden;
  font-family: "Inter", "PingFang SC", "Microsoft YaHei", sans-serif;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
}

.showroom-background {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.showroom-panel {
  position: relative;
  background: rgba(30, 30, 50, 0.95);
  border-radius: 20px;
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(255, 255, 255, 0.1);
  overflow: hidden;
  width: 90vw;
  max-width: 1200px;
  max-height: 85vh;
  animation: panelSlideIn 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}


.showroom-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 3vh 4vw 2vh;
  background: linear-gradient(135deg, rgba(100, 255, 218, 0.1) 0%, rgba(120, 180, 255, 0.1) 100%);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.showroom-title {
  display: flex;
  align-items: center;
  gap: 1.5vw;
  color: #ffffff;
  flex-wrap: wrap;
}

.showroom-title i {
  font-size: clamp(20px, 3vw, 28px);
  color: #64ffda;
}

.showroom-title h2 {
  font-size: clamp(18px, 2.5vw, 24px);
  font-weight: 600;
  margin: 0;
}

.status-badge {
  background: rgba(100, 255, 218, 0.2);
  border: 1px solid rgba(100, 255, 218, 0.4);
  border-radius: 15px;
  padding: 0.5vh 1.5vw;
}

.status-badge span {
  color: #64ffda;
  font-size: clamp(10px, 1.2vw, 14px);
  font-weight: 600;
}

.close-button-header {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  width: clamp(35px, 4vw, 45px);
  height: clamp(35px, 4vw, 45px);
  color: rgba(255, 255, 255, 0.8);
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-button-header:hover {
  background: rgba(255, 100, 100, 0.2);
  border-color: rgba(255, 100, 100, 0.4);
  transform: scale(1.1);
}

.showrooms-container {
  padding: 2vh 4vw;
  max-height: 60vh;
  overflow-y: auto;
}

.showrooms-list {
  display: flex;
  flex-direction: column;
  gap: 1.5vh;
}

.showroom-item {
  display: flex;
  align-items: center;
  gap: 2vw;
  padding: 2vh 2vw;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 15px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.showroom-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(100, 255, 218, 0.1), transparent);
  transition: left 0.5s ease;
}

.showroom-item:hover::before {
  left: 100%;
}

.showroom-item:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(100, 255, 218, 0.3);
  transform: translateY(-2px);
  box-shadow: 0 5px 20px rgba(100, 255, 218, 0.2);
}

.showroom-item.has-vehicle {
  background: rgba(100, 255, 218, 0.08);
  border-color: rgba(100, 255, 218, 0.2);
}

.showroom-number {
  display: flex;
  align-items: center;
  justify-content: center;
  width: clamp(40px, 5vw, 50px);
  height: clamp(40px, 5vw, 50px);
  background: rgba(100, 255, 218, 0.2);
  border: 1px solid rgba(100, 255, 218, 0.3);
  border-radius: 50%;
  color: #64ffda;
  font-size: clamp(14px, 2vw, 18px);
  font-weight: 600;
  flex-shrink: 0;
}

.showroom-info {
  flex: 1;
  color: #ffffff;
  min-width: 0;
}

.showroom-name {
  font-size: clamp(14px, 1.8vw, 18px);
  font-weight: 600;
  margin-bottom: 0.5vh;
}

.showroom-coordinates {
  font-size: clamp(10px, 1.2vw, 12px);
  color: rgba(255, 255, 255, 0.6);
  font-family: 'Courier New', monospace;
  margin-bottom: 0.5vh;
}

.current-vehicle, .empty-showroom {
  display: flex;
  align-items: center;
  gap: 0.5vw;
  font-size: clamp(12px, 1.4vw, 14px);
  font-weight: 500;
}

.current-vehicle {
  color: #64ffda;
}

.empty-showroom {
  color: rgba(255, 255, 255, 0.5);
}

.showroom-controls {
  display: flex;
  flex-direction: column;
  gap: 1vh;
  flex-shrink: 0;
  min-width: 25vw;
}

.input-group {
  display: flex;
  gap: 1vw;
  align-items: center;
}

.vehicle-input {
  flex: 1;
  padding: 1vh 1.5vw;
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.1);
  color: #ffffff;
  font-size: clamp(12px, 1.4vw, 14px);
  transition: all 0.3s ease;
}

.vehicle-input:focus {
  outline: none;
  border-color: #64ffda;
  box-shadow: 0 0 10px rgba(100, 255, 218, 0.3);
}

.vehicle-input::placeholder {
  color: rgba(255, 255, 255, 0.5);
}

.place-btn, .remove-btn {
  display: flex;
  align-items: center;
  gap: 0.5vw;
  padding: 1vh 1.5vw;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: clamp(11px, 1.3vw, 13px);
  font-weight: 500;
  transition: all 0.3s ease;
  white-space: nowrap;
}

.place-btn {
  background: rgba(100, 255, 218, 0.2);
  border: 1px solid rgba(100, 255, 218, 0.4);
  color: #64ffda;
}

.place-btn:hover:not(:disabled) {
  background: rgba(100, 255, 218, 0.3);
  transform: translateY(-1px);
}

.place-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none !important;
}

.remove-btn {
  background: rgba(255, 100, 100, 0.2);
  border: 1px solid rgba(255, 100, 100, 0.4);
  color: #ff6464;
}

.remove-btn:hover:not(:disabled) {
  background: rgba(255, 100, 100, 0.3);
  transform: translateY(-1px);
}

.remove-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none !important;
}

.showroom-footer {
  padding: 2vh 4vw 3vh;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  background: rgba(20, 20, 35, 0.5);
  display: flex;
  flex-direction: column;
  gap: 2vh;
}

.footer-stats {
  display: flex;
  align-items: center;
  justify-content: center;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 1vw;
  color: rgba(255, 255, 255, 0.7);
  font-size: clamp(11px, 1.3vw, 13px);
}

.stat-item i {
  color: #64ffda;
}

.close-button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1vw;
  width: 100%;
  padding: 1.5vh 2vw;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 10px;
  color: rgba(255, 255, 255, 0.8);
  font-size: clamp(13px, 1.5vw, 15px);
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.close-button:hover {
  background: rgba(255, 255, 255, 0.15);
  border-color: rgba(255, 255, 255, 0.3);
  transform: translateY(-1px);
}

/* 滚动条样式 */
.showrooms-container::-webkit-scrollbar {
  width: 0.6vw;
}

.showrooms-container::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
}

.showrooms-container::-webkit-scrollbar-thumb {
  background: rgba(100, 255, 218, 0.3);
  border-radius: 3px;
}

.showrooms-container::-webkit-scrollbar-thumb:hover {
  background: rgba(100, 255, 218, 0.5);
}

</style>
