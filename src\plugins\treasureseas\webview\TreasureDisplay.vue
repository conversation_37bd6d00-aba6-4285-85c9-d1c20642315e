<template>
  <div v-if="visible" class="treasure-overlay">
    <div class="treasure-container">
      <!-- 背景效果 -->
      <div class="bg-effect">
        <div class="glow-orb"></div>
        <div class="grid-lines"></div>
      </div>

      <!-- 深度信息 -->
      <div class="depth-indicator" v-if="showRewards">
        <span class="depth-value">{{ Math.abs(displayData.depth) }}m</span>
        <span class="depth-label">DEPTH</span>
      </div>

      <!-- 主内容区域 -->
      <div class="content-wrapper">
        <!-- 标题动画 -->
        <div class="title-section" v-if="showRewards">
          <div class="achievement-badge">
            <div class="badge-inner"></div>
          </div>
          <h1 class="main-title">{{ displayData.depthLevel }}发现</h1>
          <div class="subtitle">TREASURE UNLOCKED</div>
        </div>

        <!-- 奖励网格 -->
        <div class="rewards-grid" v-if="showRewards">
          <!-- 金条奖励 -->
          <div 
            v-if="displayData.goldBars > 0" 
            class="reward-card legendary"
            :style="{ animationDelay: '0.2s' }"
          >
            <div class="card-glow"></div>
            <div class="item-visual">
              <img :src="findIcon('goldbar.webp')" alt="金条" />
            </div>
            <div class="item-details">
              <h3>金条</h3>
              <span class="quantity">{{ displayData.goldBars }}</span>
            </div>
            <div class="rarity-tag">传奇</div>
          </div>

          <!-- 其他奖励 -->
          <div 
            v-for="(item, index) in displayData.treasureItems" 
            :key="index"
            class="reward-card"
            :class="getItemRarityClass(item.name)"
            :style="{ animationDelay: `${0.2 + (index + 1) * 0.1}s` }"
          >
            <div class="card-glow"></div>
            <div class="item-visual">
              <img :src="getItemImage(item.name)" :alt="item.name" />
            </div>
            <div class="item-details">
              <h3>{{ item.name }}</h3>
              <span class="quantity">{{ item.quantity }}</span>
            </div>
            <div class="rarity-tag">{{ getItemRarity(item.name).toUpperCase() }}</div>
          </div>
        </div>

        <!-- 继续按钮 -->
        <button 
          v-if="showCloseButton" 
          @click="closeTreasureDisplay" 
          class="continue-btn"
        >
          <span>继续</span>
          <svg width="20" height="20" viewBox="0 0 24 24">
            <path d="M5 12h14m-7-7l7 7-7 7" stroke="currentColor" stroke-width="2" fill="none"/>
          </svg>
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, nextTick } from 'vue'
import type { TreasureDisplayData, TreasureItem } from '../shared/types.js'
import { ITEM_RARITY_CONFIG, TREASURE_EVENTS } from '../shared/types.js'
import { findIcon } from '../../inventory/shared/findicon.js'
import { useEvents } from '../../../../webview/composables/useEvents.js'

const events = useEvents()

// 响应式数据
const visible = ref(false)
const showChestAnimation = ref(false)
const chestOpened = ref(false)
const showRewards = ref(false)
const showCloseButton = ref(false)

const displayData = reactive<TreasureDisplayData>({
  goldBars: 0,
  treasureItems: [],
  depth: 0,
  depthLevel: ''
})

// 使用共享的物品稀有度配置
const itemRarityConfig = ITEM_RARITY_CONFIG

// 获取物品图片
const getItemImage = (itemName: string): string => {
  const imageFileMap: Record<string, string> = {
    '古代金币': 'ancient_gold_coin.webp',
    '海洋珍珠': 'ocean_pearl.webp',
    '蓝宝石': 'sapphire.webp',
    '祖母绿': 'emerald.webp',
    '钻石': 'diamond.webp',
    '古代文物': 'ancient_artifact.webp',
    '沉船宝箱钥匙': 'treasure_key.webp'
  }
  const fileName = imageFileMap[itemName] || 'ancient_artifact.webp'
  return findIcon(fileName)
}

// 获取物品稀有度类名
const getItemRarityClass = (itemName: string): string => {
  return itemRarityConfig[itemName]?.class || 'common'
}

// 获取物品稀有度文本
const getItemRarity = (itemName: string): string => {
  return itemRarityConfig[itemName]?.rarity || '普通'
}

// 移除不需要的气泡效果函数

// 展示宝藏的主要动画流程
const playTreasureAnimation = async () => {
  // 简化的动画流程，更快更流畅
  setTimeout(() => {
    showRewards.value = true
  }, 300)
  
  setTimeout(() => {
    showCloseButton.value = true
  }, 1000)
}

// 关闭宝藏展示
const closeTreasureDisplay = () => {
  events.emitServer('treasureseas:closePage')
  resetDisplay()
}

// 重置显示状态
const resetDisplay = () => {
  visible.value = false
  showChestAnimation.value = false
  chestOpened.value = false
  showRewards.value = false
  showCloseButton.value = false
  
  // 重置数据
  displayData.goldBars = 0
  displayData.treasureItems = []
  displayData.depth = 0
  displayData.depthLevel = ''
}

// 显示宝藏展示数据的函数
const showTreasureDisplay = (data: TreasureDisplayData) => {
  // 更新数据
  displayData.goldBars = data.goldBars
  displayData.treasureItems = data.treasureItems
  displayData.depth = data.depth
  displayData.depthLevel = data.depthLevel
  
  // 显示界面并开始动画
  visible.value = true
  nextTick(() => {
    playTreasureAnimation()
  })
}

// 监听服务端事件
events.on(TREASURE_EVENTS.SHOW_TREASURE_DISPLAY, showTreasureDisplay)

onMounted(() => {
  // 测试数据（开发时使用）
  if (process.env.NODE_ENV === 'development') {
    setTimeout(() => {
      // 模拟接收服务端数据
      const testData: TreasureDisplayData = {
        goldBars: 3,
        treasureItems: [
          { name: '钻石', quantity: 1 },
          { name: '古代金币', quantity: 2 },
          { name: '海洋珍珠', quantity: 1 }
        ],
        depth: -150,
        depthLevel: '深层'
      }
      
      // 更新数据
      displayData.goldBars = testData.goldBars
      displayData.treasureItems = testData.treasureItems
      displayData.depth = testData.depth
      displayData.depthLevel = testData.depthLevel
      
      // 显示界面并开始动画
      visible.value = true
      nextTick(() => {
        playTreasureAnimation()
      })
    }, 2000)
  }
})
</script>

<style scoped>
/* 现代简约风格 */
* {
  box-sizing: border-box;
}

/* 主容器 */
.treasure-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: #0a0a0a;
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
  overflow: hidden;
}

.treasure-container {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 背景效果 */
.bg-effect {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 0;
}

.glow-orb {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 60vw;
  height: 60vw;
  background: radial-gradient(circle, rgba(147, 51, 234, 0.1) 0%, transparent 70%);
  transform: translate(-50%, -50%);
  animation: pulse 8s ease-in-out infinite;
}

@keyframes pulse {
  0%, 100% { transform: translate(-50%, -50%) scale(1); opacity: 0.5; }
  50% { transform: translate(-50%, -50%) scale(1.1); opacity: 0.3; }
}

.grid-lines {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: 
    linear-gradient(rgba(147, 51, 234, 0.03) 1px, transparent 1px),
    linear-gradient(90deg, rgba(147, 51, 234, 0.03) 1px, transparent 1px);
  background-size: 50px 50px;
  animation: grid-move 20s linear infinite;
}

@keyframes grid-move {
  0% { transform: translate(0, 0); }
  100% { transform: translate(50px, 50px); }
}

/* 深度指示器 */
.depth-indicator {
  position: absolute;
  top: 5vh;
  right: 5vw;
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 0.5vh;
  animation: fadeIn 0.6s ease-out;
}

.depth-value {
  font-size: 2.5rem;
  font-weight: 300;
  color: #ffffff;
  letter-spacing: -0.02em;
}

.depth-label {
  font-size: 0.75rem;
  font-weight: 600;
  color: #666;
  letter-spacing: 0.15em;
}

/* 内容包装器 */
.content-wrapper {
  position: relative;
  z-index: 10;
  width: 90%;
  max-width: 1200px;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 5vh;
}

/* 标题部分 */
.title-section {
  text-align: center;
  animation: fadeInUp 0.8s ease-out;
}

.achievement-badge {
  width: 80px;
  height: 80px;
  margin: 0 auto 3vh;
  position: relative;
}

.badge-inner {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #9333ea 0%, #6b21a8 100%);
  border-radius: 50%;
  position: relative;
  animation: rotate 20s linear infinite;
}

.badge-inner::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 60%;
  height: 60%;
  background: #0a0a0a;
  border-radius: 50%;
  transform: translate(-50%, -50%);
}

.badge-inner::after {
  content: '✦';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 2rem;
  color: #9333ea;
}

@keyframes rotate {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.main-title {
  font-size: 3.5rem;
  font-weight: 200;
  color: #ffffff;
  margin: 0 0 1vh;
  letter-spacing: -0.02em;
}

.subtitle {
  font-size: 0.875rem;
  font-weight: 600;
  color: #9333ea;
  letter-spacing: 0.3em;
  opacity: 0.8;
}

/* 奖励网格 */
.rewards-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 2rem;
  width: 100%;
  max-width: 800px;
}

/* 奖励卡片 */
.reward-card {
  position: relative;
  background: rgba(255, 255, 255, 0.02);
  border: 1px solid rgba(255, 255, 255, 0.05);
  border-radius: 16px;
  padding: 2.5rem 1.5rem;
  text-align: center;
  transition: all 0.3s ease;
  animation: cardIn 0.6s ease-out both;
}

@keyframes cardIn {
  0% {
    opacity: 0;
    transform: translateY(30px) scale(0.9);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.reward-card:hover {
  transform: translateY(-5px);
  background: rgba(255, 255, 255, 0.04);
}

.card-glow {
  position: absolute;
  top: -1px;
  left: -1px;
  right: -1px;
  bottom: -1px;
  background: linear-gradient(135deg, transparent, transparent);
  border-radius: 16px;
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: -1;
}

.reward-card.rare .card-glow {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
}

.reward-card.epic .card-glow {
  background: linear-gradient(135deg, #9333ea, #7c3aed);
}

.reward-card.legendary .card-glow {
  background: linear-gradient(135deg, #f59e0b, #d97706);
}

.reward-card:hover .card-glow {
  opacity: 0.2;
}

.item-visual {
  width: 80px;
  height: 80px;
  margin: 0 auto 1.5rem;
  position: relative;
}

.item-visual img {
  width: 100%;
  height: 100%;
  object-fit: contain;
  filter: brightness(0.9) contrast(1.1);
}

.item-details h3 {
  font-size: 1.25rem;
  font-weight: 400;
  color: #ffffff;
  margin: 0 0 0.5rem;
  letter-spacing: -0.01em;
}

.quantity {
  font-size: 2rem;
  font-weight: 300;
  color: #ffffff;
  opacity: 0.6;
}

.rarity-tag {
  position: absolute;
  top: 1rem;
  right: 1rem;
  font-size: 0.625rem;
  font-weight: 700;
  letter-spacing: 0.1em;
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  background: rgba(255, 255, 255, 0.05);
  color: #666;
}

.reward-card.rare .rarity-tag {
  background: rgba(59, 130, 246, 0.1);
  color: #3b82f6;
}

.reward-card.epic .rarity-tag {
  background: rgba(147, 51, 234, 0.1);
  color: #9333ea;
}

.reward-card.legendary .rarity-tag {
  background: rgba(245, 158, 11, 0.1);
  color: #f59e0b;
}

/* 继续按钮 */
.continue-btn {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  background: none;
  border: 1px solid rgba(255, 255, 255, 0.1);
  color: #ffffff;
  padding: 1rem 2rem;
  border-radius: 50px;
  font-size: 0.875rem;
  font-weight: 500;
  letter-spacing: 0.05em;
  cursor: pointer;
  transition: all 0.3s ease;
  animation: fadeIn 0.8s ease-out both;
  animation-delay: 0.4s;
}

.continue-btn:hover {
  background: rgba(255, 255, 255, 0.05);
  border-color: rgba(255, 255, 255, 0.2);
  transform: translateX(5px);
}

.continue-btn svg {
  transition: transform 0.3s ease;
}

.continue-btn:hover svg {
  transform: translateX(3px);
}

/* 通用动画 */
@keyframes fadeIn {
  0% { opacity: 0; }
  100% { opacity: 1; }
}

@keyframes fadeInUp {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}
</style> 