import * as alt from 'alt-server';
import { useRebar } from '@Server/index.js';

const Rebar = useRebar();

class SecurityManager {
    private static instance: SecurityManager;
    private transactionLocks = new Map<number, boolean>();
    private lastTransactionTime = new Map<number, number>();
    private transactionAttempts = new Map<number, number>();

    public static getInstance(): SecurityManager {
        if (!SecurityManager.instance) {
            SecurityManager.instance = new SecurityManager();
        }
        return SecurityManager.instance;
    }

    private constructor() {}

    public async validateTransactionSecurity(player: alt.Player, operation: string, minInterval: number): Promise<boolean> {
        if (!player || !player.valid) {
            console.error(`[SecurityManager] 无效玩家尝试进行${operation}操作`);
            return false;
        }

        const playerId = player.id;
        const currentTime = Date.now();

        if (this.transactionLocks.get(playerId)) {
            const notifyapi = await Rebar.useApi().getAsync('notify-api');
            notifyapi.shownotify(player, '交易正在处理中，请稍等...', 'error');
            console.warn(`[SecurityManager] 玩家 ${player.getStreamSyncedMeta('name')} (ID: ${playerId}) 在交易锁定期间尝试${operation}`);
            return false;
        }

        const lastTime = this.lastTransactionTime.get(playerId) || 0;
        if (currentTime - lastTime < minInterval) {
            const remaining = Math.ceil((minInterval - (currentTime - lastTime)) / 1000);
            const notifyapi = await Rebar.useApi().getAsync('notify-api');
            notifyapi.shownotify(player, `操作过于频繁，请等待 ${remaining} 秒`, 'error');
            console.warn(`[SecurityManager] 玩家 ${player.getStreamSyncedMeta('name')} (ID: ${playerId}) ${operation}操作过于频繁`);
            return false;
        }

        const attempts = this.transactionAttempts.get(playerId) || 0;
        if (attempts > 10) {
            const notifyapi = await Rebar.useApi().getAsync('notify-api');
            notifyapi.shownotify(player, '操作次数过多，请稍后再试', 'error');
            console.error(`[SecurityManager] 玩家 ${player.getStreamSyncedMeta('name')} (ID: ${playerId}) ${operation}操作尝试次数过多`);
            return false;
        }

        return true;
    }

    public setTransactionLock(playerId: number, locked: boolean): void {
        this.transactionLocks.set(playerId, locked);
        if (locked) {
            setTimeout(() => {
                this.transactionLocks.delete(playerId);
            }, 5000);
        }
    }

    public updateTransactionRecord(playerId: number): void {
        const currentTime = Date.now();
        this.lastTransactionTime.set(playerId, currentTime);
        const attempts = this.transactionAttempts.get(playerId) || 0;
        this.transactionAttempts.set(playerId, attempts + 1);

        setTimeout(() => {
            this.transactionAttempts.delete(playerId);
        }, 3600000);
    }

    public async safeCurrencyDeduction(player: alt.Player, amount: number, reason: string): Promise<boolean> {
        try {
            if (!player || !player.valid) {
                console.error(`[SecurityManager] 无效玩家尝试扣除资金: ${amount}`);
                return false;
            }

            if (typeof amount !== 'number' || amount <= 0 || amount > 1000000) {
                console.error(`[SecurityManager] 玩家 ${player.getStreamSyncedMeta('name')} 尝试扣除无效金额: ${amount}`);
                const notifyapi = await Rebar.useApi().getAsync('notify-api');
                notifyapi.shownotify(player, '交易金额无效', 'error');
                return false;
            }

            if (!reason || typeof reason !== 'string') {
                console.error(`[SecurityManager] 玩家 ${player.getStreamSyncedMeta('name')} 扣除资金缺少原因说明`);
                return false;
            }

            const currencyapi = await Rebar.useApi().getAsync('currency-api');
            const success = await currencyapi.cost({ player: player }, amount, reason);

            if (success) {
                console.log(`[SecurityManager] 玩家 ${player.getStreamSyncedMeta('name')} (ID: ${player.id}) 成功扣除 ${amount} 元 - 原因: ${reason}`);
            } else {
                console.warn(`[SecurityManager] 玩家 ${player.getStreamSyncedMeta('name')} (ID: ${player.id}) 扣除 ${amount} 元失败 - 原因: ${reason}`);
            }

            return success;
        } catch (error) {
            console.error(`[SecurityManager] 资金扣除过程中发生错误:`, error);
            const notifyapi = await Rebar.useApi().getAsync('notify-api');
            notifyapi.shownotify(player, '交易处理失败，请稍后重试', 'error');
            return false;
        }
    }

    public async safeCurrencyAddition(player: alt.Player, type: string, amount: number, reason: string): Promise<boolean> {
        try {
            if (!player || !player.valid) {
                console.error(`[SecurityManager] 无效玩家尝试增加资金: ${amount}`);
                return false;
            }

            if (typeof amount !== 'number' || amount <= 0 || amount > 1000000) {
                console.error(`[SecurityManager] 玩家 ${player.getStreamSyncedMeta('name')} 尝试增加无效金额: ${amount}`);
                const notifyapi = await Rebar.useApi().getAsync('notify-api');
                notifyapi.shownotify(player, '交易金额无效', 'error');
                return false;
            }

            if (!type || typeof type !== 'string' || !reason || typeof reason !== 'string') {
                console.error(`[SecurityManager] 玩家 ${player.getStreamSyncedMeta('name')} 增加资金参数无效`);
                return false;
            }

            const currencyapi = await Rebar.useApi().getAsync('currency-api');
            await currencyapi.add({ player: player }, type, amount);

            console.log(`[SecurityManager] 玩家 ${player.getStreamSyncedMeta('name')} (ID: ${player.id}) 成功增加 ${amount} 元${type} - 原因: ${reason}`);
            return true;
        } catch (error) {
            console.error(`[SecurityManager] 资金增加过程中发生错误:`, error);
            const notifyapi = await Rebar.useApi().getAsync('notify-api');
            notifyapi.shownotify(player, '交易处理失败，请稍后重试', 'error');
            return false;
        }
    }

    public async safeFactionFundsOperation(
        factionId: string,
        currentFunds: number,
        amount: number,
        operation: 'withdraw' | 'deposit',
        reason: string
    ): Promise<boolean> {
        try {
            if (!factionId || typeof factionId !== 'string') {
                console.error(`[SecurityManager] 无效的派系ID: ${factionId}`);
                return false;
            }

            if (typeof currentFunds !== 'number' || currentFunds < 0) {
                console.error(`[SecurityManager] 无效的当前资金: ${currentFunds}`);
                return false;
            }

            if (typeof amount !== 'number' || amount <= 0 || amount > 10000000) {
                console.error(`[SecurityManager] 无效的操作金额: ${amount}`);
                return false;
            }

            let newFunds: number;
            if (operation === 'withdraw') {
                if (currentFunds < amount) {
                    console.warn(`[SecurityManager] 派系 ${factionId} 资金不足: 当前 ${currentFunds}, 需要 ${amount}`);
                    return false;
                }
                newFunds = currentFunds - amount;
            } else {
                newFunds = currentFunds + amount;
            }

            if (newFunds < 0 || newFunds > 100000000) {
                console.error(`[SecurityManager] 派系 ${factionId} 计算后资金异常: ${newFunds}`);
                return false;
            }

            const db = Rebar.database.useDatabase();
            const updateResult = await db.update({ _id: factionId, funds: newFunds }, 'faction');

            if (updateResult) {
                console.log(`[SecurityManager] 派系 ${factionId} ${operation} ${amount} 元成功 - 原因: ${reason} - 余额: ${newFunds}`);
                return true;
            } else {
                console.error(`[SecurityManager] 派系 ${factionId} 资金更新失败`);
                return false;
            }
        } catch (error) {
            console.error(`[SecurityManager] 派系资金操作过程中发生错误:`, error);
            return false;
        }
    }
}

export const securityManager = SecurityManager.getInstance();