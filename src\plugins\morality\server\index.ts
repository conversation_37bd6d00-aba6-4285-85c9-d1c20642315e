import * as alt from 'alt-server';
import { useRebar } from '@Server/index.js';
import { Character } from '@Shared/types/character.js';
import { GlobalTimerManager } from '@Plugins/animmenu/server/interval.js';
import { PROTECTION_ITEMS, GhostEncounterResult } from '../shared/types.js';
import { MarkerType } from 'alt-server';

const Rebar = useRebar();
const db = Rebar.database.useDatabase();

// 获取物品和背包 API
const dbItemApi = await Rebar.useApi().getAsync('dbitem-api');
const inventoryApi = await Rebar.useApi().getAsync('inventory-api');
const currencyApi = await Rebar.useApi().getAsync('currency-api');
const choiceApi = await Rebar.useApi().getAsync('choice-api');
const notifyApi = await Rebar.useApi().getAsync('notify-api');

// 创建防护道具
dbItemApi.createitem({
    name: '圣水',
    desc: '神圣的水，可以抵挡一次撞鬼事件',
    type: '消耗品',
    icon: 'holywater.webp',
    weight: 0.5,
    maxStack: 10,
});

dbItemApi.createitem({
    name: '十字架',
    desc: '神圣的十字架，可以抵挡一次撞鬼事件',
    type: '消耗品',
    icon: 'cross.webp',
    weight: 0.3,
    maxStack: 10,
});

dbItemApi.createitem({
    name: '符箓',
    desc: '道教符箓，可以抵挡一次撞鬼事件',
    type: '消耗品',
    icon: 'talisman.webp',
    weight: 0.1,
    maxStack: 10,
});

// 道德值配置
const MORALITY_CONFIG = {
    DEFAULT_VALUE: 700,      // 默认道德值
    MIN_VALUE: 0,            // 最小道德值
    MAX_VALUE: 2000,         // 最大道德值

    // 道德值变化量
    DAMAGE_PENALTY: -10,     // 伤害他人减少的道德值
    KILL_PENALTY: -100,      // 击杀他人减少的道德值
    HEAL_REWARD: 20,         // 救治他人增加的道德值
    CHARITY_REWARD: 5,       // 施舍增加的道德值
};

// 道德值等级
const MORALITY_LEVELS = [
    { min: 0, max: 200, name: '极恶' },
    { min: 201, max: 400, name: '恶人' },
    { min: 401, max: 600, name: '坏人' },
    { min: 601, max: 800, name: '普通人' },
    { min: 801, max: 1200, name: '好人' },
    { min: 1201, max: 1600, name: '善人' },
    { min: 1601, max: 2000, name: '圣人' },
];

// 撞鬼机制配置
const GHOST_CONFIG = {
    // 基础撞鬼概率 (每分钟检查一次的基础概率)
    BASE_CHANCE: 0.001,          // 0.1% 基础概率

    // 道德值影响系数 (道德值越低，系数越高)
    MORALITY_MULTIPLIERS: {
        0: 15.0,      // 极恶: 15倍概率
        201: 8.0,     // 恶人: 8倍概率  
        401: 4.0,     // 坏人: 4倍概率
        601: 1.0,     // 普通人: 正常概率
        801: 0.3,     // 好人: 0.3倍概率
        1201: 0.1,    // 善人: 0.1倍概率
        1601: 0.0,    // 圣人: 不撞鬼
    },

    // 时间影响系数
    TIME_MULTIPLIERS: {
        NIGHT: 3.0,      // 夜晚 (22:00-06:00): 3倍概率
        DUSK: 2.0,       // 黄昏 (18:00-22:00): 2倍概率  
        DAWN: 1.5,       // 黎明 (06:00-08:00): 1.5倍概率
        DAY: 0.5,        // 白天 (08:00-18:00): 0.5倍概率
    },

    // 附近玩家数量影响系数 (40米范围内)
    PLAYER_COUNT_MULTIPLIERS: {
        0: 10.0,     // 独自一人: 10倍概率
        1: 5.0,      // 附近1个玩家: 5倍概率
        2: 2.0,      // 附近2个玩家: 2倍概率
        3: 1.0,      // 附近3个玩家: 正常概率
        4: 0.5,      // 附近4个玩家: 0.5倍概率
        5: 0.2,      // 附近5个玩家: 0.2倍概率
        6: 0.1,      // 附近6个或更多玩家: 0.1倍概率
    },

    // 冷却时间 (毫秒)
    COOLDOWN_TIME: 10 * 60 * 1000,  // 10分钟冷却
};

// 存储玩家上次撞鬼时间的Map
const playerGhostCooldowns = new Map<number, number>();

// 扩展 Character 类型
declare module '@Shared/types/character.js' {
    interface Character {
        morality?: number;
    }
}

/**
 * 在玩家创建角色后给予道德值属性
 */
async function giveCharacterDefaultData(player: alt.Player) {
    const doc = Rebar.document.character.useCharacter(player);
    if (!doc) {
        return;
    }

    // 设置初始道德值
    const defaultMorality: number = 700;

    doc.set('morality', defaultMorality)
}

// 玩家创建角色时触发
const charSelectApi = await Rebar.useApi().getAsync('character-creator-api');
charSelectApi.onCreate(giveCharacterDefaultData);

alt.on('rebar:playerCharacterBound', async (player: alt.Player, document: Character) => {
    if (!('morality' in document)) {
        await giveCharacterDefaultData(player);
    }
});

// 获取道德值等级
function getMoralityLevel(morality: number) {
    return MORALITY_LEVELS.find(level => morality >= level.min && morality <= level.max) || MORALITY_LEVELS[3];
}

// 道德值 API
export const MoralityAPI = {
    /**
     * 获取玩家的道德值
     */
    getMorality(player: alt.Player): number {
        const document = Rebar.document.character.useCharacter(player);
        const data = document.get();
        return data.morality ?? MORALITY_CONFIG.DEFAULT_VALUE;
    },

    /**
     * 设置玩家的道德值
     */
    async setMorality(player: alt.Player, value: number): Promise<void> {
        const document = Rebar.document.character.useCharacter(player);
        const clampedValue = Math.max(MORALITY_CONFIG.MIN_VALUE, Math.min(MORALITY_CONFIG.MAX_VALUE, value));

        await document.set('morality', clampedValue);
    },

    /**
     * 增加玩家的道德值
     */
    async addMorality(player: alt.Player, amount: number): Promise<void> {
        const currentMorality = this.getMorality(player);
        await this.setMorality(player, currentMorality + amount);
    },

    /**
     * 减少玩家的道德值
     */
    async reduceMorality(player: alt.Player, amount: number): Promise<void> {
        await this.addMorality(player, -Math.abs(amount));
    },

    /**
     * 获取道德值等级信息
     */
    getMoralityLevel(player: alt.Player) {
        const morality = this.getMorality(player);
        return getMoralityLevel(morality);
    },
};

// 监听玩家伤害事件
alt.on('weaponDamage', async (source: alt.Player, victim: alt.Player | alt.Vehicle, weaponHash: number, damage: number, offset: alt.Vector3, bodyPart: number) => {
    if (!(victim instanceof alt.Player) || !source || source === victim) return;

    // 伤害他人减少道德值
    await MoralityAPI.addMorality(source, MORALITY_CONFIG.DAMAGE_PENALTY);
});

// 监听玩家死亡事件
alt.on('playerDeath', async (victim: alt.Player, killer: alt.Player | alt.Vehicle | null, weaponHash: number) => {
    if (!(killer instanceof alt.Player) || !killer || killer === victim) return;

    // 击杀他人大幅减少道德值
    await MoralityAPI.addMorality(killer, MORALITY_CONFIG.KILL_PENALTY);
});

// 注册遇鬼命令
Rebar.messenger.useMessenger().commands.register({
    name: 'spawnGhost',
    desc: '/spawnGhost [playerId] - 生成幽灵',
    options: { permissions: ['admin'] },
    callback: (player: alt.Player, playerId?: string) => {
        let targetPlayer: alt.Player | undefined;

        // 如果指定了玩家ID，则为指定玩家生成幽灵
        if (playerId) {
            targetPlayer = alt.Player.all.find(p => p.getStreamSyncedMeta('id') === parseInt(playerId));
            if (!targetPlayer) {
                notifyApi.shownotify(player, '未找到指定ID的玩家', 'error');
                return;
            }
        } else {
            // 否则为命令执行者生成幽灵
            targetPlayer = player;
        }

        // 向目标玩家客户端发送生成幽灵事件
        alt.emitClientRaw(targetPlayer, 'spawnGhostNear');

        notifyApi.shownotify(player, `已为玩家 ${targetPlayer.name} 生成幽灵`, 'success');
    }
});

// 注册道德值查看命令
Rebar.messenger.useMessenger().commands.register({
    name: 'morality',
    desc: '/morality [playerId] - 查看道德值',
    callback: (player: alt.Player, playerId?: string) => {
        let targetPlayer: alt.Player | undefined;

        if (playerId) {
            targetPlayer = alt.Player.all.find(p => p.getStreamSyncedMeta('id') === parseInt(playerId));
            if (!targetPlayer) {
                notifyApi.shownotify(player, '未找到指定ID的玩家', 'error');
                return;
            }
        } else {
            targetPlayer = player;
        }

        const morality = MoralityAPI.getMorality(targetPlayer);
        const level = MoralityAPI.getMoralityLevel(targetPlayer);

        notifyApi.shownotify(player, `玩家: ${targetPlayer.name} | 道德值: ${morality} | 等级: ${level.name}`, 'info');
    }
});

// 注册道德值设置命令
Rebar.messenger.useMessenger().commands.register({
    name: 'setMorality',
    desc: '/setMorality <playerId> <value> - 设置道德值',
    options: { permissions: ['admin'] },
    callback: async (player: alt.Player, playerId: string, value: string) => {
        if (!playerId || !value) {
            notifyApi.shownotify(player, '用法: /setMorality <玩家ID> <道德值>', 'warning');
            return;
        }

        const targetPlayer = alt.Player.all.find(p => p.getStreamSyncedMeta('id') === parseInt(playerId));
        if (!targetPlayer) {
            notifyApi.shownotify(player, '未找到指定ID的玩家', 'error');
            return;
        }

        const moralityValue = parseInt(value);
        if (isNaN(moralityValue)) {
            notifyApi.shownotify(player, '道德值必须是数字', 'error');
            return;
        }

        await MoralityAPI.setMorality(targetPlayer, moralityValue);
        const newLevel = MoralityAPI.getMoralityLevel(targetPlayer);

        notifyApi.shownotify(player, `已设置玩家 ${targetPlayer.name} 的道德值为 ${moralityValue} (${newLevel.name})`, 'success');
    }
});

/**
 * 获取道德值对应的撞鬼概率倍数
 */
function getMoralityMultiplier(morality: number): number {
    // 如果道德值>=601（普通人及以上），直接返回0，不会遇鬼
    if (morality >= 601) {
        return 0;
    }
    
    const entries = Object.entries(GHOST_CONFIG.MORALITY_MULTIPLIERS)
        .map(([key, value]) => [parseInt(key), value])
        .sort(([a], [b]) => b - a); // 从高到低排序

    for (const [threshold, multiplier] of entries) {
        if (morality >= threshold) {
            return multiplier;
        }
    }

    return GHOST_CONFIG.MORALITY_MULTIPLIERS[0]; // 默认返回最低道德值的倍数
}

/**
 * 获取当前时间对应的撞鬼概率倍数
 */
function getTimeMultiplier(): number {
    const hour = new Date().getHours();

    if (hour >= 22 || hour < 6) {
        return GHOST_CONFIG.TIME_MULTIPLIERS.NIGHT;
    } else if (hour >= 18) {
        return GHOST_CONFIG.TIME_MULTIPLIERS.DUSK;
    } else if (hour < 8) {
        return GHOST_CONFIG.TIME_MULTIPLIERS.DAWN;
    } else {
        return GHOST_CONFIG.TIME_MULTIPLIERS.DAY;
    }
}

/**
 * 获取附近玩家数量对应的撞鬼概率倍数
 */
function getPlayerCountMultiplier(player: alt.Player): number {
    // 计算40米范围内的其他玩家数量（不包括自己）
    const nearbyPlayers = alt.Player.all.filter(p =>
        p.valid &&
        p !== player &&
        p.pos.distanceTo(player.pos) <= 40
    );
    const nearbyCount = nearbyPlayers.length;

    // 根据附近玩家数量返回对应的倍数
    if (nearbyCount === 0) return GHOST_CONFIG.PLAYER_COUNT_MULTIPLIERS[0];
    if (nearbyCount === 1) return GHOST_CONFIG.PLAYER_COUNT_MULTIPLIERS[1];
    if (nearbyCount === 2) return GHOST_CONFIG.PLAYER_COUNT_MULTIPLIERS[2];
    if (nearbyCount === 3) return GHOST_CONFIG.PLAYER_COUNT_MULTIPLIERS[3];
    if (nearbyCount === 4) return GHOST_CONFIG.PLAYER_COUNT_MULTIPLIERS[4];
    if (nearbyCount === 5) return GHOST_CONFIG.PLAYER_COUNT_MULTIPLIERS[5];

    // 6个或更多玩家
    return GHOST_CONFIG.PLAYER_COUNT_MULTIPLIERS[6];
}

/**
 * 计算玩家的撞鬼概率
 */
function calculateGhostChance(player: alt.Player): number {
    const morality = MoralityAPI.getMorality(player);
    const moralityMultiplier = getMoralityMultiplier(morality);
    const timeMultiplier = getTimeMultiplier();
    const playerCountMultiplier = getPlayerCountMultiplier(player);

    // 如果道德值太高（圣人），直接返回0
    if (moralityMultiplier === 0) {
        return 0;
    }

    const finalChance = GHOST_CONFIG.BASE_CHANCE * moralityMultiplier * timeMultiplier * playerCountMultiplier;

    return Math.min(finalChance, 0.8); // 最大概率限制为80%
}

/**
 * 检查玩家是否可以撞鬼（冷却时间检查）
 */
function canPlayerEncounterGhost(player: alt.Player): boolean {
    const playerId = player.id;
    const lastEncounter = playerGhostCooldowns.get(playerId) || 0;
    const now = Date.now();

    return (now - lastEncounter) >= GHOST_CONFIG.COOLDOWN_TIME;
}

/**
 * 检查并消耗防护道具
 */
async function checkAndConsumeProtectionItem(player: alt.Player): Promise<GhostEncounterResult> {
    // 按优先级排序检查防护道具
    const sortedItems = [...PROTECTION_ITEMS].sort((a, b) => a.priority - b.priority);

    for (const item of sortedItems) {
        const hasItem = await inventoryApi.hasitem(item.name, 1, { player });
        if (hasItem) {
            const [success] = await inventoryApi.subItem(item.name, 1, { player });
            if (success) {
                return {
                    protected: true,
                    itemUsed: item.name,
                    message: item.message
                };
            }
        }
    }

    return { protected: false };
}

/**
 * 尝试为玩家触发撞鬼事件
 */
async function tryTriggerGhostEncounter(player: alt.Player): Promise<boolean> {
    // 检查冷却时间
    if (!canPlayerEncounterGhost(player)) {
        return false;
    }

    // 计算撞鬼概率
    const chance = calculateGhostChance(player);

    // 随机判断是否触发
    const random = Math.random();
    if (random > chance) {
        return false;
    }

    // 检查防护道具
    const protection = await checkAndConsumeProtectionItem(player);
    if (protection.protected) {
        // 记录撞鬼时间（即使被防护道具阻止了，也算一次遭遇）
        playerGhostCooldowns.set(player.id, Date.now());

        // 通知玩家防护道具生效
        notifyApi.shownotify(player, protection.message!, 'warning');

        // 记录日志
        const morality = MoralityAPI.getMorality(player);
        const level = MoralityAPI.getMoralityLevel(player);
        const nearbyCount = alt.Player.all.filter(p => p.valid && p !== player && p.pos.distanceTo(player.pos) <= 40).length;
        alt.log(`[撞鬼系统] 玩家 ${player.name} 撞鬼被 ${protection.itemUsed} 阻止！概率: ${(chance * 100).toFixed(2)}%, 附近玩家: ${nearbyCount}人, 当前道德值: ${morality} (${level.name})`);

        return false; // 返回false表示没有实际触发撞鬼
    }

    // 记录撞鬼时间
    playerGhostCooldowns.set(player.id, Date.now());

    // 触发撞鬼事件（不再减少道德值）
    alt.emitClientRaw(player, 'spawnGhostNear');

    // 记录日志
    const morality = MoralityAPI.getMorality(player);
    const level = MoralityAPI.getMoralityLevel(player);
    const nearbyCount = alt.Player.all.filter(p => p.valid && p !== player && p.pos.distanceTo(player.pos) <= 40).length;
    alt.log(`[撞鬼系统] 玩家 ${player.name} 撞鬼！概率: ${(chance * 100).toFixed(2)}%, 附近玩家: ${nearbyCount}人, 当前道德值: ${morality} (${level.name})`);

    return true;
}

/**
 * 撞鬼系统主循环 - 每分钟检查一次所有在线玩家
 */
function startGhostSystem() {
    GlobalTimerManager.getInstance().addTask(async () => {
        const players = alt.Player.all.filter(p => p.valid && Rebar.document.character.useCharacter(p).get());

        for (const player of players) {
            // 异步处理每个玩家，避免阻塞
            tryTriggerGhostEncounter(player).catch(err => {
                alt.logError(`[撞鬼系统] 处理玩家 ${player.name} 时出错:`, err);
            });
        }

        // 清理过期的冷却记录 (超过1小时的记录)
        const oneHourAgo = Date.now() - 60 * 60 * 1000;
        for (const [playerId, timestamp] of playerGhostCooldowns.entries()) {
            if (timestamp < oneHourAgo) {
                playerGhostCooldowns.delete(playerId);
            }
        }

    }, 60 * 10); // 每分钟检查一次

    alt.log('[撞鬼系统] 自动撞鬼系统已启动');
}

// 监听客户端撞鬼事件（不再处理道德值惩罚）
alt.onClient('ghostEncounterFinished', async (player: alt.Player, wasJumpscare: boolean) => {
    // 只记录日志，不再减少道德值
    if (wasJumpscare) {
        alt.log(`[撞鬼系统] 玩家 ${player.name} 被鬼抓到了！`);
    } else {
        alt.log(`[撞鬼系统] 玩家 ${player.name} 撞鬼事件结束`);
    }
});

// 注册撞鬼概率查看命令
Rebar.messenger.useMessenger().commands.register({
    name: 'ghostChance',
    desc: '/ghostChance [playerId] - 查看撞鬼概率',
    callback: async (player: alt.Player, playerId?: string) => {
        let targetPlayer: alt.Player | undefined;

        if (playerId) {
            targetPlayer = alt.Player.all.find(p => p.getStreamSyncedMeta('id') === parseInt(playerId));
            if (!targetPlayer) {
                notifyApi.shownotify(player, '未找到指定ID的玩家', 'error');
                return;
            }
        } else {
            targetPlayer = player;
        }

        const morality = MoralityAPI.getMorality(targetPlayer);
        const level = MoralityAPI.getMoralityLevel(targetPlayer);
        const chance = calculateGhostChance(targetPlayer);
        const canEncounter = canPlayerEncounterGhost(targetPlayer);

        const moralityMultiplier = getMoralityMultiplier(morality);
        const timeMultiplier = getTimeMultiplier();
        const playerCountMultiplier = getPlayerCountMultiplier(targetPlayer);
        const nearbyCount = alt.Player.all.filter(p => p.valid && p !== targetPlayer && p.pos.distanceTo(targetPlayer.pos) <= 40).length;

        // 获取防护道具数量
        const protectionInfo: string[] = [];
        for (const item of PROTECTION_ITEMS) {
            const count = await inventoryApi.getitemquantity(item.name, { player: targetPlayer });
            protectionInfo.push(`${item.name}×${count}`);
        }

        const cooldownRemaining = canEncounter ? 0 :
            Math.max(0, GHOST_CONFIG.COOLDOWN_TIME - (Date.now() - (playerGhostCooldowns.get(targetPlayer.id) || 0)));

        let message = `玩家: ${targetPlayer.name}\n`;
        message += `道德值: ${morality} (${level.name})\n`;
        message += `撞鬼概率: ${(chance * 100).toFixed(3)}%\n`;
        message += `道德影响: x${moralityMultiplier}\n`;
        message += `时间影响: x${timeMultiplier}\n`;
        message += `玩家数量影响: x${playerCountMultiplier} (附近${nearbyCount}人)\n`;
        message += `防护道具: ${protectionInfo.join(', ')}\n`;
        message += `冷却状态: ${canEncounter ? '可触发' : `${Math.ceil(cooldownRemaining / 1000 / 60)}分钟后可触发`}`;

        notifyApi.shownotify(player, message, 'info');
    }
});

// 注册强制撞鬼命令（管理员）
Rebar.messenger.useMessenger().commands.register({
    name: 'forceGhost',
    desc: '/forceGhost [playerId] - 强制触发撞鬼（忽略冷却）',
    options: { permissions: ['admin'] },
    callback: async (player: alt.Player, playerId?: string) => {
        let targetPlayer: alt.Player | undefined;

        if (playerId) {
            targetPlayer = alt.Player.all.find(p => p.getStreamSyncedMeta('id') === parseInt(playerId));
            if (!targetPlayer) {
                notifyApi.shownotify(player, '未找到指定ID的玩家', 'error');
                return;
            }
        } else {
            targetPlayer = player;
        }

        // 强制设置冷却时间为0
        playerGhostCooldowns.set(targetPlayer.id, 0);

        // 尝试触发撞鬼
        const triggered = await tryTriggerGhostEncounter(targetPlayer);

        if (triggered) {
            notifyApi.shownotify(player, `已为玩家 ${targetPlayer.name} 强制触发撞鬼`, 'success');
        } else {
            notifyApi.shownotify(player, `玩家 ${targetPlayer.name} 撞鬼触发失败（可能道德值过高）`, 'warning');
        }
    }
});







const promptbarapi = await Rebar.useApi().getAsync('promptbar-api');

// 购买赎罪券的交互点
const chruchpos = new alt.Vector3(-778.8132, -2.4396, 40.4454)

Rebar.controllers.useMarkerGlobal({
    pos: chruchpos,
    color: new alt.RGBA(255, 215, 0, 150),
    scale: new alt.Vector3(1, 1, 0.2),
    type: MarkerType.MarkerCylinder,
});

const creditsinteraction2 = Rebar.controllers.useInteraction(
    new alt.ColshapeCylinder(chruchpos.x, chruchpos.y, chruchpos.z, 0.8, 3),
    'player',
);

creditsinteraction2.onEnter((player) => {
    promptbarapi.showPromptBar(player, '购买赎罪券')
});

creditsinteraction2.onLeave((player) => {
    promptbarapi.hidePromptBar(player)
});

creditsinteraction2.on(async (player) => {
    await showIndulgenceChoice(player);
});

// 显示赎罪券选择界面
async function showIndulgenceChoice(player: alt.Player): Promise<void> {
    const currentMorality = MoralityAPI.getMorality(player);
    const currentLevel = MoralityAPI.getMoralityLevel(player);
    const currentCash = await currencyApi.get({ player }, 'cash');
    
    // 检查是否已达到赎罪券上限
    if (currentMorality >= 700) {
        notifyApi.shownotify(player, '你已不需要购买赎罪券了', 'success');
        return;
    }
    
    const config = {
        title: '购买赎罪券',
        description: `现金: $${currentCash}`,
        options: [
            {
                id: 'buy1',
                text: '1张赎罪券',
                value: 1,
                icon: 'fas fa-scroll',
                color: 'primary' as const,
                disabled: currentCash < 100
            },
            {
                id: 'buy10',
                text: '10张赎罪券',
                value: 10,
                icon: 'fas fa-scrolls',
                color: 'warning' as const,
                disabled: currentCash < 1000
            },
            {
                id: 'buy100',
                text: '100张赎罪券',
                value: 100,
                icon: 'fas fa-book',
                color: 'error' as const,
                disabled: currentCash < 10000
            }
        ],
        allowCancel: true,
        cancelText: '离开',
        timeout: 30000 // 30秒超时
    };
    
    // 添加价格信息到选项文本
    config.options.forEach(option => {
        const cost = option.value * 100;
        const canAfford = currentCash >= cost;
        const maxIncrease = Math.min(option.value, 700 - currentMorality);
        const actualCost = maxIncrease * 100;
        
        if (maxIncrease < option.value) {
            option.text += ` ($${actualCost} - 只能提升${maxIncrease}点)`;
        } else {
            option.text += ` ($${cost})`;
        }
        
        if (!canAfford) {
            option.text += ' [金钱不足]';
        }
    });
    
    const result = await choiceApi.showChoice(player, config);
    
    if (result !== null) {
        await buyIndulgences(player, result);
    }
}

// 购买赎罪券的核心函数
async function buyIndulgences(player: alt.Player, amount: number): Promise<void> {
    const currentMorality = MoralityAPI.getMorality(player);
    
    // 检查是否已达到赎罪券上限
    if (currentMorality >= 700) {
        notifyApi.shownotify(player, '你已不需要购买赎罪券了', 'success');
        return;
    }
    
    const cost = amount * 100;
    const currentCash = await currencyApi.get({ player }, 'cash');
    
    // 检查金钱是否足够
    if (currentCash < cost) {
        notifyApi.shownotify(player, `现金不足，需要${cost}元`, 'error');
        return;
    }
    
    // 计算实际能提升的道德值（不能超过700）
    const actualIncrease = Math.min(amount, 700 - currentMorality);
    const actualCost = actualIncrease * 100;
    
    // 扣除金钱
    const success = await currencyApi.sub({ player }, 'cash', actualCost);
    if (!success) {
        notifyApi.shownotify(player, '扣除金钱失败', 'error');
        return;
    }
    
    // 增加道德值
    await MoralityAPI.setMorality(player, currentMorality + actualIncrease);
    
    const newLevel = MoralityAPI.getMoralityLevel(player);
    
    // 通知购买成功
    if (actualIncrease < amount) {
        notifyApi.shownotify(player, `感觉轻松了不少`, 'success');
    } else {
        notifyApi.shownotify(player, `感觉轻松了不少`, 'success');
    }
}





const chruchpos1 = new alt.Vector3(-776.8748, -2.3209, 40.0747)

Rebar.controllers.useMarkerGlobal({
    pos: chruchpos1,
    color: new alt.RGBA(255, 215, 0, 150),
    scale: new alt.Vector3(1, 1, 0.2),
    type: MarkerType.MarkerCylinder,
});

const creditsinteraction21 = Rebar.controllers.useInteraction(
    new alt.ColshapeCylinder(chruchpos1.x, chruchpos1.y, chruchpos1.z, 0.8, 3),
    'player',
);

creditsinteraction21.onEnter((player) => {
    promptbarapi.showPromptBar(player, '祈祷')
});

creditsinteraction21.onLeave((player) => {
    promptbarapi.hidePromptBar(player)
});

creditsinteraction21.on(async (player) => {
    await showProtectionShop(player);
});

// 显示防护道具商店界面
async function showProtectionShop(player: alt.Player): Promise<void> {
    const currentCash = await currencyApi.get({ player }, 'cash');
    
    const config = {
        title: '防护道具商店',
        description: `现金: $${currentCash}`,
        options: [
            {
                id: 'buy_holy_water',
                text: '圣水',
                value: '圣水',
                icon: 'fas fa-tint',
                color: 'info' as const,
                disabled: currentCash < 200
            },
            {
                id: 'buy_cross',
                text: '十字架',
                value: '十字架',
                icon: 'fas fa-cross',
                color: 'warning' as const,
                disabled: currentCash < 200
            },
            {
                id: 'buy_talisman',
                text: '符箓',
                value: '符箓',
                icon: 'fas fa-scroll',
                color: 'error' as const,
                disabled: currentCash < 200
            }
        ],
        allowCancel: true,
        cancelText: '离开',
        timeout: 30000 // 30秒超时
    };
    
    // 添加价格信息到选项文本
    config.options.forEach(option => {
        const cost = 200;
        const canAfford = currentCash >= cost;
        
        option.text += ` ($${cost})`;
        
        if (!canAfford) {
            option.text += ' [金钱不足]';
        }
    });
    
    const result = await choiceApi.showChoice(player, config);
    
    if (result !== null) {
        await buyProtectionItem(player, result);
    }
}

// 购买防护道具的核心函数
async function buyProtectionItem(player: alt.Player, itemName: string): Promise<void> {
    const cost = 200;
    const currentCash = await currencyApi.get({ player }, 'cash');
    
    // 检查金钱是否足够
    if (currentCash < cost) {
        notifyApi.shownotify(player, `现金不足，需要${cost}元`, 'error');
        return;
    }
    
    // 获取物品重量并检查背包容量
    const itemWeights = {
        '圣水': 0.5,
        '十字架': 0.3,
        '符箓': 0.1
    };
    
    const itemWeight = itemWeights[itemName] || 0.5;
    const hasCapacity = await inventoryApi.doihavemuchcapacity(player, itemWeight);
    
    if (!hasCapacity) {
        notifyApi.shownotify(player, '背包空间不足', 'error');
        return;
    }
    
    // 扣除金钱
    const success = await currencyApi.sub({ player }, 'cash', cost);
    if (!success) {
        notifyApi.shownotify(player, '扣除金钱失败', 'error');
        return;
    }
    
    // 添加物品到背包
    const addSuccess = await inventoryApi.addItem(itemName, 1, { player });
    if (!addSuccess) {
        // 如果添加失败，退还金钱
        await currencyApi.add({ player }, 'cash', cost);
        notifyApi.shownotify(player, '购买失败，已退还金钱', 'error');
        return;
    }
    
    // 通知购买成功
    notifyApi.shownotify(player, `成功购买 ${itemName}，愿它能保护你`, 'success');
}


// 启动撞鬼系统
startGhostSystem();