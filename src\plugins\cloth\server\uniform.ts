import * as alt from 'alt-server';
import { useRebar } from '@Server/index.js';
import { ModClothes } from '../shared/addclothes.js';
import { PageNames } from '@Shared/webview/index.js';

const Rebar = useRebar();
const messenger = Rebar.messenger.useMessenger();
const promptbarapi = await Rebar.useApi().getAsync('promptbar-api');
const notifyapi = await Rebar.useApi().getAsync('notify-api')
const db = Rebar.database.useDatabase();

const API_NAME = 'faction-uniforms-api';

declare global {
    export interface ServerPlugin {
        [API_NAME]: ReturnType<typeof useApi>;
    }
}

// API注册将在文件末尾进行



// 导出API供其他插件使用
function useApi() {
    // 获取派系制服配置
    async function getFactionUniformConfig(factionId: string): Promise<FactionUniformConfig | null> {
        try {
            const config = await db.get<FactionUniformConfig>({
                factionId: factionId
            }, 'faction_uniforms');
            return config || null;
        } catch (error) {
            console.error('获取派系制服配置时出错:', error);
            return null;
        }
    }

    // 保存派系制服配置
    async function saveFactionUniformConfig(config: FactionUniformConfig): Promise<boolean> {
        try {
            config.lastModified = Date.now();

            const existingConfig = await getFactionUniformConfig(config.factionId);
            if (existingConfig) {
                // 更新现有配置
                await db.update({ ...existingConfig, ...config }, 'faction_uniforms');
            } else {
                // 创建新配置
                config.createdAt = Date.now();
                await db.create(config, 'faction_uniforms');
            }
            return true;
        } catch (error) {
            console.error('保存派系制服配置时出错:', error);
            return false;
        }
    }

    // 获取指定派系的制服列表（供其他插件使用）
    async function getFactionUniforms(factionId: string, playerRank?: number): Promise<FactionUniform[]> {
        try {
            const config = await getFactionUniformConfig(factionId);
            if (!config || !config.uniforms) {
                return [];
            }

            // 如果提供了玩家等级，则过滤出可穿的制服
            if (typeof playerRank === 'number') {
                return config.uniforms.filter(uniform => {
                    const meetsMinRank = !uniform.requiredRank || playerRank >= uniform.requiredRank;
                    const meetsMaxRank = !uniform.maxRank || playerRank <= uniform.maxRank;
                    return meetsMinRank && meetsMaxRank;
                });
            }

            return config.uniforms;
        } catch (error) {
            console.error('获取派系制服列表时出错:', error);
            return [];
        }
    }

    // 获取派系制服可用的服装数据（只包含boyuniform_01）
    function getFactionUniformClothes() {
        return ModClothes.filter(cloth => cloth.dlc === 'boyuniform_01');
    }


    
    return {
        getFactionUniforms,
        getFactionUniformConfig,
        saveFactionUniformConfig,
        getFactionUniformClothes
    };
}

// 注册API
Rebar.useApi().register(API_NAME, useApi());










// 检查玩家是否是派系领导人
async function isFactionLeader(player: alt.Player): Promise<{ isLeader: boolean, factionId?: string, factionName?: string }> {
    try {
        const character = Rebar.document.character.useCharacter(player);
        const characterData = character.get();

        if (!characterData?.job?.faction) {
            return { isLeader: false };
        }

        const jobData = characterData.job;
        const factionId = jobData.faction;

        // 检查是否有dissolve_faction权限（通常只有领导人有此权限）
        const hasLeaderPermission = jobData.permissions?.includes('dissolve_faction') ||
            jobData.permissions?.includes('manage_members') ||
            jobData.rank === 0; // rank 0 是最高等级，faction leader

        if (hasLeaderPermission) {
            return {
                isLeader: true,
                factionId: factionId,
                factionName: factionId // 可以根据需要获取实际的派系名称
            };
        }

        return { isLeader: false };
    } catch (error) {
        console.error('检查派系领导人权限时出错:', error);
        return { isLeader: false };
    }
}





// 创建派系制服数据库集合
await db.createCollection('faction_uniforms');

// 派系制服商店位置
const factionUniformShopPos = new alt.Vector3(-163.7407, -310.8659, 38.7267);

// 派系制服接口定义
export interface FactionUniformComponent {
    component: number;
    drawable: number;
    texture: number;
    dlcname: string | number;
    prop: boolean;
    sex: number; // 0=女性, 1=男性
}

export interface FactionUniform {
    uid: string;
    uniformname: string;
    uniformimage?: string;
    uniformcomponents: FactionUniformComponent[];
    requiredRank?: number; // 需要的最低等级，undefined表示所有等级可穿
    maxRank?: number; // 需要的最高等级，undefined表示无上限
}

export interface FactionUniformConfig {
    _id?: string;
    factionId: string; // 派系ID
    factionName: string; // 派系名称
    uniforms: FactionUniform[];
    createdAt: number;
    lastModified: number;
    modifiedBy: string; // 最后修改者名称
}

// 添加地图标记
Rebar.controllers.useBlipGlobal({
    uid: 'faction_uniform_shop',
    text: '派系制服商店',
    sprite: 366,
    color: 26,
    scale: 0.8,
    shortRange: true,
    pos: factionUniformShopPos
});

// 添加地面标记
Rebar.controllers.useMarkerGlobal({
    uid: 'faction_uniform_shop_marker',
    type: 1,
    color: new alt.RGBA(100, 255, 218, 150),
    scale: new alt.Vector3(1.5, 1.5, 0.3),
    pos: factionUniformShopPos
});

// 创建派系制服商店互动点
const factionUniformShopInteraction = Rebar.controllers.useInteraction(
    new alt.ColshapeCylinder(factionUniformShopPos.x, factionUniformShopPos.y, factionUniformShopPos.z, 1.5, 3),
    'player'
);

// 进入提示
factionUniformShopInteraction.onEnter((player) => {
    promptbarapi.showPromptBar(player, '派系制服商店');
});

// 离开清除提示
factionUniformShopInteraction.onLeave((player) => {
    promptbarapi.hidePromptBar(player);
});

// 派系制服商店交互逻辑
factionUniformShopInteraction.on(async (player) => {
    promptbarapi.hidePromptBar(player);

    // 检查是否是派系领导人
    const leaderCheck = await isFactionLeader(player);
    if (!leaderCheck.isLeader) {
        notifyapi.shownotify(player, '只有派系领导人才能使用派系制服商店', 'error');
        return;
    }

    try {
        // 打开派系制服商店界面
        Rebar.player.useWebview(player).show('factionUniformShop', 'page', true);

        // 等待界面准备就绪
        let isReady = await Rebar.player.useWebview(player).isReady('factionUniformShop', 'page');
        while (!isReady) {
            await alt.Utils.wait(100);
            isReady = await Rebar.player.useWebview(player).isReady('factionUniformShop', 'page');
        }

        // 获取当前派系的制服配置，优先从派系数据库读取
        let currentConfig = await useApi().getFactionUniformConfig(leaderCheck.factionId!);

        // 尝试从派系数据库获取制服配置
        try {
            const factionDoc = await db.get<any>({ name: leaderCheck.factionName }, 'faction');
            if (factionDoc && factionDoc.uniforms) {
                // 如果派系数据库中有制服配置，使用它并更新独立的制服配置
                currentConfig = {
                    factionId: leaderCheck.factionId!,
                    factionName: leaderCheck.factionName!,
                    uniforms: factionDoc.uniforms,
                    createdAt: currentConfig?.createdAt || Date.now(),
                    lastModified: Date.now(),
                    modifiedBy: 'System'
                };
            }
        } catch (error) {
            console.error('从派系数据库获取制服配置时出错:', error);
        }

        // 获取玩家性别
        const character = Rebar.document.character.useCharacter(player);
        const characterData = character.get();
        const playerSex = characterData?.appearance?.sex || 1;

        // 发送数据到前端（包含玩家性别）
        Rebar.player.useWebview(player).emit('factionUniformShop:setConfig', {
            factionId: leaderCheck.factionId,
            factionName: leaderCheck.factionName,
            config: currentConfig,
            playerSex: playerSex, // 直接发送玩家性别
            availableClothes: useApi().getFactionUniformClothes() // 发送可用的制服服装数据
        });

    } catch (error) {
        console.error('打开派系制服商店时出错:', error);
        notifyapi.shownotify(player, '打开派系制服商店失败', 'error');
    }
});

// 处理获取制服列表请求（为UniformAssignment组件提供）
alt.onRpc('faction:getUniforms', async (player: alt.Player) => {
    try {
        const leaderCheck = await isFactionLeader(player);
        if (!leaderCheck.isLeader) {
            return [];
        }

        const config = await useApi().getFactionUniformConfig(leaderCheck.factionId!);
        return config?.uniforms || [];
    } catch (error) {
        console.error('获取制服列表时出错:', error);
        return [];
    }
});

// 处理更新制服分配请求
alt.onRpc('faction:updateUniformAssignment', async (player: alt.Player, updateData: {
    uniformId: string,
    requiredRank?: number,
    maxRank?: number
}) => {
    try {
        const leaderCheck = await isFactionLeader(player);
        if (!leaderCheck.isLeader) {
            notifyapi.shownotify(player, '权限不足', 'error');
            return false;
        }

        const config = await useApi().getFactionUniformConfig(leaderCheck.factionId!);
        if (!config) return false;

        const uniformIndex = config.uniforms.findIndex(u => u.uid === updateData.uniformId);
        if (uniformIndex === -1) return false;

        // 更新制服权限设置
        config.uniforms[uniformIndex].requiredRank = updateData.requiredRank;
        config.uniforms[uniformIndex].maxRank = updateData.maxRank;
        config.lastModified = Date.now();

        const success = await useApi().saveFactionUniformConfig(config);
        if (success) {
            notifyapi.shownotify(player, '制服权限更新成功', 'success');
        }
        return success;
    } catch (error) {
        console.error('更新制服分配时出错:', error);
        return false;
    }
});

// 处理删除制服请求
alt.onRpc('faction:deleteUniform', async (player: alt.Player, uniformId: string) => {
    try {
        const leaderCheck = await isFactionLeader(player);
        if (!leaderCheck.isLeader) {
            notifyapi.shownotify(player, '权限不足', 'error');
            return false;
        }

        const config = await useApi().getFactionUniformConfig(leaderCheck.factionId!);
        if (!config) return false;

        const uniformIndex = config.uniforms.findIndex(u => u.uid === uniformId);
        if (uniformIndex === -1) return false;

        // 删除制服
        config.uniforms.splice(uniformIndex, 1);
        config.lastModified = Date.now();

        const success = await useApi().saveFactionUniformConfig(config);
        if (success) {
            notifyapi.shownotify(player, '制服删除成功', 'success');
        }
        return success;
    } catch (error) {
        console.error('删除制服时出错:', error);
        return false;
    }
});


// 获取派系职位数据
alt.onRpc('factionUniformShop:getFactionJobs', async (player: alt.Player) => {
    try {
        const leaderCheck = await isFactionLeader(player);
        if (!leaderCheck.isLeader) {
            return [];
        }

        // 获取派系数据
        const factionDoc = await db.get<any>({ name: leaderCheck.factionName }, 'faction');
        if (!factionDoc || !factionDoc.jobs) {
            return [];
        }

        // 返回按rank排序的职位数据
        return factionDoc.jobs.sort((a: any, b: any) => a.rank - b.rank);
    } catch (error) {
        console.error('获取派系职位数据时出错:', error);
        return [];
    }
});

// 创建新制服
alt.onRpc('factionUniformShop:createUniform', async (player: alt.Player, uniformData: FactionUniform) => {
    try {
        const leaderCheck = await isFactionLeader(player);
        if (!leaderCheck.isLeader) {
            notifyapi.shownotify(player, '权限不足', 'error');
            return false;
        }

        // 获取现有配置
        let config = await useApi().getFactionUniformConfig(leaderCheck.factionId!);

        if (!config) {
            // 创建新配置
            const character = Rebar.document.character.useCharacter(player);
            const characterData = character.get();

            config = {
                factionId: leaderCheck.factionId!,
                factionName: leaderCheck.factionName!,
                uniforms: [],
                createdAt: Date.now(),
                lastModified: Date.now(),
                modifiedBy: characterData?.name || 'Unknown'
            };
        }

        // 检查名称是否重复
        const existingUniform = config.uniforms.find(u => u.uniformname === uniformData.uniformname);
        if (existingUniform) {
            notifyapi.shownotify(player, `制服名称 "${uniformData.uniformname}" 已存在`, 'error');
            return false;
        }

        // 添加新制服
        config.uniforms.push(uniformData);
        config.lastModified = Date.now();

        const success = await useApi().saveFactionUniformConfig(config);

        // 同时更新派系数据库中的制服字段
        if (success) {
            try {
                const factionDoc = await db.get<any>({ name: leaderCheck.factionName }, 'faction');
                if (factionDoc) {
                    await db.update({ _id: factionDoc._id, uniforms: config.uniforms }, 'faction');
                }
            } catch (error) {
                console.error('更新派系制服字段时出错:', error);
            }
        }

        if (success) {
            notifyapi.shownotify(player, `制服 "${uniformData.uniformname}" 创建成功`, 'success');
        } else {
            notifyapi.shownotify(player, '创建制服失败', 'error');
        }

        return success;
    } catch (error) {
        console.error('创建制服时出错:', error);
        notifyapi.shownotify(player, '创建制服失败', 'error');
        return false;
    }
});

// 处理保存制服配置请求
alt.onClient('factionUniformShop:saveConfig', async (player: alt.Player, configData: any) => {
    try {
        // 再次验证权限
        const leaderCheck = await isFactionLeader(player);
        if (!leaderCheck.isLeader) {
            notifyapi.shownotify(player, '权限不足', 'error');
            return;
        }

        const character = Rebar.document.character.useCharacter(player);
        const characterData = character.get();

        const config: FactionUniformConfig = {
            factionId: leaderCheck.factionId!,
            factionName: leaderCheck.factionName!,
            uniforms: configData.uniforms || [],
            createdAt: configData.createdAt || Date.now(),
            lastModified: Date.now(),
            modifiedBy: characterData?.name || 'Unknown'
        };

        const success = await useApi().saveFactionUniformConfig(config);

        // 同时更新派系数据库中的制服字段
        if (success) {
            try {
                const factionDoc = await db.get<any>({ name: leaderCheck.factionName }, 'faction');
                if (factionDoc) {
                    await db.update({ _id: factionDoc._id, uniforms: config.uniforms }, 'faction');
                }
            } catch (error) {
                console.error('更新派系制服字段时出错:', error);
            }
        }

        if (success) {
            notifyapi.shownotify(player, '派系制服配置保存成功', 'success');
            Rebar.player.useWebview(player).emit('factionUniformShop:saveResult', { success: true });
        } else {
            notifyapi.shownotify(player, '保存配置失败', 'error');
            Rebar.player.useWebview(player).emit('factionUniformShop:saveResult', { success: false });
        }

    } catch (error) {
        console.error('保存派系制服配置时出错:', error);
        notifyapi.shownotify(player, '保存配置失败', 'error');
        Rebar.player.useWebview(player).emit('factionUniformShop:saveResult', { success: false });
    }
});

// 处理删除制服请求
alt.onClient('factionUniformShop:removeUniform', async (player: alt.Player, uniformUid: string) => {
    try {
        // 验证权限
        const leaderCheck = await isFactionLeader(player);
        if (!leaderCheck.isLeader) {
            notifyapi.shownotify(player, '权限不足', 'error');
            return;
        }

        const config = await useApi().getFactionUniformConfig(leaderCheck.factionId!);
        if (!config) {
            notifyapi.shownotify(player, '未找到派系制服配置', 'error');
            return;
        }

        // 移除指定的制服
        config.uniforms = config.uniforms.filter(uniform => uniform.uid !== uniformUid);

        const success = await useApi().saveFactionUniformConfig(config);
        if (success) {
            notifyapi.shownotify(player, '制服删除成功', 'success');
            Rebar.player.useWebview(player).emit('factionUniformShop:uniformRemoved', uniformUid);
        } else {
            notifyapi.shownotify(player, '删除制服失败', 'error');
        }

    } catch (error) {
        console.error('删除制服时出错:', error);
        notifyapi.shownotify(player, '删除制服失败', 'error');
    }
});




console.log('派系制服商店系统已加载');


alt.on('rebar:playerPageOpened', (player: alt.Player, page: PageNames) => {
    if (page === 'factionUniformShop') {
        Rebar.player.useWorld(player).disableControls();
    }
});

alt.on('rebar:playerPageClosed', (player: alt.Player, page: PageNames) => {
    if (page === 'factionUniformShop') {
        Rebar.player.useWorld(player).enableControls();
    }
});


