# Choice API 使用说明

Choice API 是一个用于在游戏中显示选择对话框的插件，类似于 input API 的设计模式。

## 特性

- 🎯 **简单易用**: 支持简单的字符串数组选择
- 🎨 **丰富配置**: 支持图标、颜色、描述等高级配置
- ⏰ **超时控制**: 支持设置自动关闭时间
- 🎪 **现代界面**: 符合设计规范的美观界面
- 🔄 **Promise支持**: 使用async/await语法

## 服务端API

### 获取API

```typescript
const choiceApi = await Rebar.useApi().getAsync('choice-api');
```

### 基本用法

#### 1. 简单选择
```typescript
const result = await choiceApi.showSimpleChoice(player, '选择你的行动', [
    '前往商店',
    '返回家中',
    '探索城市'
]);

console.log('玩家选择:', result); // 输出: "前往商店" 或 "返回家中" 或 "探索城市" 或 null(取消)
```

#### 2. 高级选择
```typescript
const config: ChoiceConfig = {
    title: '购买物品',
    description: '选择你要购买的物品类型',
    options: [
        {
            id: 'weapon',
            text: '武器',
            value: 'weapon_category',
            icon: 'fas fa-gun',
            color: 'error'
        },
        {
            id: 'food',
            text: '食物',
            value: 'food_category',
            icon: 'fas fa-hamburger',
            color: 'success'
        },
        {
            id: 'vehicle',
            text: '载具',
            value: 'vehicle_category',
            icon: 'fas fa-car',
            color: 'primary'
        }
    ],
    allowCancel: true,
    cancelText: '不购买',
    timeout: 10000 // 10秒超时
};

const result = await choiceApi.showChoice(player, config);
```

## 客户端API

### 获取API

```typescript
const choiceClientApi = await Rebar.useClientApi().getAsync('choice-client-api');
```

### 客户端使用
```typescript
// 在客户端显示选择对话框
const result = await choiceClientApi.showSimpleChoice('选择操作', ['选项1', '选项2', '选项3']);
```

## 类型定义

### ChoiceOption
```typescript
interface ChoiceOption {
    id: string;                    // 选项唯一ID
    text: string;                  // 显示文本
    value: any;                    // 返回值
    icon?: string;                 // FontAwesome图标类名
    color?: 'primary' | 'success' | 'warning' | 'error' | 'info'; // 颜色主题
    disabled?: boolean;            // 是否禁用
}
```

### ChoiceConfig
```typescript
interface ChoiceConfig {
    title: string;                 // 标题
    description?: string;          // 描述文本
    options: ChoiceOption[];       // 选项数组
    allowCancel?: boolean;         // 是否允许取消
    cancelText?: string;           // 取消按钮文本
    timeout?: number;              // 超时时间（毫秒）
}
```

## 使用示例

### 1. 商店购买选择
```typescript
const shopResult = await choiceApi.showChoice(player, {
    title: '武器商店',
    description: '选择你要购买的武器',
    options: [
        { id: 'pistol', text: '手枪', value: 'weapon_pistol', icon: 'fas fa-gun', color: 'primary' },
        { id: 'rifle', text: '步枪', value: 'weapon_rifle', icon: 'fas fa-crosshairs', color: 'warning' },
        { id: 'shotgun', text: '散弹枪', value: 'weapon_shotgun', icon: 'fas fa-bullseye', color: 'error' }
    ],
    allowCancel: true,
    cancelText: '离开商店'
});

if (shopResult) {
    // 处理购买逻辑
    console.log('玩家选择购买:', shopResult);
}
```

### 2. 交通工具选择
```typescript
const vehicleResult = await choiceApi.showSimpleChoice(player, '选择交通工具', [
    '汽车',
    '摩托车',
    '自行车',
    '步行'
]);

switch (vehicleResult) {
    case '汽车':
        // 生成汽车
        break;
    case '摩托车':
        // 生成摩托车
        break;
    case '自行车':
        // 生成自行车
        break;
    case '步行':
        // 什么都不做
        break;
    default:
        // 玩家取消了选择
        break;
}
```

### 3. 带超时的选择
```typescript
const urgentChoice = await choiceApi.showChoice(player, {
    title: '紧急情况',
    description: '你有10秒时间做出选择！',
    options: [
        { id: 'run', text: '逃跑', value: 'escape', icon: 'fas fa-running', color: 'warning' },
        { id: 'fight', text: '战斗', value: 'combat', icon: 'fas fa-fist-raised', color: 'error' },
        { id: 'hide', text: '躲藏', value: 'stealth', icon: 'fas fa-eye-slash', color: 'info' }
    ],
    timeout: 10000, // 10秒超时
    allowCancel: false
});

if (urgentChoice === null) {
    // 超时处理
    console.log('玩家超时未选择');
}
```

## 测试命令

在游戏中使用命令 `/choice-demo` 来测试Choice API的功能。（需要管理员权限）

## 设计规范

- 界面遵循项目设计规范
- 使用vh/vw单位确保响应式设计
- 最大宽度80vw，最大高度90vh
- 现代化美观的界面设计
- 支持触摸和键盘操作 