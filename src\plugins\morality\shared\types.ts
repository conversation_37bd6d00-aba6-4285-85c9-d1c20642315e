/**
 * 防护道具配置
 */
export interface ProtectionItem {
    name: string;
    message: string;
    priority: number; // 优先级，数字越小优先级越高
}

/**
 * 防护道具列表
 */
export const PROTECTION_ITEMS: ProtectionItem[] = [
    { 
        name: '符箓', 
        message: '你的符箓开始自己燃烧了，保护了你免受邪灵侵扰',
        priority: 1
    },
    { 
        name: '十字架', 
        message: '你的十字架断开了，但保护了你免受邪灵侵扰',
        priority: 2
    },
    { 
        name: '圣水', 
        message: '你的圣水蒸发了，但保护了你免受邪灵侵扰',
        priority: 3
    }
];

/**
 * 道德值等级定义
 */
export interface MoralityLevel {
    min: number;
    max: number;
    name: string;
}

/**
 * 撞鬼结果
 */
export interface GhostEncounterResult {
    protected: boolean;
    itemUsed?: string;
    message?: string;
} 