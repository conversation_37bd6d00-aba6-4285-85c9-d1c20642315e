# 附件系统使用指南

## 概述

增强后的附件系统提供了强大的物体附加功能，具有以下特性：

- 🔒 **健壮性**: 完整的错误处理和验证机制
- 🔄 **重试机制**: 自动重试失败的操作
- 📝 **详细日志**: 完整的操作日志记录
- ⚡ **性能优化**: 定期清理无效对象
- 🛡️ **类型安全**: 严格的TypeScript类型检查

## 基本使用

### 服务端使用

```typescript
import { useAttachment } from '@Server/player/attachment.js';
import { BONE_INDEXES, AttachmentUtils } from '@Shared/types/attachment.js';

// 为玩家创建附件系统
const attachment = useAttachment(player);

// 添加附件
const success = attachment.add({
    uid: AttachmentUtils.generateUID('weapon'),
    model: 'w_ar_assaultrifle',
    bone: BONE_INDEXES.RIGHT_HAND,
    offset: { x: 0.1, y: 0.0, z: 0.0 },
    rot: { x: 0, y: 0, z: 0 }
});

if (success) {
    console.log('附件添加成功');
} else {
    console.log('附件添加失败');
}

// 移除附件
const removed = attachment.remove('weapon_123456789_abc123def');

// 检查是否存在附件
if (attachment.has('weapon_123456789_abc123def')) {
    console.log('附件存在');
}

// 获取所有附件
const allAttachments = attachment.getAll();
console.log(`玩家有 ${allAttachments.length} 个附件`);

// 更新附件
attachment.update('weapon_123456789_abc123def', {
    offset: { x: 0.2, y: 0.0, z: 0.0 }
});

// 清除所有附件
attachment.removeAll();
```

### 客户端

客户端代码会自动处理附件的创建、更新和销毁，无需手动干预。

## 预定义骨骼索引

```typescript
import { BONE_INDEXES } from '@Shared/types/attachment.js';

// 常用骨骼索引
BONE_INDEXES.HEAD          // 头部
BONE_INDEXES.RIGHT_HAND    // 右手
BONE_INDEXES.LEFT_HAND     // 左手
BONE_INDEXES.SPINE         // 脊柱
BONE_INDEXES.CHEST         // 胸部
BONE_INDEXES.RIGHT_FOOT    // 右脚
BONE_INDEXES.LEFT_FOOT     // 左脚
BONE_INDEXES.RIGHT_SHOULDER // 右肩
BONE_INDEXES.LEFT_SHOULDER  // 左肩
BONE_INDEXES.PELVIS        // 腰部
```

## 验证工具

```typescript
import { AttachmentValidator } from '@Shared/types/attachment.js';

// 验证附件数据
const isValid = AttachmentValidator.isValidAttachment(attachmentData);

// 验证UID
const isValidUID = AttachmentValidator.isValidUID('my_attachment_uid');

// 验证模型名称
const isValidModel = AttachmentValidator.isValidModel('w_ar_assaultrifle');

// 验证骨骼索引
const isValidBone = AttachmentValidator.isValidBone(31086);

// 验证向量
const isValidVector = AttachmentValidator.isValidVector3({ x: 0, y: 0, z: 0 });
```

## 工具函数

```typescript
import { AttachmentUtils } from '@Shared/types/attachment.js';

// 生成唯一UID
const uid = AttachmentUtils.generateUID('weapon'); // weapon_1234567890_abc123def

// 创建向量
const offset = AttachmentUtils.createVector(0.1, 0.0, 0.0);
const zeroVector = AttachmentUtils.createZeroVector();

// 复制附件
const clonedAttachment = AttachmentUtils.cloneAttachment(originalAttachment);

// 比较附件
const areEqual = AttachmentUtils.areAttachmentsEqual(attachment1, attachment2);
```

## 错误处理

系统会自动处理各种错误情况：

- 无效的玩家对象
- 无效的附件数据
- 物体生成失败
- 网络同步问题
- 内存泄漏

所有错误都会记录到日志中，便于调试。

## 性能优化

- **自动清理**: 每30秒自动清理无效的附件对象
- **重试机制**: 失败的操作会自动重试最多3次
- **超时保护**: 物体生成有5秒超时限制
- **内存管理**: 及时释放不再使用的资源

## 最佳实践

### 1. 使用有意义的UID

```typescript
// 好的做法
const uid = AttachmentUtils.generateUID('player_weapon');
const uid2 = `backpack_${player.id}_${Date.now()}`;

// 避免的做法
const uid = 'attachment1';
const uid2 = Math.random().toString();
```

### 2. 验证数据

```typescript
// 在添加附件前验证数据
if (AttachmentValidator.isValidAttachment(attachmentData)) {
    attachment.add(attachmentData);
} else {
    console.error('附件数据无效');
}
```

### 3. 错误处理

```typescript
// 检查操作结果
const success = attachment.add(attachmentData);
if (!success) {
    // 处理失败情况
    console.error('无法添加附件');
}
```

### 4. 资源清理

```typescript
// 玩家断开连接时清理附件
alt.on('real:playerDisconnect', (player) => {
    const attachment = useAttachment(player);
    if (attachment) {
        attachment.removeAll();
    }
});
```

## 故障排除

### 常见问题

1. **附件不显示**
   - 检查模型名称是否正确
   - 验证骨骼索引是否有效
   - 查看控制台日志

2. **附件位置错误**
   - 调整offset和rot值
   - 确认骨骼索引正确

3. **性能问题**
   - 限制同时附加的物体数量
   - 定期清理不需要的附件

### 调试技巧

1. 启用详细日志查看操作过程
2. 使用验证工具检查数据格式
3. 监控附件数量避免过多

## 更新日志

### v2.0.0 - 健壮性增强
- 添加完整的错误处理机制
- 实现自动重试功能
- 增强数据验证
- 添加性能优化
- 改进日志记录
- 添加工具函数和验证器 