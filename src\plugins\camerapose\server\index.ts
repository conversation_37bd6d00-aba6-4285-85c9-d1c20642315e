import * as alt from 'alt-server';
import { useRebar } from '@Server/index.js';
import { registerItemEffect } from '@Plugins/inventory/server/itemEffects.js';
import { BagItem } from '@Plugins/inventory/server/index.js';
import { GlobalTimerManager } from '@Plugins/animmenu/server/interval.js';

const Rebar = useRebar();
const notify = await Rebar.useApi().getAsync('notify-api');
const progressbarapi = await Rebar.useApi().getAsync('progressbar-api');
const inventoryapi = await Rebar.useApi().getAsync('inventory-api');
const db = Rebar.database.useDatabase();

// 存储玩家举牌数据
const playerMugshotData = new Map<number, {
    title: string;
    topText: string;
    midText: string;
    bottomText: string;
}>();

// 获取物品API
const dbItemApi = await Rebar.useApi().getAsync('dbitem-api');
dbItemApi.createitem({
    name: '文字牌',
    desc: '可以自定义内容并举牌，其他玩家也能看到',
    type: '消耗品',
    icon: 'wordcard.webp',
    weight: 1.0,
    maxStack: 1,
    effect: 'usewordcard',
});

// Vue编辑器事件处理 - 简化后的版本

// 展示木牌（从Vue界面触发）
alt.onClient('camerapose:displaySign', (player: alt.Player, signData: any) => {
    if (!player || !player.valid) return;

    // 验证文字内容
    const validatedData = {
        title: validateText(signData.title || ''),
        topText: validateText(signData.topText || ''),
        midText: validateText(signData.midText || ''),
        bottomText: validateText(signData.bottomText || '')
    };

    // 检查是否有任何内容
    const hasContent = validatedData.title || validatedData.topText ||
        validatedData.midText || validatedData.bottomText;

    if (!hasContent) {
        notify.shownotify(player, '请至少输入一些文字内容', 'error');
        return;
    }

    // 存储玩家数据
    playerMugshotData.set(player.id, validatedData);

    // 设置StreamSyncedMeta让其他玩家知道这个玩家在举牌
    player.setStreamSyncedMeta('isMugshot', true);
    player.setStreamSyncedMeta('mugshotData', validatedData);

    // 通知客户端开始显示木牌
    alt.emitClientRaw(player, 'camerapose:displayMugshot',
        validatedData.title, validatedData.topText, validatedData.midText, validatedData.bottomText);

    // 通知附近的玩家显示这个玩家的文字牌
    const nearbyPlayers = alt.Player.all.filter(p =>
        p.valid &&
        p !== player &&
        p.pos.distanceTo(player.pos) <= 50 // 50米范围内
    );

    nearbyPlayers.forEach(nearbyPlayer => {
        alt.emitClientRaw(nearbyPlayer, 'mugshot:showOtherPlayer', player.id,
            validatedData.title, validatedData.topText, validatedData.midText, validatedData.bottomText);
    });

    alt.log(`玩家 ${player.name}(${player.id}) 展示文字牌: ${validatedData.title || '(无标题)'}`);
});

// 文字验证函数
function validateText(text: string): string {
    if (!text || typeof text !== 'string') return '';

    // 移除危险字符和HTML标签
    text = text.replace(/<[^>]*>/g, '');
    text = text.replace(/[<>&"']/g, '');

    // 限制长度
    if (text.length > 30) {
        text = text.substring(0, 30);
    }

    return text.trim();
}

// 处理玩家开始举牌
alt.onClient('mugshot:playerStart', (player: alt.Player, title: string, topText: string, midText: string, bottomText: string) => {
    if (!player || !player.valid) return;

    // 验证文字内容
    const validatedData = {
        title: validateText(title),
        topText: validateText(topText),
        midText: validateText(midText),
        bottomText: validateText(bottomText)
    };

    // 存储玩家数据
    playerMugshotData.set(player.id, validatedData);

    // 设置StreamSyncedMeta让其他玩家知道这个玩家在举牌
    player.setStreamSyncedMeta('isMugshot', true);
    player.setStreamSyncedMeta('mugshotData', validatedData);

    // 通知附近的玩家显示这个玩家的文字牌
    const nearbyPlayers = alt.Player.all.filter(p =>
        p.valid &&
        p !== player &&
        p.pos.distanceTo(player.pos) <= 50 // 50米范围内
    );

    nearbyPlayers.forEach(nearbyPlayer => {
        alt.emitClientRaw(nearbyPlayer, 'mugshot:showOtherPlayer', player.id,
            validatedData.title, validatedData.topText, validatedData.midText, validatedData.bottomText);
    });

    alt.log(`玩家 ${player.name}(${player.id}) 开始举牌: ${validatedData.title}`);
});

// 处理玩家停止举牌
alt.onClient('mugshot:playerStop', (player: alt.Player) => {
    if (!player || !player.valid) return;

    // 清除数据
    playerMugshotData.delete(player.id);

    // 清除StreamSyncedMeta
    player.deleteStreamSyncedMeta('isMugshot');
    player.deleteStreamSyncedMeta('mugshotData');

    // 通知附近的玩家隐藏这个玩家的文字牌
    const nearbyPlayers = alt.Player.all.filter(p =>
        p.valid &&
        p !== player &&
        p.pos.distanceTo(player.pos) <= 50
    );

    nearbyPlayers.forEach(nearbyPlayer => {
        alt.emitClientRaw(nearbyPlayer, 'mugshot:hideOtherPlayer', player.id);
    });

    alt.log(`玩家 ${player.name}(${player.id}) 停止举牌`);
});

// 当玩家进入游戏时，检查附近是否有正在举牌的玩家
alt.on('playerConnect', (player: alt.Player) => {
    alt.setTimeout(() => {
        if (!player || !player.valid) return;

        const nearbyPlayers = alt.Player.all.filter(p =>
            p.valid &&
            p !== player &&
            p.hasStreamSyncedMeta('isMugshot') &&
            p.pos.distanceTo(player.pos) <= 50
        );

        nearbyPlayers.forEach(nearbyPlayer => {
            const mugshotData = nearbyPlayer.getStreamSyncedMeta('mugshotData') as any;
            if (mugshotData) {
                alt.emitClientRaw(player, 'mugshot:showOtherPlayer', nearbyPlayer.id,
                    mugshotData.title, mugshotData.topText, mugshotData.midText, mugshotData.bottomText);
            }
        });
    }, 5000); // 延迟5秒，确保玩家完全加载
});

// 当玩家断开连接时清理数据
alt.on('playerDisconnect', (player: alt.Player) => {
    if (playerMugshotData.has(player.id)) {
        playerMugshotData.delete(player.id);
    }
});

// 周期性检查玩家位置，更新附近玩家的可见性
GlobalTimerManager.getInstance().addTask(async () => {
    const activePlayers = alt.Player.all.filter(p => p.valid && p.hasStreamSyncedMeta('isMugshot'));

    activePlayers.forEach(mugshotPlayer => {
        const mugshotData = mugshotPlayer.getStreamSyncedMeta('mugshotData') as any;
        if (!mugshotData) return;

        const nearbyPlayers = alt.Player.all.filter(p =>
            p.valid &&
            p !== mugshotPlayer &&
            p.pos.distanceTo(mugshotPlayer.pos) <= 50
        );

        const farPlayers = alt.Player.all.filter(p =>
            p.valid &&
            p !== mugshotPlayer &&
            p.pos.distanceTo(mugshotPlayer.pos) > 50
        );

        // 对于在范围内的玩家，显示文字牌
        nearbyPlayers.forEach(player => {
            alt.emitClientRaw(player, 'mugshot:showOtherPlayer', mugshotPlayer.id,
                mugshotData.title, mugshotData.topText, mugshotData.midText, mugshotData.bottomText);
        });

        // 对于超出范围的玩家，隐藏文字牌
        farPlayers.forEach(player => {
            alt.emitClientRaw(player, 'mugshot:hideOtherPlayer', mugshotPlayer.id);
        });
    });
}, 5); // 每5秒检查一次

// 注册物品使用效果
registerItemEffect('usewordcard', async (player: alt.Player, item: BagItem) => {
    if (player.vehicle) {
        notify.shownotify(player, '在车内无法使用文字牌', 'error');
        return;
    }

    const isReady = await Rebar.player.useWebview(player).isReady('inven1', 'page')
    if (isReady) {
        Rebar.player.useWebview(player).hide('inven1')
    }

    const isCurrentlyActive = player.hasStreamSyncedMeta('isMugshot');

    if (isCurrentlyActive) {
        // 如果已经在举牌，则停止
        alt.emitClientRaw(player, 'mugshot:stop');
    } else {
        // 如果没有举牌，则触发客户端打开Vue编辑器
        alt.emitClientRaw(player, 'mugshot:start');
    }
});