<template>
  <div class="airport-overlay">
    <div class="airport-container">
      <!-- 背景效果 -->
      <div class="bg-effect">
        <div class="glow-orb"></div>
        <div class="grid-lines"></div>
      </div>

      <!-- 状态指示器 -->
      <div class="status-indicator">
        <span class="status-value">ONLINE</span>
        <span class="status-label">STATUS</span>
      </div>

      <!-- 主内容区域 -->
      <div class="content-wrapper">
        <!-- 标题部分 -->
        <div class="title-section">
          <div class="achievement-badge">
            <div class="badge-inner"></div>
          </div>
          <h1 class="main-title">机场系统</h1>
          <div class="subtitle">AIRPORT TICKETING</div>
        </div>

        <!-- 目的地网格 -->
        <div class="destinations-grid" v-if="!isLoading">
          <div 
            v-for="(destination, index) in destinations" 
            :key="index"
            class="destination-card"
            :class="{ 'selected': selectedDestination === index, 'legendary': destination.price > 300, 'epic': destination.price > 200, 'rare': destination.price <= 200 }"
            :style="{ animationDelay: `${0.1 * index}s` }"
            @click="selectDestination(index, destination)"
            @mouseenter="playHoverSound"
          >
            <div class="card-glow"></div>
            <div class="destination-visual">
              <i class="fas fa-plane"></i>
            </div>
            <div class="destination-details">
              <h3>{{ destination.name }}</h3>
              <span class="price">{{ destination.price }}</span>
            </div>
            <div class="rarity-tag">{{ getPriceCategory(destination.price) }}</div>
          </div>
        </div>

        <!-- 加载状态 -->
        <div v-if="isLoading" class="loading-grid">
          <div class="loading-card" v-for="n in 6" :key="n" :style="{ animationDelay: `${0.1 * n}s` }">
            <div class="loading-shimmer"></div>
          </div>
        </div>

        <!-- 选中详情和操作按钮 -->
        <div class="action-section" v-if="selectedDestination !== null">
          <div class="selected-info">
            <span class="selected-label">已选择目的地</span>
            <span class="selected-name">{{ destinations[selectedDestination].name }}</span>
            <span class="selected-price">${{ destinations[selectedDestination].price }}</span>
          </div>
          
          <div class="action-buttons">
            <button 
              @click="purchaseTicket" 
              class="purchase-btn"
              :disabled="isProcessing"
              @mouseenter="playHoverSound"
            >
              <span v-if="!isProcessing">购买机票</span>
              <span v-else>处理中...</span>
              <i v-if="!isProcessing" class="fas fa-plane-departure"></i>
              <i v-else class="fas fa-spinner fa-spin"></i>
            </button>
            
            <button 
              @click="closePage" 
              class="cancel-btn"
              :disabled="isProcessing"
              @mouseenter="playHoverSound"
            >
              <span>取消</span>
              <svg width="20" height="20" viewBox="0 0 24 24">
                <path d="M18 6L6 18M6 6l12 12" stroke="currentColor" stroke-width="2" fill="none"/>
              </svg>
            </button>
          </div>
        </div>

        <!-- 未选择状态的提示 -->
        <div class="empty-action" v-else>
          <div class="empty-icon">
            <i class="fas fa-hand-pointer"></i>
          </div>
          <span class="empty-text">选择目的地开始您的旅程</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, onUnmounted } from 'vue';
import * as Tone from 'tone';
import { useEvents } from '../../../../webview/composables/useEvents.js';

const events = useEvents();

// 模拟数据
const mockDestinations = [
  { name: "洛圣都国际机场", price: 150, pos: { x: -1037.2, y: -2738.3, z: 20.2 } },
  { name: "沙漠机场", price: 200, pos: { x: 1747.8, y: 3273.7, z: 41.1 } },
  { name: "军事基地机场", price: 350, pos: { x: -2353.9, y: 3249.5, z: 32.8 } },
  { name: "麦肯齐机场", price: 280, pos: { x: 2121.7, y: 4796.5, z: 41.0 } },
  { name: "佩莱托湾机场", price: 320, pos: { x: -1652.2, y: 5327.3, z: 3.3 } },
  { name: "LSIA南停机坪", price: 100, pos: { x: -1267.0, y: -3013.1, z: 13.9 } },
  { name: "市中心直升机场", price: 180, pos: { x: -724.3, y: -1444.0, z: 5.0 } },
  { name: "医院直升机场", price: 160, pos: { x: 313.5, y: -1465.1, z: 46.5 } },
  { name: "警察局直升机场", price: 140, pos: { x: 449.3, y: -981.2, z: 43.7 } },
  { name: "码头直升机场", price: 190, pos: { x: -1145.1, y: -1520.7, z: 10.6 } }
];

// 响应式数据
const destinations = ref<{ name: string, price: number, pos: any }[]>([]);
const selectedDestination = ref<number | null>(null);
const isLoading = ref(true);
const isProcessing = ref(false);

// 音效系统
const sounds = {
  // 按钮悬停音效
  hover: new Tone.Synth({
    oscillator: { type: 'sine' },
    envelope: { attack: 0.001, decay: 0.05, sustain: 0, release: 0.1 },
    volume: -28
  }).toDestination(),

  // 选择音效
  select: new Tone.PolySynth(Tone.Synth, {
    oscillator: { type: 'triangle' },
    envelope: { attack: 0.02, decay: 0.2, sustain: 0.1, release: 0.5 },
    volume: -20
  }).toDestination(),

  // 购买音效
  purchase: new Tone.PolySynth(Tone.Synth, {
    oscillator: { type: 'sine' },
    envelope: { attack: 0.01, decay: 0.1, sustain: 0.1, release: 0.8 },
    volume: -15
  }).toDestination(),

  // 按钮点击音效
  click: new Tone.Synth({
    oscillator: { type: 'sine' },
    envelope: { attack: 0.01, decay: 0.1, sustain: 0.05, release: 0.3 },
    volume: -18
  }).toDestination()
};

// 音效播放函数
const playHoverSound = () => {
  Tone.start();
  sounds.hover.triggerAttackRelease('G5', 0.05);
};

const playSelectSound = (index: number) => {
  Tone.start();
  const notes = ['C4', 'D4', 'E4', 'F4', 'G4', 'A4', 'B4', 'C5'];
  const note = notes[index % notes.length];
  sounds.select.triggerAttackRelease([note, `${note.slice(0, -1)}${parseInt(note.slice(-1)) + 1}`], 0.2);
};

const playPurchaseSound = () => {
  Tone.start();
  const now = Tone.now();
  sounds.purchase.triggerAttackRelease(['C4', 'E4', 'G4'], 0.3, now);
  sounds.purchase.triggerAttackRelease(['F4', 'A4', 'C5'], 0.4, now + 0.2);
};

const playClickSound = () => {
  Tone.start();
  sounds.click.triggerAttackRelease('C5', 0.1);
};

// 函数：加载FontAwesome
const loadFontAwesome = () => {
  const link = document.createElement('link');
  link.rel = 'stylesheet';
  link.href = 'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css';
  link.integrity = 'sha512-iecdLmaskl7CVkqkXNQ/ZH/XLlvWZOJyj7Yy7tcenmpD1ypASozpmT/E0iPtmFIB46ZmdtAc9eNBvH0H/ZpiBw==';
  link.crossOrigin = 'anonymous';
  link.referrerPolicy = 'no-referrer';
  document.head.appendChild(link);
};

// 获取价格分类
const getPriceCategory = (price: number): string => {
  if (price > 300) return 'PREMIUM'
  if (price > 200) return 'STANDARD'
  return 'ECONOMY'
}

// 选择目的地
const selectDestination = (index: number, destination: { name: string, price: number, pos: any }) => {
  if (isProcessing.value) return;

  selectedDestination.value = index;
  playSelectSound(index);
};

// 购买机票
const purchaseTicket = async () => {
  if (selectedDestination.value === null || isProcessing.value) return;

  isProcessing.value = true;
  playPurchaseSound();

  const destination = destinations.value[selectedDestination.value];

  // 延迟一下，让音效播放完
  setTimeout(() => {
    // 发送到客户端进行传送
    events.emitServer('takeplanetopoint', destination.pos, destination.price);

    // 关闭页面
    setTimeout(() => {
      closePage();
    }, 1000);
  }, 500);
};

// 关闭页面
const closePage = () => {
  if (isProcessing.value) return;

  playClickSound();
  // 页面会自动关闭，这里不需要特殊处理
  events.emitServer('airport:closePage');
};

// 监听服务器事件
events.on('airport:sync', (airportData: { name: string, price: number, pos: any }[]) => {
  destinations.value = airportData;
  isLoading.value = false;
});

// 加载模拟数据（开发测试用）
const loadMockData = () => {
  setTimeout(() => {
    destinations.value = mockDestinations;
    isLoading.value = false;
  }, 1000); // 模拟加载时间
};

onMounted(async () => {
  loadFontAwesome();
  await Tone.start();

  // 加载模拟数据（如果没有服务器数据）
  //loadMockData();

  // 播放开启音效
  setTimeout(() => {
    playClickSound();
  }, 300);
});

onUnmounted(() => {
  // 清理音效资源
  Object.values(sounds).forEach(sound => {
    if (sound.dispose) {
      sound.dispose();
    }
  });
});
</script>

<style scoped>
/* 现代极简风格 */
* {
  box-sizing: border-box;
}

/* 主容器 */
.airport-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: #0a0a0a;
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
  overflow: hidden;
}

.airport-container {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 背景效果 */
.bg-effect {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 0;
}

.glow-orb {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 60vw;
  height: 60vw;
  background: radial-gradient(circle, rgba(147, 51, 234, 0.1) 0%, transparent 70%);
  transform: translate(-50%, -50%);
  animation: pulse 8s ease-in-out infinite;
}

@keyframes pulse {
  0%, 100% { transform: translate(-50%, -50%) scale(1); opacity: 0.5; }
  50% { transform: translate(-50%, -50%) scale(1.1); opacity: 0.3; }
}

.grid-lines {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: 
    linear-gradient(rgba(147, 51, 234, 0.03) 1px, transparent 1px),
    linear-gradient(90deg, rgba(147, 51, 234, 0.03) 1px, transparent 1px);
  background-size: 50px 50px;
  animation: grid-move 20s linear infinite;
}

@keyframes grid-move {
  0% { transform: translate(0, 0); }
  100% { transform: translate(50px, 50px); }
}

/* 状态指示器 */
.status-indicator {
  position: absolute;
  top: 5vh;
  right: 5vw;
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 0.5vh;
  animation: fadeIn 0.6s ease-out;
}

.status-value {
  font-size: 2.5rem;
  font-weight: 300;
  color: #ffffff;
  letter-spacing: -0.02em;
}

.status-label {
  font-size: 0.75rem;
  font-weight: 600;
  color: #666;
  letter-spacing: 0.15em;
}

/* 内容包装器 */
.content-wrapper {
  position: relative;
  z-index: 10;
  width: 90%;
  max-width: 1200px;
  height: 90vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 3vh;
}

/* 标题部分 */
.title-section {
  text-align: center;
  animation: fadeInUp 0.8s ease-out;
  flex-shrink: 0;
}

.achievement-badge {
  width: 80px;
  height: 80px;
  margin: 0 auto 3vh;
  position: relative;
}

.badge-inner {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #9333ea 0%, #6b21a8 100%);
  border-radius: 50%;
  position: relative;
  animation: rotate 20s linear infinite;
}

.badge-inner::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 60%;
  height: 60%;
  background: #0a0a0a;
  border-radius: 50%;
  transform: translate(-50%, -50%);
}

.badge-inner::after {
  content: '✈';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 2rem;
  color: #9333ea;
}

@keyframes rotate {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.main-title {
  font-size: 3.5rem;
  font-weight: 200;
  color: #ffffff;
  margin: 0 0 1vh;
  letter-spacing: -0.02em;
}

.subtitle {
  font-size: 0.875rem;
  font-weight: 600;
  color: #9333ea;
  letter-spacing: 0.3em;
  opacity: 0.8;
}

/* 目的地网格 */
.destinations-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(15vw, 1fr));
  gap: 2rem;
  width: 100%;
  max-width: 80vw;
  max-height: 50vh;
  overflow-y: auto;
  padding: 1rem;
  
  /* 自定义滚动条样式 */
  scrollbar-width: thin;
  scrollbar-color: rgba(147, 51, 234, 0.3) transparent;
}

.destinations-grid::-webkit-scrollbar {
  width: 6px;
}

.destinations-grid::-webkit-scrollbar-track {
  background: transparent;
}

.destinations-grid::-webkit-scrollbar-thumb {
  background: rgba(147, 51, 234, 0.3);
  border-radius: 3px;
}

.destinations-grid::-webkit-scrollbar-thumb:hover {
  background: rgba(147, 51, 234, 0.5);
}

/* 目的地卡片 */
.destination-card {
  position: relative;
  background: rgba(255, 255, 255, 0.02);
  border: 1px solid rgba(255, 255, 255, 0.05);
  border-radius: 16px;
  padding: 2.5rem 1.5rem;
  text-align: center;
  transition: all 0.3s ease;
  animation: cardIn 0.6s ease-out both;
  cursor: pointer;
}

@keyframes cardIn {
  0% {
    opacity: 0;
    transform: translateY(30px) scale(0.9);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.destination-card:hover {
  transform: translateY(-5px);
  background: rgba(255, 255, 255, 0.04);
}

.destination-card.selected {
  background: rgba(147, 51, 234, 0.1);
  border-color: #9333ea;
}

.card-glow {
  position: absolute;
  top: -1px;
  left: -1px;
  right: -1px;
  bottom: -1px;
  background: linear-gradient(135deg, transparent, transparent);
  border-radius: 16px;
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: -1;
}

.destination-card.rare .card-glow {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
}

.destination-card.epic .card-glow {
  background: linear-gradient(135deg, #9333ea, #7c3aed);
}

.destination-card.legendary .card-glow {
  background: linear-gradient(135deg, #f59e0b, #d97706);
}

.destination-card:hover .card-glow {
  opacity: 0.2;
}

.destination-visual {
  width: 80px;
  height: 80px;
  margin: 0 auto 1.5rem;
  position: relative;
  background: rgba(147, 51, 234, 0.1);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #9333ea;
  font-size: 2rem;
}

.destination-details h3 {
  font-size: 1.25rem;
  font-weight: 400;
  color: #ffffff;
  margin: 0 0 0.5rem;
  letter-spacing: -0.01em;
}

.price {
  font-size: 2rem;
  font-weight: 300;
  color: #ffffff;
  opacity: 0.6;
}

.price::before {
  content: '$';
  font-size: 1rem;
  opacity: 0.8;
}

.rarity-tag {
  position: absolute;
  top: 1rem;
  right: 1rem;
  font-size: 0.625rem;
  font-weight: 700;
  letter-spacing: 0.1em;
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  background: rgba(255, 255, 255, 0.05);
  color: #666;
}

.destination-card.rare .rarity-tag {
  background: rgba(59, 130, 246, 0.1);
  color: #3b82f6;
}

.destination-card.epic .rarity-tag {
  background: rgba(147, 51, 234, 0.1);
  color: #9333ea;
}

.destination-card.legendary .rarity-tag {
  background: rgba(245, 158, 11, 0.1);
  color: #f59e0b;
}

/* 加载网格 */
.loading-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(15vw, 1fr));
  gap: 2rem;
  width: 100%;
  max-width: 80vw;
  max-height: 50vh;
  overflow-y: auto;
  padding: 1rem;
}

.loading-card {
  height: 200px;
  background: rgba(255, 255, 255, 0.02);
  border: 1px solid rgba(255, 255, 255, 0.05);
  border-radius: 16px;
  position: relative;
  overflow: hidden;
  animation: cardIn 0.6s ease-out both;
}

.loading-shimmer {
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, 
    transparent 0%, 
    rgba(255, 255, 255, 0.1) 50%, 
    transparent 100%);
  animation: shimmer 2s ease-in-out infinite;
}

@keyframes shimmer {
  0% { left: -100%; }
  100% { left: 100%; }
}

/* 操作区域 */
.action-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 3vh;
  animation: fadeInUp 0.8s ease-out;
  flex-shrink: 0;
}

.selected-info {
  text-align: center;
  display: flex;
  flex-direction: column;
  gap: 0.5vh;
}

.selected-label {
  font-size: 0.875rem;
  font-weight: 600;
  color: #666;
  letter-spacing: 0.1em;
  text-transform: uppercase;
}

.selected-name {
  font-size: 1.5rem;
  font-weight: 400;
  color: #ffffff;
  margin: 0.5vh 0;
}

.selected-price {
  font-size: 2.5rem;
  font-weight: 300;
  color: #9333ea;
}

.action-buttons {
  display: flex;
  gap: 1.5vw;
}

.purchase-btn, .cancel-btn {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 1vh 3vw;
  border-radius: 50px;
  font-size: 0.875rem;
  font-weight: 500;
  letter-spacing: 0.05em;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid;
}

.purchase-btn {
  background: rgba(147, 51, 234, 0.1);
  border-color: rgba(147, 51, 234, 0.3);
  color: #9333ea;
}

.purchase-btn:hover:not(:disabled) {
  background: rgba(147, 51, 234, 0.2);
  border-color: rgba(147, 51, 234, 0.5);
  transform: translateY(-2px);
}

.cancel-btn {
  background: none;
  border-color: rgba(255, 255, 255, 0.1);
  color: #ffffff;
}

.cancel-btn:hover:not(:disabled) {
  background: rgba(255, 255, 255, 0.05);
  border-color: rgba(255, 255, 255, 0.2);
  transform: translateY(-2px);
}

.purchase-btn:disabled, .cancel-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none !important;
}

/* 空状态 */
.empty-action {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2vh;
  animation: fadeInUp 0.8s ease-out;
  flex-shrink: 0;
}

.empty-icon {
  width: 6vh;
  height: 6vh;
  background: rgba(147, 51, 234, 0.1);
  border: 2px solid rgba(147, 51, 234, 0.3);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #9333ea;
  font-size: 2rem;
  animation: float 3s ease-in-out infinite;
}

@keyframes float {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-10px); }
}

.empty-text {
  font-size: 1.2rem;
  color: rgba(255, 255, 255, 0.6);
  text-align: center;
}

/* 通用动画 */
@keyframes fadeIn {
  0% { opacity: 0; }
  100% { opacity: 1; }
}

@keyframes fadeInUp {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>