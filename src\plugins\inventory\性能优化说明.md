# 背包系统性能优化

## 问题描述
当背包中有几百个物品时，`getinventory` 事件处理器执行时间过长（70-92ms），触发alt:V警告。

## 优化措施

### 1. 服务端优化

#### `getinventory` 事件处理器优化
- **批量获取字段**: 使用 `character.get()` 一次性获取所有字段，而不是多次调用 `getField()`
- **批量发送数据**: 使用单个 `inventory:batch-sync` 事件替代6个独立的emit调用
- **减少网络开销**: 将所有数据打包在一个对象中发送

#### `changeitemslot` 事件处理器优化  
- **直接访问**: 使用 `character.getField('inventory')` 替代 `useInventory().getInventory()`
- **优化查找算法**: 使用Map存储 `{item, index}` 对象，避免重复的 `findIndex` 调用
- **直接更新**: 使用 `character.set('inventory', inventory)` 替代 `updateInventory()` API

### 2. 客户端优化

#### 批量同步处理
- **新增事件**: `inventory:batch-sync` 处理所有数据的批量更新
- **减少响应式更新**: 一次性更新所有响应式数据，减少重复渲染
- **保留兼容性**: 保留原有的独立事件处理器作为备用

### 3. 性能改进

#### 时间复杂度优化
- **Map查找**: O(1) 替代 O(n) 的数组查找
- **减少遍历**: 避免重复遍历大数组
- **批量操作**: 减少数据库和网络调用次数

#### 内存优化
- **markRaw**: 避免Vue深度响应式处理大数组
- **缓存清理**: 及时清理不需要的缓存数据

### 4. 核心函数性能优化

#### `findnextslot` 函数优化
- **移除async**: 函数不需要异步操作，移除async提升性能
- **所有调用点更新**: 移除所有await调用

#### `addItem` 函数优化
- **容量查询优化**: 减少重复的数据库查询和getField调用
- **权重计算优化**: 对于玩家角色直接使用缓存的权重值
- **批量数据获取**: 一次性获取所需的所有字段

#### `subItem` 函数优化
- **数组删除优化**: 使用双指针技术一次性删除所有无效项，避免多次splice操作
- **时间复杂度**: 从O(n²)降低到O(n)

#### 物品数据缓存机制
- **内存缓存**: 使用Map缓存频繁查询的物品数据
- **TTL机制**: 5分钟自动过期，定时清理
- **缓存失效**: 更新/删除物品时自动清除相关缓存

### 5. 算法复杂度改进

- **查找优化**: Map查找O(1) 替代数组查找O(n)
- **删除优化**: 双指针O(n) 替代多次splice O(n²)
- **缓存优化**: 内存查找O(1) 替代数据库查询O(log n)

## 预期效果

1. **执行时间**: 从70-92ms降低到10-20ms以下
2. **网络开销**: 减少80%的网络请求数量
3. **数据库查询**: 减少50-70%的物品数据查询
4. **内存使用**: 增加少量内存换取大幅性能提升
5. **响应速度**: 背包打开和物品移动更加流畅
6. **兼容性**: 保持现有功能完全兼容

## 使用说明

优化后的代码向后兼容，无需修改其他相关代码。缓存机制自动工作，会在物品数据更新时自动失效。如果需要回退，可以简单地将服务端的 `inventory:batch-sync` 改回原来的多个独立emit调用。