# 代码质量检查规则

在每次输出代码或修改文件之前，必须进行以下低级格式错误检查：

## 必检项目

### HTML/Vue 模板检查
- [ ] 所有HTML标签都有正确的结束标签
- [ ] 标签嵌套正确，没有交叉嵌套
- [ ] 属性值正确使用引号包围
- [ ] 自闭合标签格式正确（如 `<input />` 或 `<br>`）

### JavaScript/TypeScript 检查
- [ ] 所有函数、对象、数组的大括号和小括号正确配对
- [ ] 字符串引号正确配对（单引号、双引号、模板字符串）
- [ ] 语句结尾分号使用一致
- [ ] 变量声明语法正确（const、let、var）
- [ ] 函数参数和调用参数匹配

### CSS 检查
- [ ] 所有CSS规则的大括号正确配对
- [ ] 属性值格式正确，包含必要的单位
- [ ] 选择器语法正确
- [ ] 注释格式正确 `/* */`

### Vue 特定检查
- [ ] `<template>`、`<script>`、`<style>` 标签正确闭合
- [ ] Vue 指令语法正确（v-if、v-for、@click 等）
- [ ] 组件属性绑定语法正确（:prop、v-model 等）
- [ ] script setup 语法正确

### 通用格式检查
- [ ] 缩进一致（使用空格或制表符，但保持一致）
- [ ] 行尾没有多余空格
- [ ] 文件结尾有换行符
- [ ] 特殊字符正确转义

## 检查流程

1. **代码输出前**：在每次使用 `fsWrite`、`strReplace`、`fsAppend` 等工具前，先在心中快速检查上述项目
2. **发现问题时**：立即修正，不要输出有明显格式错误的代码
3. **复杂修改时**：对于大段代码修改，分段检查，确保每个部分都格式正确

## 常见错误示例

### ❌ 错误示例
```html
<!-- 标签未闭合 -->
<div class="container"
  <p>内容</p>
</div>

<!-- script 标签分割 -->
</template><script set
up lang="ts">

<!-- CSS 大括号不匹配 -->
.class {
  color: red;
  background: blue;
```

### ✅ 正确示例
```html
<!-- 标签正确闭合 -->
<div class="container">
  <p>内容</p>
</div>

<!-- script 标签完整 -->
</template>

<script setup lang="ts">

<!-- CSS 大括号匹配 -->
.class {
  color: red;
  background: blue;
}
```

## 执行原则

- **预防胜于修复**：在输出前检查比输出后修复更高效
- **细心胜于速度**：宁可慢一点，也要确保代码质量
- **用户体验优先**：避免因格式错误影响用户的开发体验

这个规则适用于所有代码相关的操作，无论是新建文件、修改现有代码还是重构项目。