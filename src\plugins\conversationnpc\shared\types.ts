import * as alt from 'alt-server';

export interface ConversationOption {
    text: string;
    nextId?: string;
    callback?: (player: alt.Player) => void;
    endConversation?: boolean;
}

export interface Conversation {
    id: string;
    text: string;
    options: ConversationOption[];
    callback?: (player: alt.Player) => void;
}

export interface NPC {
    model: string;
    name: string;
    pos: alt.Vector3;
    rotz: number;
    conversations: Conversation[];
    ped: alt.Ped;
}

export interface TransactionRecord {
    playerId: number;
    operation: string;
    amount?: number;
    timestamp: number;
    success: boolean;
}

export interface SecurityConfig {
    DRUG_PURCHASE: number;
    WEAPON_PURCHASE: number;
    INDUSTRY_PURCHASE: number;
    GENERAL_PURCHASE: number;
    BLACK_MONEY_EXCHANGE: number;
}

export interface ItemExchangeConfig {
    name: string;
    price: number;
    description: string;
}

export interface ConversationData {
    name: string;
    text: string;
    options: {
        text: string;
        optionIndex: string;
    }[];
}