import * as alt from 'alt-server';
import { useRebar } from '@Server/index.js';
import { Character } from '@Shared/types/character.js';
import { CollectionNames } from '@Server/document/shared.js';
import { stashBlips } from '@Plugins/Stash/server/stash.js';
import { Vehicle } from '@Shared/types/index.js';
import { dbobjectdata } from '@Plugins/inventory/server/index.js';
import { GlobalTimerManager } from '@Plugins/animmenu/server/interval.js';

const Rebar = useRebar();
const db = Rebar.database.useDatabase();

// 直接声明需要的接口，防止循环引用
interface Member {
    id: number;
    name: string;
    job: JobState;
    desc?: string;
}

interface JobState {
    name: string;
    rank: number;
    permissions: string[];
    beOnDuty?: boolean;
    faction: string;
    joinDate: number;
    salary?: number;
    description?: string;
}

interface Faction {
    _id: string;
    name: string;
    description: string;
    type: string;
    members: Member[];
    perm: string;
    funds: number;
    jobs: any[];
    activityLogs: any[];
    fundsHistory: any[];
    announcements?: any[];
    jobApplications?: any[];
    resignationApplications?: any[];
    uniforms?: any[]; // 派系制服配置
}

// 定义仓库接口
interface Stash {
    _id: string;
    label: string;
    rentplayerid: number | null;
    renttime: number | null;
    canrent: boolean;
    lock: boolean;
}

// 定义房屋接口
interface House {
    _id: string;
    id: number;
    pos: alt.IVector3;
    ownerid: number | null;
    cansell: boolean;
    price: number;
    pricebegin: number;
    locked: boolean;
    rentplayerid?: number;
    name: string;
}

// 定义旅馆接口
interface Motel {
    _id: string;
    id: number;
    rentplayerid: number | null;
    renttime: number | null;
    canrent: boolean;
    locked: boolean;
    name: string;
}

// 定义短信接口
interface Message {
    _id: string;
    id: number;
    sender: string;
    receiver: string;
    content: string;
    timestamp: number;
    read?: boolean;
}

// 定义海关背景信息接口
interface EntryNote {
    _id: string;
    playerid: number;
    playername: string;
    notes: string;
    ok?: boolean;
    back: boolean;
}

/**
 * 当玩家角色信息更新时，同步更新派系成员信息
 * 主要用于处理玩家改名和其他重要属性变更
 */
alt.on('rebar:playerCharacterUpdated', async (player: alt.Player, fieldName: string, value: any) => {
    // 处理名称变更
    if (fieldName === 'name') {
        await handleNameChange(player, value);
    }
});



alt.on('rebar:character:delete', async (characterId: string) => {
    const character = await db.get<Character>({ _id: characterId }, CollectionNames.Characters);
    if (!character) return;

    try {
        // 处理派系相关数据
        if (character.job?.faction) {
            // 查找角色所在的派系
            const factionName = character.job.faction;
            const factions = await db.getMany<Faction>({ name: factionName }, 'faction');
            if (factions.length > 0) {
                const faction = factions[0];

                // 找到角色在派系成员列表中的索引
                const memberIndex = faction.members.findIndex(m => m.id === character.id);
                if (memberIndex !== -1) {
                    const memberName = faction.members[memberIndex].name;

                    // 在活动日志中记录成员被移除的信息
                    if (!faction.activityLogs) {
                        faction.activityLogs = [];
                    }

                    faction.activityLogs.push({
                        timestamp: Date.now(),
                        action: '角色删除',
                        description: `角色 ${memberName} (ID: ${character.id}) 被删除，自动从派系中移除`
                    });

                    // 如果活动日志过多，保留最新的150条
                    if (faction.activityLogs.length > 150) {
                        faction.activityLogs = faction.activityLogs.slice(-150);
                    }

                    // 从派系成员列表中移除该角色
                    faction.members.splice(memberIndex, 1);

                    // 更新数据库
                    await db.update({
                        _id: faction._id,
                        members: faction.members,
                        activityLogs: faction.activityLogs
                    }, 'faction');

                                    }
            }
        }


        try {
            // 处理玩家载具
            const playervehicles = await db.getMany<Vehicle>({ owner: characterId }, CollectionNames.Vehicles).then(vehicles => vehicles.filter(v => !v.faction));
            for (const vehicle of playervehicles) {
                vehicle.canAuction = true
                vehicle.faction = '交管局'
                await db.update(vehicle, CollectionNames.Vehicles);
            }
        }
        catch (error) {
            console.error(`Error handling stash for deleted character:`, error);
        }


        try {
            // 查找玩家租用的仓库
            const rentedStash = await db.get<Stash & { _id: string }>({ rentplayerid: character.id }, 'stash');
            if (rentedStash) {
                // 重置仓库状态
                await db.update({
                    _id: rentedStash._id,
                    rentplayerid: null,
                    renttime: null,
                    canrent: true,
                    lock: true
                }, 'stash');

                
                // 如果有仓库blip管理系统，这里可以更新blip
                // 例如：更新为可租用状态的blip
                const blip = stashBlips.get(rentedStash._id);
                if (blip) {
                    blip.update({ sprite: 473 });
                }
            }
        } catch (stashError) {
            console.error(`Error handling stash for deleted character:`, stashError);
        }
        
        // 处理房屋所有权
        try {
            // 查找玩家拥有的房屋
            const ownedHouses = await db.getMany<House>({ ownerid: character.id }, 'house');
            
            for (const house of ownedHouses) {
                // 将房屋恢复为可购买状态
                await db.update({ 
                    _id: house._id, 
                    ownerid: null,
                    cansell: true,
                    price: house.pricebegin,
                    locked: true,
                    taxDebt: 0,
                    lastTaxPaid: null,
                    purchaseTime: null
                }, 'house');
                
                                
                // 查找该位置的所有房屋，检查是否需要更新地图标记
                const housesAtSamePos = await db.getMany<House>({ pos: house.pos }, 'house');
                
                // 检查是否所有房屋都已无主
                if (housesAtSamePos.length > 0) {
                    // 如果所有房屋都无主，则更新为可购买状态
                    const allForSale = housesAtSamePos.every(h => !h.ownerid);
                    if (allForSale) {
                        // 这里本应调用updateHouseBlip，但因为跨模块关系，所以需要复制相关逻辑
                        // 由于blip管理在house模块，这里仅记录日志
                                                // 发送事件通知house模块更新地图标记
                        alt.emitRaw('house:updateBlipAfterOwnerRemoved', house);
                    }
                }
            }
            
            // 查找玩家租用的房屋
            const rentedHouses = await db.getMany<House>({ rentplayerid: character.id }, 'house');
            for (const house of rentedHouses) {
                // 重置租用状态
                await db.update({ 
                    _id: house._id, 
                    rentplayerid: null,
                    renttime: null,
                    locked: true
                }, 'house');
                
                            }
        } catch (houseError) {
            console.error(`Error handling house ownership for deleted character:`, houseError);
        }
        
        // 处理旅馆租用
        try {
            // 查找玩家租用的旅馆
            const rentedMotels = await db.getMany<Motel>({ rentplayerid: character.id }, 'motel');
            
            for (const motel of rentedMotels) {
                // 重置旅馆状态
                await db.update({ 
                    _id: motel._id, 
                    rentplayerid: null,
                    renttime: null,
                    canrent: true,
                    locked: true
                }, 'motel');
                
                            }
        } catch (motelError) {
            console.error(`Error handling motel rentals for deleted character:`, motelError);
        }

        // 处理短信记录
        try {
            // 获取角色的电话号码
            const phoneNumber = character.phone;
            if (phoneNumber) {
                // 删除该角色发送的所有短信
                const sentMessages = await db.getMany<Message>({ sender: phoneNumber }, 'messages');
                for (const message of sentMessages) {
                    await db.deleteDocument(message._id, 'messages');
                }
                
                // 删除该角色接收的所有短信
                const receivedMessages = await db.getMany<Message>({ receiver: phoneNumber }, 'messages');
                for (const message of receivedMessages) {
                    await db.deleteDocument(message._id, 'messages');
                }
                
                            }
        } catch (messageError) {
            console.error(`Error handling message history for deleted character:`, messageError);
        }

        // 处理海关背景信息
        try {
            // 查找玩家的海关背景信息
            const entryNote = await db.get<EntryNote>({ playerid: character.id }, 'entrynotes');
            if (entryNote) {
                // 删除背景信息
                await db.deleteDocument(entryNote._id, 'entrynotes');
                            }
        } catch (noteError) {
            console.error(`Error handling entry notes for deleted character:`, noteError);
        }

        //处理放置物品
        try{
        const dbobjectexits = await db.getMany<dbobjectdata>({ ownerid: character.id }, 'dbobject');
        for(const dbobject of dbobjectexits){
            const object = alt.Object.all.find(o => o.hasStreamSyncedMeta('document:objectid') && o.getStreamSyncedMeta('document:objectid') === dbobject.id);
            if(object){
                object.destroy();
            }
            await db.deleteDocument(dbobject._id, 'dbobject');
        }
        }
        catch{

        }

    } catch (error) {
        console.error(`Error handling character deletion:`, error);
    }
});

/**
 * 处理玩家名称变更，更新所有相关派系数据
 */
async function handleNameChange(player: alt.Player, newName: string) {
    try {
        const characterDoc = Rebar.document.character.useCharacter(player);
        if (!characterDoc) return;

        const character = characterDoc.get();
        const playerId = character.id;

        // 检查该玩家是否在任何派系中
        if (!character.job?.faction) return;

        const factionName = character.job.faction;
        const factions = await db.getMany<Faction>({ name: factionName }, 'faction');
        if (factions.length === 0) return;

        const faction = factions[0];

        // 更新派系中的成员名称
        const memberIndex = faction.members.findIndex(m => m.id === playerId);
        if (memberIndex === -1) return;

        faction.members[memberIndex].name = newName;

        // 更新派系的活动日志中涉及该玩家的记录
        if (faction.activityLogs) {
            const oldName = character.job.name;
            for (const log of faction.activityLogs) {
                // 简单替换日志中的玩家名称
                log.description = log.description.replace(
                    new RegExp(`${oldName} ${character.name}\\b`, 'g'),
                    `${oldName} ${newName}`
                );
            }
        }

        // 更新资金历史记录中的玩家名称
        if (faction.fundsHistory) {
            for (const record of faction.fundsHistory) {
                if (record.reason.includes(character.name)) {
                    record.reason = record.reason.replace(
                        new RegExp(`\\b${character.name}\\b`, 'g'),
                        newName
                    );
                }
            }
        }

        // 更新派系职位申请中的成员名称
        if (faction.jobApplications) {
            for (const app of faction.jobApplications) {
                if (app.memberId === playerId) {
                    app.memberName = newName;
                }
            }
        }

        // 更新离职申请中的成员名称
        if (faction.resignationApplications) {
            for (const app of faction.resignationApplications) {
                if (app.memberId === playerId) {
                    app.memberName = newName;
                }
            }
        }

        // 更新公告中的作者名称
        if (faction.announcements) {
            for (const announcement of faction.announcements) {
                if (announcement.author === character.name) {
                    announcement.author = newName;
                }
            }
        }

        // 更新数据库中的派系数据
        await db.update({
            _id: faction._id,
            members: faction.members,
            activityLogs: faction.activityLogs,
            fundsHistory: faction.fundsHistory,
            jobApplications: faction.jobApplications,
            resignationApplications: faction.resignationApplications,
            announcements: faction.announcements
        }, 'faction');

        
        // 通知同派系在线成员
        notifyFactionMembersAboutNameChange(faction, playerId, character.name, newName);

    } catch (error) {
        console.error(`Error handling name change for player:`, error);
    }
}

/**
 * 通知同派系在线成员关于名称变更的消息
 */
async function notifyFactionMembersAboutNameChange(faction: Faction, playerId: number, oldName: string, newName: string) {
    // 获取所有在线的同派系成员
    const factionPlayers = alt.Player.all.filter(p => {
        if (p.getStreamSyncedMeta('id') === playerId) return false; // 排除自己

        const playerDoc = Rebar.document.character.useCharacter(p);
        if (!playerDoc) return false;

        const playerJob = playerDoc.getField('job');
        return playerJob && playerJob.faction === faction.name;
    });

    // 通知这些玩家
    const notifyApi = await Rebar.useApi().getAsync('notify-api');
    for (const player of factionPlayers) {
        notifyApi.shownotify(player, `派系成员 ${oldName} 已更名为 ${newName}`, 'info');

        // 刷新派系数据
        Rebar.player.useWebview(player).emit('updateFactionData', await getFactionData(player));
    }
}

/**
 * 获取派系数据
 */
async function getFactionData(player: alt.Player) {
    const doc = Rebar.document.character.useCharacter(player);
    if (!doc) return null;

    const factions = await db.getMany<Faction>({ name: doc.getField('job').faction }, 'faction');
    if (factions.length === 0) return null;

    // 这里可以添加额外的数据处理逻辑
    return factions[0];
}

/**
 * 优化数据库系统，定期清理过时数据
 */
export async function optimizeFactionDatabase() {
    try {
        const factions = await db.getMany<Faction>({}, 'faction');

        for (const faction of factions) {
            let isModified = false;

            // 清理过旧的活动日志（保留最近150条）
            if (faction.activityLogs && faction.activityLogs.length > 150) {
                faction.activityLogs = faction.activityLogs.slice(-150);
                isModified = true;
            }

            // 清理过旧的资金历史记录（保留最近100条）
            if (faction.fundsHistory && faction.fundsHistory.length > 100) {
                faction.fundsHistory = faction.fundsHistory.slice(-100);
                isModified = true;
            }

            // 同步成员信息，确保所有成员都存在对应的Character记录
            if (faction.members && faction.members.length > 0) {
                const validMembers: Member[] = [];

                for (const member of faction.members) {
                    // 检查成员是否存在对应的Character，使用get方法代替exists
                    const character = await db.get({ id: member.id }, CollectionNames.Characters);
                    if (character) {
                        validMembers.push(member);
                    } else {
                        // 记录无效成员被移除
                                                isModified = true;
                    }
                }

                faction.members = validMembers;
            }

            // 如果有修改，更新数据库
            if (isModified) {
                await db.update({
                    _id: faction._id,
                    activityLogs: faction.activityLogs,
                    fundsHistory: faction.fundsHistory,
                    members: faction.members
                }, 'faction');

                            }
        }
    } catch (error) {
        console.error(`Error optimizing faction database:`, error);
    }
}

// 每天执行一次数据库优化
GlobalTimerManager.getInstance().addTask('faction:optimizeFactionDatabase', optimizeFactionDatabase, 24 * 60 * 60);

// 服务器启动时执行一次数据库优化
alt.on('rebar:ready', () => {
    setTimeout(optimizeFactionDatabase, 60 * 1000); // 服务器启动1分钟后执行
});