import { useRebarClient } from '@Client/index.js';
import { drawText2D } from '@Client/screen/textlabel.js';
import { Events } from '@Shared/events/index.js';



import * as alt from 'alt-client';
import * as native from 'natives';

const Rebar = useRebarClient();
const webview = Rebar.webview.useWebview();

/* ─── 配置 ─────────────────────────────────────────── */
const GHOST_MODEL = 'u_m_y_zombie_01';

const MIN_DIST = 5;        // 幽灵刷距玩家最小距离
const MAX_DIST = 15;       // 幽灵刷距玩家最大距离

const OFFSCREEN_FRAMES = 15;  // 幽灵完全离屏后计数帧数
const CLOSE_DISTANCE = 2.0;   // 幽灵接近玩家多少距离时消失
const DODGE_TIME = 10000;     // 躲闪成功需要维持的时间（毫秒）
const TELEPORT_INTERVAL = 500; // 瞬移间隔（毫秒）
const TELEPORT_DISTANCE = 2.0; // 每次瞬移的距离
/* ─────────────────────────────────────────────────── */

let ghost: number | null = null;
let tickHandle: number | null = null;
let stepTimer: number | null = null;
let dodgeTimer: number | null = null;  // 躲闪计时器
let teleportTimer: number | null = null; // 瞬移计时器
let seenOnce = false;
let offScreenFrames = 0;
let isMoving = false;
let isDodging = false;        // 是否正在躲闪模式
let dodgeStartTime = 0;       // 躲闪开始时间

let alreadyMove = false;

let metghost = false;


export let isInVehicle = false;

alt.everyTick(() => {
    if (alt.Player.local.vehicle) {
        isInVehicle = true;
    } else {
        isInVehicle = false;
    }
});


let hasevent = false;

/* ─── 事件入口 ─────────────────────────────────────── */
alt.onServer('spawnGhostNear', async () => {
    if (hasevent) return;

    if (isInVehicle) {
        hasevent = true;

        alt.log('[幽灵] 玩家在车上，等待下车后触发');

        // 等待玩家下车
        const waitForExitVehicle = () => {
            if (!isInVehicle) {
                alt.log('[幽灵] 玩家已下车，开始生成幽灵');
                trySpawnGhost();
                hasevent = false;
                return;
            }

            // 每500ms检查一次
            alt.setTimeout(waitForExitVehicle, 500);
        };

        waitForExitVehicle();
        return;
    }

    alt.log('[幽灵] 收到生成幽灵事件');
    await trySpawnGhost();
});
/* ─────────────────────────────────────────────────── */

/**
 * 尝试生成幽灵，如果找不到安全位置就重试
 */
async function trySpawnGhost() {
    let attempts = 0;
    const maxAttempts = 20; // 最大尝试次数

    const trySpawn = async () => {
        attempts++;
        alt.log(`[幽灵] 第${attempts}次尝试生成幽灵`);

        const pos = getSafePosNearPlayer();

        // 检查位置是否有效
        if (pos && isValidPosition(pos)) {
            alt.log(`[幽灵] 找到有效位置: ${pos.x}, ${pos.y}, ${pos.z}`);
            await spawnGhost(pos);
            return;
        }

        if (attempts >= maxAttempts) {
            alt.log('[幽灵] 超过最大尝试次数，生成失败');
            return;
        }

        // 等待500ms后重试
        alt.setTimeout(() => {
            trySpawn();
        }, 500);
    };

    await trySpawn();
}

/**
 * 检查位置是否有效
 */
function isValidPosition(pos: alt.Vector3): boolean {
    // 检查坐标是否有效
    if (!pos || pos.x === 0 && pos.y === 0) {
        return false;
    }

    // 检查是否与玩家位置有足够距离
    const player = alt.Player.local;
    const distance = Math.sqrt(
        Math.pow(pos.x - player.pos.x, 2) +
        Math.pow(pos.y - player.pos.y, 2)
    );

    return distance >= MIN_DIST && distance <= MAX_DIST;
}

/**
 * 在玩家背后 ±30°、5–15 m 取安全落点
 * 如果是第二次遇鬼，则在正前方生成
 */
function getSafePosNearPlayer(): alt.Vector3 | null {
    const player = alt.Player.local;
    const origin = player.pos;
    const camRotZ = native.getGameplayCamRot(2).z;

    // 第二次遇鬼时在正前方生成
    if (metghost) {
        alt.log('[幽灵] 第二次遇鬼，在玩家正前方生成');
        const frontRad = camRotZ * Math.PI / 180;
        const dist = MAX_DIST; // 使用最大距离
        const x = origin.x + Math.cos(frontRad) * dist;
        const y = origin.y + Math.sin(frontRad) * dist;

        // 使用玩家当前高度
        return new alt.Vector3(x, y, origin.z);
    }

    // 第一次遇鬼，在背后生成
    const camBackRad = (camRotZ + 180) * Math.PI / 180;
    for (let i = 0; i < 10; i++) {
        const ang = camBackRad + (Math.random() - 0.5) * (Math.PI / 3);
        const dist = MIN_DIST + Math.random() * (MAX_DIST - MIN_DIST);
        const x = origin.x + Math.cos(ang) * dist;
        const y = origin.y + Math.sin(ang) * dist;

        // 使用玩家当前高度
        return new alt.Vector3(x, y, origin.z);
    }

    return null; // 找不到有效位置
}

/**
 * 加载模型并生成幽灵
 */
async function spawnGhost(pos: alt.Vector3) {
    cleanupGhost();

    alt.log(`[幽灵] 开始加载模型: ${GHOST_MODEL}`);
    const modelHash = native.getHashKey(GHOST_MODEL);
    await alt.loadModel(modelHash);
    alt.log('[幽灵] 模型加载完成');

    ghost = native.createPed(
        4,
        modelHash,
        pos.x, pos.y, pos.z - 1,
        0,
        false,
        true,
    );

    alt.log(`[幽灵] 幽灵实体ID: ${ghost}`);
    alt.log(`[幽灵] 幽灵生成位置: ${pos.x}, ${pos.y}, ${pos.z}`);
    alt.log(`[幽灵] 当前模式: ${metghost ? '第二次遇鬼(jumpscare)' : '第一次遇鬼'}`);

    // 屏蔽所有默认行为
    native.setEntityInvincible(ghost, true);                    // 无敌
    native.setBlockingOfNonTemporaryEvents(ghost, true);        // 屏蔽默认事件响应
    native.setPedFleeAttributes(ghost, 0, false);               // 不会逃跑
    native.setPedCombatAttributes(ghost, 17, true);             // 禁用战斗反应
    native.setPedConfigFlag(ghost, 281, true);                  // 禁用害怕反应
    native.setPedConfigFlag(ghost, 208, true);                  // 禁用受惊反应

    // 设置无碰撞但保持物理
    //  native.setEntityCollision(ghost, false, false);            // 无碰撞
    // native.setEntityCompletelyDisableCollision(ghost, false, false); // 不完全禁用碰撞（保持地面检测）
    // native.freezeEntityPosition(ghost, false);                 // 确保不被冻结
    // native.setEntityDynamic(ghost, true);                // 保持动态物理

    seenOnce = false;
    offScreenFrames = 0;
    isMoving = false;

    // 检查幽灵是否成功创建
    if (!ghost || !native.doesEntityExist(ghost)) {
        alt.log('[幽灵] 错误：幽灵创建失败');
        return;
    }

    // 确保幽灵可见
    native.setEntityVisible(ghost, true, false);
    native.setEntityAlpha(ghost, 255, false);

    tickHandle = alt.everyTick(tickGhost);
    alt.log('[幽灵] 开始主循环');
}


/**
 * 主循环：判断视野、控制移动、控制消失
 */
function tickGhost() {
    if (!ghost || !native.doesEntityExist(ghost)) {
        cleanupGhost();
        return;
    }

    const player = alt.Player.local;
    const onScreen = native.isEntityOnScreen(ghost);
    const ghostPos = native.getEntityCoords(ghost, false);
    const playerPos = player.pos;
    const distance = Math.sqrt(
        Math.pow(ghostPos.x - playerPos.x, 2) +
        Math.pow(ghostPos.y - playerPos.y, 2) +
        Math.pow(ghostPos.z - playerPos.z, 2)
    );

    // 调试信息
    if (Math.random() < 0.01) { // 1%概率打印调试信息，避免刷屏
        alt.log(`[幽灵] 调试 - 在屏幕上: ${onScreen}, 距离: ${distance.toFixed(2)}, 已看到: ${seenOnce}`);
    }

    // 第一次进入视野：开始移动
    if (onScreen && !seenOnce) {
        seenOnce = true;
        isMoving = true;
        alt.log('[幽灵] 玩家第一次看到幽灵，开始向玩家移动');

        // 第二次遇鬼时的jumpscare效果
        if (metghost) {
            alt.log('[幽灵] 第二次遇鬼，触发jumpscare效果');

            // 强制切换到第一人称视角
            native.setFollowPedCamViewMode(4); // 第一人称

            // 等待一帧让视角切换完成，然后对准鬼
            alt.nextTick(() => {
                lookAtGhost();
            });

            // 禁止玩家进行任何操作
            Rebar.player.useControls().setControls(false);

            // 播放ghostrun音效
            webview.emit(Events.player.audio.play.local, 'sounds/ghostrun.ogg', 0.8);

            alt.log('[幽灵] jumpscare效果已激活');
        } else {
            // 第一次遇鬼：启动躲闪机制
            isDodging = true;
            dodgeStartTime = Date.now();
            alt.log('[幽灵] 第一次遇鬼：启动躲闪机制，玩家需要盯着鬼逃跑10秒');

            webview.emit(Events.player.audio.play.local, 'sounds/ghostrun.ogg', 0.8);

            // 启动躲闪计时器
            dodgeTimer = alt.setTimeout(() => {
                if (isDodging) {
                    dodgeSuccess();
                }
            }, DODGE_TIME);
        }
    }

    if (seenOnce) {
        // 让幽灵始终面向玩家
        if (ghost && native.doesEntityExist(ghost)) {
            const player = alt.Player.local;
            const playerPos = player.pos;
            const ghostPos = native.getEntityCoords(ghost, false);

            const dx = playerPos.x - ghostPos.x;
            const dy = playerPos.y - ghostPos.y;
            const heading = Math.atan2(dy, dx) * 180 / Math.PI - 90;
            native.setEntityHeading(ghost, heading);
        }

        // 第二次遇鬼时不允许转头消失，必须看着鬼过来
        if (metghost) {
            // 持续对准鬼（每帧更新）
            lookAtGhost();

            // 检查距离，太近则消失并触发jumpscare
            if (distance <= CLOSE_DISTANCE) {
                alt.log('[幽灵] 第二次遇鬼：幽灵太靠近玩家，触发jumpscare');
                cleanupGhost();
                alreadyMove = false;
                showJumpscare();
                return;
            }

            // 继续移动向玩家
            if (isMoving && !alreadyMove) {
                alreadyMove = true;
                moveGhostToPlayer();
            }
        } else {
            // 第一次遇鬼的躲闪逻辑
            if (isDodging) {
                // 显示躲闪进度
                const elapsed = Date.now() - dodgeStartTime;
                const remaining = Math.max(0, DODGE_TIME - elapsed);
                const progress = Math.min(100, (elapsed / DODGE_TIME) * 100);

                /*  drawText2D(
                      `躲闪进度: ${progress.toFixed(1)}% (剩余: ${(remaining / 1000).toFixed(1)}秒)`,
                      { x: 0.5, y: 0.1 },
                      0.5,
                      new alt.RGBA(255, 255, 255, 255)
                  );
                  
                  drawText2D(
                      `盯着鬼不要转头，疯狂逃跑！`,
                      { x: 0.5, y: 0.15 },
                      0.4,
                      new alt.RGBA(255, 100, 100, 255)
                  );*/

                if (!onScreen) {
                    // 玩家转头，躲闪失败
                    offScreenFrames++;
                    if (offScreenFrames > OFFSCREEN_FRAMES) {
                        alt.log('[幽灵] 躲闪失败：玩家转头，幽灵消失');
                        isDodging = false;
                        if (dodgeTimer) {
                            alt.clearTimeout(dodgeTimer);
                            dodgeTimer = null;
                        }
                        webview.emit(Events.player.audio.play.local, 'sounds/ghostyall.ogg', 0.5);
                        cleanupGhost();
                        alreadyMove = false;
                        return;
                    }
                } else {
                    offScreenFrames = 0;

                    // 检查距离，太近则躲闪失败
                    if (distance <= CLOSE_DISTANCE) {
                        alt.log('[幽灵] 躲闪失败：幽灵太靠近玩家');
                        isDodging = false;
                        if (dodgeTimer) {
                            alt.clearTimeout(dodgeTimer);
                            dodgeTimer = null;
                        }
                        webview.emit(Events.player.audio.play.local, 'sounds/ghostyall.ogg', 0.5);

                        cleanupGhost();
                        alreadyMove = false;
                        return;
                    }

                    // 继续移动向玩家
                    if (isMoving && !alreadyMove) {
                        alreadyMove = true;
                        moveGhostToPlayer();
                    }
                }
            } else {
                // 非躲闪模式的第一次遇鬼逻辑（保持原有逻辑作为后备）
                if (!onScreen) {
                    // 玩家转头，幽灵消失
                    offScreenFrames++;
                    if (offScreenFrames > OFFSCREEN_FRAMES) {
                        alt.log('[幽灵] 玩家转头，幽灵消失');
                        webview.emit(Events.player.audio.play.local, 'sounds/ghostyall.ogg', 0.5);
                        cleanupGhost();
                        alreadyMove = false;
                        return;
                    }
                } else {
                    offScreenFrames = 0;

                    // 检查距离，太近则消失
                    if (distance <= CLOSE_DISTANCE) {
                        alt.log('[幽灵] 幽灵太靠近玩家，自动消失');
                        webview.emit(Events.player.audio.play.local, 'sounds/ghostyall.ogg', 0.5);
                        cleanupGhost();
                        alreadyMove = false;
                        return;
                    }

                    // 继续移动向玩家
                    if (isMoving && !alreadyMove) {
                        alreadyMove = true;
                        moveGhostToPlayer();
                    }
                }
            }
        }
    }
}

/**
 * 让摄像头对准幽灵
 */
function lookAtGhost() {
    if (!ghost || !native.doesEntityExist(ghost)) return;

    const player = alt.Player.local;
    const playerPos = player.pos;
    const ghostPos = native.getEntityCoords(ghost, false);

    // 计算从玩家到鬼的方向向量
    const dx = ghostPos.x - playerPos.x;
    const dy = ghostPos.y - playerPos.y;
    const dz = ghostPos.z - playerPos.z;

    // 计算水平角度 (heading)
    let heading = Math.atan2(dy, dx) * 180 / Math.PI;
    heading = heading - 90; // 调整GTA的坐标系
    if (heading < 0) heading += 360;

    // 计算垂直角度 (pitch)
    const horizontalDistance = Math.sqrt(dx * dx + dy * dy);
    let pitch = Math.atan2(-dz, horizontalDistance) * 180 / Math.PI;

    // 设置玩家朝向
    native.setEntityHeading(player.scriptID, heading);

    // 设置摄像头角度
    native.setGameplayCamRelativeHeading(0);
    native.setGameplayCamRelativePitch(pitch, 1.0);

    // 只在初次对准时打印日志，避免刷屏
    if (Math.random() < 0.01) {
        alt.log(`[幽灵] 对准鬼 - Heading: ${heading.toFixed(2)}, Pitch: ${pitch.toFixed(2)}`);
    }
}

/**
 * 启动幽灵的瞬移逻辑
 */
function moveGhostToPlayer() {
    if (teleportTimer) {
        alt.clearInterval(teleportTimer);
    }

    // 启动瞬移定时器
    teleportTimer = alt.setInterval(() => {
        teleportGhostTowardsPlayer();
    }, TELEPORT_INTERVAL);

    alt.log('[幽灵] 启动瞬移移动模式');
}

/**
 * 让幽灵瞬移向玩家靠近
 */
function teleportGhostTowardsPlayer() {
    if (!ghost || !native.doesEntityExist(ghost)) {
        return;
    }

    const player = alt.Player.local;
    const playerPos = player.pos;
    const ghostPos = native.getEntityCoords(ghost, false);

    // 计算从幽灵到玩家的方向向量
    const dx = playerPos.x - ghostPos.x;
    const dy = playerPos.y - ghostPos.y;
    const dz = playerPos.z - ghostPos.z;

    // 计算距离
    const distance = Math.sqrt(dx * dx + dy * dy + dz * dz);

    // 如果已经很接近玩家，停止瞬移
    if (distance <= CLOSE_DISTANCE) {
        return;
    }

    // 标准化方向向量
    const normalizedDx = dx / distance;
    const normalizedDy = dy / distance;
    const normalizedDz = dz / distance;

    // 计算新位置
    const newX = ghostPos.x + normalizedDx * TELEPORT_DISTANCE;
    const newY = ghostPos.y + normalizedDy * TELEPORT_DISTANCE;
    const newZ = ghostPos.z + normalizedDz * TELEPORT_DISTANCE;

    // 瞬移幽灵到新位置
    native.setEntityCoords(ghost, newX, newY, newZ - 1, false, false, false, true);

    native.freezeEntityPosition(ghost, true);

    // 让幽灵面向玩家
    const heading = Math.atan2(dy, dx) * 180 / Math.PI - 90;
    native.setEntityHeading(ghost, heading);

    // 调试信息（低频率输出）
    if (Math.random() < 0.1) {
        alt.log(`[幽灵] 瞬移向玩家 - 距离: ${distance.toFixed(2)} -> ${(distance - TELEPORT_DISTANCE).toFixed(2)}`);
    }
}

/**
 * 删除幽灵并清理定时器/循环
 */
function cleanupGhost() {
    if (stepTimer) {
        alt.clearTimeout(stepTimer);
        stepTimer = null;
    }
    if (tickHandle) {
        alt.clearEveryTick(tickHandle);
        tickHandle = null;
    }
    if (dodgeTimer) {
        alt.clearTimeout(dodgeTimer);
        dodgeTimer = null;
    }
    if (teleportTimer) {
        alt.clearInterval(teleportTimer);
        teleportTimer = null;
    }
    if (ghost && native.doesEntityExist(ghost)) {
        native.deletePed(ghost);
    }
    ghost = null;
    alreadyMove = false;
    isMoving = false;
    isDodging = false;  // 重置躲闪状态
    dodgeStartTime = 0; // 重置躲闪开始时间

    // 向服务端发送撞鬼结束事件
    alt.emitServerRaw('ghostEncounterFinished', false); // false表示正常撞鬼结束

    // 第一次完整遇鬼后标记 (只在第一次正常遇鬼结束时)
    if (!metghost && seenOnce) {
        metghost = true;
        alt.log('[幽灵] 第一次完整遇鬼结束，设置下次为jumpscare模式');
        alt.log(`[幽灵] metghost状态变更: false -> true`);
    } else {
        alt.log(`[幽灵] 不改变metghost状态 - metghost: ${metghost}, seenOnce: ${seenOnce}`);
    }

    // 重置状态
    seenOnce = false;
    offScreenFrames = 0;
}

/**
 * 躲闪成功处理
 */
function dodgeSuccess() {
    alt.log('[幽灵] 躲闪成功！玩家成功逃脱了10秒');

    // 清理幽灵（会自动重置所有状态）
    cleanupGhost();
    alreadyMove = false;
}

/**
 * 显示jumpscare效果
 */
function showJumpscare() {
    alt.log('[幽灵] 显示jumpscare效果');

    // 向服务端发送jumpscare事件
    alt.emitServerRaw('ghostEncounterFinished', true); // true表示被抓到了

    // 立即重置为正常模式，避免状态混乱
    metghost = false;
    alt.log('[幽灵] 重置为正常模式，下次将是第一次遇鬼');

    // 播放被抓音效
    webview.emit(Events.player.audio.play.local, 'sounds/ghostcatch.ogg', 1.0);

    alt.setTimeout(() => {
        // 显示jumpscare webview
        webview.show('ghostcatch', 'overlay');
    }, 1000);

    // 延迟恢复玩家控制（给点时间看jumpscare效果）
    alt.setTimeout(() => {
        webview.hide('ghostcatch');
        Rebar.player.useControls().setControls(true);
        alt.log('[幽灵] 恢复玩家控制，隐藏jumpscare');
    }, 4000); // 4秒后恢复控制
}

//ghostrun  第二次遇到鬼追逐时的音效
//ghostcatch 被抓到时的音效
//Rebar.player.useControls().setControls(false) 禁止玩家进行任何操作

