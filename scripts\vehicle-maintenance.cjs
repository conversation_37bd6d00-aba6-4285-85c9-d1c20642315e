const fs = require('fs');
const path = require('path');

// 获取车辆文件夹路径
const vehiclePath = path.join(__dirname, '../resources/mods/vehicle');

// 扫描所有车辆文件夹
function scanVehicleFolders() {
    const folders = fs.readdirSync(vehiclePath);
    const vehicleData = [];
    
    for (const folder of folders) {
        const folderPath = path.join(vehiclePath, folder);
        if (fs.statSync(folderPath).isDirectory()) {
            try {
                // 检查 stream/assets 文件夹
                const assetsPath = path.join(folderPath, 'stream/assets');
                if (fs.existsSync(assetsPath)) {
                    // 检查单层结构
                    let yftFiles = fs.readdirSync(assetsPath)
                        .filter(file => file.endsWith('.yft') && !file.endsWith('_hi.yft'));
                    
                    if (yftFiles.length === 0) {
                        // 检查双层结构 (assets/assets)
                        const deepAssetsPath = path.join(assetsPath, 'assets');
                        if (fs.existsSync(deepAssetsPath)) {
                            yftFiles = fs.readdirSync(deepAssetsPath)
                                .filter(file => file.endsWith('.yft') && !file.endsWith('_hi.yft'));
                        }
                    }
                    
                    if (yftFiles.length > 0) {
                        // 找到主yft文件（最大的或与文件夹名最相似的）
                        let mainYft = yftFiles[0];
                        
                        // 如果有多个yft文件，选择文件名匹配度最高的
                        for (const yft of yftFiles) {
                            const baseName = yft.replace('.yft', '');
                            if (folder.toLowerCase().includes(baseName.toLowerCase()) || 
                                baseName.toLowerCase().includes(folder.toLowerCase().substring(0, 4))) {
                                mainYft = yft;
                                break;
                            }
                        }
                        
                        vehicleData.push({
                            folder: folder,
                            yftName: mainYft.replace('.yft', '')
                        });
                    }
                }
            } catch (error) {
                console.warn(`跳过车辆文件夹 ${folder}: ${error.message}`);
            }
        }
    }
    
    return vehicleData;
}

// 生成server.toml配置
function generateServerTomlConfig(vehicleData) {
    const tomlEntries = vehicleData.map(vehicle => `    'mods/vehicle/${vehicle.folder}',`);
    return tomlEntries.join('\n');
}

// 生成addvehicle.ts配置
function generateAddVehicleConfig(vehicleData) {
    const entries = vehicleData.map(vehicle => `    '${vehicle.yftName}',`);
    return `export const addvehicle = [\n${entries.join('\n')}\n]`;
}

// 主函数
function main() {
    console.log('扫描车辆文件夹...');
    const vehicleData = scanVehicleFolders();
    
    console.log(`\n找到 ${vehicleData.length} 个车辆模组:\n`);
    
    vehicleData.forEach(vehicle => {
        console.log(`文件夹: ${vehicle.folder} -> YFT: ${vehicle.yftName}`);
    });
    
    console.log('\n\n=== server.toml 配置 ===');
    console.log(generateServerTomlConfig(vehicleData));
    
    console.log('\n\n=== addvehicle.ts 配置 ===');
    console.log(generateAddVehicleConfig(vehicleData));
    
    // 自动更新文件
    const addVehiclePath = path.join(__dirname, '../src/plugins/veh/shared/addvehicle.ts');
    fs.writeFileSync(addVehiclePath, generateAddVehicleConfig(vehicleData));
    console.log('\n已更新 addvehicle.ts 文件');
}

if (require.main === module) {
    main();
}

module.exports = { scanVehicleFolders, generateServerTomlConfig, generateAddVehicleConfig }; 