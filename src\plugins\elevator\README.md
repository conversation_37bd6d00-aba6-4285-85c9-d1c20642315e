# 电梯系统 (Elevator System)

一个现代化的电梯系统插件，提供多楼层传送功能，具有美观的用户界面和音效系统。

## 功能特性

- 🏢 **多楼层支持**: 支持任意数量的楼层配置
- 🎵 **音效系统**: 使用 Tone.js 提供现代化音效反馈
- 🎨 **现代化UI**: 参考GTA6风格的界面设计
- 📱 **响应式设计**: 使用vh/vw单位，适配不同分辨率
- ⚡ **平滑动画**: 丰富的过渡动画和视觉效果
- 🔧 **易于配置**: 简单的配置文件管理

## 界面功能

### 主要组件
- **电梯面板**: 显示电梯状态和楼层列表
- **楼层按钮**: 显示楼层名称和坐标信息
- **状态指示器**: 显示电梯运行状态
- **进度动画**: 电梯移动时的视觉反馈

### 音效系统
- **按钮点击音效**: 界面交互反馈
- **楼层选择音效**: 根据楼层高度播放不同音调
- **电梯运行音效**: 移动过程中的背景音效
- **到达音效**: 成功到达目标楼层的提示音

## 配置说明

### 电梯配置文件 (`shared/config.ts`)

```typescript
export const elevatorConfig: elevator[] = [
    {
        pos: new alt.Vector3(-1396.0, -479.0, 30.0), // 电梯位置
        floor: [
            {name: '1F - 大厅', pos: new alt.Vector3(-1396.0, -479.0, 30.0)},
            {name: '2F - 办公区', pos: new alt.Vector3(-1396.0, -479.0, 34.0)},
            // ... 更多楼层
        ],
        dimension: 0 // 可选：维度设置
    }
];
```

### 配置项说明
- `pos`: 电梯交互区域的中心位置
- `floor`: 楼层数组，包含楼层名称和传送位置
- `dimension`: 可选的维度参数，默认为0

## 使用方法

1. **接近电梯**: 玩家进入电梯交互区域时会显示提示
2. **打开界面**: 按下交互键打开电梯控制面板
3. **选择楼层**: 点击目标楼层按钮
4. **等待传送**: 电梯会播放动画并传送玩家
5. **自动关闭**: 到达目标楼层后界面自动关闭

## 技术特性

### 前端技术
- **Vue 3**: 使用Composition API
- **TypeScript**: 类型安全的开发体验
- **Tone.js**: 专业的Web音频合成
- **CSS动画**: 流畅的视觉效果

### 服务端集成
- **Rebar框架**: 完整的服务端集成
- **交互系统**: 基于ColShape的交互检测
- **事件通信**: 客户端与服务端的双向通信
- **屏幕淡入淡出**: 传送时的视觉过渡

## 设计理念

界面设计参考了GTA6的现代化风格，具有以下特点：
- **深色主题**: 符合游戏环境的配色方案
- **毛玻璃效果**: 现代化的背景模糊效果
- **霓虹色彩**: 使用青绿色作为主要强调色
- **平滑过渡**: 所有交互都有流畅的动画效果

## 扩展性

系统设计时考虑了良好的扩展性：
- 易于添加新的电梯位置
- 支持任意数量的楼层
- 音效系统可以轻松定制
- 界面样式可以独立修改 