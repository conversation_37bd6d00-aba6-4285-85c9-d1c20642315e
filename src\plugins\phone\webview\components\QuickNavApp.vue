<template>
  <div class="quicknav-app">
    <div class="app-header">
      <h2>快捷导航</h2>
      <button @click="$emit('goBack')" class="close-button">
        <svg viewBox="0 0 24 24" width="100%" height="100%" fill="none" stroke="currentColor">
          <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z" 
          fill="currentColor"></path>
        </svg>
      </button>
    </div>

    <div class="quicknav-content">
      <!-- 便民设施分类 -->
      <div class="nav-section">
        <div class="section-header">
          <div class="section-icon convenience-icon">
            <svg viewBox="0 0 24 24" width="100%" height="100%" fill="none">
              <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z" fill="currentColor"></path>
            </svg>
          </div>
          <h3>便民设施</h3>
        </div>
        <div class="nav-grid">
          <button class="nav-card atm-card" @click="navigateToNearestATM" :disabled="isNavigating">
            <div class="card-icon">
              <svg viewBox="0 0 24 24" width="100%" height="100%" fill="currentColor">
                <path d="M11 8c0 2.21-1.79 4-4 4s-4-1.79-4-4 1.79-4 4-4 4 1.79 4 4zm0 6.72V23h2v-8.28c1.44-.45 2.5-1.81 2.5-3.42 0-1.97-1.53-3.57-3.46-3.72C11.53 6.43 10.97 6 10.3 6H9.7c-.67 0-1.23.43-1.46 1.04C6.53 7.43 5 9.03 5 11c0 1.61 1.06 2.97 2.5 3.42V23h2v-8.28z"/>
              </svg>
            </div>
            <div class="card-content">
              <span class="card-title">ATM机</span>
              <span class="card-subtitle">{{ isNavigating ? '搜索中...' : '银行取款机' }}</span>
            </div>
          </button>

          <button class="nav-card phonebox-card" @click="navigateToNearestPhonebox" :disabled="isNavigating">
            <div class="card-icon">
              <svg viewBox="0 0 24 24" width="100%" height="100%" fill="currentColor">
                <path d="M6.62 10.79c1.44 2.83 3.76 5.14 6.59 6.59l2.2-2.2c.27-.27.67-.36 1.02-.24 1.12.37 2.33.57 3.57.57.55 0 1 .45 1 1V20c0 .55-.45 1-1 1-9.39 0-17-7.61-17-17 0-.55.45-1 1-1h3.5c.55 0 1 .45 1 1 0 1.25.2 2.45.57 3.57.11.35.03.74-.25 1.02l-2.2 2.2z"/>
              </svg>
            </div>
            <div class="card-content">
              <span class="card-title">公共电话</span>
              <span class="card-subtitle">{{ isNavigating ? '搜索中...' : '电话亭' }}</span>
            </div>
          </button>

          <button class="nav-card vending-card" @click="navigateToNearestVending" :disabled="isNavigating">
            <div class="card-icon">
              <svg viewBox="0 0 24 24" width="100%" height="100%" fill="currentColor">
                <path d="M18.06 23h1.66c.84 0 1.53-.64 1.63-1.47L23 5.05h-5V1.84c0-.83-.52-1.54-1.3-1.82C16.07.01 15.38.01 14.9.35L9.93 3.5c-.36.26-.93.26-1.29 0L3.1.35c-.48-.34-1.17-.34-1.8-.02C.52.61 0 1.32 0 2.15V23h4.25l.32-3.08c.1-.83.79-1.47 1.63-1.47h11.86zM7.12 15.96c-.39 0-.72-.33-.72-.72s.33-.72.72-.72.72.33.72.72-.33.72-.72.72zm4.88 0c-.39 0-.72-.33-.72-.72s.33-.72.72-.72.72.33.72.72-.33.72-.72.72zm4.88 0c-.39 0-.72-.33-.72-.72s.33-.72.72-.72.72.33.72.72-.33.72-.72.72z"/>
              </svg>
            </div>
            <div class="card-content">
              <span class="card-title">自动售货机</span>
              <span class="card-subtitle">{{ isNavigating ? '搜索中...' : '饮料零食' }}</span>
            </div>
          </button>
        </div>
      </div>

      <!-- 环保设施分类 -->
      <div class="nav-section">
        <div class="section-header">
          <div class="section-icon eco-icon">
            <svg viewBox="0 0 24 24" width="100%" height="100%" fill="none">
              <path d="M17 8C8 10 5.9 16.17 3.82 21.34l1.4.93C7.5 17.17 9 14 17 12V8zm-5.8 3.2c.6.6 1.6.6 2.2 0 .6-.6.6-1.6 0-2.2-.6-.6-1.6-.6-2.2 0-.6.6-.6 1.6 0 2.2zM12 5.83L15.17 9c-.51.02-1.02.05-1.54.09L12 7.46 10.37 9.09c-.52-.04-1.03-.07-1.54-.09L12 5.83z" fill="currentColor"></path>
            </svg>
          </div>
          <h3>环保设施</h3>
        </div>
        <div class="nav-grid">
          <button class="nav-card trash-card" @click="navigateToNearestTrash" :disabled="isNavigating">
            <div class="card-icon">
              <svg viewBox="0 0 24 24" width="100%" height="100%" fill="currentColor">
                <path d="M6 19c0 1.1.9 2 2 2h8c1.1 0 2-.9 2-2V7H6v12zM19 4h-3.5l-1-1h-5l-1 1H5v2h14V4z"/>
              </svg>
            </div>
            <div class="card-content">
              <span class="card-title">垃圾桶</span>
              <span class="card-subtitle">{{ isNavigating ? '搜索中...' : '垃圾处理' }}</span>
            </div>
          </button>

          <button class="nav-card recycle-card" @click="navigateToNearestRecycle" :disabled="isNavigating">
            <div class="card-icon">
              <svg viewBox="0 0 24 24" width="100%" height="100%" fill="currentColor">
                <path d="M5.77 7.15L7.2 4.78l1.03-1.71c.39-.65 1.33-.65 1.72 0l1.48 2.46c.18.3.5.47.84.47h2.85c.78 0 1.31-.78.97-1.45L14.58 2.1c-.39-.78-1.39-.78-1.78 0L11.6 4.05 8.43 2.1c-.39-.78-1.39-.78-1.78 0L5.15 4.55c-.34.67.19 1.45.97 1.45h2.85c.34 0 .66-.17.84-.47L11.29 4l1.03 1.71L5.77 7.15zm7.57 9.54l-1.03-1.71L18.87 16.85l-1.43 2.37-1.03 1.71c-.39.65-1.33.65-1.72 0l-1.48-2.46c-.18-.3-.5-.47-.84-.47H9.52c-.78 0-1.31.78-.97 1.45l1.51 2.45c.39.78 1.39.78 1.78 0l1.2-1.95 3.17 1.95c.39.78 1.39.78 1.78 0l1.5-2.45c.34-.67-.19-1.45-.97-1.45h-2.85c-.34 0-.66.17-.84.47L12.71 20l-1.03-1.71 6.55-1.6z"/>
              </svg>
            </div>
            <div class="card-content">
              <span class="card-title">回收站</span>
              <span class="card-subtitle">{{ isNavigating ? '搜索中...' : '废品回收' }}</span>
            </div>
          </button>
        </div>
      </div>

      <!-- 导航状态 -->
      <div class="nav-section" v-if="lastNavigation">
        <div class="section-header">
          <div class="section-icon status-icon">
            <svg viewBox="0 0 24 24" width="100%" height="100%" fill="none">
              <path d="M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7z" fill="currentColor"></path>
              <circle cx="12" cy="9" r="2.5" fill="white"></circle>
            </svg>
          </div>
          <h3>导航历史</h3>
        </div>
        <div class="status-card">
          <div class="status-content">
            <div class="status-main">
              <span class="status-target">{{ lastNavigation.target }}</span>
              <span class="status-time">{{ formatTime(lastNavigation.time) }}</span>
            </div>
            <div class="status-badge">
              <svg viewBox="0 0 24 24" width="100%" height="100%" fill="currentColor">
                <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41L9 16.17z"/>
              </svg>
            </div>
          </div>
        </div>
      </div>

      <!-- 使用提示 -->
      <div class="nav-section">
        <div class="section-header">
          <div class="section-icon tips-icon">
            <svg viewBox="0 0 24 24" width="100%" height="100%" fill="none">
              <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z" fill="currentColor"></path>
            </svg>
          </div>
          <h3>使用说明</h3>
        </div>
        <div class="tips-card">
          <div class="tip-item">
            <span class="tip-number">1</span>
            <span class="tip-text">点击任意设施卡片开始导航</span>
          </div>
          <div class="tip-item">
            <span class="tip-number">2</span>
            <span class="tip-text">系统自动搜索1000米范围内最近目标</span>
          </div>
          <div class="tip-item">
            <span class="tip-number">3</span>
            <span class="tip-text">导航点会自动设置到你的GPS地图</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue';
import { useEvents } from '@Composables/useEvents.js';

const events = useEvents();

const emit = defineEmits(['goBack']);

const isNavigating = ref(false);
const lastNavigation = ref<{target: string, time: number} | null>(null);

// 导航到最近的ATM
const navigateToNearestATM = () => {
  if (isNavigating.value) return;
  
  isNavigating.value = true;
  
  // 调用服务端查找最近的ATM
  events.emitServer('quicknav:findNearestATM');
};

// 导航到最近的公共电话亭
const navigateToNearestPhonebox = () => {
  if (isNavigating.value) return;
  
  isNavigating.value = true;
  
  // 调用服务端查找最近的公共电话亭
  events.emitServer('quicknav:findNearestPhonebox');
};

// 导航到最近的自动售货机
const navigateToNearestVending = () => {
  if (isNavigating.value) return;
  
  isNavigating.value = true;
  
  // 调用服务端查找最近的自动售货机
  events.emitServer('quicknav:findNearestVending');
};

// 导航到最近的垃圾桶
const navigateToNearestTrash = () => {
  if (isNavigating.value) return;
  
  isNavigating.value = true;
  
  // 调用服务端查找最近的垃圾桶
  events.emitServer('quicknav:findNearestTrash');
};

// 导航到最近的回收站
const navigateToNearestRecycle = () => {
  if (isNavigating.value) return;
  
  isNavigating.value = true;
  
  // 调用服务端查找最近的回收站
  events.emitServer('quicknav:findNearestRecycle');
};

// 格式化时间显示
const formatTime = (timestamp: number) => {
  const now = Date.now();
  const diff = Math.floor((now - timestamp) / 1000);
  
  if (diff < 60) return '刚刚';
  if (diff < 3600) return `${Math.floor(diff / 60)}分钟前`;
  if (diff < 86400) return `${Math.floor(diff / 3600)}小时前`;
  return `${Math.floor(diff / 86400)}天前`;
};

// 监听来自服务端的导航事件
events.on('quicknav:navigationSet', (data: {target: string}) => {
  lastNavigation.value = {
    target: data.target,
    time: Date.now()
  };
  isNavigating.value = false;
});

// 监听导航成功
events.on('quicknav:success', (data: {target: string, message: string}) => {
  lastNavigation.value = {
    target: data.target,
    time: Date.now()
  };
  isNavigating.value = false;
});

// 监听导航失败
events.on('quicknav:error', (message: string) => {
  isNavigating.value = false;
});
</script>

<style scoped>
.quicknav-app {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
  background-color: #f8f9fa;
  color: #212529;
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.app-header {
  background-color: #ffffff;
  padding: 1.6vh;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  box-shadow: 0 0.1vh 0.5vh rgba(0, 0, 0, 0.05);
}

.app-header h2 {
  font-size: 2vh;
  font-weight: 600;
  margin: 0;
  color: #333;
}

.close-button {
  width: 2.4vh;
  height: 2.4vh;
  background: none;
  border: none;
  color: #666;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  border-radius: 50%;
  transition: all 0.2s ease;
}

.close-button:hover {
  background-color: rgba(0, 0, 0, 0.05);
  color: #333;
}

.quicknav-content {
  flex: 1;
  overflow-y: auto;
  padding: 0 0 2vh 0;
  scrollbar-width: thin;
  scrollbar-color: rgba(0, 0, 0, 0.1) transparent;
}

.quicknav-content::-webkit-scrollbar {
  width: 0.4vh;
}

.quicknav-content::-webkit-scrollbar-track {
  background: transparent;
}

.quicknav-content::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.1);
  border-radius: 1vh;
}

.quicknav-content::-webkit-scrollbar-thumb:hover {
  background-color: rgba(0, 0, 0, 0.2);
}

.nav-section {
  background-color: #ffffff;
  margin: 1.2vh 0;
  border-top: 1px solid rgba(0, 0, 0, 0.05);
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  padding: 1.2vh 1.6vh;
}

.section-header {
  display: flex;
  align-items: center;
  margin-bottom: 1vh;
}

.section-icon {
  width: 2.4vh;
  height: 2.4vh;
  margin-right: 1.2vh;
  display: flex;
  align-items: center;
  justify-content: center;
}

.convenience-icon {
  color: #3b82f6;
}

.eco-icon {
  color: #10b981;
}

.status-icon {
  color: #8b5cf6;
}

.tips-icon {
  color: #f59e0b;
}

.section-header h3 {
  font-size: 1.5vh;
  font-weight: 600;
  margin: 0;
  flex: 1;
}

.nav-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1vh;
  margin-top: 1vh;
}

.nav-card {
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-radius: 1vh;
  padding: 1.2vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
  overflow: hidden;
  min-height: 8vh;
}

.nav-card:hover:not(:disabled) {
  transform: translateY(-0.2vh);
  box-shadow: 0 0.4vh 1.2vh rgba(0, 0, 0, 0.15);
  border-color: rgba(0, 0, 0, 0.2);
}

.nav-card:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.card-icon {
  width: 3vh;
  height: 3vh;
  margin-bottom: 0.8vh;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 0.6vh;
  padding: 0.4vh;
}

.atm-card .card-icon {
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
}

.phonebox-card .card-icon {
  background: linear-gradient(135deg, #3b82f6, #2563eb);
  color: white;
}

.vending-card .card-icon {
  background: linear-gradient(135deg, #f59e0b, #d97706);
  color: white;
}

.trash-card .card-icon {
  background: linear-gradient(135deg, #6b7280, #4b5563);
  color: white;
}

.recycle-card .card-icon {
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
}

.card-content {
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.card-title {
  font-size: 1.4vh;
  font-weight: 600;
  color: #333;
  margin-bottom: 0.2vh;
}

.card-subtitle {
  font-size: 1.1vh;
  color: #666;
  opacity: 0.8;
  line-height: 1.2;
}

.status-card {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-radius: 0.8vh;
  padding: 1vh 1.2vh;
  margin-top: 1vh;
}

.status-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.status-main {
  display: flex;
  flex-direction: column;
}

.status-target {
  font-size: 1.4vh;
  font-weight: 600;
  color: #333;
  margin-bottom: 0.2vh;
}

.status-time {
  font-size: 1.1vh;
  color: #666;
}

.status-badge {
  width: 2vh;
  height: 2vh;
  background: linear-gradient(135deg, #10b981, #059669);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.tips-card {
  margin-top: 1vh;
  background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
  border: 1px solid #fbbf24;
  border-radius: 0.8vh;
  padding: 1vh 1.2vh;
}

.tip-item {
  display: flex;
  align-items: center;
  margin-bottom: 0.8vh;
  padding: 0.4vh 0;
}

.tip-item:last-child {
  margin-bottom: 0;
}

.tip-number {
  width: 2vh;
  height: 2vh;
  background: linear-gradient(135deg, #f59e0b, #d97706);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.1vh;
  font-weight: 600;
  margin-right: 1vh;
  flex-shrink: 0;
}

.tip-text {
  font-size: 1.2vh;
  color: #92400e;
  font-weight: 500;
  line-height: 1.3;
}

/* 加载状态动画 */
.nav-card:disabled .card-icon {
  animation: pulse 1.5s ease-in-out infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}
</style> 