<template>
  <!-- 模板选择页面 -->
  <div v-if="currentPage === 'templateSelection'" class="template-selection-container">
    <div class="selection-header">
      <div class="header-info">
        <h1>服装设计 - 模板选择</h1>
        <span class="subtitle">Clothing Design - Template Selection</span>
      </div>
    </div>
    
         <div class="template-grid">
       <div class="template-card" 
            v-for="template in templates" 
            :key="template.value"
            @click="selectTemplate(template.value)"
            :class="{ selected: selectedTemplate === template.value }">
         <div class="template-icon" :style="{ '--icon-color': template.color }">
           <i :class="template.icon"></i>
         </div>
         <div class="template-info">
           <h3>{{ template.label }}</h3>
           <p>{{ template.description }}</p>
         </div>
       </div>
     </div>
    
    <div class="selection-actions">
      <button @click="confirmTemplate" 
              class="btn btn-primary" 
              :disabled="!selectedTemplate || isProcessing">
        <i class="fas fa-check" v-if="!isProcessing"></i>
        <i class="fas fa-spinner fa-spin" v-if="isProcessing"></i>
        {{ isProcessing ? '准备中...' : '开始绘制' }}
      </button>
    </div>
  </div>

  <!-- 绘画页面 -->
  <div v-else-if="currentPage === 'drawing'" class="drawing-container">
    <div class="drawing-header">
      <div class="header-info">
        <h1>服装设计绘图 - {{ getTemplateLabel(selectedTemplate) }}</h1>
        <span class="subtitle">Clothing Design Drawing</span>
      </div>
      <div class="timer-info">
        <div class="time-progress-container">
          <div class="time-progress-bar">
            <div class="time-progress-fill" :style="{ width: timeProgressPercent + '%' }"></div>
          </div>
          <span class="time-text">绘制进度</span>
        </div>
      </div>
    </div>

    <!-- 主绘图区域 -->
    <div class="main-canvas-area">
      <div class="canvas-container" ref="canvasContainer" @mousemove="updateCursor" @mouseleave="hideCursor">
        <canvas 
          ref="templateCanvas" 
          class="template-canvas"
          :width="canvasWidth" 
          :height="canvasHeight"
        ></canvas>
        <canvas 
          ref="drawingCanvas" 
          class="drawing-canvas"
          :width="canvasWidth" 
          :height="canvasHeight"
          @pointerdown="startDrawing"
          @pointermove="draw"
          @pointerup="stopDrawing"
          @pointercancel="stopDrawing"
        ></canvas>
      </div>
    </div>
    
    <!-- 完成按钮 -->
    <div class="drawing-actions">
      <button @click="completeDrawing" 
              class="btn btn-complete" 
              :disabled="timeRemaining <= 0 || isSubmitting">
        <i class="fas fa-check" v-if="!isSubmitting"></i>
        <i class="fas fa-spinner fa-spin" v-if="isSubmitting"></i>
        {{ isSubmitting ? '提交中...' : '完成绘制' }}
      </button>
    </div>
  </div>
    
  <!-- 通知模态框 -->
  <div v-if="showModal" class="modal-overlay">
    <div class="modal-content" @click.stop>
      <div class="modal-header">
        <h3>{{ modalTitle }}</h3>
        <button @click="closeModal" class="modal-close">&times;</button>
      </div>
      <div class="modal-body">
        <p>{{ modalMessage }}</p>
      </div>
      <div class="modal-footer">
        <button @click="closeModal" class="btn btn-primary">确定</button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, nextTick } from 'vue';
import { useEvents } from '@Composables/useEvents.js';
import { findIcon } from '../../inventory/shared/findicon.js';

const event = useEvents();

// 页面状态
const currentPage = ref<'templateSelection' | 'drawing'>('templateSelection');
const isProcessing = ref(false);

// 模板数据
const templates = ref([
  {
    value: 'boyuppr',
    label: '男士上衣',
    description: '设计男士上衣样式',
    icon: 'fas fa-tshirt',
    color: '#3b82f6'
  },
  {
    value: 'boylowr',
    label: '男士裤子',
    description: '设计男士裤子样式',
    icon: 'fas fa-socks',
    color: '#8b5cf6'
  },
  {
    value: 'boyshoe',
    label: '男士鞋子',
    description: '设计男士鞋子样式',
    icon: 'fas fa-shoe-prints',
    color: '#f59e0b'
  },
  {
    value: 'boyneak',
    label: '男士饰品',
    description: '设计男士饰品样式',
    icon: 'fas fa-gem',
    color: '#ec4899'
  },
  {
    value: 'boymask',
    label: '男士面具',
    description: '设计男士面具样式',
    icon: 'fas fa-mask',
    color: '#10b981'
  },
  {
    value: 'car_side',
    label: '汽车侧视图',
    description: '绘制汽车侧面设计图',
    icon: 'fas fa-car-side',
    color: '#ef4444'
  },
  {
    value: 'car_top',
    label: '汽车俯视图',
    description: '绘制汽车顶部设计图',
    icon: 'fas fa-car',
    color: '#06b6d4'
  },
  {
    value: 'car_front',
    label: '汽车正视图',
    description: '绘制汽车正面设计图',
    icon: 'fas fa-car-front',
    color: '#8b5a2b'
  }
]);

// Canvas refs
const templateCanvas = ref<HTMLCanvasElement>();
const drawingCanvas = ref<HTMLCanvasElement>();
const canvasContainer = ref<HTMLElement>();

// Canvas settings
const canvasWidth = ref(400);
const canvasHeight = ref(600);

// Template data
const selectedTemplate = ref('');
const templateMask = ref<boolean[][]>([]);
const templateImage = ref<HTMLImageElement>();

// Drawing state
const isDrawing = ref(false);
const currentPath = ref<Path2D>();

// Timer state
const timeLimit = ref(50); // 50 seconds
const timeRemaining = ref(50);
const timerInterval = ref<NodeJS.Timeout>();

// Completion tracking
const completionRate = ref(0);
const precisionScore = ref(0);
const finalScore = ref(0);
const userDrawnPixels = ref<boolean[][]>([]);
const strokePaths = ref<Array<{x: number, y: number, timestamp: number}>>([]);
const currentStroke = ref<Array<{x: number, y: number, timestamp: number}>>([]);

// Modal state
const showModal = ref(false);
const modalTitle = ref('');
const modalMessage = ref('');

// 防重复提交状态
const isSubmitting = ref(false);

// 计算时间进度百分比
const timeProgressPercent = computed(() => {
  if (timeLimit.value === 0) return 0;
  const usedTime = timeLimit.value - timeRemaining.value;
  return Math.min(100, (usedTime / timeLimit.value) * 100);
});



// 获取模板标签
const getTemplateLabel = (templateValue: string) => {
  const template = templates.value.find(t => t.value === templateValue);
  return template ? template.label : '';
};

// 模板选择函数
const selectTemplate = (templateValue: string) => {
  selectedTemplate.value = templateValue;
};

// 确认模板并开始绘制
const confirmTemplate = async () => {
  if (!selectedTemplate.value || isProcessing.value) return;
  
  isProcessing.value = true;
  
  // 发送到服务器消耗空白设计图
  try {
    event.emitServer('cloth:startDrawing', { template: selectedTemplate.value });
  } catch (error) {
    console.error('发送开始绘制事件失败:', error);
    showNotification('错误', '启动绘制失败，请重试');
    isProcessing.value = false;
  }
};

// 监听服务器响应
event.on('cloth:drawingStarted', (data: { success: boolean, message?: string }) => {
  isProcessing.value = false;
  
  if (data.success) {
    // 切换到绘画页面并加载模板
    currentPage.value = 'drawing';
    nextTick(() => {
      loadTemplate();
    });
  } else {
    showNotification('错误', data.message || '启动绘制失败');
  }
});

// Modal functions
const showNotification = (title: string, message: string) => {
  modalTitle.value = title;
  modalMessage.value = message;
  showModal.value = true;
};

const closeModal = () => {
  showModal.value = false;
};

// 自定义光标控制
const updateCursor = (e: MouseEvent) => {
  if (!canvasContainer.value) return;
  
  const container = canvasContainer.value;
  const rect = container.getBoundingClientRect();
  const x = e.clientX - rect.left;
  const y = e.clientY - rect.top;
  
  // 检查鼠标是否在画布区域内
  if (x >= 0 && x <= rect.width && y >= 0 && y <= rect.height) {
    // 使用相对于容器的坐标，因为伪元素现在是absolute定位
    container.style.setProperty('--cursor-x', `${x}px`);
    container.style.setProperty('--cursor-y', `${y}px`);
    container.style.setProperty('--cursor-visible', '1');
  } else {
    container.style.setProperty('--cursor-visible', '0');
  }
};

const hideCursor = () => {
  if (!canvasContainer.value) return;
  canvasContainer.value.style.setProperty('--cursor-visible', '0');
};

// Drawing functions
const startDrawing = (e: PointerEvent) => {
  if (!drawingCanvas.value || timeRemaining.value <= 0) return;
  
  isDrawing.value = true;
  const rect = drawingCanvas.value.getBoundingClientRect();
  const x = e.clientX - rect.left;
  const y = e.clientY - rect.top;
  
  currentPath.value = new Path2D();
  currentPath.value.moveTo(x, y);
  
  // Start new stroke tracking
  currentStroke.value = [{x, y, timestamp: Date.now()}];
  
  drawPoint(x, y);
};

const draw = (e: PointerEvent) => {
  if (!isDrawing.value || !drawingCanvas.value || !currentPath.value) return;
  
  const rect = drawingCanvas.value.getBoundingClientRect();
  const x = e.clientX - rect.left;
  const y = e.clientY - rect.top;
  
  currentPath.value.lineTo(x, y);
  
  // Record stroke path
  currentStroke.value.push({x, y, timestamp: Date.now()});
  
  drawPoint(x, y);
};

const stopDrawing = () => {
  if (isDrawing.value) {
    isDrawing.value = false;
    currentPath.value = undefined;
    
    // Save completed stroke
    if (currentStroke.value.length > 1) {
      strokePaths.value.push(...currentStroke.value);
    }
    currentStroke.value = [];
  }
};

const drawPoint = (x: number, y: number) => {
  if (!drawingCanvas.value) return;
  
  const ctx = drawingCanvas.value.getContext('2d');
  if (!ctx) return;
  
  // 动态笔刷大小：根据绘制速度调整
  const speed = calculateDrawingSpeed();
  const brushSize = Math.max(2, Math.min(8, 5 - speed * 2)); // 2-8px范围
  
  ctx.globalCompositeOperation = 'source-over';
  ctx.strokeStyle = '#000000';
  ctx.lineWidth = brushSize;
  ctx.lineCap = 'round';
  ctx.lineJoin = 'round';
  
  if (currentPath.value) {
    ctx.stroke(currentPath.value);
  }
  
  // Update user drawn pixels with dynamic brush size
  updateUserDrawnPixels(x, y, brushSize);
};

const calculateDrawingSpeed = (): number => {
  if (currentStroke.value.length < 2) return 0;
  
  const recent = currentStroke.value.slice(-2);
  const distance = Math.sqrt(
    Math.pow(recent[1].x - recent[0].x, 2) + 
    Math.pow(recent[1].y - recent[0].y, 2)
  );
  const timeDiff = recent[1].timestamp - recent[0].timestamp;
  
  return timeDiff > 0 ? distance / timeDiff : 0;
};

const updateUserDrawnPixels = (x: number, y: number, brushSize: number) => {
  const radius = Math.floor(brushSize / 2);
  
  for (let dy = -radius; dy <= radius; dy++) {
    for (let dx = -radius; dx <= radius; dx++) {
      const px = Math.floor(x + dx);
      const py = Math.floor(y + dy);
      
      if (px >= 0 && px < canvasWidth.value && py >= 0 && py < canvasHeight.value) {
        if (dx * dx + dy * dy <= radius * radius) {
          if (!userDrawnPixels.value[py]) {
            userDrawnPixels.value[py] = [];
          }
          userDrawnPixels.value[py][px] = true;
        }
      }
    }
  }
};

const completeDrawing = () => {
  console.log('[completeDrawing] 开始执行，当前模板：', selectedTemplate.value);

  // 防重复提交检查
  if (isSubmitting.value) {
    console.log('[completeDrawing] 正在提交中，忽略重复请求');
    return;
  }

  if (!selectedTemplate.value) {
    console.log('[completeDrawing] 未选择模板，终止');
    showNotification('错误', '请先选择模板');
    return;
  }

  // 设置提交状态，禁用按钮
  isSubmitting.value = true;
  console.log('[completeDrawing] 设置提交状态为true');

  // 发送前进行最终完整评分
  console.log('[completeDrawing] 调用 calculateCompletion()');
  calculateCompletion();

  // 发送到服务器，消耗空白设计图并转化为服装设计图
  const payload = {
    template: selectedTemplate.value,
    completion: completionRate.value,
  };
  console.log('[completeDrawing] 发送到服务器，payload：', payload);

  event.emitServer('cloth:saveDesign', payload);
  
  // 显示提交中的提示
  showNotification('提交中', '正在保存设计图，请稍候...');
};

const startTimer = () => {
  if (timerInterval.value) {
    clearInterval(timerInterval.value);
  }
  
  timerInterval.value = setInterval(() => {
    timeRemaining.value--;
    
    if (timeRemaining.value <= 0) {
      clearInterval(timerInterval.value);
      timerInterval.value = undefined;
      
      if (isDrawing.value) {
        stopDrawing();
      }
      
      // 时间到时自动发送设计
      if (!isSubmitting.value) {
        completeDrawing();
      }
      showNotification('时间到', '绘制已结束');
    }
  }, 1000);
};

const loadTemplate = async () => {
  if (!selectedTemplate.value || !templateCanvas.value) return;
  
  try {
    const img = new Image();
    img.crossOrigin = 'anonymous';
    
    await new Promise((resolve, reject) => {
      img.onload = resolve;
      img.onerror = reject;
      img.src = findIcon(selectedTemplate.value + '.webp');
    });
    
    templateImage.value = img;
    
    const ctx = templateCanvas.value.getContext('2d');
    if (ctx) {
      ctx.clearRect(0, 0, canvasWidth.value, canvasHeight.value);
      
      // 计算居中位置
      const scale = Math.min(canvasWidth.value / img.width, canvasHeight.value / img.height);
      const scaledWidth = img.width * scale;
      const scaledHeight = img.height * scale;
      const x = (canvasWidth.value - scaledWidth) / 2;
      const y = (canvasHeight.value - scaledHeight) / 2;
      
      // 绘制居中的模板图片
      ctx.drawImage(img, x, y, scaledWidth, scaledHeight);
      
      // Extract template mask
      extractTemplateMask();
    }
    
    // Clear previous drawing and start timer when template is loaded
    if (drawingCanvas.value) {
      const ctx = drawingCanvas.value.getContext('2d');
      if (ctx) {
        ctx.clearRect(0, 0, canvasWidth.value, canvasHeight.value);
      }
    }
    
    // Reset all tracking data
    userDrawnPixels.value = [];
    strokePaths.value = [];
    currentStroke.value = [];
    completionRate.value = 0;
    precisionScore.value = 0;
    finalScore.value = 0;
    isSubmitting.value = false; // 重置提交状态
    
    // Reset and start timer
    timeRemaining.value = timeLimit.value;
    if (timerInterval.value) {
      clearInterval(timerInterval.value);
      timerInterval.value = undefined;
    }
    startTimer();
    
  } catch (error) {
    console.error('Failed to load template:', error);
    showNotification('错误', '加载模板失败，请检查文件是否存在');
  }
};

const extractTemplateMask = () => {
  if (!templateCanvas.value) return;
  
  const ctx = templateCanvas.value.getContext('2d');
  if (!ctx) return;
  
  const imageData = ctx.getImageData(0, 0, canvasWidth.value, canvasHeight.value);
  const data = imageData.data;
  
  templateMask.value = [];
  
  for (let y = 0; y < canvasHeight.value; y++) {
    templateMask.value[y] = [];
    for (let x = 0; x < canvasWidth.value; x++) {
      const index = (y * canvasWidth.value + x) * 4;
      const alpha = data[index + 3];
      templateMask.value[y][x] = alpha > 0;
    }
  }
};

const calculateCompletion = () => {
  if (!templateMask.value.length || !userDrawnPixels.value.length) {
    completionRate.value = 0;
    precisionScore.value = 0;
    finalScore.value = 0;
    return;
  }
  
  // 一次遍历完成所有计算
  let templatePixelCount = 0;
  let matchedPixelCount = 0;
  let drawnPixelCount = 0;
  let templateMatchCount = 0;
  let edgePixelCount = 0;
  let edgeMatchCount = 0;
  
  for (let y = 1; y < canvasHeight.value - 1; y++) {
    for (let x = 1; x < canvasWidth.value - 1; x++) {
      const hasTemplate = templateMask.value[y] && templateMask.value[y][x];
      const hasDrawing = userDrawnPixels.value[y] && userDrawnPixels.value[y][x];
      
      // 计算基础覆盖度
      if (hasTemplate) {
        templatePixelCount++;
        if (hasDrawing) {
          matchedPixelCount++;
        }
      }
      
      // 计算绘制像素和整洁度
      if (hasDrawing) {
        drawnPixelCount++;
        
        // 检测是否在模板附近（简化版本，只检查邻近8个像素）
        const nearTemplate = hasTemplate || 
          (templateMask.value[y-1] && templateMask.value[y-1][x]) ||
          (templateMask.value[y+1] && templateMask.value[y+1][x]) ||
          (templateMask.value[y] && templateMask.value[y][x-1]) ||
          (templateMask.value[y] && templateMask.value[y][x+1]);
          
        if (nearTemplate) {
          templateMatchCount++;
        }
      }
      
      // 计算边缘质量（简化版本）
      if (hasTemplate) {
        const isEdge = !templateMask.value[y-1][x] || !templateMask.value[y+1][x] || 
                      !templateMask.value[y][x-1] || !templateMask.value[y][x+1];
        
        if (isEdge) {
          edgePixelCount++;
          if (hasDrawing) {
            edgeMatchCount++;
          }
        }
      }
    }
  }
  
  // 计算各项分数
  const baseCompletion = templatePixelCount > 0 ? (matchedPixelCount / templatePixelCount) * 100 : 0;
  completionRate.value = Math.round(baseCompletion);
  
  // 简化的精度计算
  const edgeScore = edgePixelCount > 0 ? (edgeMatchCount / edgePixelCount) * 100 : 0;
  const cleanlinessScore = drawnPixelCount > 0 ? (templateMatchCount / drawnPixelCount) * 100 : 100;
  const strokeQuality = calculateSimpleStrokeQuality();
  
  precisionScore.value = Math.round((edgeScore + strokeQuality + cleanlinessScore) / 3);
  
  // 简化的最终评分
  const coverageWeight = 0.4;
  const precisionWeight = 0.4;
  const bonusWeight = 0.2;
  
  const timeBonus = calculateSimpleTimeBonus();
  const bonus = timeBonus;
  
  const rawScore = (baseCompletion * coverageWeight) + (precisionScore.value * precisionWeight) + (bonus * bonusWeight);
  
  // 简化的反作弊检测
  let penalty = 0;
  const fillRatio = drawnPixelCount / (canvasWidth.value * canvasHeight.value);
  if (fillRatio > 0.2) {
    penalty = (fillRatio - 0.2) * 50;
  }
  
  finalScore.value = Math.max(0, Math.min(100, Math.round(rawScore - penalty)));
};

// 简化的笔触质量分析
const calculateSimpleStrokeQuality = (): number => {
  if (strokePaths.value.length < 10) return 20;
  
  // 简化版本：只检查笔触总长度和平均速度
  let totalLength = 0;
  let speedSum = 0;
  let speedCount = 0;
  
  for (let i = 1; i < Math.min(strokePaths.value.length, 100); i++) { // 只检查前100个点
    const p1 = strokePaths.value[i-1];
    const p2 = strokePaths.value[i];
    
    const distance = Math.sqrt(Math.pow(p2.x - p1.x, 2) + Math.pow(p2.y - p1.y, 2));
    totalLength += distance;
    
    const time = p2.timestamp - p1.timestamp;
    if (time > 0) {
      speedSum += distance / time;
      speedCount++;
    }
  }
  
  const avgSpeed = speedCount > 0 ? speedSum / speedCount : 0;
  const lengthScore = Math.min(100, totalLength / 5);
  const speedScore = Math.max(0, 100 - avgSpeed * 20);
  
  return (lengthScore * 0.7) + (speedScore * 0.3);
};

// 简化的时间奖励计算
const calculateSimpleTimeBonus = (): number => {
  const usedTime = timeLimit.value - timeRemaining.value;
  const efficiency = usedTime / timeLimit.value;
  
  // 简化：使用30-80%时间给最高奖励
  if (efficiency >= 0.3 && efficiency <= 0.8) {
    return 100;
  } else if (efficiency < 0.3) {
    return efficiency / 0.3 * 100;
  } else {
    return (1 - efficiency) / 0.2 * 100;
  }
};




// 检测指定位置附近是否有绘制
const checkNearbyDrawing = (x: number, y: number, radius: number): boolean => {
  for (let dy = -radius; dy <= radius; dy++) {
    for (let dx = -radius; dx <= radius; dx++) {
      const checkX = x + dx;
      const checkY = y + dy;
      
      if (checkX >= 0 && checkX < canvasWidth.value && 
          checkY >= 0 && checkY < canvasHeight.value) {
        if (userDrawnPixels.value[checkY] && userDrawnPixels.value[checkY][checkX]) {
          return true;
        }
      }
    }
  }
  return false;
};

// 笔触质量分析
const calculateStrokeQuality = (): number => {
  if (strokePaths.value.length < 10) return 20; // 太少的笔触给低分
  
  let totalLength = 0;
  let smoothnessScore = 0;
  let strokeCount = 0;
  
  // 分析每个笔触的流畅度
  for (let i = 0; i < strokePaths.value.length - 2; i++) {
    const p1 = strokePaths.value[i];
    const p2 = strokePaths.value[i + 1];
    const p3 = strokePaths.value[i + 2];
    
    // 计算角度变化
    const angle1 = Math.atan2(p2.y - p1.y, p2.x - p1.x);
    const angle2 = Math.atan2(p3.y - p2.y, p3.x - p2.x);
    const angleDiff = Math.abs(angle1 - angle2);
    
    // 流畅的线条角度变化应该较小
    smoothnessScore += Math.max(0, 1 - angleDiff / Math.PI);
    strokeCount++;
    
    // 计算总长度
    totalLength += Math.sqrt(Math.pow(p2.x - p1.x, 2) + Math.pow(p2.y - p1.y, 2));
  }
  
  const avgSmoothness = strokeCount > 0 ? smoothnessScore / strokeCount : 0;
  const lengthScore = Math.min(100, totalLength / 10); // 长度分数
  
  return (avgSmoothness * 100 * 0.7) + (lengthScore * 0.3);
};

// 整洁度检测 - 检测乱涂乱画
const calculateCleanliness = (): number => {
  let drawnPixelCount = 0;
  let templateMatchCount = 0;
  
  for (let y = 0; y < canvasHeight.value; y++) {
    for (let x = 0; x < canvasWidth.value; x++) {
      if (userDrawnPixels.value[y] && userDrawnPixels.value[y][x]) {
        drawnPixelCount++;
        
        // 检测绘制是否在模板区域内或附近
        if (isNearTemplate(x, y, 5)) {
          templateMatchCount++;
        }
      }
    }
  }
  
  return drawnPixelCount > 0 ? (templateMatchCount / drawnPixelCount) * 100 : 100;
};

// 检测是否靠近模板
const isNearTemplate = (x: number, y: number, radius: number): boolean => {
  for (let dy = -radius; dy <= radius; dy++) {
    for (let dx = -radius; dx <= radius; dx++) {
      const checkX = x + dx;
      const checkY = y + dy;
      
      if (checkX >= 0 && checkX < canvasWidth.value && 
          checkY >= 0 && checkY < canvasHeight.value) {
        if (templateMask.value[checkY] && templateMask.value[checkY][checkX]) {
          return true;
        }
      }
    }
  }
  return false;
};

// 时间奖励计算
const calculateTimeBonus = (): number => {
  const usedTime = timeLimit.value - timeRemaining.value;
  const efficiency = usedTime / timeLimit.value;
  
  // 使用30-80%时间给最高奖励
  if (efficiency >= 0.3 && efficiency <= 0.8) {
    return 100;
  } else if (efficiency < 0.3) {
    return efficiency / 0.3 * 100; // 太快扣分
  } else {
    return (1 - efficiency) / 0.2 * 100; // 太慢扣分
  }
};

// 一致性奖励
const calculateConsistencyBonus = (): number => {
  if (strokePaths.value.length < 20) return 50;
  
  // 检测绘制的一致性
  let consistencyScore = 0;
  let checks = 0;
  
  for (let i = 0; i < strokePaths.value.length - 5; i += 5) {
    const segment = strokePaths.value.slice(i, i + 5);
    const avgSpeed = calculateSegmentSpeed(segment);
    
    // 稳定的绘制速度得高分
    consistencyScore += Math.max(0, 100 - avgSpeed * 10);
    checks++;
  }
  
  return checks > 0 ? consistencyScore / checks : 50;
};

// 计算线段速度
const calculateSegmentSpeed = (segment: Array<{x: number, y: number, timestamp: number}>): number => {
  if (segment.length < 2) return 0;
  
  let totalDistance = 0;
  let totalTime = 0;
  
  for (let i = 1; i < segment.length; i++) {
    const distance = Math.sqrt(
      Math.pow(segment[i].x - segment[i-1].x, 2) + 
      Math.pow(segment[i].y - segment[i-1].y, 2)
    );
    const time = segment[i].timestamp - segment[i-1].timestamp;
    
    totalDistance += distance;
    totalTime += time;
  }
  
  return totalTime > 0 ? totalDistance / totalTime : 0;
};

// 反作弊检测
const detectCheating = (): number => {
  let penalty = 0;
  
  // 检测大面积填充作弊
  const largeAreaPenalty = detectLargeAreaFilling();
  penalty += largeAreaPenalty;
  
  // 检测过快绘制作弊
  const speedPenalty = detectSpeedCheating();
  penalty += speedPenalty;
  
  // 检测模板外绘制过多
  const outsidePenalty = detectOutsideDrawing();
  penalty += outsidePenalty;
  
  return Math.min(50, penalty); // 最多扣50分
};

// 检测大面积填充
const detectLargeAreaFilling = (): number => {
  let drawnPixelCount = 0;
  
  for (let y = 0; y < canvasHeight.value; y++) {
    for (let x = 0; x < canvasWidth.value; x++) {
      if (userDrawnPixels.value[y] && userDrawnPixels.value[y][x]) {
        drawnPixelCount++;
      }
    }
  }
  
  const totalPixels = canvasWidth.value * canvasHeight.value;
  const fillRatio = drawnPixelCount / totalPixels;
  
  // 如果填充超过20%的画布，开始扣分
  if (fillRatio > 0.2) {
    return (fillRatio - 0.2) * 100; // 每超过1%扣1分
  }
  
  return 0;
};

// 检测过快绘制
const detectSpeedCheating = (): number => {
  if (strokePaths.value.length < 10) return 0;
  
  let highSpeedCount = 0;
  
  for (let i = 1; i < strokePaths.value.length; i++) {
    const speed = calculatePointSpeed(strokePaths.value[i-1], strokePaths.value[i]);
    if (speed > 5) { // 速度阈值
      highSpeedCount++;
    }
  }
  
  const speedRatio = highSpeedCount / strokePaths.value.length;
  return speedRatio > 0.3 ? speedRatio * 30 : 0; // 超过30%高速点扣分
};

// 计算两点间速度
const calculatePointSpeed = (p1: {x: number, y: number, timestamp: number}, p2: {x: number, y: number, timestamp: number}): number => {
  const distance = Math.sqrt(Math.pow(p2.x - p1.x, 2) + Math.pow(p2.y - p1.y, 2));
  const time = p2.timestamp - p1.timestamp;
  return time > 0 ? distance / time : 0;
};

// 检测模板外过度绘制
const detectOutsideDrawing = (): number => {
  let outsideCount = 0;
  let totalCount = 0;
  
  for (let y = 0; y < canvasHeight.value; y++) {
    for (let x = 0; x < canvasWidth.value; x++) {
      if (userDrawnPixels.value[y] && userDrawnPixels.value[y][x]) {
        totalCount++;
        
        if (!isNearTemplate(x, y, 8)) {
          outsideCount++;
        }
      }
    }
  }
  
  const outsideRatio = totalCount > 0 ? outsideCount / totalCount : 0;
  return outsideRatio > 0.1 ? (outsideRatio - 0.1) * 50 : 0; // 超过10%模板外绘制扣分
};





onMounted(() => {
  // Set up canvas contexts when switching to drawing page
  nextTick(() => {
    if (currentPage.value === 'drawing' && drawingCanvas.value) {
      const ctx = drawingCanvas.value.getContext('2d');
      if (ctx) {
        ctx.lineCap = 'round';
        ctx.lineJoin = 'round';
      }
    }
  });
  
  // 清理计时器
  return () => {
    if (timerInterval.value) {
      clearInterval(timerInterval.value);
    }
  };
});
</script>

<style scoped>
/* 模板选择页面样式 */
.template-selection-container {
  display: flex;
  flex-direction: column;
  height: 90vh;
  width: 80vw;
  max-width: 1200px;
  background: #0f172a;
  color: #f8fafc;
  font-family: "Inter", "PingFang SC", "Microsoft YaHei", sans-serif;
  border-radius: 1.5vh;
  box-shadow: 0 1vh 3vh rgba(0, 0, 0, 0.3);
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 1000;
  border: 1px solid rgba(100, 255, 218, 0.15);
  padding: 2vh;
}

.selection-header {
  text-align: center;
  margin-bottom: 3vh;
  padding-bottom: 2vh;
  border-bottom: 1px solid rgba(100, 255, 218, 0.15);
}

.selection-header h1 {
  color: #64ffda;
  font-size: 2.5vh;
  margin: 0 0 1vh 0;
  font-weight: 600;
}

.template-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(12vw, 1fr));
  gap: 2.5vh;
  flex: 1;
  padding: 2vh 0;
  overflow-y: auto;
  max-height: 60vh;
  scrollbar-width: thin;
  scrollbar-color: #64ffda #1e293b;
}

.template-grid::-webkit-scrollbar {
  width: 0.8vw;
  background: #1e293b;
  border-radius: 0.5vh;
}

.template-grid::-webkit-scrollbar-thumb {
  background: linear-gradient(90deg, #64ffda 30%, #0ea5e9 100%);
  border-radius: 0.5vh;
  min-height: 2vh;
}

.template-grid::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(90deg, #0ea5e9 0%, #64ffda 100%);
}


.template-card {
  background: #1e293b;
  border: 0.2vh solid rgba(100, 255, 218, 0.1);
  border-radius: 1.2vh;
  padding: 2.5vh;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  min-height: 20vh;
  position: relative;
  overflow: hidden;
}

.template-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 0.4vh;
  background: linear-gradient(90deg, var(--icon-color), rgba(100, 255, 218, 0.3));
  opacity: 0;
  transition: opacity 0.3s ease;
}

.template-card:hover {
  border-color: rgba(100, 255, 218, 0.4);
  background: #334155;
  transform: translateY(-0.3vh);
  box-shadow: 0 0.8vh 2.5vh rgba(0, 0, 0, 0.15);
}

.template-card:hover::before {
  opacity: 1;
}

.template-card.selected {
  border-color: #64ffda;
  background: rgba(100, 255, 218, 0.08);
  box-shadow: 0 0 2vh rgba(100, 255, 218, 0.4);
  transform: translateY(-0.2vh);
}

.template-card.selected::before {
  opacity: 1;
  background: linear-gradient(90deg, #64ffda, var(--icon-color));
}

.template-icon {
  width: 8vw;
  height: 12vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(248, 250, 252, 0.05);
  border-radius: 50%;
  margin-bottom: 2vh;
  position: relative;
  transition: all 0.3s ease;
}

.template-icon::before {
  content: '';
  position: absolute;
  inset: -0.2vh;
  border-radius: 50%;
  background: linear-gradient(45deg, var(--icon-color), rgba(100, 255, 218, 0.3));
  opacity: 0.2;
  transition: opacity 0.3s ease;
}

.template-card:hover .template-icon::before,
.template-card.selected .template-icon::before {
  opacity: 0.4;
}

.template-icon i {
  font-size: 2.5rem;
  color: var(--icon-color);
  position: relative;
  z-index: 1;
  transition: all 0.3s ease;
}

.template-card:hover .template-icon i {
  transform: scale(1.1);
  filter: brightness(1.2);
}

.template-card.selected .template-icon i {
  color: #64ffda;
  transform: scale(1.05);
}

.template-info h3 {
  color: #64ffda;
  font-size: 1.8vh;
  margin: 0 0 0.8vh 0;
  font-weight: 600;
  transition: color 0.3s ease;
}

.template-card:hover .template-info h3 {
  color: #ffffff;
}

.template-card.selected .template-info h3 {
  color: #64ffda;
  font-weight: 700;
}

.template-info p {
  color: rgba(248, 250, 252, 0.65);
  font-size: 1.3vh;
  margin: 0;
  line-height: 1.5;
  transition: color 0.3s ease;
}

.template-card:hover .template-info p {
  color: rgba(248, 250, 252, 0.85);
}

.template-card.selected .template-info p {
  color: rgba(100, 255, 218, 0.8);
}

.selection-actions {
  display: flex;
  justify-content: center;
  padding-top: 2.5vh;
  border-top: 0.1vh solid rgba(100, 255, 218, 0.15);
  margin-top: 1vh;
}

.selection-actions .btn {
  padding: 1.2vh 2.5vw;
  font-size: 1.4vh;
  border-radius: 0.8vh;
  min-width: 12vw;
  font-weight: 600;
  letter-spacing: 0.05vw;
}

.selection-actions .btn-primary {
  background: linear-gradient(135deg, #64ffda, #00bcd4);
  border: none;
  color: #0f172a;
  box-shadow: 0 0.4vh 1.5vh rgba(100, 255, 218, 0.3);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.selection-actions .btn-primary:hover:not(:disabled) {
  background: linear-gradient(135deg, #ffffff, #64ffda);
  transform: translateY(-0.2vh);
  box-shadow: 0 0.8vh 2.5vh rgba(100, 255, 218, 0.4);
}

.selection-actions .btn-primary:disabled {
  background: rgba(100, 255, 218, 0.2);
  color: rgba(15, 23, 42, 0.5);
  box-shadow: none;
  cursor: not-allowed;
}

.drawing-actions {
  display: flex;
  justify-content: center;
  padding: 2vh;
  background: #1e293b;
  border-top: 1px solid rgba(100, 255, 218, 0.1);
}

.drawing-container {
  display: flex;
  flex-direction: column;
  height: 90vh;
  width: 80vw;
  max-width: 1200px;
  background: #0f172a;
  color: #f8fafc;
  font-family: "Inter", "PingFang SC", "Microsoft YaHei", sans-serif;
  border-radius: 1.5vh;
  box-shadow: 0 1vh 3vh rgba(0, 0, 0, 0.3);
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 1000;
  border: 1px solid rgba(100, 255, 218, 0.15);
}

.drawing-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5vh 2vw;
  background: rgba(100, 255, 218, 0.1);
  border-bottom: 1px solid rgba(100, 255, 218, 0.15);
  flex-shrink: 0;
}

.header-info h1 {
  color: #64ffda;
  font-size: 2vh;
  margin: 0;
  font-weight: 600;
}

.subtitle {
  color: rgba(100, 255, 218, 0.7);
  font-size: 1vh;
}

.timer-info {
  display: flex;
  align-items: center;
}

.time-text {
  font-size: 1.4vh;
  font-weight: 600;
  color: #f59e0b;
  padding: 0.5vh 1vh;
  background: rgba(245, 158, 11, 0.1);
  border-radius: 0.5vh;
  border: 1px solid rgba(245, 158, 11, 0.3);
}

.time-display {
  font-size: 1.3vh;
  font-weight: 500;
  color: #f59e0b;
  min-width: 8vw;
  padding: 0.5vh 1vh;
  background: rgba(245, 158, 11, 0.1);
  border-radius: 0.4vh;
  border: 1px solid rgba(245, 158, 11, 0.2);
}

/* 进度条样式 */
.time-progress-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5vh;
  min-width: 12vw;
}

.time-progress-bar {
  width: 100%;
  height: 0.8vh;
  background: rgba(245, 158, 11, 0.2);
  border-radius: 0.4vh;
  overflow: hidden;
  border: 1px solid rgba(245, 158, 11, 0.3);
}

.time-progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #f59e0b, #fbbf24);
  border-radius: 0.3vh;
  transition: width 0.3s ease;
  box-shadow: 0 0 0.3vh rgba(245, 158, 11, 0.5);
}

.time-text {
  font-size: 1.1vh;
  font-weight: 500;
  color: #f59e0b;
  text-align: center;
}

.completion-info {
  display: flex;
  align-items: center;
  gap: 1vw;
}



/* 紧凑控制栏 */
.control-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1vh 2vw;
  background: #1e293b;
  border-bottom: 1px solid rgba(100, 255, 218, 0.1);
  gap: 2vw;
  flex-shrink: 0;
}

.control-section {
  display: flex;
  align-items: center;
  gap: 1vw;
}

.template-label {
  font-size: 1.1vh;
  color: #cbd5e1;
  min-width: 4vw;
  font-weight: 500;
}

.template-select.compact {
  padding: 0.8vh 1vw;
  background: #334155;
  border: 1px solid rgba(100, 255, 218, 0.2);
  border-radius: 0.4vh;
  color: #f8fafc;
  font-size: 1.1vh;
  min-width: 8vw;
  transition: all 0.3s ease;
}

.template-select.compact:disabled {
  background: rgba(51, 65, 85, 0.5);
  border-color: rgba(100, 255, 218, 0.1);
  color: rgba(248, 250, 252, 0.5);
  cursor: not-allowed;
}

.lock-hint {
  font-size: 1vh;
  color: rgba(100, 255, 218, 0.7);
  margin-left: 1vw;
  display: flex;
  align-items: center;
  gap: 0.3vw;
}


.action-buttons.compact {
  display: flex;
  gap: 1vw;
}

.btn.compact {
  padding: 0.8vh 1.2vw;
  font-size: 1.1vh;
  border-radius: 0.4vh;
}

/* 主绘图区域 */
.main-canvas-area {
  flex: 1;
  display: flex;
  padding: 1vh;
  justify-content: center;
  align-items: center;
}


.canvas-container {
  position: relative;
  border: 1px solid rgba(100, 255, 218, 0.2);
  border-radius: 1vh;
  overflow: hidden;
  width: 50vw;
  max-width: 500px;
  height: 70vh;
  max-height: 750px;
  display: flex;
  justify-content: center;
  align-items: center;
  background: #f8fafc;
  cursor: none;
  box-shadow: 0 0.5vh 1vh rgba(0, 0, 0, 0.2);
}

.canvas-container {
  --cursor-x: 0px;
  --cursor-y: 0px;
  --cursor-visible: 0;
}

.canvas-container::after {
  content: '';
  position: absolute;
  width: 8px;
  height: 8px;
  background: rgba(0, 0, 0, 0.6);
  border-radius: 50%;
  pointer-events: none;
  z-index: 1001;
  left: var(--cursor-x);
  top: var(--cursor-y);
  transform: translate(-50%, -50%);
  opacity: var(--cursor-visible);
  transition: opacity 0.1s ease;
  will-change: left, top;
}

.template-canvas {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  opacity: 0.3;
  pointer-events: none;
}

.drawing-canvas {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  cursor: none;
  background: transparent;
}


.btn {
  padding: 1vh 1.5vw;
  border: none;
  border-radius: 0.6vh;
  font-size: 1.2vh;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5vw;
  min-height: 3vh;
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}





.btn-complete {
  background: rgba(34, 197, 94, 0.15);
  color: #22c55e;
  border: 1px solid rgba(34, 197, 94, 0.2);
}

.btn-complete:hover:not(:disabled) {
  background: rgba(34, 197, 94, 0.25);
  border-color: rgba(34, 197, 94, 0.4);
  transform: translateY(-1px);
}

.btn-primary {
  background: #64ffda;
  color: #0f172a;
  border: 1px solid #64ffda;
  font-weight: 600;
}

.btn-primary:hover:not(:disabled) {
  background: #ffffff;
  color: #64ffda;
  transform: translateY(-1px);
}

/* 模态框样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  background: none;
}

.modal-content {
  background: #1e293b;
  border: 1px solid rgba(100, 255, 218, 0.2);
  border-radius: 1vh;
  width: 25vw;
  max-width: 350px;
  max-height: 70vh;
  overflow: hidden;
  box-shadow: 0 1vh 3vh rgba(0, 0, 0, 0.5);
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5vh 2vw;
  border-bottom: 1px solid rgba(100, 255, 218, 0.15);
}

.modal-header h3 {
  color: #64ffda;
  margin: 0;
  font-size: 1.6vh;
  font-weight: 600;
}

.modal-close {
  background: none;
  border: none;
  color: rgba(255, 255, 255, 0.7);
  font-size: 2vh;
  cursor: pointer;
  padding: 0;
  width: 2.5vh;
  height: 2.5vh;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.2s ease;
}

.modal-close:hover {
  background: rgba(255, 255, 255, 0.1);
  color: #f8fafc;
}

.modal-body {
  padding: 1.5vh 2vw;
  color: #f8fafc;
  line-height: 1.4;
  font-size: 1.2vh;
}

.modal-footer {
  padding: 1.5vh 2vw;
  border-top: 1px solid rgba(100, 255, 218, 0.15);
  display: flex;
  justify-content: flex-end;
}

</style>