import fs from 'fs';
import path from 'path';
import { exec } from 'child_process';

// 构建性能监控工具
const STATS_DIR = './build-stats';
const BUILD_STATS_FILE = path.join(STATS_DIR, 'build-stats.json');

// 确保统计目录存在
if (!fs.existsSync(STATS_DIR)) {
    fs.mkdirSync(STATS_DIR, { recursive: true });
}

// 初始化统计文件
if (!fs.existsSync(BUILD_STATS_FILE)) {
    fs.writeFileSync(BUILD_STATS_FILE, JSON.stringify({
        builds: []
    }));
}

// 读取构建统计
function readBuildStats() {
    try {
        return JSON.parse(fs.readFileSync(BUILD_STATS_FILE, 'utf-8'));
    } catch (err) {
        console.error(`Error reading build stats: ${err.message}`);
        return { builds: [] };
    }
}

// 保存构建统计
function saveBuildStats(stats) {
    fs.writeFileSync(BUILD_STATS_FILE, JSON.stringify(stats, null, 2));
}

// 记录构建信息
function recordBuild(type, startTime, endTime, buildData) {
    const stats = readBuildStats();
    
    stats.builds.push({
        timestamp: new Date().toISOString(),
        type,
        duration: endTime - startTime,
        details: buildData || {}
    });
    
    // 保留最近100条记录
    if (stats.builds.length > 100) {
        stats.builds = stats.builds.slice(-100);
    }
    
    saveBuildStats(stats);
}

// 显示构建统计
function showBuildStats() {
    const stats = readBuildStats();
    
    if (stats.builds.length === 0) {
        console.log('没有构建记录');
        return;
    }
    
    // 计算平均构建时间
    const totalTime = stats.builds.reduce((sum, build) => sum + build.duration, 0);
    const avgTime = totalTime / stats.builds.length;
    
    // 按类型分组统计
    const typeStats = {};
    stats.builds.forEach(build => {
        if (!typeStats[build.type]) {
            typeStats[build.type] = {
                count: 0,
                totalTime: 0,
                tsFileCount: 0,
                vueFileCount: 0
            };
        }
        
        typeStats[build.type].count++;
        typeStats[build.type].totalTime += build.duration;
        
        // 统计文件数量
        if (build.details) {
            typeStats[build.type].tsFileCount += build.details.tsFiles || 0;
            typeStats[build.type].vueFileCount += build.details.vueFiles || 0;
        }
    });
    
    // 打印统计信息
    console.log('\n===== 构建统计 =====');
    console.log(`总构建次数: ${stats.builds.length}`);
    console.log(`平均构建时间: ${avgTime.toFixed(2)}ms`);
    console.log('\n按类型统计:');
    
    Object.entries(typeStats).forEach(([type, typeStat]) => {
        console.log(`\n  ${type}:`);
        console.log(`    次数: ${typeStat.count}`);
        console.log(`    平均时间: ${(typeStat.totalTime / typeStat.count).toFixed(2)}ms`);
        
        if (typeStat.tsFileCount > 0 || typeStat.vueFileCount > 0) {
            console.log(`    平均处理TS文件: ${(typeStat.tsFileCount / typeStat.count).toFixed(2)}个`);
            console.log(`    平均处理Vue文件: ${(typeStat.vueFileCount / typeStat.count).toFixed(2)}个`);
        }
    });
    
    // 最近5次构建
    console.log('\n最近构建:');
    stats.builds.slice(-5).reverse().forEach((build, index) => {
        const date = build.timestamp.split('T')[0];
        const time = build.timestamp.split('T')[1].split('.')[0];
        const files = [];
        
        if (build.details.tsFiles) {
            files.push(`TS: ${build.details.tsFiles}`);
        }
        
        if (build.details.vueFiles) {
            files.push(`Vue: ${build.details.vueFiles}`);
        }
        
        const fileInfo = files.length > 0 ? ` (${files.join(', ')})` : '';
        
        console.log(`  ${index + 1}. ${date} ${time} - ${build.type} - ${build.duration}ms${fileInfo}`);
    });
    
    // 显示性能提示
    if (stats.builds.length >= 5) {
        const recentBuilds = stats.builds.slice(-5);
        const avgRecentTime = recentBuilds.reduce((sum, build) => sum + build.duration, 0) / recentBuilds.length;
        
        const incremental = recentBuilds.filter(b => b.type === '增量构建');
        const force = recentBuilds.filter(b => b.type === '全量构建');
        
        if (incremental.length > 0 && force.length > 0) {
            const avgIncremental = incremental.reduce((sum, build) => sum + build.duration, 0) / incremental.length;
            const avgForce = force.reduce((sum, build) => sum + build.duration, 0) / force.length;
            
            console.log('\n性能分析:');
            console.log(`  最近全量构建平均时间: ${avgForce.toFixed(2)}ms`);
            console.log(`  最近增量构建平均时间: ${avgIncremental.toFixed(2)}ms`);
            console.log(`  增量编译节省时间: ${((avgForce - avgIncremental) / avgForce * 100).toFixed(2)}%`);
            
            if (avgIncremental > avgForce * 0.8) {
                console.log('\n⚠️ 增量构建优化不明显，可能需要进一步调整');
            } else {
                console.log('\n✅ 增量构建优化效果显著');
            }
        }
    }
}

// 从构建输出中解析信息
function parseBuildOutput(output) {
    const buildInfo = {
        tsFiles: 0,
        vueFiles: 0,
        changedFiles: 0
    };
    
    // 解析变更的文件数量
    const changedFilesMatch = output.match(/File changed: (.*)/g);
    if (changedFilesMatch) {
        buildInfo.changedFiles = changedFilesMatch.length;
    }
    
    // 解析处理的TS文件
    const tsFilesMatch = output.match(/增量编译 (\d+) 个 TypeScript 文件/);
    if (tsFilesMatch && tsFilesMatch[1]) {
        buildInfo.tsFiles = parseInt(tsFilesMatch[1], 10);
    }
    
    // 检查是否重新构建了webview
    if (output.includes('检测到 Vue 文件变更')) {
        buildInfo.vueFiles = 1; // 只记录重建了webview
    }
    
    return buildInfo;
}

// 启动并监控构建过程
async function monitorBuild(type, command) {
    const startTime = Date.now();
    let buildInfo = {};
    
    try {
        // 执行构建命令
        const output = await new Promise((resolve, reject) => {
            exec(command, (error, stdout, stderr) => {
                if (error) {
                    reject(error);
                    return;
                }
                resolve(stdout);
            });
        });
        
        // 解析构建输出
        buildInfo = parseBuildOutput(output);
        
        console.log(output);
    } catch (err) {
        console.error(`构建失败: ${err.message}`);
        return;
    }
    
    const endTime = Date.now();
    const duration = endTime - startTime;
    
    // 记录构建统计
    recordBuild(type, startTime, endTime, buildInfo);
    
    // 显示构建信息摘要
    console.log(`\n构建完成!`);
    console.log(`类型: ${type}`);
    console.log(`耗时: ${duration}ms`);
    
    if (buildInfo.changedFiles > 0) {
        console.log(`变更文件: ${buildInfo.changedFiles}个`);
    }
    
    if (buildInfo.tsFiles > 0) {
        console.log(`处理TS文件: ${buildInfo.tsFiles}个`);
    }
    
    if (buildInfo.vueFiles > 0) {
        console.log(`重新构建webview`);
    }
}

// 命令行参数解析
const args = process.argv.slice(2);
const command = args[0];

if (command === 'stats') {
    // 显示构建统计
    showBuildStats();
} else if (command === 'incremental') {
    // 增量构建
    monitorBuild('增量构建', 'node ./scripts/compile.js');
} else if (command === 'force') {
    // 强制全量构建
    monitorBuild('全量构建', 'node ./scripts/compile.js --force');
} else if (command === 'clean') {
    // 清理缓存并重新构建
    monitorBuild('清理构建', 'pnpm run build:clean');
} else {
    console.log('使用方式:');
    console.log('  node ./scripts/buildMonitor.js stats - 显示构建统计');
    console.log('  node ./scripts/buildMonitor.js incremental - 执行增量构建');
    console.log('  node ./scripts/buildMonitor.js force - 执行强制全量构建');
    console.log('  node ./scripts/buildMonitor.js clean - 清理缓存并重新构建');
} 