# 车库载具类型限制功能实现

## 功能概述

实现了车库载具类型限制功能，确保不同类型的车库只能存取对应类型的载具。例如：
- 车库只能存取汽车
- 船库只能存取船只
- 摩托车库只能存取摩托车等

## 实现细节

### 1. 载具类型映射 (`shared/vehicleTypes.ts`)

创建了完整的GTA V载具类别到车库类型的映射：

```typescript
// 支持的车库载具类型
export enum GarageVehicleType {
    CAR = 'CAR',       // 汽车
    BIKE = 'BIKE',     // 摩托车和自行车
    SHIP = 'SHIP',     // 船只
    HELI = 'HELI',     // 直升机
    PLANE = 'PLANE',   // 飞机
    TRAIN = 'TRAIN'    // 火车
}

// GTA V载具类别映射 (0-21)
export const VEHICLE_CLASS_TO_GARAGE_TYPE: Record<number, GarageVehicleType>
```

### 2. 客户端实现 (`client/index.ts`)

添加了两个RPC调用：

- `garage:getVehicleGarageType` - 获取载具的车库类型
- `garage:canStoreVehicleType` - 检查载具是否可以存入指定车库类型

使用GTA V原生函数 `native.getVehicleClass()` 获取载具类别。

### 3. 服务端验证 (`server/index.ts`)

在以下操作中添加了载具类型验证：

#### 取车验证 (`garage:spawn`)
- 生成载具后检查类型是否匹配当前车库
- 如果不匹配，销毁载具并提示错误

#### 存车验证 (`garage:despawn`)
- 存车前检查载具类型是否匹配当前车库
- 如果不匹配，拒绝存车并提示错误

#### 快速存车验证 (`garage:quickstore`)
- 快速存车前检查载具类型是否匹配目标车库
- 如果不匹配，拒绝存车并提示错误

#### 跨车库调车验证 (`garage:transfer`)
- 调车前检查载具类型是否匹配目标车库
- 使用临时载具进行类型检测，避免实际生成
- 如果不匹配，拒绝调车并提示错误

## 配置示例

### 现有车库配置

```typescript
// 汽车车库
{
    name: '市中心车库',
    types: ['CAR'],  // 只允许汽车
    // ...
}

// 船库
{
    name: '船库',
    types: ['SHIP'], // 只允许船只
    // ...
}
```

### 可扩展配置

可以轻松添加新的车库类型：

```typescript
// 摩托车车库
{
    name: '摩托车库',
    types: ['BIKE'],
    // ...
}

// 综合车库（支持多种类型）
{
    name: '综合车库',
    types: ['CAR', 'BIKE'],
    // ...
}

// 机场（支持飞行器）
{
    name: '机场车库',
    types: ['HELI', 'PLANE'],
    // ...
}
```

## 错误处理

### 向后兼容性
- 所有类型验证都包含在try-catch块中
- 如果RPC调用失败，系统会继续执行（向后兼容）
- 保留了旧版本的`garage:checkvehicletype`RPC调用

### 用户友好的错误提示
- 使用中文载具类型名称进行提示
- 明确指出哪种类型的载具不能存入当前车库
- 例如："此车库不支持船只类型载具"

## 载具类型对应关系

| GTA V载具类别 | 载具类型 | 车库类型 | 示例 |
|--------------|---------|---------|------|
| 0-7, 9-12, 17-20 | 各种汽车 | CAR | 轿车、SUV、跑车等 |
| 8, 13 | 摩托车/自行车 | BIKE | 摩托车、自行车 |
| 14 | 船只 | SHIP | 快艇、游艇 |
| 15 | 直升机 | HELI | 直升机 |
| 16 | 飞机 | PLANE | 飞机 |
| 21 | 火车 | TRAIN | 火车 |

## 安全性

### 双重验证
- 客户端进行初步检查（性能优化）
- 服务端进行最终验证（安全保证）

### 临时载具处理
- 跨车库调车使用临时载具进行类型检测
- 确保临时载具在检测后立即销毁
- 避免内存泄漏和性能问题

## 测试建议

1. **基本功能测试**
   - 汽车存入车库：✅ 应该成功
   - 船只存入车库：❌ 应该失败
   - 船只存入船库：✅ 应该成功

2. **跨车库调车测试**
   - 汽车从车库调到船库：❌ 应该失败
   - 船只从船库调到车库：❌ 应该失败
   - 汽车从车库调到车库：✅ 应该成功

3. **边界情况测试**
   - RPC调用失败时的向后兼容
   - 临时载具的正确销毁
   - 错误提示的正确显示

## 未来扩展

1. **更多载具类型**
   - 可以轻松添加新的载具类型支持
   - 例如：特殊载具、武装载具等

2. **权限系统**
   - 可以基于玩家权限限制某些载具类型的使用
   - 例如：只有飞行员可以使用飞机车库

3. **动态配置**
   - 可以添加运行时修改车库类型限制的功能
   - 管理员命令动态调整车库配置