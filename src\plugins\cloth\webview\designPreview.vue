<template>
  <div :class="['preview-container', { 'dragging': isDragging }]">
    <button :class="['toggle-btn', { 'closed': !isMenuOpen }]" @click="toggleMenu">
      <i :class="['fas', isMenuOpen ? 'fa-chevron-right' : 'fa-chevron-left']"></i>
    </button>

    <div :class="['preview-panel', { 'closed': !isMenuOpen }]">
      <div class="preview-content">
        <div class="preview-header">
          <div class="header-icon">
            <i class="fas fa-drafting-compass"></i>
          </div>
          <div class="title-info">
            <h1>设计图预览</h1>
            <span class="subtitle">Design Blueprint Preview</span>
            <div class="character-info">
              <i :class="characterSex === 0 ? 'fas fa-venus' : 'fas fa-mars'"></i>
              <span>{{ characterSex === 0 ? '女性角色' : '男性角色' }}</span>
            </div>
          </div>
          <button @click="closePreview" class="close-btn">
            <i class="fas fa-times"></i>
          </button>
        </div>

        <div class="search-section">
          <div class="search-box">
            <i class="fas fa-search"></i>
            <input v-model="searchTerm" placeholder="搜索设计图..." class="styled-input" />
          </div>

          <!-- 分类筛选 -->
          <div class="category-filter">
            <div class="filter-header">
              <i class="fas fa-filter"></i>
              <span>服装分类</span>
            </div>
            <div class="category-buttons">
              <button 
                v-for="category in availableCategories" 
                :key="category.key"
                @click="setTypeFilter(category.key)"
                :class="['category-btn', { 'active': currentTypeFilter === category.key }]"
                :title="`筛选${category.name}`">
                <i :class="getCategoryIcon(category.key)"></i>
                <span>{{ category.name }}</span>
              </button>
            </div>
          </div>

          <!-- 旋转提示 -->
          <div class="rotation-hint">
            <i class="fas fa-hand-paper"></i>
            <span>按住右键拖拽可旋转角色</span>
            <div class="rotation-indicator">
              <span class="rotation-angle">{{ Math.round(rotationY) }}°</span>
            </div>
            <button @click="resetRotation" class="reset-rotation-btn" title="重置角色朝向">
              <i class="fas fa-undo"></i>
            </button>
          </div>

          <!-- 视角控制 -->
          <div class="camera-control">
            <div class="camera-header">
              <i class="fas fa-video"></i>
              <span>视角控制</span>
              <button @click="toggleAutoCamera" :class="['auto-toggle', { 'active': isAutoCamera }]">
                <i :class="isAutoCamera ? 'fas fa-eye' : 'fas fa-eye-slash'"></i>
                <span>{{ isAutoCamera ? '自动' : '手动' }}</span>
              </button>
            </div>
            <div class="camera-buttons" v-show="!isAutoCamera">
              <button 
                v-for="(position, viewName) in CAMERA_POSITIONS" 
                :key="viewName"
                v-show="viewName === 'overview' || (viewName === 'body') || hasItemsInCategory(viewName)"
                @click="setManualCameraPosition(viewName)"
                :class="['camera-btn', { 'active': currentFocusedView === viewName }]"
                :title="`查看${getViewName(viewName)}`">
                <i :class="getCameraIcon(viewName)"></i>
                <span>{{ getViewName(viewName) }}</span>
              </button>
            </div>
          </div>
        </div>

        <!-- 订单管理界面 -->
        <div v-if="currentMode === 'orderManagement' && currentOrder" class="order-management">
          <div class="order-header">
            <h2>服装订单管理</h2>
            <div class="order-info">
              <span class="order-type">{{ currentOrder.typeName }}</span>
              <!-- 移除材料价值显示 -->
            </div>
            <div v-if="currentOrder.blueprintFinish" class="completion-info">
              <span class="completion-rate">设计完成度: {{ currentOrder.blueprintFinish }}%</span>
              <span v-if="currentOrder.blueprintFinish >= 90" class="design-quality excellent">
                大师级设计
              </span>
              <span v-else-if="currentOrder.blueprintFinish >= 80" class="design-quality great">
                优秀设计
              </span>
              <span v-else-if="currentOrder.blueprintFinish > 75" class="design-quality good">
                良好设计
              </span>
              <span v-else-if="currentOrder.blueprintFinish === 75" class="design-quality normal">
                标准设计
              </span>
              <span v-else-if="currentOrder.blueprintFinish >= 65" class="design-quality fair">
                一般设计
              </span>
              <span v-else-if="currentOrder.blueprintFinish >= 55" class="design-quality poor">
                粗糙设计
              </span>
              <span v-else class="design-quality bad">
                简陋设计
              </span>
            </div>
          </div>

          <!-- 预览订单服装按钮 -->
          <div class="order-preview-section">
            <button @click="previewOrderCloth" class="action-btn preview">
              <div class="btn-icon">
                <i class="fas fa-eye"></i>
              </div>
              <span>预览订单服装</span>
            </button>
          </div>
          
          <div class="materials-section">
            <h3>所需材料</h3>
            <div class="quick-submit-section" v-if="canSubmitAllMaterials">
              <button @click="submitAllMaterials" :disabled="submittingAll" class="quick-submit-btn">
                <i class="fas fa-rocket"></i>
                <span>{{ submittingAll ? '提交中...' : '一键添加所有材料' }}</span>
              </button>
            </div>
            <div class="materials-grid">
              <div v-for="(required, material) in currentOrder.materials" :key="String(material)" class="material-item">
                <div class="material-info">
                  <span class="material-name">{{ material }}</span>
                  <span class="material-progress">{{ currentOrder.materialsSubmitted[material] || 0 }}/{{ required }}</span>
                </div>
                <div class="material-controls">
                  <div class="progress-bar">
                    <div class="progress-fill" :style="{ width: ((currentOrder.materialsSubmitted[material] || 0) / required * 100) + '%' }"></div>
                  </div>
                  <div class="submit-controls" v-if="(currentOrder.materialsSubmitted[material] || 0) < required">
                    <span class="player-amount">拥有: {{ currentOrder.playerMaterials[material] || 0 }}</span>
                    <input 
                      type="number" 
                      :max="Math.min(currentOrder.playerMaterials[material] || 0, required - (currentOrder.materialsSubmitted[material] || 0))"
                      min="1"
                      v-model.number="materialSubmissionAmounts[material]"
                      class="amount-input"
                    />
                    <button @click="submitMaterial(String(material))" class="submit-btn" :disabled="!canSubmitMaterial(String(material))">
                      <i class="fas fa-plus"></i>
                      提交
                    </button>
                  </div>
                  <span v-else class="completed-mark">
                    <i class="fas fa-check"></i>
                    已完成
                  </span>
                </div>
              </div>
            </div>
          </div>
          
          <div class="order-actions">
            <button @click="cancelOrder" class="action-btn cancel">
              <div class="btn-icon">
                <i class="fas fa-times"></i>
              </div>
              <span>取消订单</span>
            </button>
            <button @click="refreshOrderData" class="action-btn refresh">
              <div class="btn-icon">
                <i class="fas fa-sync-alt"></i>
              </div>
              <span>刷新</span>
            </button>
            <button @click="completeOrder" class="action-btn complete" :disabled="!isOrderComplete">
              <div class="btn-icon">
                <i class="fas fa-check-circle"></i>
              </div>
              <span>完成订单</span>
            </button>
          </div>
        </div>

        <!-- 普通预览模式：只有在没有订单时才显示 -->
        <div v-if="currentMode === 'preview' || currentMode === 'order'" class="normal-preview">
          <!-- 原有的设计图网格和功能 -->
          <div class="blueprint-grid">
            <div v-for="(blueprint, index) in filteredBlueprints" :key="`${blueprint.slot}-${blueprint.type}`"
              :class="['blueprint-card', { 'selected': selectedIndex === index, 'no-component': !blueprint.component }]" 
              @click="selectBlueprint(index)">
              <div class="card-preview">
                <div class="preview-icon">
                  <i :class="getCategoryIcon(blueprint.type)"></i>
                </div>
                <div class="blueprint-info">
                  <div class="blueprint-name">{{ blueprint.customname }}</div>
                  <div class="blueprint-details">
                    <span class="type-info">{{ blueprint.typeName }}</span>
                    <span class="finish-info">完成度: {{ blueprint.finish }}%</span>
                    <span v-if="blueprint.component" class="component-info">含服装组件</span>
                    <span v-else class="no-component-info">无组件信息</span>
                  </div>
                </div>
              </div>
              
              <!-- 完成度条 -->
              <div class="finish-bar">
                <div class="finish-progress" :style="{ width: blueprint.finish + '%' }"></div>
              </div>
              
              <!-- 选择指示器 -->
              <div class="selection-indicator" v-if="selectedIndex === index">
                <i class="fas fa-check"></i>
              </div>
            </div>
          </div>

          <div class="preview-footer">
            <div class="blueprint-stats" v-if="blueprints.length > 0">
              <span class="stats-item">
                <i class="fas fa-clipboard-list"></i>
                共 {{ blueprints.length }} 张设计图
              </span>
              <span class="stats-item">
                <i class="fas fa-cogs"></i>
                {{ blueprintsWithComponent }} 张含组件
              </span>
            </div>
            
            <div class="action-buttons">
              <button @click="closePreview" class="action-btn cancel">
                <div class="btn-icon">
                  <i class="fas fa-times"></i>
                </div>
                <span>关闭</span>
              </button>
              <button @click="previewBlueprint" class="action-btn preview" :disabled="selectedIndex === -1 || !selectedBlueprint?.component">
                <div class="btn-icon">
                  <i class="fas fa-eye"></i>
                </div>
                <span>预览外观</span>
              </button>
              <!-- 订单相关按钮 -->
              <button v-if="showOrderButton" @click="placeOrder" class="action-btn order" :disabled="selectedIndex === -1 || !selectedBlueprint?.component">
                <div class="btn-icon">
                  <i class="fas fa-shopping-cart"></i>
                </div>
                <span>下订单</span>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue';
import { useEvents } from '@Composables/useEvents.js';

interface BlueprintData {
  slot: number;
  type: string;
  typeName: string;
  finish: number;
  component: {
    dlc: string | number;
    drawable: number;
    texture: number;
    sex: number;
    id: number;
    isProp: boolean;
  } | null;
  customname: string;
}

const event = useEvents();

// 设计图数据
const blueprints = ref<BlueprintData[]>([]);
const selectedIndex = ref(-1);
const searchTerm = ref('');
const currentTypeFilter = ref('all');
const characterSex = ref(0); // 角色性别，0为女性，1为男性

// 菜单控制
const isMenuOpen = ref(true);

// 旋转控制
const isDragging = ref(false);
const lastMouseX = ref(0);
const rotationY = ref(0);

// 视角控制
const currentFocusedView = ref('overview');
const isAutoCamera = ref(true); // 新增：自动视角控制

// 服装分类图标映射
const categoryIcons = {
  'tops': 'fas fa-tshirt',
  'legs': 'fas fa-socks',
  'shoes': 'fas fa-shoe-prints',
  'under': 'fas fa-vest',
  'hat': 'fas fa-hat-cowboy',
  'glasses': 'fas fa-glasses',
  'ears': 'fas fa-gem',
  'watch': 'fas fa-clock',
  'bracelet': 'fas fa-ring',
  'mask': 'fas fa-mask',
  'bag': 'fas fa-shopping-bag',
  'accessories': 'fas fa-star',
  'bodyarmor': 'fas fa-shield-alt',
  'decals': 'fas fa-paint-brush',
  'head': 'fas fa-user',
  'torso': 'fas fa-male'
};

// 摄像机位置定义 - 参考clothshop.vue的详细设置
const CAMERA_POSITIONS = ref({
  overview: { zpos: 0.0, fov: 90 },    // 全景
  tops: { zpos: 0.19, fov: 49 },       // 上衣
  legs: { zpos: -0.47, fov: 59 },      // 裤子
  shoes: { zpos: -0.71, fov: 53 },     // 鞋子
  under: { zpos: 0.19, fov: 49 },      // 内衬
  hat: { zpos: 0.6, fov: 33 },         // 帽子
  glasses: { zpos: 0.61, fov: 29 },    // 眼镜
  ears: { zpos: 0.62, fov: 29 },       // 耳环
  watch: { zpos: -0.1, fov: 45 },      // 手表
  bracelet: { zpos: -0.1, fov: 45 },   // 手环
  mask: { zpos: 0.6, fov: 33 },        // 面具
  body: { zpos: 0.0, fov: 90 }         // 躯体
});

// 可用的服装分类
const availableCategories = computed(() => {
  const categories = [{ key: 'all', name: '全部' }];
  
  // 首先按性别过滤设计图
  const genderFilteredBlueprints = blueprints.value.filter(blueprint => {
    if (blueprint.component && typeof blueprint.component.sex === 'number') {
      return blueprint.component.sex === characterSex.value;
    }
    return true;
  });
  
  const types = [...new Set(genderFilteredBlueprints.map(b => b.type))];
  
  types.forEach(type => {
    const typeName = genderFilteredBlueprints.find(b => b.type === type)?.typeName;
    if (typeName) {
      categories.push({ key: type, name: typeName });
    }
  });
  
  return categories;
});

// 过滤后的设计图
const filteredBlueprints = computed(() => {
  let filtered = blueprints.value;
  
  // 按性别筛选 - 只显示与角色性别匹配的设计图
  filtered = filtered.filter(blueprint => {
    if (blueprint.component && typeof blueprint.component.sex === 'number') {
      return blueprint.component.sex === characterSex.value;
    }
    return true; // 如果没有性别信息，默认显示
  });
  
  // 按类型筛选
  if (currentTypeFilter.value !== 'all') {
    filtered = filtered.filter(b => b.type === currentTypeFilter.value);
  }
  
  // 按搜索词筛选
  if (searchTerm.value) {
    const query = searchTerm.value.toLowerCase();
    filtered = filtered.filter(b => 
      b.customname.toLowerCase().includes(query) ||
      b.typeName.toLowerCase().includes(query)
    );
  }
  
  return filtered;
});

// 当前选中的设计图
const selectedBlueprint = computed(() => {
  return selectedIndex.value >= 0 ? filteredBlueprints.value[selectedIndex.value] : null;
});

// 含组件的设计图数量
const blueprintsWithComponent = computed(() => {
  return blueprints.value.filter(b => b.component).length;
});

// 订单相关数据
const currentMode = ref<'preview' | 'order' | 'orderManagement'>('preview');
const currentOrder = ref<any>(null);
const materialSubmissionAmounts = ref<{[key: string]: number}>({});
const showOrderButton = ref(false); // 控制是否显示订单按钮
const submittingAll = ref(false); // 一键提交状态

// 订单完成状态
const isOrderComplete = computed(() => {
  if (!currentOrder.value) return false;
  
  for (const [material, required] of Object.entries(currentOrder.value.materials)) {
    const submitted = currentOrder.value.materialsSubmitted[material] || 0;
    if (submitted < required) {
      return false;
    }
  }
  return true;
});

// 检查是否可以提交材料
const canSubmitMaterial = (material: string) => {
  const amount = materialSubmissionAmounts.value[material] || 0;
  const playerAmount = currentOrder.value?.playerMaterials[material] || 0;
  const required = currentOrder.value?.materials[material] || 0;
  const submitted = currentOrder.value?.materialsSubmitted[material] || 0;
  
  return amount > 0 && amount <= playerAmount && amount <= (required - submitted);
};

// 检查是否可以一键提交所有材料
const canSubmitAllMaterials = computed(() => {
  if (!currentOrder.value || !currentOrder.value.playerMaterials) return false;
  
  const materials = currentOrder.value.materials;
  const submitted = currentOrder.value.materialsSubmitted || {};
  const playerMaterials = currentOrder.value.playerMaterials;
  
  // 检查是否有任何材料可以提交
  for (const [material, required] of Object.entries(materials)) {
    const alreadySubmitted = submitted[material] || 0;
    const stillNeeded = (required as number) - alreadySubmitted;
    const playerHas = playerMaterials[material] || 0;
    
    if (stillNeeded > 0 && playerHas > 0) {
      return true; // 至少有一种材料可以提交
    }
  }
  
  return false;
});

// 检查某个分类是否有设计图
const hasItemsInCategory = (category: string) => {
  return blueprints.value.some(blueprint => blueprint.type === category);
};

// 获取分类图标
const getCategoryIcon = (type: string) => {
  return categoryIcons[type] || 'fas fa-question';
};

// 设置类型筛选
const setTypeFilter = (type: string) => {
  currentTypeFilter.value = type;
  selectedIndex.value = -1; // 重置选择
};

// 选择设计图
const selectBlueprint = (index: number) => {
  selectedIndex.value = index;
  
  // 如果是自动视角模式，根据服装类型自动切换视角
  if (isAutoCamera.value && selectedBlueprint.value) {
    const blueprintType = selectedBlueprint.value.type;
    if (CAMERA_POSITIONS.value[blueprintType]) {
      setManualCameraPosition(blueprintType);
    }
  }
};

// 预览设计图
const previewBlueprint = () => {
  if (selectedBlueprint.value && selectedBlueprint.value.component) {
    event.emitServer('designPreview:preview', selectedBlueprint.value);
  }
};

// 关闭预览
const closePreview = () => {
  event.emitClient('designPreview:close');
};

// 切换菜单显示/隐藏
const toggleMenu = () => {
  isMenuOpen.value = !isMenuOpen.value;
};

// 重置旋转
const resetRotation = () => {
  rotationY.value = 0;
  event.emitClient('PLAYER_ROTATE', 0);
};

// 切换自动/手动视角
const toggleAutoCamera = () => {
  isAutoCamera.value = !isAutoCamera.value;
  if (isAutoCamera.value) {
    event.emitClient('CAMERA_SET_POSITION', 0, 90); // 自动视角时，固定在全景
  }
};

// 设置手动摄像机位置
const setManualCameraPosition = (viewName: string) => {
  const position = CAMERA_POSITIONS.value[viewName];
  if (position) {
    currentFocusedView.value = viewName;
    event.emitClient("CAMERA_SET_POSITION", position.zpos, position.fov);
  }
};

// 获取视角名称
const getViewName = (viewName: string) => {
  const viewNames: Record<string, string> = {
    overview: '全景',
    tops: '上衣',
    legs: '裤子',
    shoes: '鞋子',
    under: '内衬',
    hat: '帽子',
    glasses: '眼镜',
    ears: '耳环',
    watch: '手表',
    bracelet: '手环',
    mask: '面具',
    body: '躯体'
  };
  return viewNames[viewName] || viewName;
};

// 获取摄像机图标
const getCameraIcon = (viewName: string) => {
  const icons: Record<string, string> = {
    overview: 'fas fa-expand-arrows-alt',
    tops: 'fas fa-tshirt',
    legs: 'fas fa-socks',
    shoes: 'fas fa-shoe-prints',
    under: 'fas fa-vest',
    hat: 'fas fa-hat-cowboy',
    glasses: 'fas fa-glasses',
    ears: 'fas fa-gem',
    watch: 'fas fa-clock',
    bracelet: 'fas fa-ring',
    mask: 'fas fa-mask',
    body: 'fas fa-male'
  };
  return icons[viewName] || 'fas fa-video';
};

// 鼠标拖拽事件处理
const handleMouseDown = (e: MouseEvent) => {
  if (e.button === 2) { // 右键
    isDragging.value = true;
    lastMouseX.value = e.clientX;
    e.preventDefault();
  }
};

const handleMouseMove = (e: MouseEvent) => {
  if (isDragging.value) {
    const deltaX = e.clientX - lastMouseX.value;
    const rotationSpeed = 0.3;

    let newRotation = rotationY.value + deltaX * rotationSpeed;

    if (newRotation > 180) {
      newRotation = newRotation - 360;
    } else if (newRotation < -180) {
      newRotation = newRotation + 360;
    }

    rotationY.value = newRotation;
    event.emitClient("PLAYER_ROTATE", rotationY.value);

    lastMouseX.value = e.clientX;
    e.preventDefault();
  }
};

const handleMouseUp = (e: MouseEvent) => {
  if (e.button === 2) { // 右键
    isDragging.value = false;
    e.preventDefault();
  }
};

// 下订单
const placeOrder = () => {
  if (selectedBlueprint.value) {
    event.emitServer('clothOrder:confirm', selectedBlueprint.value.slot);
  }
};

// 提交材料
const submitMaterial = (material: string) => {
  const amount = materialSubmissionAmounts.value[material] || 0;
  if (canSubmitMaterial(material)) {
    event.emitServer('clothOrder:submitMaterial', material, amount);
    materialSubmissionAmounts.value[material] = 1; // 重置输入为1
  }
};

// 一键提交所有可提交的材料
const submitAllMaterials = () => {
  if (!currentOrder.value || !currentOrder.value.playerMaterials) return;
  
  submittingAll.value = true;
  
  const materials = currentOrder.value.materials;
  const submitted = currentOrder.value.materialsSubmitted || {};
  const playerMaterials = currentOrder.value.playerMaterials;
  const submissionList: { material: string, amount: number }[] = [];
  
  // 计算每种材料可以提交的数量
  for (const [material, required] of Object.entries(materials)) {
    const alreadySubmitted = submitted[material] || 0;
    const stillNeeded = (required as number) - alreadySubmitted;
    const playerHas = playerMaterials[material] || 0;
    
    if (stillNeeded > 0 && playerHas > 0) {
      const canSubmit = Math.min(stillNeeded, playerHas);
      if (canSubmit > 0) {
        submissionList.push({ material, amount: canSubmit });
      }
    }
  }
  
  // 发送批量提交请求到服务器
  if (submissionList.length > 0) {
    event.emitServer('clothOrder:submitAllMaterials', submissionList);
  } else {
    submittingAll.value = false;
  }
};

// 完成订单
const completeOrder = () => {
  if (isOrderComplete.value) {
    event.emitServer('clothOrder:complete');
  }
};

// 刷新订单数据
const refreshOrderData = () => {
  // 服务器会重新发送数据
};

// 预览订单服装
const previewOrderCloth = () => {
  if (currentOrder.value && currentOrder.value.blueprintData) {
    event.emitServer('designPreview:preview', currentOrder.value.blueprintData);
  }
};

// 取消订单
const cancelOrder = () => {
  if (currentOrder.value) {
    event.emitServer('clothOrder:cancel');
  }
};

// 监听设计图数据
event.on('designPreview:setBlueprints', (data: BlueprintData[]) => {
  blueprints.value = data;
  selectedIndex.value = -1;
  currentTypeFilter.value = 'all';

  // 设置摄像机到全景
  setManualCameraPosition('overview');
});

// 监听角色性别设置
event.on('designPreview:setCharacterSex', (sex: number) => {
  characterSex.value = sex;
  // 强制重新计算过滤结果
  currentTypeFilter.value = 'all';
  selectedIndex.value = -1;
});

// 监听订单模式设置
event.on('designPreview:setMode', (mode: string) => {
  currentMode.value = mode as any;
  showOrderButton.value = mode === 'order';
});

// 监听订单数据
event.on('clothOrder:setData', (orderData: any) => {
  currentOrder.value = orderData;
  currentMode.value = 'orderManagement';
  
  // 初始化提交数量
  for (const material in orderData.materials) {
    materialSubmissionAmounts.value[material] = 1;
  }
});

// 监听订单提交成功
event.on('clothOrder:orderSubmitted', () => {
  // 订单提交成功，等待服务器发送新数据
});

// 监听材料提交成功
event.on('clothOrder:materialSubmitted', () => {
  // 材料提交成功，等待服务器发送更新数据
});

// 监听一键提交完成
event.on('clothOrder:allMaterialsSubmitted', () => {
  submittingAll.value = false;
});

// 监听订单完成
event.on('clothOrder:orderComplete', () => {
  // 显示完成按钮可用状态
});

// 监听订单取消
event.on('clothOrder:orderCancelled', () => {
  currentOrder.value = null;
  currentMode.value = 'preview';
});

// 监听服装获得
event.on('clothOrder:clothReceived', () => {
  currentOrder.value = null;
  currentMode.value = 'preview';
});

onMounted(() => {
  // 使用cloth插件的控制事件
  event.emitClient('clothshopopendisablecontrols');

  // 添加鼠标事件监听
  document.addEventListener('mousedown', handleMouseDown);
  document.addEventListener('mousemove', handleMouseMove);
  document.addEventListener('mouseup', handleMouseUp);

  // 防止拖拽时选中文本
  document.addEventListener('selectstart', (e) => {
    if (isDragging.value) {
      e.preventDefault();
    }
  });
});

onUnmounted(() => {
  // 移除事件监听
  document.removeEventListener('mousedown', handleMouseDown);
  document.removeEventListener('mousemove', handleMouseMove);
  document.removeEventListener('mouseup', handleMouseUp);

  // 恢复原始外观
  event.emitServer('designPreview:restore');
});
</script>

<style scoped>
/* CSS变量定义 - 在组件内部定义，不使用:root */
.preview-container {
  --primary-color: #64ffda;
  --primary-dark: #1de9b6;
  --primary-light: #4fd1c7;
  --card-bg: rgba(15, 23, 42, 0.95);
  --card-bg-light: rgba(30, 41, 59, 0.8);
  --text-primary: #ffffff;
  --text-secondary: rgba(255, 255, 255, 0.7);
  --text-muted: rgba(255, 255, 255, 0.5);
  --border-color: rgba(100, 255, 218, 0.2);
  --border-hover: rgba(100, 255, 218, 0.3);
  --shadow-sm: 0 0.2vh 0.8vh rgba(0, 0, 0, 0.2);
  --shadow-md: 0 0.4vh 1.6vh rgba(0, 0, 0, 0.3);
  --shadow-lg: 0 0.8vh 3.2vh rgba(0, 0, 0, 0.4);
  --shadow-primary: 0 0.4vh 2vh rgba(100, 255, 218, 0.3);

  position: fixed;
  top: 0;
  right: 0;
  height: 100vh;
  font-family: "Inter", "PingFang SC", "Microsoft YaHei", sans-serif;
  z-index: 1000;
  animation: fadeIn 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

.preview-container.dragging {
  cursor: grabbing !important;
  user-select: none;
}

.preview-container.dragging * {
  cursor: grabbing !important;
  user-select: none;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.preview-panel {
  position: fixed;
  top: 0;
  right: 0;
  width: 25vw;
  height: 100vh;
  background: var(--card-bg);
  border-left: 0.1vh solid var(--border-color);
  border-radius: 1.2vh 0 0 1.2vh;
  transition: transform 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  overflow:auto;
  box-shadow: var(--shadow-lg), 0 0 2vh rgba(100, 255, 218, 0.1);
}

.preview-panel.closed {
  transform: translateX(100%);
}

.toggle-btn {
  position: absolute;
  left: -3vh;
  top: 50%;
  transform: translateY(-50%);
  width: 3vh;
  height: 12vh;
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
  border: 0.1vh solid var(--border-color);
  border-radius: 1.2vh 0 0 1.2vh;
  color: #0f172a;
  font-size: 1.4vh;
  cursor: pointer;
  z-index: 10;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: var(--shadow-md);
  display: flex;
  align-items: center;
  justify-content: center;
}

.toggle-btn:hover {
  background: linear-gradient(135deg, var(--primary-dark) 0%, var(--primary-color) 100%);
  box-shadow: var(--shadow-primary);
  transform: translateY(-50%) scale(1.02);
}

.preview-content {
  height: 100%;
  padding: 2vh;
  display: flex;
  flex-direction: column;
  gap: 1.5vh;
  overflow: hidden;
}

.preview-header {
  display: flex;
  align-items: center;
  gap: 1.2vh;
  padding: 2vh 1.5vh;
  background: linear-gradient(135deg, rgba(100, 255, 218, 0.05) 0%, transparent 60%);
  border: 0.1vh solid var(--border-color);
  border-radius: 1.2vh;
  flex-shrink: 0;
  box-shadow: var(--shadow-sm);
}

.header-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 4vh;
  height: 4vh;
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
  border: 0.1vh solid var(--border-color);
  border-radius: 50%;
  color: #0f172a;
  font-size: 1.8vh;
  box-shadow: var(--shadow-sm);
}

.title-info h1 {
  color: var(--primary-color);
  font-size: 2vh;
  font-weight: 700;
  margin: 0 0 0.2vh 0;
}

.subtitle {
  font-size: 1vh;
  color: var(--text-secondary);
  text-transform: uppercase;
}

.character-info {
  display: flex;
  align-items: center;
  gap: 0.5vh;
  margin-top: 0.5vh;
  font-size: 1.1vh;
  color: var(--primary-color);
  font-weight: 500;
}

.character-info i.fa-venus {
  color: #ec4899;
}

.character-info i.fa-mars {
  color: #3b82f6;
}

.close-btn {
  background: linear-gradient(135deg, rgba(239, 68, 68, 0.1) 0%, rgba(239, 68, 68, 0.05) 100%);
  border: 0.1vh solid rgba(239, 68, 68, 0.3);
  border-radius: 50%;
  width: 3vh;
  height: 3vh;
  color: #ef4444;
  font-size: 1.2vh;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: var(--shadow-sm);
}

.close-btn:hover {
  background: linear-gradient(135deg, rgba(239, 68, 68, 0.2) 0%, rgba(239, 68, 68, 0.1) 100%);
  border-color: rgba(239, 68, 68, 0.5);
  box-shadow: 0 0.4vh 2vh rgba(239, 68, 68, 0.3);
  transform: scale(1.05);
}

.search-section {
  display: flex;
  flex-direction: column;
  gap: 1.2vh;
  flex-shrink: 0;
}

.search-box {
  position: relative;
}

.search-box i {
  position: absolute;
  left: 1.2vh;
  top: 50%;
  transform: translateY(-50%);
  color: var(--primary-color);
  font-size: 1.4vh;
}

.styled-input {
  width: 100%;
  padding: 1.2vh 1.2vh 1.2vh 3.5vh;
  background: var(--card-bg-light);
  border: 0.1vh solid var(--border-color);
  border-radius: 1vh;
  color: var(--text-primary);
  font-size: 1.4vh;
  font-family: inherit;
  outline: none;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: var(--shadow-sm);
}

.styled-input:focus {
  border-color: var(--primary-color);
  box-shadow: var(--shadow-primary);
}

.category-filter {
  background: var(--card-bg-light);
  border: 0.1vh solid var(--border-color);
  border-radius: 1vh;
  padding: 1vh;
  box-shadow: var(--shadow-sm);
}

.filter-header {
  display: flex;
  align-items: center;
  gap: 0.5vh;
  margin-bottom: 0.8vh;
  color: var(--primary-color);
  font-size: 1.1vh;
  font-weight: 500;
}

.category-buttons {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(8vw, 1fr));
  gap: 0.5vh;
}

.category-btn {
  background: var(--card-bg);
  border: 0.1vh solid var(--border-color);
  border-radius: 0.6vh;
  padding: 0.6vh 0.8vh;
  color: var(--text-secondary);
  font-size: 0.9vh;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.3vh;
  min-height: 4vh;
  box-shadow: var(--shadow-sm);
}

.category-btn:hover {
  background: var(--card-bg-light);
  border-color: var(--border-hover);
  box-shadow: var(--shadow-md);
}

.category-btn.active {
  background: linear-gradient(135deg, rgba(100, 255, 218, 0.15) 0%, rgba(100, 255, 218, 0.05) 100%);
  border-color: var(--primary-color);
  color: var(--primary-color);
  box-shadow: var(--shadow-primary);
}

.rotation-hint {
  display: flex;
  align-items: center;
  gap: 0.5vh;
  color: var(--text-secondary);
  font-size: 1.1vh;
  padding: 0.8vh 1.2vh;
  background: var(--card-bg-light);
  border: 0.1vh solid var(--border-color);
  border-radius: 1vh;
  box-shadow: var(--shadow-sm);
}

.rotation-indicator {
  background: linear-gradient(135deg, rgba(100, 255, 218, 0.1) 0%, rgba(100, 255, 218, 0.05) 100%);
  border: 0.1vh solid var(--border-color);
  border-radius: 0.6vh;
  padding: 0.3vh 0.8vh;
  font-size: 1.1vh;
  font-weight: 600;
  color: var(--primary-color);
  box-shadow: var(--shadow-sm);
}

.reset-rotation-btn {
  background: linear-gradient(135deg, rgba(168, 85, 247, 0.1) 0%, rgba(168, 85, 247, 0.05) 100%);
  border: 0.1vh solid rgba(168, 85, 247, 0.3);
  border-radius: 50%;
  width: 2.5vh;
  height: 2.5vh;
  color: #a78bfa;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: var(--shadow-sm);
}

.reset-rotation-btn:hover {
  background: linear-gradient(135deg, rgba(168, 85, 247, 0.2) 0%, rgba(168, 85, 247, 0.1) 100%);
  border-color: rgba(168, 85, 247, 0.5);
  box-shadow: 0 0.4vh 2vh rgba(168, 85, 247, 0.3);
  transform: scale(1.05);
}

.camera-control {
  background: var(--card-bg-light);
  border: 0.1vh solid var(--border-color);
  border-radius: 1vh;
  padding: 1vh;
  display: flex;
  flex-direction: column;
  gap: 0.8vh;
  box-shadow: var(--shadow-sm);
}

.camera-header {
  display: flex;
  align-items: center;
  gap: 0.5vh;
  color: var(--primary-color);
  font-size: 1.1vh;
  font-weight: 500;
}

.camera-header .auto-toggle {
  background: var(--card-bg);
  border: 0.1vh solid var(--border-color);
  border-radius: 0.6vh;
  padding: 0.6vh 0.8vh;
  color: var(--text-secondary);
  font-size: 0.9vh;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  align-items: center;
  gap: 0.3vh;
  min-width: 6vw;
  box-shadow: var(--shadow-sm);
}

.camera-header .auto-toggle:hover {
  background: var(--card-bg-light);
  border-color: var(--border-hover);
  box-shadow: var(--shadow-md);
}

.camera-header .auto-toggle.active {
  background: linear-gradient(135deg, rgba(100, 255, 218, 0.15) 0%, rgba(100, 255, 218, 0.05) 100%);
  border-color: var(--primary-color);
  color: var(--primary-color);
  box-shadow: var(--shadow-primary);
}

.camera-buttons {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(6vw, 1fr));
  gap: 0.5vh;
  animation: slideIn 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-1vh);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.camera-btn {
  background: var(--card-bg);
  border: 0.1vh solid var(--border-color);
  border-radius: 0.6vh;
  padding: 0.6vh;
  color: var(--text-secondary);
  cursor: pointer;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.3vh;
  min-height: 4vh;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: var(--shadow-sm);
}

.camera-btn:hover {
  background: var(--card-bg-light);
  border-color: var(--border-hover);
  box-shadow: var(--shadow-md);
}

.camera-btn.active {
  background: linear-gradient(135deg, rgba(100, 255, 218, 0.15) 0%, rgba(100, 255, 218, 0.05) 100%);
  border-color: var(--primary-color);
  color: var(--primary-color);
  box-shadow: var(--shadow-primary);
}

.blueprint-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(18vh, 1fr));
  gap: 1.2vh;
  padding: 1vh;
  overflow-y: auto;
  flex: 1;
  max-height: calc(100vh - 35vh); /* 限制最大高度确保滚动 */
}

.blueprint-card {
  background: var(--card-bg-light);
  border: 0.1vh solid var(--border-color);
  border-radius: 1vh;
  padding: 1.2vh;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  box-shadow: var(--shadow-sm);
  min-height: 12vh; /* 确保最小高度 */
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.blueprint-card:hover {
  background: var(--card-bg);
  border-color: var(--border-hover);
  transform: translateY(-0.2vh);
  box-shadow: var(--shadow-md);
}

.blueprint-card.selected {
  background: linear-gradient(135deg, rgba(100, 255, 218, 0.1) 0%, rgba(100, 255, 218, 0.05) 100%);
  border-color: var(--primary-color);
  box-shadow: var(--shadow-primary);
}

.blueprint-card.no-component {
  opacity: 0.6;
}

.card-preview {
  display: flex;
  align-items: flex-start;
  gap: 1vh;
  flex: 1;
  min-height: 8vh;
}

.preview-icon {
  width: 3vh;
  height: 3vh;
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
  border: 0.1vh solid var(--border-color);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #0f172a;
  font-size: 1.3vh;
  box-shadow: var(--shadow-sm);
}

.blueprint-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  min-width: 0; /* 允许内容收缩 */
}

.blueprint-name {
  color: var(--text-primary);
  font-size: 1.3vh;
  font-weight: 600;
  margin-bottom: 0.5vh;
  line-height: 1.3;
  word-wrap: break-word;
  overflow-wrap: break-word;
  hyphens: auto;
}

.blueprint-details {
  display: flex;
  flex-direction: column;
  gap: 0.3vh;
  font-size: 1vh;
  line-height: 1.2;
}

.type-info {
  color: var(--primary-color);
  font-weight: 500;
}

.finish-info {
  color: var(--text-secondary);
}

.component-info {
  color: #10b981;
}

.no-component-info {
  color: #f59e0b;
}

.finish-bar {
  margin-top: 0.8vh;
  height: 0.4vh;
  background: var(--card-bg);
  border: 0.1vh solid var(--border-color);
  border-radius: 0.2vh;
  overflow: hidden;
  box-shadow: var(--shadow-sm);
  flex-shrink: 0;
}

.finish-progress {
  height: 100%;
  background: linear-gradient(90deg, #f59e0b, #10b981, var(--primary-color));
  transition: width 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: var(--shadow-sm);
}

.selection-indicator {
  position: absolute;
  top: 0.8vh;
  right: 0.8vh;
  width: 2vh;
  height: 2vh;
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
  border: 0.1vh solid var(--border-color);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #0f172a;
  font-size: 1vh;
  box-shadow: var(--shadow-sm);
  z-index: 2;
}

.preview-footer {
  flex-shrink: 0;
  padding-top: 1vh;
  border-top: 0.1vh solid var(--border-color);
}

.blueprint-stats {
  display: flex;
  justify-content: space-between;
  margin-bottom: 1vh;
  font-size: 1vh;
  color: var(--text-muted);
}

.stats-item {
  display: flex;
  align-items: center;
  gap: 0.3vh;
}

.action-buttons {
  display: flex;
  gap: 1.5vh;
  justify-content: center;
}

.action-btn {
  padding: 1.4vh 2vh;
  border-radius: 1vh;
  font-size: 1.3vh;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  align-items: center;
  gap: 0.8vh;
  border: 0.1vh solid transparent;
  min-width: 10vh;
  box-shadow: var(--shadow-sm);
}

.action-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.btn-icon {
  width: 2.5vh;
  height: 2.5vh;
  background: var(--card-bg-light);
  border: 0.1vh solid var(--border-color);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2vh;
  box-shadow: var(--shadow-sm);
}

.action-btn.cancel {
  background: linear-gradient(135deg, rgba(239, 68, 68, 0.1) 0%, rgba(239, 68, 68, 0.05) 100%);
  color: #ef4444;
  border-color: rgba(239, 68, 68, 0.3);
}

.action-btn.preview {
  background: linear-gradient(135deg, rgba(100, 255, 218, 0.1) 0%, rgba(100, 255, 218, 0.05) 100%);
  color: var(--primary-color);
  border-color: var(--border-color);
}

.action-btn:hover:not(:disabled) {
  transform: translateY(-0.2vh);
  box-shadow: var(--shadow-md);
}

.action-btn.cancel:hover:not(:disabled) {
  background: linear-gradient(135deg, rgba(239, 68, 68, 0.2) 0%, rgba(239, 68, 68, 0.1) 100%);
  border-color: rgba(239, 68, 68, 0.5);
  box-shadow: 0 0.4vh 2vh rgba(239, 68, 68, 0.3);
}

.action-btn.preview:hover:not(:disabled) {
  background: linear-gradient(135deg, rgba(100, 255, 218, 0.2) 0%, rgba(100, 255, 218, 0.1) 100%);
  border-color: var(--primary-color);
  box-shadow: var(--shadow-primary);
}

/* 订单管理界面样式 */
.order-management {
  background: var(--card-bg);
  border: 0.1vh solid var(--border-color);
  border-radius: 1vh;
  padding: 1.5vh;
  margin-top: 1vh;
  box-shadow: var(--shadow-sm);
}

.quick-submit-section {
  margin-bottom: 1.5vh;
  padding-bottom: 1vh;
  border-bottom: 0.1vh solid var(--border-color);
}

.quick-submit-btn {
  width: 100%;
  padding: 1.2vh 2vh;
  background: linear-gradient(135deg, rgba(34, 197, 94, 0.1) 0%, rgba(34, 197, 94, 0.05) 100%);
  border: 0.1vh solid rgba(34, 197, 94, 0.3);
  border-radius: 1vh;
  color: #22c55e;
  font-size: 1.3vh;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.8vh;
  box-shadow: var(--shadow-sm);
}

.quick-submit-btn:hover:not(:disabled) {
  background: linear-gradient(135deg, rgba(34, 197, 94, 0.2) 0%, rgba(34, 197, 94, 0.1) 100%);
  border-color: rgba(34, 197, 94, 0.5);
  box-shadow: 0 0.4vh 2vh rgba(34, 197, 94, 0.3);
  transform: translateY(-0.1vh);
}

.quick-submit-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.quick-submit-btn i {
  font-size: 1.4vh;
}

.order-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1vh;
  padding-bottom: 0.8vh;
  border-bottom: 0.1vh solid var(--border-color);
}

.order-header h2 {
  color: var(--primary-color);
  font-size: 1.4vh;
  font-weight: 600;
  margin: 0;
}

.order-info {
  display: flex;
  align-items: baseline;
  gap: 0.5vh;
  color: var(--text-secondary);
  font-size: 1.1vh;
}

.order-type {
  color: var(--primary-color);
  font-weight: 500;
}

.order-cost {
  color: #10b981;
  font-weight: 600;
}

.price-adjustment {
  font-size: 0.9vh;
  color: var(--text-muted);
}

.completion-info {
  display: flex;
  align-items: center;
  gap: 0.5vh;
  margin-top: 0.5vh;
  font-size: 0.9vh;
  color: var(--text-muted);
}

.completion-rate {
  color: var(--text-secondary);
}

.design-quality {
  font-weight: 600;
  padding: 0.2vh 0.6vh;
  border-radius: 0.4vh;
  font-size: 0.8vh;
}

.design-quality.excellent {
  background-color: rgba(100, 255, 218, 0.1);
  color: #10b981;
  border: 0.1vh solid #10b981;
}

.design-quality.great {
  background-color: rgba(100, 255, 218, 0.1);
  color: #f59e0b;
  border: 0.1vh solid #f59e0b;
}

.design-quality.good {
  background-color: rgba(100, 255, 218, 0.1);
  color: #f59e0b;
  border: 0.1vh solid #f59e0b;
}

.design-quality.normal {
  background-color: rgba(100, 255, 218, 0.1);
  color: #f59e0b;
  border: 0.1vh solid #f59e0b;
}

.design-quality.fair {
  background-color: rgba(100, 255, 218, 0.1);
  color: #f59e0b;
  border: 0.1vh solid #f59e0b;
}

.design-quality.poor {
  background-color: rgba(100, 255, 218, 0.1);
  color: #f59e0b;
  border: 0.1vh solid #f59e0b;
}

.design-quality.bad {
  background-color: rgba(100, 255, 218, 0.1);
  color: #ef4444;
  border: 0.1vh solid #ef4444;
}

.order-preview-section {
  margin-top: 1.5vh;
  padding-top: 1vh;
  border-top: 0.1vh solid var(--border-color);
}

.order-preview-section .action-btn {
  width: 100%;
  justify-content: center;
}

.materials-section {
  margin-bottom: 1.5vh;
  padding-bottom: 1vh;
  border-bottom: 0.1vh solid var(--border-color);
}

.materials-section h3 {
  color: var(--primary-color);
  font-size: 1.2vh;
  font-weight: 500;
  margin-bottom: 0.8vh;
}

.materials-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(15vw, 1fr));
  gap: 0.8vh;
}

.material-item {
  background: var(--card-bg-light);
  border: 0.1vh solid var(--border-color);
  border-radius: 0.8vh;
  padding: 0.8vh 1vh;
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 1vh;
  box-shadow: var(--shadow-sm);
}

.material-info {
  display: flex;
  flex-direction: column;
  gap: 0.2vh;
  font-size: 1.1vh;
}

.material-name {
  color: var(--text-primary);
  font-weight: 500;
}

.material-progress {
  color: var(--text-secondary);
  font-size: 0.9vh;
}

.material-controls {
  display: flex;
  align-items: center;
  gap: 0.8vh;
}

.progress-bar {
  flex: 1;
  height: 0.4vh;
  background: var(--border-color);
  border-radius: 0.2vh;
  overflow: hidden;
  box-shadow: var(--shadow-sm);
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #f59e0b, #10b981, var(--primary-color));
  border-radius: 0.2vh;
  box-shadow: var(--shadow-sm);
}

.submit-controls {
  display: flex;
  align-items: center;
  gap: 0.5vh;
  background: var(--card-bg-light);
  border: 0.1vh solid var(--border-color);
  border-radius: 0.6vh;
  padding: 0.4vh 0.8vh;
  box-shadow: var(--shadow-sm);
}

.player-amount {
  color: var(--text-secondary);
  font-size: 0.9vh;
}

.amount-input {
  width: 4vh;
  text-align: center;
  background: var(--text-primary);
  border: 0.1vh solid var(--border-color);
  border-radius: 0.4vh;
  color: #0f172a;
  font-size: 1.1vh;
  font-weight: 600;
  box-shadow: var(--shadow-sm);
}

.amount-input:focus {
  border-color: var(--primary-color);
  box-shadow: var(--shadow-primary);
}

.submit-btn {
  background: linear-gradient(135deg, rgba(100, 255, 218, 0.1) 0%, rgba(100, 255, 218, 0.05) 100%);
  border: 0.1vh solid var(--border-color);
  border-radius: 0.6vh;
  padding: 0.4vh 0.8vh;
  color: var(--primary-color);
  font-size: 0.9vh;
  font-weight: 600;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.3vh;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: var(--shadow-sm);
}

.submit-btn:hover {
  background: linear-gradient(135deg, rgba(100, 255, 218, 0.2) 0%, rgba(100, 255, 218, 0.1) 100%);
  border-color: var(--primary-color);
  box-shadow: var(--shadow-md);
}

.submit-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  background: var(--card-bg-light);
  border-color: var(--border-color);
  color: var(--text-secondary);
}

.completed-mark {
  background: linear-gradient(135deg, rgba(100, 255, 218, 0.1) 0%, rgba(100, 255, 218, 0.05) 100%);
  border: 0.1vh solid var(--border-color);
  border-radius: 0.6vh;
  padding: 0.4vh 0.8vh;
  color: var(--primary-color);
  font-size: 0.9vh;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.3vh;
  box-shadow: var(--shadow-sm);
}

.order-actions {
  display: flex;
  justify-content: space-around;
  margin-top: 1.5vh;
  padding-top: 1vh;
  border-top: 0.1vh solid var(--border-color);
}

.action-btn.refresh {
  background: linear-gradient(135deg, rgba(168, 85, 247, 0.1) 0%, rgba(168, 85, 247, 0.05) 100%);
  color: #a78bfa;
  border-color: rgba(168, 85, 247, 0.3);
}

.action-btn.refresh:hover:not(:disabled) {
  background: linear-gradient(135deg, rgba(168, 85, 247, 0.2) 0%, rgba(168, 85, 247, 0.1) 100%);
  border-color: rgba(168, 85, 247, 0.5);
  box-shadow: 0 0.4vh 2vh rgba(168, 85, 247, 0.3);
}

.action-btn.complete {
  background: linear-gradient(135deg, rgba(100, 255, 218, 0.1) 0%, rgba(100, 255, 218, 0.05) 100%);
  color: var(--primary-color);
  border-color: var(--border-color);
}

.action-btn.complete:hover:not(:disabled) {
  background: linear-gradient(135deg, rgba(100, 255, 218, 0.2) 0%, rgba(100, 255, 218, 0.1) 100%);
  border-color: var(--primary-color);
  box-shadow: var(--shadow-primary);
}

/* 响应式设计优化 */
/* 高分辨率屏幕优化 */
@media (min-height: 1200px) {
  .blueprint-grid {
    grid-template-columns: repeat(auto-fill, minmax(16vh, 1fr));
    gap: 1.5vh;
  }
  
  .blueprint-card {
    min-height: 14vh;
    padding: 1.5vh;
  }
  
  .blueprint-name {
    font-size: 1.4vh;
  }
  
  .blueprint-details {
    font-size: 1.1vh;
  }
}

/* 低分辨率屏幕优化 */
@media (max-height: 800px) {
  .blueprint-grid {
    grid-template-columns: repeat(auto-fill, minmax(14vh, 1fr));
    gap: 0.8vh;
    max-height: calc(100vh - 30vh);
  }
  
  .blueprint-card {
    min-height: 9vh;
    padding: 0.8vh;
  }
  
  .card-preview {
    min-height: 6vh;
  }
  
  .blueprint-name {
    font-size: 1.1vh;
    margin-bottom: 0.3vh;
  }
  
  .blueprint-details {
    font-size: 0.9vh;
    gap: 0.2vh;
  }
}
</style> 