<script lang="ts" setup>
import { ref, onMounted } from 'vue';

const isVisible = ref(true); // 默认显示

onMounted(() => {
    // 组件挂载时立即显示jumpscare效果
    isVisible.value = true;
});
</script>

<template>
    <Transition name="jumpscare">
        <div v-if="isVisible" class="jumpscare-container">
            <div class="ghost-face"></div>
        </div>
    </Transition>
</template>

<style scoped>
.jumpscare-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    z-index: 9999;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(0, 0, 0, 0.95);
}

.ghost-face {
    width: 100vw;
    height: 100vh;
    background-image: url('../images/ghost.png');
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    animation: scareShake 0.1s infinite;
}

/* Jumpscare动画 - 快速出现 */
.jumpscare-enter-active {
    transition: none; /* 立即出现，没有过渡 */
}

.jumpscare-leave-active {
    transition: opacity 0.5s ease-out;
}

.jumpscare-enter-from {
    opacity: 0;
    transform: scale(0.8);
}

.jumpscare-leave-to {
    opacity: 0;
}

/* 震动效果增加恐怖感 */
@keyframes scareShake {
    0% { transform: translate(0); }
    25% { transform: translate(-0.2vw, 0.2vh); }
    50% { transform: translate(0.2vw, -0.2vh); }
    75% { transform: translate(-0.1vw, 0.1vh); }
    100% { transform: translate(0); }
}

/* 确保在所有设备上都能全屏显示 */
@media (orientation: portrait) {
    .ghost-face {
        background-size: contain;
    }
}

@media (orientation: landscape) {
    .ghost-face {
        background-size: cover;
    }
}
</style> 