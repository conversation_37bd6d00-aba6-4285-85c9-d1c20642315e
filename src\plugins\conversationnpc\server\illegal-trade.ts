import * as alt from 'alt-server';
import { useRebar } from '@Server/index.js';
import { Character } from '@Shared/types/character.js';
import { 
    illegalIndustryBrokerPos, 
    illegalindustrystoragepos, 
    illegalindustrychemicalpos, 
    illegalindustryweaponpos, 
    illegalgaragepos 
} from '@Plugins/illegality/shared/config.js';
import { GlobalTimerManager } from '@Plugins/animmenu/server/interval.js';
import { createInteractioninhouse, storages } from '@Plugins/inventory/server/index.js';
import { createIllegalIndustryInteractions, IllegalIndustry } from '@Plugins/illegality/server/index.js';
import { addActivityLog, Faction } from '@Plugins/faction/server/index.js';
import { createNPC } from './npc.js';
import { securityManager } from './security.js';
import { 
    RATE_LIMITS, 
    TREASURE_ITEMS, 
    INDUSTRY_PRICES, 
    DRUG_ITEMS, 
    WEAPON_ITEMS,
    TOR_PERMISSION_PRICE,
    TOR_PERMISSION_DURATION,
    MAX_BLACK_MONEY_EXCHANGE,
    BLACK_MONEY_EXCHANGE_RATE
} from '../shared/config.js';

const Rebar = useRebar();

class IllegalTradeManager {
    private static instance: IllegalTradeManager;
    private currentNPC: alt.Ped | null = null;
    private currentBlip: alt.PointBlip | null = null;
    private boughtDrugsPlayers: Set<number> = new Set();
    private boughtWeaponPlayers: Set<number> = new Set();
    private boughtHoodPlayers: Set<number> = new Set();

    public static getInstance(): IllegalTradeManager {
        if (!IllegalTradeManager.instance) {
            IllegalTradeManager.instance = new IllegalTradeManager();
        }
        return IllegalTradeManager.instance;
    }

    private constructor() {
        this.initialize();
    }

    private async initialize(): Promise<void> {
        await this.setupEventHandlers();
        await this.spawnMysteriousNPC();
        this.setupTimers();
    }

    private async setupEventHandlers(): Promise<void> {
        alt.on('rebar:playerCharacterBound', this.handlePlayerCharacterBound.bind(this));
        alt.onClient('report:close', this.handleReportClose.bind(this));
    }

    private getRandomPosition() {
        const randomIndex = Math.floor(Math.random() * illegalIndustryBrokerPos.length);
        return illegalIndustryBrokerPos[randomIndex];
    }

    private async assignIndustryToPlayer(player: alt.Player, industryType: string): Promise<boolean> {
        let config: any;

        switch (industryType) {
            case '走私仓库':
                config = illegalindustrystoragepos;
                break;
            case '化学工作室':
                config = illegalindustrychemicalpos;
                break;
            case '武器工作室':
                config = illegalindustryweaponpos;
                break;
            case '违法车库':
                config = illegalgaragepos;
                break;
            default:
                const notifyapi = await Rebar.useApi().getAsync('notify-api');
                notifyapi.shownotify(player, '未知的产业类型', 'error');
                return false;
        }

        const db = Rebar.database.useDatabase();
        const availableIndustry = await db.get<IllegalIndustry & { _id: string }>({ faction: null }, 'illegalindustry');

        if (!availableIndustry) {
            const notifyapi = await Rebar.useApi().getAsync('notify-api');
            notifyapi.shownotify(player, '当前没有可用的产业空间', 'error');
            return false;
        }

        const playerfaction = Rebar.document.character.useCharacter(player).getField('job');
        const faction = await db.get<Faction & { _id: string }>({ name: playerfaction.faction }, 'faction');
        
        if (faction.type !== '黑道') {
            const notifyapi = await Rebar.useApi().getAsync('notify-api');
            notifyapi.shownotify(player, '你不是黑道成员，无法购买产业', 'error');
            return false;
        }

        if (playerfaction.rank !== 0) {
            const notifyapi = await Rebar.useApi().getAsync('notify-api');
            notifyapi.shownotify(player, '你不是派系领袖，无法购买产业', 'error');
            return false;
        }

        await db.update({
            _id: availableIndustry._id,
            pos: config.pos ? config.pos : availableIndustry.pos,
            entrypos: config.entrypos ? config.entrypos : availableIndustry.entrypos,
            storagepos: config.storagepos ? config.storagepos : availableIndustry.storagepos,
            storagecapacity: config.storagecapacity ? config.storagecapacity : availableIndustry.storagecapacity,
            controlpos: config.controlpos ? config.controlpos : availableIndustry.controlpos,
            makepos: config.makepos ? config.makepos : null,
            name: config.name,
            faction: faction.name
        }, 'illegalindustry');

        if (industryType !== '违法车库') {
            const existingStorage = await db.getMany({ pos: new alt.Vector3(config.storagepos.x, config.storagepos.y, config.storagepos.z), dimension: availableIndustry.dimension }, 'storage');
            if (existingStorage.length === 0) {
                const _id = await db.create({
                    name: `非法产业储物仓库`,
                    pos: config.storagepos,
                    capacity: config.storagecapacity,
                    content: [],
                    dimension: availableIndustry.dimension,
                    noblip: true,
                    fingerprint: []
                }, 'storage');

                const storage = await db.getMany<storages>({ _id: _id }, 'storage');
                await createInteractioninhouse(storage[0]);
            }
        }

        const blip = new alt.PointBlip(availableIndustry.pos.x, availableIndustry.pos.y, availableIndustry.pos.z, false);
        blip.sprite = 456;
        blip.color = 0;
        blip.shortRange = true;
        blip.name = '地下空间';
        blip.addTarget(player);

        const factiodata = await db.getMany<Faction & { _id: string }>({ name: faction.name }, 'faction');
        if (factiodata.length > 0) {
            for (const member of factiodata[0].members) {
                const memberPlayer = alt.Player.all.find(p => p.getStreamSyncedMeta('id') === member.id);
                if (memberPlayer) {
                    const notifyapi = await Rebar.useApi().getAsync('notify-api');
                    notifyapi.shownotify(memberPlayer, `你的派系领袖 ${player.getStreamSyncedMeta('name')} 已购买了 ${config.name}`, 'success');
                    blip.addTarget(memberPlayer);
                }
            }
        }

        const notifyapi = await Rebar.useApi().getAsync('notify-api');
        notifyapi.shownotify(player, `你已成功购买了 ${config.name}`, 'success');

        const newindustry = await db.get<IllegalIndustry & { _id: string }>({ _id: availableIndustry._id }, 'illegalindustry');
        createIllegalIndustryInteractions(newindustry);
        return true;
    }

    public async buyIndustryForFaction(player: alt.Player, industryType: string, cost: number): Promise<boolean> {
        if (!(await securityManager.validateTransactionSecurity(player, `购买${industryType}`, RATE_LIMITS.INDUSTRY_PURCHASE))) {
            Rebar.player.useWebview(player).hide('conversation');
            return false;
        }

        const playerId = player.id;
        securityManager.setTransactionLock(playerId, true);

        try {
            if (typeof cost !== 'number' || cost <= 0 || cost > 10000000) {
                console.error(`[IllegalTrade] 玩家 ${player.getStreamSyncedMeta('name')} 尝试购买产业，金额无效: ${cost}`);
                const notifyapi = await Rebar.useApi().getAsync('notify-api');
                notifyapi.shownotify(player, '产业价格异常，交易取消', 'error');
                Rebar.player.useWebview(player).hide('conversation');
                return false;
            }

            const faction = Rebar.document.character.useCharacter(player).getField('job').faction;
            if (!faction) {
                const notifyapi = await Rebar.useApi().getAsync('notify-api');
                notifyapi.shownotify(player, '你还没有加入派系', 'error');
                Rebar.player.useWebview(player).hide('conversation');
                return false;
            }

            const db = Rebar.database.useDatabase();
            const factiondata = await db.getMany<Faction & { _id: string }>({ name: faction }, 'faction');
            if (factiondata.length === 0) {
                const notifyapi = await Rebar.useApi().getAsync('notify-api');
                notifyapi.shownotify(player, '你还没有加入派系', 'error');
                Rebar.player.useWebview(player).hide('conversation');
                return false;
            }

            if (factiondata[0].type !== '黑道') {
                const notifyapi = await Rebar.useApi().getAsync('notify-api');
                notifyapi.shownotify(player, '你不是黑道成员，无法购买产业', 'error');
                Rebar.player.useWebview(player).hide('conversation');
                return false;
            }

            const rank = Rebar.document.character.useCharacter(player).getField('job').rank;
            if (rank !== 0) {
                const notifyapi = await Rebar.useApi().getAsync('notify-api');
                notifyapi.shownotify(player, '你不是派系领袖，无法购买产业', 'error');
                Rebar.player.useWebview(player).hide('conversation');
                return false;
            }

            const isillegalindustry = await db.getMany<{ _id: string } & IllegalIndustry>({ faction: factiondata[0].name }, 'illegalindustry');
            if (isillegalindustry.length > 0) {
                const notifyapi = await Rebar.useApi().getAsync('notify-api');
                notifyapi.shownotify(player, '你的派系已经拥有了一个产业，无法购买新的产业', 'error');
                Rebar.player.useWebview(player).hide('conversation');
                return false;
            }

            const currentFunds = factiondata[0].funds;
            if (currentFunds < cost) {
                const notifyapi = await Rebar.useApi().getAsync('notify-api');
                notifyapi.shownotify(player, '没有足够的派系资金', 'error');
                Rebar.player.useWebview(player).hide('conversation');
                return false;
            }

            const handler = factiondata[0].members.find(m => m.id === player.getStreamSyncedMeta('id'));
            if (!handler) {
                const notifyapi = await Rebar.useApi().getAsync('notify-api');
                notifyapi.shownotify(player, '你不是派系成员', 'error');
                Rebar.player.useWebview(player).hide('conversation');
                return false;
            }

            const reason = `${handler.job.name} ${handler.name} 购买${industryType}`;
            const fundsSuccess = await securityManager.safeFactionFundsOperation(
                factiondata[0]._id,
                currentFunds,
                cost,
                'withdraw',
                reason
            );

            if (!fundsSuccess) {
                const notifyapi = await Rebar.useApi().getAsync('notify-api');
                notifyapi.shownotify(player, '资金扣除失败，交易取消', 'error');
                Rebar.player.useWebview(player).hide('conversation');
                return false;
            }

            await addActivityLog(factiondata[0]._id, '购买产业', reason);

            const fundsHistory = {
                date: Date.now(),
                amount: cost,
                reason: reason,
                type: 'withdraw' as 'withdraw' | 'deposit'
            };

            let oldfundsHistory = factiondata[0].fundsHistory || [];
            oldfundsHistory.push(fundsHistory);

            try {
                await db.update({ _id: factiondata[0]._id, fundsHistory: oldfundsHistory }, 'faction');
            } catch (error) {
                console.warn(`[IllegalTrade] 更新派系资金历史失败: ${error}`);
            }

            securityManager.updateTransactionRecord(playerId);

            const industrySuccess = await this.assignIndustryToPlayer(player, industryType);

            if (!industrySuccess) {
                console.error(`[IllegalTrade] 产业分配失败，尝试回滚资金`);
                await securityManager.safeFactionFundsOperation(
                    factiondata[0]._id,
                    currentFunds - cost,
                    cost,
                    'deposit',
                    `回滚: ${reason}`
                );
                const notifyapi = await Rebar.useApi().getAsync('notify-api');
                notifyapi.shownotify(player, '产业分配失败，资金已退回', 'error');
                Rebar.player.useWebview(player).hide('conversation');
                return false;
            }

            return true;

        } catch (error) {
            console.error(`[IllegalTrade] 购买产业过程中发生错误:`, error);
            const notifyapi = await Rebar.useApi().getAsync('notify-api');
            notifyapi.shownotify(player, '交易处理失败，请联系管理员', 'error');
            Rebar.player.useWebview(player).hide('conversation');
            return false;
        } finally {
            securityManager.setTransactionLock(playerId, false);
        }
    }

    private async createItemPurchaseCallback(itemName: string, price: number, quantity: number = 1, weight: number = 0.1, playerSet?: Set<number>) {
        return async (player: alt.Player) => {
            if (!(await securityManager.validateTransactionSecurity(player, `购买${itemName}`, RATE_LIMITS.DRUG_PURCHASE))) {
                Rebar.player.useWebview(player).hide('conversation');
                return;
            }

            if (playerSet && playerSet.has(player.id)) {
                const notifyapi = await Rebar.useApi().getAsync('notify-api');
                notifyapi.shownotify(player, '我今天已经卖给你货了，等我下次出现再来吧', 'error');
                Rebar.player.useWebview(player).hide('conversation');
                return;
            }

            const playerId = player.id;
            securityManager.setTransactionLock(playerId, true);

            try {
                const currentWeight = Rebar.document.character.useCharacter(player).getField('allWeightInBag');
                const maxweight = Rebar.document.character.useCharacter(player).getField('capacity');
                if (currentWeight + weight > maxweight) {
                    const notifyapi = await Rebar.useApi().getAsync('notify-api');
                    notifyapi.shownotify(player, '你身上没有足够的空间', 'error');
                    Rebar.player.useWebview(player).hide('conversation');
                    return;
                }

                const success = await securityManager.safeCurrencyDeduction(player, price, `购买${itemName}`);
                if (!success) {
                    const notifyapi = await Rebar.useApi().getAsync('notify-api');
                    notifyapi.shownotify(player, '你身上没有足够的钱', 'error');
                    Rebar.player.useWebview(player).hide('conversation');
                    return;
                }

                const inventoryapi = await Rebar.useApi().getAsync('inventory-api');
                const [addSuccess, message] = await inventoryapi.addItem(itemName, quantity, { player: player });
                if (!addSuccess) {
                    await securityManager.safeCurrencyAddition(player, '现金', price, `购买${itemName}失败退款`);
                    const notifyapi = await Rebar.useApi().getAsync('notify-api');
                    notifyapi.shownotify(player, `添加物品失败，已退款: ${message}`, 'error');
                    Rebar.player.useWebview(player).hide('conversation');
                    return;
                }

                if (playerSet) {
                    playerSet.add(player.id);
                }
                securityManager.updateTransactionRecord(playerId);

                const notifyapi = await Rebar.useApi().getAsync('notify-api');
                notifyapi.shownotify(player, `成功购买${itemName}`, 'success');
                Rebar.player.useWebview(player).hide('conversation');
            } catch (error) {
                console.error(`[IllegalTrade] 购买${itemName}时发生错误:`, error);
                const notifyapi = await Rebar.useApi().getAsync('notify-api');
                notifyapi.shownotify(player, '交易处理失败，请稍后重试', 'error');
                Rebar.player.useWebview(player).hide('conversation');
            } finally {
                securityManager.setTransactionLock(playerId, false);
            }
        };
    }

    private async createTreasureExchangeCallback(treasureItem: any) {
        return async (player: alt.Player) => {
            if (!(await securityManager.validateTransactionSecurity(player, `兑换${treasureItem.name}`, RATE_LIMITS.GENERAL_PURCHASE))) {
                Rebar.player.useWebview(player).hide('conversation');
                return;
            }

            const playerId = player.id;
            securityManager.setTransactionLock(playerId, true);

            try {
                const inventoryapi = await Rebar.useApi().getAsync('inventory-api');
                const inventory = await inventoryapi.getInventory({ player: player });
                const items = inventory.filter(item => item.name === treasureItem.name);
                if (items.length === 0) {
                    const notifyapi = await Rebar.useApi().getAsync('notify-api');
                    notifyapi.shownotify(player, `你身上没有${treasureItem.name}`, 'error');
                    Rebar.player.useWebview(player).hide('conversation');
                    return;
                }

                const totalItems = items.reduce((total, item) => total + (item.quantity || 0), 0);
                const cashAmount = totalItems * treasureItem.price;

                const subResult = await inventoryapi.subItem(treasureItem.name, totalItems, { player: player });
                if (!subResult) {
                    const notifyapi = await Rebar.useApi().getAsync('notify-api');
                    notifyapi.shownotify(player, `扣除${treasureItem.name}失败`, 'error');
                    Rebar.player.useWebview(player).hide('conversation');
                    return;
                }

                const addSuccess = await securityManager.safeCurrencyAddition(player, '现金', cashAmount, `兑换${totalItems}个${treasureItem.name}`);
                if (!addSuccess) {
                    await inventoryapi.addItem(treasureItem.name, totalItems, { player: player });
                    const notifyapi = await Rebar.useApi().getAsync('notify-api');
                    notifyapi.shownotify(player, `现金添加失败，${treasureItem.name}已退回`, 'error');
                    Rebar.player.useWebview(player).hide('conversation');
                    return;
                }

                securityManager.updateTransactionRecord(playerId);
                const notifyapi = await Rebar.useApi().getAsync('notify-api');
                notifyapi.shownotify(player, `成功兑换了 ${totalItems} 个${treasureItem.name}，获得 ${cashAmount} 元现金`, 'success');
                Rebar.player.useWebview(player).hide('conversation');
            } catch (error) {
                console.error(`[IllegalTrade] 兑换${treasureItem.name}时发生错误:`, error);
                const notifyapi = await Rebar.useApi().getAsync('notify-api');
                notifyapi.shownotify(player, '交易处理失败，请稍后重试', 'error');
                Rebar.player.useWebview(player).hide('conversation');
            } finally {
                securityManager.setTransactionLock(playerId, false);
            }
        };
    }

    private async spawnMysteriousNPC(): Promise<void> {
        if (this.currentNPC) {
            this.currentNPC.destroy();
        }

        if (this.currentBlip) {
            this.currentBlip.destroy();
        }

        this.boughtDrugsPlayers.clear();
        this.boughtWeaponPlayers.clear();
        this.boughtHoodPlayers.clear();

        await alt.Utils.wait(1000 * 10);

        console.log('神秘人位置已刷新');

        const pos = this.getRandomPosition();
        
        // 先创建所有回调函数
        const seedCallback = await this.createItemPurchaseCallback('小麻种子', 8000, 10, 0.1, this.boughtDrugsPlayers);
        const torCallback = await this.createTorPermissionCallback();
        const hoodCallback = await this.createItemPurchaseCallback('头套', 1000, 1, 1, this.boughtHoodPlayers);
        const blackMoneyCallback = await this.createBlackMoneyExchangeCallback();
        
        // 构建对话选项
        const mainOptions = [
            {
                text: '我想要订购一些洗衣粉',
                nextId: 'drug'
            },
            {
                text: '我有一些资本，我想要做一点小产业',
                nextId: 'illegalindustry'
            },
            {
                text: '我想要一把没有注册，无法被追踪的武器',
                nextId: 'weapon'
            },
            {
                text: '我有一些来路不太干净的东西，也许你会有兴趣',
                nextId: 'sell'
            },
            {
                text: '我想购买小麻种子(8000元/10个)',
                callback: seedCallback,
                endConversation: true
            },
            {
                text: '我想访问tor网络',
                callback: torCallback,
                endConversation: true
            },
            {
                text: '我想绑架某人',
                nextId: 'kidnap'
            }
        ];

        // 构建毒品选项
        const drugOptions = await Promise.all(DRUG_ITEMS.map(async (item) => ({
            text: `我想买点${item.name}($${item.price}${item.quantity > 1 ? `/${item.quantity}个` : ''})`,
            callback: await this.createItemPurchaseCallback(item.name, item.price, item.quantity, 0.1, this.boughtDrugsPlayers),
            endConversation: true
        })));

        // 构建武器选项
        const weaponOptions = await Promise.all(WEAPON_ITEMS.map(async (item) => ({
            text: `${item.name} ($${item.price})`,
            callback: await this.createItemPurchaseCallback(
                item.name, 
                item.price, 
                'quantity' in item ? item.quantity : 1, 
                item.weight, 
                item.name === '子弹' ? undefined : this.boughtWeaponPlayers
            ),
            endConversation: true
        })));

        // 构建宝藏兑换选项
        const treasureOptions = await Promise.all(TREASURE_ITEMS.map(async (item) => ({
            text: `${item.name} (${item.price}元/个)`,
            callback: await this.createTreasureExchangeCallback(item),
            endConversation: true
        })));

        // 构建产业购买选项
        const industryOptions = Object.entries(INDUSTRY_PRICES).map(([name, price]) => ({
            text: `我想购买一间${name} ($${price}派系资金)`,
            callback: async (player: alt.Player) => {
                const result = await this.buyIndustryForFaction(player, name, price);
                if (result) {
                    const notifyapi = await Rebar.useApi().getAsync('notify-api');
                    notifyapi.shownotify(player, `成功购买${name}`, 'success');
                }
                Rebar.player.useWebview(player).hide('conversation');
            },
            endConversation: true
        }));

        const npc = await createNPC('s_m_y_mime', '神秘人', pos.pos, pos.rotz, [
            {
                id: 'start',
                text: '想要什么？我还会在这里待一会儿..',
                callback: (player: alt.Player) => {
                    Rebar.player.useAudio(player).playSound('sounds/illegalnpc1.ogg', 0.5);
                },
                options: mainOptions
            },
            {
                id: 'drug',
                text: '哈，想找点乐子？',
                callback: async (player: alt.Player) => {
                    Rebar.player.useAudio(player).playSound('sounds/illegalnpc2.ogg', 0.5);

                    if (this.boughtDrugsPlayers.has(player.id)) {
                        const notifyapi = await Rebar.useApi().getAsync('notify-api');
                        notifyapi.shownotify(player, '我今天已经卖给你货了，等我下次出现再来吧', 'error');
                        Rebar.player.useWebview(player).hide('conversation');
                    }
                },
                options: drugOptions
            },
            {
                id: 'weapon',
                text: '想要去干掉什么人？',
                callback: async (player: alt.Player) => {
                    Rebar.player.useAudio(player).playSound('sounds/illegalnpc4.ogg', 0.5);

                    if (this.boughtWeaponPlayers.has(player.id)) {
                        const notifyapi = await Rebar.useApi().getAsync('notify-api');
                        notifyapi.shownotify(player, '我今天已经卖给你武器了，等我下次出现再来吧', 'error');
                        Rebar.player.useWebview(player).hide('conversation');
                    }
                },
                options: weaponOptions
            },
            {
                id: 'sell',
                text: '让我看看你有什么',
                options: [
                    {
                        text: '我有一些黑钱',
                        callback: blackMoneyCallback,
                        endConversation: true
                    },
                    {
                        text: '我有一些海底宝藏',
                        nextId: 'treasure'
                    }
                ]
            },
            {
                id: 'treasure',
                text: '嗯，让我看看你的宝藏...',
                options: treasureOptions
            },
            {
                id: 'illegalindustry',
                text: '那你可找对人了',
                callback: (player: alt.Player) => {
                    Rebar.player.useAudio(player).playSound('sounds/illegalnpc3.ogg', 0.5);
                },
                options: industryOptions
            },
            {
                id: 'kidnap',
                text: '哦？有什么计划吗？',
                callback: (player: alt.Player) => {
                    Rebar.player.useAudio(player).playSound('sounds/illegalnpc4.ogg', 0.5);
                },
                options: [
                    {
                        text: '我需要一个头套 ($1000)',
                        callback: hoodCallback,
                        endConversation: true
                    }
                ]
            }
        ]);

        this.currentNPC = npc.ped;

        const blip = new alt.PointBlip(pos.pos.x, pos.pos.y, pos.pos.z, false);
        blip.sprite = 1;
        blip.color = 0;
        blip.shortRange = true;
        blip.name = '神秘人';
        this.currentBlip = blip;

        const players = alt.Player.all;
        for (const player of players) {
            if (!player || !player.valid) continue;
            const doc = Rebar.document.character.useCharacter(player);
            if (!doc) continue;
            const docData = doc.get?.();
            if (!docData || !Array.isArray(docData.permissions)) continue;
            if (docData.permissions.includes('admin')) {
                blip.addTarget(player);
            }
        }
    }

    private async createBlackMoneyExchangeCallback() {
        return async (player: alt.Player) => {
            if (!(await securityManager.validateTransactionSecurity(player, '兑换黑钱', RATE_LIMITS.BLACK_MONEY_EXCHANGE))) {
                Rebar.player.useWebview(player).hide('conversation');
                return;
            }

            const playerId = player.id;
            securityManager.setTransactionLock(playerId, true);

            try {
                const inventoryapi = await Rebar.useApi().getAsync('inventory-api');
                const inventory = await inventoryapi.getInventory({ player: player });
                const blackmoney = inventory.filter(item => item.name === '黑钱');
                if (blackmoney.length === 0) {
                    const notifyapi = await Rebar.useApi().getAsync('notify-api');
                    notifyapi.shownotify(player, '你身上没有黑钱', 'error');
                    Rebar.player.useWebview(player).hide('conversation');
                    return;
                }

                const allblackmoney = blackmoney.reduce((total, item) => total + (item.quantity || 0), 0);
                const exchangeAmount = Math.min(allblackmoney, MAX_BLACK_MONEY_EXCHANGE);

                if (exchangeAmount <= 0 || exchangeAmount > MAX_BLACK_MONEY_EXCHANGE) {
                    const notifyapi = await Rebar.useApi().getAsync('notify-api');
                    notifyapi.shownotify(player, '兑换金额异常', 'error');
                    Rebar.player.useWebview(player).hide('conversation');
                    return;
                }

                const whitemoney = Math.floor(exchangeAmount * BLACK_MONEY_EXCHANGE_RATE);

                const subResult = await inventoryapi.subItem('黑钱', exchangeAmount, { player: player });
                if (!subResult) {
                    const notifyapi = await Rebar.useApi().getAsync('notify-api');
                    notifyapi.shownotify(player, '扣除黑钱失败', 'error');
                    Rebar.player.useWebview(player).hide('conversation');
                    return;
                }

                const addSuccess = await securityManager.safeCurrencyAddition(player, '现金', whitemoney, `兑换${exchangeAmount}元黑钱`);
                if (!addSuccess) {
                    await inventoryapi.addItem('黑钱', exchangeAmount, { player: player });
                    const notifyapi = await Rebar.useApi().getAsync('notify-api');
                    notifyapi.shownotify(player, '现金添加失败，黑钱已退回', 'error');
                    Rebar.player.useWebview(player).hide('conversation');
                    return;
                }

                securityManager.updateTransactionRecord(playerId);
                const notifyapi = await Rebar.useApi().getAsync('notify-api');
                notifyapi.shownotify(player, `成功兑换了 ${exchangeAmount} 元黑钱，获得 ${whitemoney} 元现金`, 'success');
                Rebar.player.useWebview(player).hide('conversation');
            } catch (error) {
                console.error(`[IllegalTrade] 兑换黑钱时发生错误:`, error);
                const notifyapi = await Rebar.useApi().getAsync('notify-api');
                notifyapi.shownotify(player, '交易处理失败，请稍后重试', 'error');
                Rebar.player.useWebview(player).hide('conversation');
            } finally {
                securityManager.setTransactionLock(playerId, false);
            }
        };
    }

    private async createTorPermissionCallback() {
        return async (player: alt.Player) => {
            if (!(await securityManager.validateTransactionSecurity(player, '购买tor权限', RATE_LIMITS.GENERAL_PURCHASE))) {
                Rebar.player.useWebview(player).hide('conversation');
                return;
            }

            const hastorperm = Rebar.permissions.usePermissions(player).character.permissions.has('tor');
            if (hastorperm) {
                const notifyapi = await Rebar.useApi().getAsync('notify-api');
                notifyapi.shownotify(player, '你已经拥有tor网络访问权限', 'error');
                Rebar.player.useWebview(player).hide('conversation');
                return;
            }

            const playerId = player.id;
            securityManager.setTransactionLock(playerId, true);

            try {
                const success = await securityManager.safeCurrencyDeduction(player, TOR_PERMISSION_PRICE, 'tor网络权限费');
                if (!success) {
                    const notifyapi = await Rebar.useApi().getAsync('notify-api');
                    notifyapi.shownotify(player, `你需要支付${TOR_PERMISSION_PRICE}元才能访问tor网络`, 'error');
                    Rebar.player.useWebview(player).hide('conversation');
                    return;
                }

                const doc = Rebar.document.character.useCharacter(player);
                if (!doc) {
                    await securityManager.safeCurrencyAddition(player, '现金', TOR_PERMISSION_PRICE, '权限设置失败退款');
                    const notifyapi = await Rebar.useApi().getAsync('notify-api');
                    notifyapi.shownotify(player, '权限设置失败，已退款', 'error');
                    Rebar.player.useWebview(player).hide('conversation');
                    return;
                }

                doc.set('lastgetonionpermission', Date.now());
                Rebar.permissions.usePermissions(player).character.permissions.grant('tor');

                securityManager.updateTransactionRecord(playerId);

                const notifyapi = await Rebar.useApi().getAsync('notify-api');
                notifyapi.shownotify(player, '你已获得tor网络访问权限，有效期15天', 'success');
                Rebar.player.useWebview(player).hide('conversation');
            } catch (error) {
                console.error(`[IllegalTrade] 购买tor权限时发生错误:`, error);
                const notifyapi = await Rebar.useApi().getAsync('notify-api');
                notifyapi.shownotify(player, '交易处理失败，请稍后重试', 'error');
                Rebar.player.useWebview(player).hide('conversation');
            } finally {
                securityManager.setTransactionLock(playerId, false);
            }
        };
    }

    private setupTimers(): void {
        GlobalTimerManager.getInstance().addTask('illegalnpc:spawn', () => {
            this.spawnMysteriousNPC();
        }, 2 * 60 * 60);

        GlobalTimerManager.getInstance().addTask('illegalnpc:checktorpermission', async () => {
            const players = alt.Player.all;
            for (const player of players) {
                if (!player || !player.valid) continue;

                const doc = Rebar.document.character.useCharacter(player);
                if (!doc) continue;

                const characterData = doc.get();
                if (!characterData) continue;

                const permissionSystem = Rebar.permissions.usePermissions(player);
                if (!permissionSystem || !permissionSystem.character || !permissionSystem.character.permissions) continue;

                const hastorperm = permissionSystem.character.permissions.has('tor');

                if (hastorperm && characterData.lastgetonionpermission) {
                    const now = Date.now();
                    const diff = now - characterData.lastgetonionpermission;
                    if (diff > TOR_PERMISSION_DURATION) {
                        permissionSystem.character.permissions.revoke('tor');
                        const notifyapi = await Rebar.useApi().getAsync('notify-api');
                        notifyapi.shownotify(player, '你的tor网络访问权限已过期', 'info');
                    }
                }
            }
        }, 60 * 60);
    }

    private async handlePlayerCharacterBound(player: alt.Player, document: Character): Promise<void> {
        if (!('permissions' in document)) {
            document.permissions = [];
            Rebar.document.character.useCharacter(player).set('permissions', []);
        }
        
        if (document.permissions.includes('admin')) {
            const blip = alt.PointBlip.all.find(blip => blip.name === '神秘人');
            if (blip) {
                blip.addTarget(player);
            }
        }

        const db = Rebar.database.useDatabase();
        const facitontype = await db.getMany<Faction & { _id: string }>({ name: document.job.faction }, 'faction');

        if (facitontype.length > 0) {
            if (facitontype[0].type === '黑道') {
                const blip = alt.PointBlip.all.find(blip => blip.name === '神秘人');
                if (blip) {
                    blip.addTarget(player);
                }
            }
        }

        const hastorperm = Rebar.permissions.usePermissions(player).character.permissions.has('tor');
        if (hastorperm) {
            const lastgetonionpermission = document.lastgetonionpermission;
            if (lastgetonionpermission) {
                const now = Date.now();
                const diff = now - lastgetonionpermission;
                if (diff > TOR_PERMISSION_DURATION) {
                    Rebar.permissions.usePermissions(player).character.permissions.revoke('tor');
                }
            }
        }
    }

    private handleReportClose(player: alt.Player): void {
        Rebar.player.useWebview(player).hide('viewreport');
    }
}

export const illegalTradeManager = IllegalTradeManager.getInstance();