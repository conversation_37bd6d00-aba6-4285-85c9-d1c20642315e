import * as alt from 'alt-server';
import { useRebar } from '@Server/index.js';
import { Character } from '@Shared/types/character.js';
import { CollectionNames } from '@Server/document/shared.js';
import { PageNames } from '@Shared/webview/index.js';
import { BagItem } from '@Plugins/inventory/server/index.js';
import { registerItemEffect } from '@Plugins/inventory/server/itemEffects.js';
import { GlobalTimerManager } from '@Plugins/animmenu/server/interval.js';
import type { TreasureDisplayData, TreasureReward } from '../shared/types.js';
import { DEPTH_LEVELS, TREASURE_EVENTS } from '../shared/types.js';

const Rebar = useRebar();
const db = Rebar.database.useDatabase();

const messenger = Rebar.messenger.useMessenger();
const promptbarapi = await Rebar.useApi().getAsync('promptbar-api');
const inventoryapi = await Rebar.useApi().getAsync('inventory-api');
const currencyapi = await Rebar.useApi().getAsync('currency-api');
const notifyapi = await Rebar.useApi().getAsync('notify-api');
const progressbarapi = await Rebar.useApi().getAsync('progressbar-api');

// 创建海底宝箱虚拟实体组
const treasureBoxGroup = new alt.VirtualEntityGroup(100);

// 数据库集合定义
interface TreasureBoxDoc {
    _id?: string;
    x: number;
    y: number;
    z: number;
    depth: number;
    created: number; // 创建时间戳
}

interface TreasureSystemDoc {
    _id?: string;
    lastchecktime: number;
}

const TREASURE_COLLECTION = 'TreasureBoxes';
const TREASURE_SYSTEM_COLLECTION = 'TreasureSystem';

// 配置
const modelname = 'seatreasure';
const centerPos = { x: -4336.10107421875, y: -2112.896728515625, z: -1.4095458984375 };
const searchRadius = 1000;
const treasureCount = 20;
const minDepth = -160;
const maxDepth = -40;

const MIN_TREASURE_SPACING = 300;          // 最小间距
const placedTreasurePositions: alt.Vector3[] = [];   // 已放置宝箱坐标

function distance3D(a: alt.Vector3, b: alt.Vector3): number {
    const dx = a.x - b.x;
    const dy = a.y - b.y;
    const dz = a.z - b.z;
    return Math.sqrt(dx * dx + dy * dy + dz * dz);
}

function isFarEnough(pos: alt.Vector3): boolean {
    // 与所有已放置宝箱比较
    return placedTreasurePositions.every(p => distance3D(p, pos) >= MIN_TREASURE_SPACING);
}

// 存储所有宝箱的信息
interface TreasureBox {
    virtualEntity: alt.VirtualEntity;
    interaction: any;
    pos: alt.Vector3;
    depth: number;
    dbId: string; // 数据库ID
}

const treasureBoxes: Map<string, TreasureBox> = new Map();

// 数据库操作函数
async function saveTreasureToDb(pos: { x: number, y: number, z: number }): Promise<string> {
    const treasureDoc: TreasureBoxDoc = {
        x: pos.x,
        y: pos.y,
        z: pos.z,
        depth: pos.z,
        created: Date.now()
    };
    
    const result = await db.create(treasureDoc, TREASURE_COLLECTION);
    return result;
}

async function deleteTreasureFromDb(dbId: string): Promise<void> {
    await db.deleteDocument(dbId, TREASURE_COLLECTION);
}

async function loadTreasuresFromDb(): Promise<any[]> {
    return await db.getAll(TREASURE_COLLECTION);
}

async function updateSystemTime(): Promise<void> {
    const systemDoc = await db.get({}, TREASURE_SYSTEM_COLLECTION);
    
    if (systemDoc) {
        await db.update({ _id: systemDoc._id, lastchecktime: Date.now() }, TREASURE_SYSTEM_COLLECTION);
    } else {
        await db.create({ lastchecktime: Date.now() }, TREASURE_SYSTEM_COLLECTION);
    }
}

async function getSystemData(): Promise<any | null> {
    return await db.get({}, TREASURE_SYSTEM_COLLECTION);
}

// 生成随机位置
function generateRandomPosition(): { x: number, y: number, z: number } {
    const angle = Math.random() * Math.PI * 2;
    const distance = Math.random() * searchRadius;

    const x = centerPos.x + Math.cos(angle) * distance;
    const y = centerPos.y + Math.sin(angle) * distance;
    const z = minDepth + Math.random() * (maxDepth - minDepth);

    return { x, y, z };
}

// 根据深度获取奖励类型
function getRewardByDepth(depth: number): { goldBars: number, treasureItems: Array<{name: string, quantity: number}> } {
    const absDepth = Math.abs(depth);
    const treasureItems: Array<{name: string, quantity: number}> = [];

    // 珍稀物品概率表
    const treasureRates = {
        '古代金币': { shallow: 0.15, medium: 0.20, deep: 0.25 },
        '海洋珍珠': { shallow: 0.10, medium: 0.15, deep: 0.20 },
        '蓝宝石': { shallow: 0.05, medium: 0.10, deep: 0.15 },
        '祖母绿': { shallow: 0.03, medium: 0.08, deep: 0.12 },
        '钻石': { shallow: 0.01, medium: 0.05, deep: 0.10 },
        '古代文物': { shallow: 0.02, medium: 0.06, deep: 0.08 },
        '沉船宝箱钥匙': { shallow: 0.005, medium: 0.02, deep: 0.05 }
    };

    if (absDepth <= -40 && absDepth >= -80) {
        // 浅层奖励
        // 检查珍稀物品
        for (const [itemName, rates] of Object.entries(treasureRates)) {
            if (Math.random() < rates.shallow) {
                let quantity = 1;
                if (itemName === '古代金币' || itemName === '海洋珍珠') {
                    quantity = Math.random() < 0.3 ? 2 : 1; // 30%概率获得2个
                }
                treasureItems.push({ name: itemName, quantity });
            }
        }

        // 如果没有珍稀物品，给予金条
        if (treasureItems.length === 0) {
            return { goldBars: 1, treasureItems };
        } else {
            // 有珍稀物品时，还有30%概率额外获得1根金条
            const goldBars = Math.random() < 0.3 ? 1 : 0;
            return { goldBars, treasureItems };
        }
    } else if (absDepth > -80 && absDepth <= -120) {
        // 中层奖励
        for (const [itemName, rates] of Object.entries(treasureRates)) {
            if (Math.random() < rates.medium) {
                let quantity = 1;
                if (itemName === '古代金币' || itemName === '海洋珍珠') {
                    quantity = Math.random() < 0.5 ? (Math.random() < 0.5 ? 2 : 3) : 1; // 更高概率获得多个
                }
                treasureItems.push({ name: itemName, quantity });
            }
        }

        if (treasureItems.length === 0) {
            return { goldBars: 2, treasureItems };
        } else {
            // 有珍稀物品时，还有50%概率额外获得1-2根金条
            const goldBars = Math.random() < 0.5 ? (Math.random() < 0.5 ? 1 : 2) : 0;
            return { goldBars, treasureItems };
        }
    } else if (absDepth > -120 && absDepth <= -160) {
        // 深层奖励
        for (const [itemName, rates] of Object.entries(treasureRates)) {
            if (Math.random() < rates.deep) {
                let quantity = 1;
                if (itemName === '古代金币' || itemName === '海洋珍珠') {
                    quantity = 1 + Math.floor(Math.random() * 3); // 1-3个
                } else if (itemName === '蓝宝石' || itemName === '祖母绿') {
                    quantity = Math.random() < 0.4 ? 2 : 1; // 40%概率获得2个
                }
                treasureItems.push({ name: itemName, quantity });
            }
        }

        if (treasureItems.length === 0) {
            return { goldBars: 6, treasureItems };
        } else {
            // 有珍稀物品时，还有70%概率额外获得2-4根金条
            const goldBars = Math.random() < 0.7 ? (2 + Math.floor(Math.random() * 3)) : 0; // 2-4根
            return { goldBars, treasureItems };
        }
    }

    return { goldBars: 1, treasureItems: [] };
}

// 获取深度等级文本
function getDepthLevelText(depth: number): string {
    const absDepth = Math.abs(depth);
    if (absDepth <= 80) return DEPTH_LEVELS.SHALLOW;
    if (absDepth <= 120) return DEPTH_LEVELS.MEDIUM;
    return DEPTH_LEVELS.DEEP;
}

// 显示宝藏展示界面
function showTreasureDisplay(player: alt.Player, reward: TreasureReward, depth: number) {
    const displayData: TreasureDisplayData = {
        goldBars: reward.goldBars,
        treasureItems: reward.treasureItems,
        depth: depth,
        depthLevel: getDepthLevelText(depth)
    };

    // 直接从服务端显示 webview
    const webview = Rebar.player.useWebview(player);
    webview.show('TreasureDisplay', 'page');
    
    // 发送数据到 webview
    webview.emit(TREASURE_EVENTS.SHOW_TREASURE_DISPLAY, displayData);
}

// 创建宝箱
async function createTreasureBox(pos: { x: number, y: number, z: number }, dbId?: string): Promise<void> {
    // 如果没有提供dbId，说明是新创建的宝箱，需要保存到数据库
    const treasureDbId = dbId || await saveTreasureToDb(pos);

    // 创建虚拟实体
    const virtualEntity = new alt.VirtualEntity(
        treasureBoxGroup,
        new alt.Vector3(pos.x, pos.y, pos.z),
        200, // 流媒体距离
        {
            type: 'treasureBox',
            model: modelname,
            frozen: true,
            rotation: Math.random() * 360
        }
    );

    // 创建交互区域
    const interaction = Rebar.controllers.useInteraction(
        new alt.ColshapeCylinder(pos.x, pos.y, pos.z -1, 3, 3),
        'player'
    );

    const uid = `treasure_${Date.now()}_${Math.random().toString(36).substring(7)}`;

    // 设置交互事件
    interaction.onEnter((player) => {
        promptbarapi.showPromptBar(player, '开锁');
    });

    interaction.onLeave((player) => {
        promptbarapi.hidePromptBar(player);
    });

    interaction.on(async (player) => {
        const treasure = treasureBoxes.get(uid);
        if (!treasure) {
            return;
        }

        // 显示开锁游戏
        player.setMeta('current_treasure_uid', uid);
        Rebar.player.useWebview(player).show('truelock', 'page', true);
    });

    // 存储宝箱信息
    treasureBoxes.set(uid, {
        virtualEntity: virtualEntity,
        interaction: interaction,
        pos: new alt.Vector3(pos.x, pos.y, pos.z),
        depth: pos.z,
        dbId: treasureDbId
    });
}

// 处理开锁成功 - 监听truelock的成功事件
alt.onClient('getmetal:unlockSuccess', async (player: alt.Player) => {
    // 检查是否在开海底宝箱
    const uid = player.getMeta('current_treasure_uid') as string;
    if (!uid) return; // 如果没有uid，说明不是在开海底宝箱

    const treasure = treasureBoxes.get(uid);
    if (!treasure) return;

    // 获取奖励
    const reward = getRewardByDepth(treasure.depth);
    const depthLevel = getDepthLevelText(treasure.depth);

    // 给予金条奖励
    if (reward.goldBars > 0) {
        const [success, message] = await inventoryapi.addItem('金条', reward.goldBars, { player });
        if (!success) {
            notifyapi.shownotify(player, `无法添加金条：${message}`, 'error');
            return; // 如果添加失败，不继续处理
        }
    }

    // 给予珍稀物品奖励
    if (reward.treasureItems && reward.treasureItems.length > 0) {
        for (const item of reward.treasureItems) {
            const [success, message] = await inventoryapi.addItem(item.name, item.quantity, { player });
            if (!success) {
                notifyapi.shownotify(player, `无法添加${item.name}：${message}`, 'error');
                // 继续处理其他物品，不返回
            }
        }
    }

    // 销毁宝箱
    treasure.interaction.destroy();
    treasure.virtualEntity.destroy();
    await deleteTreasureFromDb(treasure.dbId);
    treasureBoxes.delete(uid);

    // 关闭开锁界面
    Rebar.player.useWebview(player).hide('truelock');
    player.deleteMeta('current_treasure_uid');

    // 显示宝藏展示界面
    showTreasureDisplay(player, reward, treasure.depth);
});

// 处理关闭宝藏展示界面
alt.onClient('treasureseas:closePage', (player: alt.Player) => {
    // 从服务端隐藏 webview
    const webview = Rebar.player.useWebview(player);
    webview.hide('TreasureDisplay');
    
    console.log(`玩家 ${player.name} 关闭了宝藏展示界面`);
});

// 生成需要的宝箱数量
async function generateTreasures(count: number): Promise<void> {
    // console.log(`[海底寻宝] 开始生成 ${count} 个新宝箱...`);

    let generated = 0;
    let tries = 0;
    
    while (generated < count) {
        // 候选坐标
        const rawPos = generateRandomPosition();
        const pos = new alt.Vector3(rawPos.x, rawPos.y, rawPos.z);

        // 若与已有宝箱距离不足 100 米则跳过
        if (!isFarEnough(pos)) {
            if (++tries > 10_000) {
                // console.warn('[海底寻宝] 尝试次数过多，可能是 searchRadius 太小或宝箱数量太多');
                break;
            }
            continue;
        }

        await createTreasureBox(rawPos); // 创建新宝箱并保存到数据库
        placedTreasurePositions.push(pos); // 记录位置
        generated++;
    }

    // console.log(`[海底寻宝] 成功生成 ${generated} / ${count} 个新宝箱！`);
}

// 初始化宝箱系统
async function initializeTreasureBoxes(): Promise<void> {
    // console.log('[海底寻宝] 初始化宝箱系统...');

    // 确保数据库集合存在
    await db.createCollection(TREASURE_COLLECTION);
    await db.createCollection(TREASURE_SYSTEM_COLLECTION);

    // 获取系统数据
    const systemData = await getSystemData();
    const currentTime = Date.now();

    // 从数据库加载现有宝箱
    const existingTreasures = await loadTreasuresFromDb();
    
    // 重建内存中的位置记录
    placedTreasurePositions.length = 0;
    for (const treasureDoc of existingTreasures) {
        const pos = new alt.Vector3(treasureDoc.x, treasureDoc.y, treasureDoc.z);
        placedTreasurePositions.push(pos);
        
        // 重新创建宝箱对象（从数据库加载）
        await createTreasureBox({ x: treasureDoc.x, y: treasureDoc.y, z: treasureDoc.z }, treasureDoc._id);
    }

    // console.log(`[海底寻宝] 从数据库加载了 ${existingTreasures.length} 个宝箱`);

    let treasuresToCreate = 0;

    if (!systemData || !systemData.lastchecktime) {
        // 第一次启动：创建20个宝箱
        treasuresToCreate = treasureCount - existingTreasures.length;
        // console.log('[海底寻宝] 检测到第一次启动，将创建初始宝箱');
    } else {
        // 计算时间差（小时）
        const hoursPassed = (currentTime - systemData.lastchecktime) / (1000 * 60 * 60);
        
        if (existingTreasures.length === 0) {
            // 没有宝箱但有lastchecktime
            if (hoursPassed >= 20) {
                treasuresToCreate = 20;
                // console.log(`[海底寻宝] 过了${hoursPassed.toFixed(1)}小时，创建20个宝箱`);
            } else if (hoursPassed >= 1) {
                treasuresToCreate = Math.floor(hoursPassed);
                // console.log(`[海底寻宝] 过了${hoursPassed.toFixed(1)}小时，创建${treasuresToCreate}个宝箱`);
            }
        } else if (existingTreasures.length < treasureCount) {
            // 宝箱不足20个，按时间补充
            const needed = treasureCount - existingTreasures.length;
            treasuresToCreate = Math.min(needed, Math.floor(hoursPassed));
            if (treasuresToCreate > 0) {
                // console.log(`[海底寻宝] 宝箱不足，过了${hoursPassed.toFixed(1)}小时，补充${treasuresToCreate}个宝箱`);
            }
        }
    }

    // 生成需要的宝箱
    if (treasuresToCreate > 0) {
        await generateTreasures(treasuresToCreate);
    }

    // 更新系统时间
    await updateSystemTime();

    // console.log(`[海底寻宝] 系统初始化完成，当前共有 ${placedTreasurePositions.length} 个宝箱`);
}

// 定时检查和补充宝箱
function startTreasureMonitoring(): void {
    // 每小时检查一次
  GlobalTimerManager.getInstance().addTask(async () => {
        // console.log('[海底寻宝] 执行定时检查...');
        
        const currentTreasures = await loadTreasuresFromDb();
        
        if (currentTreasures.length < treasureCount) {
            const needed = treasureCount - currentTreasures.length;
            // console.log(`[海底寻宝] 宝箱不足，需要补充 ${needed} 个`);
            
            // 补充1个宝箱
            await generateTreasures(1);
        } else {
            // console.log('[海底寻宝] 宝箱数量充足，无需补充');
        }
        
        // 更新检查时间
        await updateSystemTime();
    }, 60 * 60 ); // 1小时
}

initializeTreasureBoxes().then(() => {
    startTreasureMonitoring();
    initializeSharks();
});

// 初始化鲨鱼系统
function initializeSharks(): void {
    // console.log('[海底寻宝] 初始化鲨鱼系统...');
    
    for (let i = 0; i < sharkSpawnPoints.length; i++) {
        const spawnPoint = sharkSpawnPoints[i];
        const shark = new Shark(spawnPoint, SharkType.GreatWhite);
        sharkList.push(shark);
    }
    
    // console.log(`[海底寻宝] 成功生成 ${sharkList.length} 条鲨鱼`);
}

// 鲨鱼事件处理
alt.on('entityEnterColshape', (colshape: alt.Colshape, entity: alt.Entity) => {
    if (!(entity instanceof alt.Player)) return;

    const foundShark = sharkList.find(x => x.sharkDestinationColshape != null && x.sharkDestinationColshape.id == colshape.id);
    if (foundShark == null) return;
    if (foundShark.sharkPed.id != entity.id) return;

    foundShark.reachDestination();
});

alt.onClient('treasureseas:netOwnerChange', (player: alt.Player, remoteId: number) => {
    const foundShark = sharkList.find(x => {
        try {
            return x.sharkPed && x.sharkPed.valid && x.sharkPed.id == remoteId;
        } catch (error) {
            return false;
        }
    });

    if (foundShark == null) {
        return;
    }
    
    if (!foundShark.sharkPed.netOwner || foundShark.sharkPed.netOwner.id != player.id) {
        return;
    }

    // 检查是否正在初始化，避免重复
    if (foundShark.isInitializing) {
        return;
    }

    foundShark.setInitialStatus();
});

// 处理鲨鱼死亡
alt.on('pedDeath', async (ped: alt.Ped, killer: alt.Entity, weaponHash: number) => {
    const foundShark = sharkList.find(x => {
        try {
            return x.sharkPed && x.sharkPed.valid && x.sharkPed.id == ped.id;
        } catch (error) {
            return false;
        }
    });
    
    if (foundShark == null) return;

    foundShark.isDead = true;
    foundShark.sharkPed.frozen = true;

    // 清理所有状态
    if (foundShark.sharkDestination != null) {
        foundShark.sharkDestination = null;
    }

    if (foundShark.sharkDestinationColshape != null) {
        foundShark.sharkDestinationColshape.destroy();
        foundShark.sharkDestinationColshape = null;
    }

    if (foundShark.attackTarget != null) {
        foundShark.attackTarget = null;
    }

    if (foundShark.endAttackTimeout != null) {
        alt.clearTimeout(foundShark.endAttackTimeout);
        foundShark.endAttackTimeout = null;
    }

    if (foundShark.endFleeTimeout != null) {
        alt.clearTimeout(foundShark.endFleeTimeout);
        foundShark.endFleeTimeout = null;
    }

    if (foundShark.rangeCheck != null) {
        GlobalTimerManager.getInstance().removeTask(foundShark.rangeCheck);
        foundShark.rangeCheck = null;
    }

    if (killer instanceof alt.Player) {
        notifyapi.shownotify(killer, '你击杀了一条鲨鱼！可以下去肢解获取材料', 'success');
    }

    // 10分钟后自动清理鲨鱼尸体并重新生成
    const sharkSpawn = foundShark.sharkSpawn;
    const sharkType = foundShark.sharkType;

    alt.setTimeout(() => {
        const sharkIndex = sharkList.findIndex(x => {
            try {
                return x.sharkPed && x.sharkPed.valid && x.sharkPed.id === ped.id;
            } catch (error) {
                return false;
            }
        });
        
        if (sharkIndex !== -1) {
            const shark = sharkList[sharkIndex];
            try {
                if (shark.sharkPed && shark.sharkPed.valid) {
                    shark.sharkPed.destroy();
                }
            } catch (error) {
                // 忽略销毁错误
            }
            
            sharkList.splice(sharkIndex, 1);

            // 重新生成鲨鱼
            alt.setTimeout(() => {
                const newShark = new Shark(sharkSpawn, sharkType);
                sharkList.push(newShark);
                // console.log('[海底寻宝] 重新生成了一条鲨鱼');
            }, 30000); // 30秒后重新生成
        }
    }, 10 * 60 * 1000); // 10分钟
});

// 处理鲨鱼受伤 - 受伤时会逃跑一段时间
alt.on('pedDamage', (ped: alt.Ped, attacker: alt.Entity, healthDamage: number, armourDamage: number, weapon: number) => {
    const foundShark = sharkList.find(x => {
        try {
            return x.sharkPed && x.sharkPed.valid && x.sharkPed.id == ped.id;
        } catch (error) {
            return false;
        }
    });
    
    if (foundShark == null) return;

    if ((foundShark.sharkPed.health - healthDamage) > 0) {
        // 鲨鱼受伤后会短暂逃跑
        foundShark.setFleeing();
    }
});

// 肢解鲨鱼功能
alt.onClient('treasureseas:pickupShark', async (player: alt.Player) => {
    const closestShark = sharkList.find(x => {
        try {
            return x.isDead && x.sharkPed && x.sharkPed.valid && x.sharkPed.pos.distanceTo(player.pos) < 3;
        } catch (error) {
            return false;
        }
    });
    
    if (closestShark == null) {
        //notifyapi.shownotify(player, '附近没有可以肢解的鲨鱼', 'error');
        return;
    }

    if (player.getMeta("dismemberingShark") == true) return;

    if (closestShark.sharkPed.getMeta("isDismembered") == true) {
        notifyapi.shownotify(player, '这条鲨鱼已经被肢解过了', 'error');
        return;
    }

    // 检查玩家是否在水中或船上
    const isInWater = player.getStreamSyncedMeta('isInWater') || false;
    const isNearWater = player.pos.z < 10; // 接近水面
    
    if (!isInWater && !isNearWater) {
        notifyapi.shownotify(player, '你需要在水中或接近水面才能肢解鲨鱼', 'error');
        return;
    }

    const hasKnife = await inventoryapi.hasitem('剥皮刀', 1, { player });
    if (!hasKnife) {
        notifyapi.shownotify(player, '需要剥皮刀才能肢解鲨鱼', 'error');
        return;
    }

    player.emit('icantmove');
    player.setMeta("dismemberingShark", true);
    
    // 播放肢解动画
    player.playAnimation("amb@medic@standing@kneel@base", "base", 8.0, -8.0, -1, 1, 0, false, false, false);
    
    Rebar.player.useAttachment(player).add({
        uid: 'sharkknife',
        model: 'w_me_machette_lr',
        bone: 57,
        offset: { x: 0.0, y: -0.0, z: 0.0 },
        rot: { x: 0, y: -0.0, z: -0.0 }
    });

    progressbarapi.showProgressBar(player, '肢解鲨鱼', 45); // 45秒肢解时间
    closestShark.sharkPed.setMeta("isDismembered", true);

    await alt.Utils.wait(45000);

    Rebar.player.useAttachment(player).remove('sharkknife');
    player.emit('icanmove');
    player.clearTasks();
    player.setMeta("dismemberingShark", false);

    // 剥皮刀有概率损坏
    const random = Math.random();
    if (random <= 0.15) { // 15%概率损坏
        inventoryapi.subItem('剥皮刀', 1, { player });
        notifyapi.shownotify(player, '剥皮刀在肢解过程中损坏了', 'error');
    }

    // 给予鲨鱼材料
    const rewards = [
        { name: '鲨鱼肉', amount: 1, message: '获得了新鲜的鲨鱼肉' },
        { name: '鲨鱼鳍', amount: 1, message: '获得了珍贵的鲨鱼鳍' },
        { name: '鲨鱼牙', amount: 1, message: '获得了锋利的鲨鱼牙' },
        { name: '鲨鱼皮', amount: 1, message: '获得了坚韧的鲨鱼皮' }
    ];

    for (const reward of rewards) {
        const [success] = await inventoryapi.addItem(reward.name, reward.amount, { player });
        if (success) {
            notifyapi.shownotify(player, reward.message, 'success');
        }
    }

    notifyapi.shownotify(player, '成功肢解了鲨鱼，获得了珍贵的海洋材料！', 'success');
});


// 测试命令 - 传送到最近的宝箱
messenger.commands.register({
    name: 'gototreasure',
    desc: '传送到最近的宝箱',
    options: { permissions: ['admin'] },
    callback: (player: alt.Player) => {
        let nearestTreasure: TreasureBox | null = null;
        let nearestDistance = Infinity;
        let nearestUid = '';

        treasureBoxes.forEach((treasure, uid) => {
                const dist = player.pos.distanceTo(treasure.pos);
                if (dist < nearestDistance) {
                    nearestDistance = dist;
                    nearestTreasure = treasure;
                    nearestUid = uid;
            }
        });

        if (nearestTreasure) {
            player.pos = new alt.Vector3(nearestTreasure.pos.x, nearestTreasure.pos.y, nearestTreasure.pos.z + 2);
            const depthInfo = Math.abs(nearestTreasure.depth) <= 60 ? '浅层' :
                Math.abs(nearestTreasure.depth) <= 100 ? '中层' : '深层';
            notifyapi.shownotify(player, `已传送到最近的宝箱 (深度: ${nearestTreasure.depth.toFixed(1)}m, ${depthInfo})`, 'success');
        } else {
            notifyapi.shownotify(player, '没有找到未开启的宝箱', 'error');
        }
    }
});

// 传送到鲨鱼位置
messenger.commands.register({
    name: 'gotoshark',
    desc: '传送到最近的鲨鱼',
    options: { permissions: ['admin'] },
    callback: (player: alt.Player) => {
        let nearestShark: Shark | null = null;
        let nearestDistance = Infinity;

        sharkList.forEach((shark) => {
            if (!shark.isDead) {
                const dist = player.pos.distanceTo(shark.sharkPed.pos);
                if (dist < nearestDistance) {
                    nearestDistance = dist;
                    nearestShark = shark;
                }
            }
        });

        if (nearestShark) {
            player.pos = new alt.Vector3(nearestShark.sharkPed.pos.x, nearestShark.sharkPed.pos.y, nearestShark.sharkPed.pos.z + 5);
            notifyapi.shownotify(player, `已传送到最近的鲨鱼 (距离: ${nearestDistance.toFixed(1)}m)`, 'success');
        } else {
            notifyapi.shownotify(player, '没有找到存活的鲨鱼', 'error');
        }
    }
});


// 强制重新初始化鲨鱼
messenger.commands.register({
    name: 'resetshark',
    desc: '强制重新初始化所有鲨鱼',
    options: { permissions: ['admin'] },
    callback: (player: alt.Player) => {
        // console.log(`[鲨鱼调试] 管理员 ${player.name} 请求重新初始化鲨鱼`);
        
        sharkList.forEach((shark, index) => {
            // console.log(`[鲨鱼调试] 重新初始化鲨鱼 ${index + 1}`);
            if (shark.sharkPed.netOwner) {
                shark.setInitialStatus();
            } else {
                // console.log(`[鲨鱼调试] 鲨鱼 ${index + 1} 没有网络拥有者，跳过初始化`);
            }
        });
        
        notifyapi.shownotify(player, '已尝试重新初始化所有鲨鱼', 'success');
    }
});


// 创建鲨鱼相关物品
const dbItemApi = await Rebar.useApi().getAsync('dbitem-api');

dbItemApi.createitem({
    name: '鲨鱼牙',
    desc: '锋利的鲨鱼牙齿，可以制作装饰品或工艺品',
    type: '材料',
    icon: 'shark_tooth.webp',
    weight: 0.2,
    maxStack: 50,
});

dbItemApi.createitem({
    name: '鲨鱼皮',
    desc: '坚韧的鲨鱼皮，是制作高级皮具的优质材料',
    type: '材料',
    icon: 'shark_skin.webp',
    weight: 2.0,
    maxStack: 15,
});

// 创建珍稀宝藏物品
dbItemApi.createitem({
    name: '古代金币',
    desc: '来自古代沉船的金币，极具收藏价值',
    type: '材料',
    icon: 'ancient_gold_coin.webp',
    weight: 0.1,
    maxStack: 100,
});

dbItemApi.createitem({
    name: '海洋珍珠',
    desc: '深海中孕育的天然珍珠，光泽动人',
    type: '材料',
    icon: 'ocean_pearl.webp',
    weight: 0.05,
    maxStack: 100,
});

dbItemApi.createitem({
    name: '蓝宝石',
    desc: '稀有的海蓝宝石，闪烁着海洋的光芒',
    type: '材料',
    icon: 'sapphire.webp',
    weight: 0.08,
    maxStack: 50,
});

dbItemApi.createitem({
    name: '祖母绿',
    desc: '极其珍贵的祖母绿宝石，价值连城',
    type: '材料',
    icon: 'emerald.webp',
    weight: 0.1,
    maxStack: 30,
});

dbItemApi.createitem({
    name: '钻石',
    desc: '最稀有的海底钻石，闪闪发光',
    type: '材料',
    icon: 'diamond.webp',
    weight: 0.05,
    maxStack: 20,
});

dbItemApi.createitem({
    name: '古代文物',
    desc: '神秘的古代文物，蕴含着历史的秘密',
    type: '材料',
    icon: 'ancient_artifact.webp',
    weight: 0.5,
    maxStack: 10,
});

dbItemApi.createitem({
    name: '沉船宝箱钥匙',
    desc: '传说中沉船宝箱的钥匙，极其罕见',
    type: '材料',
    icon: 'treasure_key.webp',
    weight: 0.1,
    maxStack: 5,
});

// 创建氧气瓶物品
dbItemApi.createitem({
    name: '氧气瓶',
    desc: '便携式氧气瓶，可在水下立即恢复氧气并延长潜水时间30秒。基础潜水时间受肺活量技能影响（0-100技能=10-60秒基础时间）',
    type: '工具',
    icon: 'oxygen_tank.webp',
    weight: 0.1,
    maxStack: 100,
});


// 处理客户端请求自动使用氧气瓶
alt.onClient('treasureseas:requestOxygenRefill', async (player: alt.Player) => {
    const hasOxygen = await inventoryapi.hasitem('氧气瓶', 1, { player });

    if (hasOxygen) {
        // 移除一个氧气瓶
        const [success] = await inventoryapi.subItem('氧气瓶', 1, { player });
        if (success) {
            notifyapi.shownotify(player, '自动使用氧气瓶补充氧气', 'info');
            player.emit('treasureseas:refillOxygen');
        }
    } else {
       // notifyapi.shownotify(player, '你没有氧气瓶了！', 'warning');
    }
});

// 处理玩家水中状态更新
alt.onClient('treasureseas:updateWaterStatus', (player: alt.Player, isInWater: boolean) => {
    const currentStatus = player.getStreamSyncedMeta('isInWater');
    if (currentStatus !== isInWater) {
        player.setStreamSyncedMeta('isInWater', isInWater);
    }
});

// 处理鲨鱼咬击攻击
alt.onClient('treasureseas:sharkBitePlayer', (player: alt.Player, sharkRemoteId: number, targetRemoteId: number) => {
    // 查找对应的鲨鱼和目标玩家
    const shark = sharkList.find(s => s.sharkPed && s.sharkPed.valid && s.sharkPed.id === sharkRemoteId);
    const targetPlayer = alt.Player.all.find(p => p.id === targetRemoteId);
    
    if (!shark || !targetPlayer) {
        return;
    }
    
    if (shark.isDead) {
        return;
    }
    
    // 验证距离（防止作弊）
    const distance = shark.sharkPed.pos.distanceTo(targetPlayer.pos);
    if (distance > 5.0) {
        return;
    }
    
    // 造成伤害
    const currentHealth = targetPlayer.health;
    const damage = 25; // 每次咬击造成25点伤害
    const newHealth = Math.max(0, currentHealth - damage);
    
    targetPlayer.health = newHealth;
    
    // 通知玩家被鲨鱼攻击
    notifyapi.shownotify(targetPlayer, `你被鲨鱼咬伤了！(-${damage} 血量)`, 'error');
    
    // 如果玩家死亡
    if (newHealth <= 0) {
        notifyapi.shownotify(targetPlayer, '你被鲨鱼杀死了！', 'error');
    }
    
    // 其他玩家也能看到攻击信息
    const nearbyPlayers = alt.Player.all.filter(p => 
        p.id !== targetPlayer.id && 
        p.pos.distanceTo(targetPlayer.pos) <= 50
    );
    
    nearbyPlayers.forEach(p => {
        notifyapi.shownotify(p, `${targetPlayer.name} 被鲨鱼攻击了！`, 'warning');
    });
});

alt.on('rebar:playerPageOpened', (player: alt.Player, page: PageNames) => {
    if (page === 'truelock') {
      Rebar.player.useWorld(player).disableControls();
      player.setStreamSyncedMeta('do', '正在开锁');
    }
  });
  
  alt.on('rebar:playerPageClosed', (player: alt.Player, page: PageNames) => {
    if (page === 'truelock') {
      Rebar.player.useWorld(player).enableControls();
      player.deleteMeta('do');
    }
  });
  
  // 鲨鱼系统配置
  const sharkMoveRadius = 50; // 鲨鱼游荡半径 - 从200减少到50米
  const sharkAttackRange = 30; // 鲨鱼攻击范围
  const sharkFleeDistance = 50; // 鲨鱼逃跑距离

  // 鲨鱼生成点（浅层海域）
  const sharkSpawnPoints: alt.Vector3[] = [
      new alt.Vector3(-4200, -2000, -20), // 海面附近
      new alt.Vector3(-4400, -2100, -30),
      new alt.Vector3(-4300, -2200, -25),
      new alt.Vector3(-4500, -2000, -35),
      new alt.Vector3(-4150, -2150, -40),
      new alt.Vector3(-4350, -2050, -15),
      new alt.Vector3(-4450, -2250, -45),
      new alt.Vector3(-4250, -2300, -30),
  ];

  let sharkList: Shark[] = [];

  const enum SharkStatus {
      Wandering,
      Attacking,
      Fleeing
  }

  const enum SharkType {
      GreatWhite = "a_c_sharktiger"
  }

  // 辅助函数获取鲨鱼状态名称
  function getSharkStatusName(status: SharkStatus): string {
      switch (status) {
          case SharkStatus.Wandering: return 'Wandering';
          case SharkStatus.Attacking: return 'Attacking';
          case SharkStatus.Fleeing: return 'Fleeing';
          default: return 'Unknown';
      }
  }

  // 鲨鱼类
  class Shark {
      sharkPed: alt.Ped;
      sharkSpawn: alt.Vector3;
      sharkStatus: SharkStatus;
      sharkType: SharkType;
      sharkDestination: alt.Vector2 | null;
      sharkDestinationColshape: alt.Colshape | null;
      attackTarget: alt.Player | null;
      endAttackTimeout: number | null;
      endFleeTimeout: number | null;
      rangeCheck: string | null;
      isDead: boolean;
      isInitializing: boolean; // 防止重复初始化
      lastInitTime: number; // 最后初始化时间

          constructor(spawnPosition: alt.Vector3, sharkType: SharkType) {
        this.sharkPed = new alt.Ped(sharkType, spawnPosition, new alt.Vector3(0, 0, 0));
        this.sharkSpawn = spawnPosition;
        this.sharkStatus = SharkStatus.Wandering;
        this.sharkType = sharkType;
        this.sharkDestination = null;
        this.sharkDestinationColshape = null;
        this.attackTarget = null;
        this.endAttackTimeout = null;
        this.endFleeTimeout = null;
        this.isDead = false;
        this.isInitializing = false;
        this.lastInitTime = 0;

        // 设置鲨鱼血量
        alt.setTimeout(() => {
            if (this.sharkPed && this.sharkPed.valid) {
                this.sharkPed.health = 800; // 鲨鱼血量
                this.sharkPed.maxHealth = 800;
            }
        }, 1000);

        // 鲨鱼攻击性检测范围 - 只攻击潜水状态的玩家
        this.rangeCheck = GlobalTimerManager.getInstance().addTask(() => {
            if (this.isDead) return;

            // 检查鲨鱼是否距离生成点太远
            const distanceFromSpawn = this.sharkPed.pos.distanceTo(this.sharkSpawn);
            if (distanceFromSpawn > sharkMoveRadius + 50) {
                if (this.sharkPed.netOwner) {
                    this.setWandering(); // 只有在有网络拥有者时才设置游荡状态
                }
                return;
            }

            // 只检测攻击，不处理网络拥有者
            if (this.sharkStatus !== SharkStatus.Wandering || !this.sharkPed.netOwner) {
                return;
            }

            let allPlayers = alt.Player.all;
            let nearbyPlayers = allPlayers.filter(player => {
                const distance = player.pos.distanceTo(this.sharkPed.pos);
                const isUnderwater = player.getStreamSyncedMeta('isInWater') || false;
                const isDiving = player.pos.z < -5; // 潜水深度至少5米
                
                return distance < sharkAttackRange && isUnderwater && isDiving;
            });

            if (nearbyPlayers.length > 0) {
                // 攻击最近的潜水玩家
                const targetPlayer = nearbyPlayers.reduce((closest, player) => {
                    return player.pos.distanceTo(this.sharkPed.pos) < closest.pos.distanceTo(this.sharkPed.pos) ? player : closest;
                });
                
                this.setAttacking(targetPlayer);
            }
        }, 5); // 每5秒检查一次
    }



      public setInitialStatus() {
          if (this.sharkPed.netOwner == null) {
              return;
          }

          // 防止重复初始化 - 5秒内不重复初始化
          const currentTime = Date.now();
          if (this.isInitializing || (currentTime - this.lastInitTime) < 5000) {
              return;
          }

          this.isInitializing = true;
          this.lastInitTime = currentTime;

          this.sharkPed.netOwner.emitRaw('treasureseas:setSharkInitialStatus', this.sharkPed);

          // 清理状态
          if (this.sharkDestination != null) {
              this.sharkDestination = null;
          }

          if (this.sharkDestinationColshape != null) {
              this.sharkDestinationColshape.destroy();
              this.sharkDestinationColshape = null;
          }

          if (this.attackTarget != null) {
              this.attackTarget = null;
          }

          if (this.endAttackTimeout != null) {
              alt.clearTimeout(this.endAttackTimeout);
              this.endAttackTimeout = null;
          }

          if (this.endFleeTimeout != null) {
              alt.clearTimeout(this.endFleeTimeout);
              this.endFleeTimeout = null;
          }

          // 2秒后开始游荡，并标记初始化完成
          alt.setTimeout(() => {
              this.isInitializing = false;
              this.setWandering();
          }, 2000);
      }

      public reachDestination() {
          if (this.sharkDestination == null) return;
          if (this.sharkDestinationColshape == null) return;

          this.sharkDestination = null;
          this.sharkDestinationColshape.destroy();
          this.sharkDestinationColshape = null;

          this.setWandering();
      }

      public setSharkStatus() {
          if (this.sharkPed.netOwner == null) {
              return;
          }
          if (this.isDead) return;

          switch (this.sharkStatus) {
              case SharkStatus.Wandering:
                  const randomCoords = this.generateRandomWaterPosition();
                  const coordsAngle = this.calculateHeading(this.sharkPed.pos.x, this.sharkPed.pos.y, randomCoords.x, randomCoords.y);

                  this.sharkDestinationColshape = new alt.ColshapeCircle(randomCoords.x, randomCoords.y, 5);
                  this.sharkPed.netOwner.emitRaw('treasureseas:setSharkWandering', this.sharkPed, randomCoords, coordsAngle);
                  this.sharkDestination = randomCoords;
                  break;

              case SharkStatus.Attacking:
                  this.sharkPed.netOwner.emitRaw('treasureseas:setSharkAttacking', this.sharkPed, this.attackTarget);

                  this.endAttackTimeout = alt.setTimeout(() => {
                      this.setWandering();
                      this.attackTarget = null;
                      this.endAttackTimeout = null;
                  }, 20 * 1000); // 20秒攻击时间
                  break;

              case SharkStatus.Fleeing:
                  this.sharkPed.netOwner.emitRaw('treasureseas:setSharkFleeing', this.sharkPed, sharkFleeDistance);

                  this.endFleeTimeout = alt.setTimeout(() => {
                      this.setWandering();
                      this.endFleeTimeout = null;
                  }, 15 * 1000); // 15秒逃跑时间
                  break;
          }
      }

      private generateRandomWaterPosition(): alt.Vector2 {
          const angle = Math.random() * Math.PI * 2;
          const distance = Math.random() * sharkMoveRadius;
          let x = this.sharkSpawn.x + Math.cos(angle) * distance;
          let y = this.sharkSpawn.y + Math.sin(angle) * distance;
          
          // 确保鲨鱼不会游得太远离生成点
          const maxDistanceFromSpawn = sharkMoveRadius + 20; // 允许稍微超出游荡半径
          const distanceFromSpawn = Math.sqrt(
              Math.pow(x - this.sharkSpawn.x, 2) + Math.pow(y - this.sharkSpawn.y, 2)
          );
          
          if (distanceFromSpawn > maxDistanceFromSpawn) {
              // 如果太远，强制回到生成点附近
              const backAngle = Math.random() * Math.PI * 2;
              const backDistance = Math.random() * 30; // 回到生成点30米内
              x = this.sharkSpawn.x + Math.cos(backAngle) * backDistance;
              y = this.sharkSpawn.y + Math.sin(backAngle) * backDistance;
          }
          
          return new alt.Vector2(x, y);
      }

      private calculateHeading(startX: number, startY: number, endX: number, endY: number): number {
          const deltaX = endX - startX;
          const deltaY = endY - startY;
          const heading = Math.atan2(deltaY, deltaX);
          const headingDegrees = (heading * 180) / Math.PI;
          return (headingDegrees + 360) % 360;
      }

      public setWandering() {
          if (this.isDead) return;

          this.sharkStatus = SharkStatus.Wandering;
          this.setSharkStatus();
      }

      public setAttacking(targetPlayer: alt.Player) {
          if (this.isDead) return;

          // 清理其他状态
          if (this.sharkDestination != null) {
              this.sharkDestination = null;
          }

          if (this.sharkDestinationColshape != null) {
              this.sharkDestinationColshape.destroy();
              this.sharkDestinationColshape = null;
          }

          if (this.endFleeTimeout != null) {
              alt.clearTimeout(this.endFleeTimeout);
              this.endFleeTimeout = null;
          }

          if (this.attackTarget != null) {
              return; // 已经在攻击状态
          }

          this.sharkStatus = SharkStatus.Attacking;
          this.attackTarget = targetPlayer;

          this.setSharkStatus();
      }

      public setFleeing() {
          if (this.isDead) return;

          // 清理其他状态
          if (this.sharkDestination != null) {
              this.sharkDestination = null;
          }

          if (this.sharkDestinationColshape != null) {
              this.sharkDestinationColshape.destroy();
              this.sharkDestinationColshape = null;
          }

          if (this.attackTarget != null) {
              this.attackTarget = null;
          }

          if (this.endAttackTimeout != null) {
              alt.clearTimeout(this.endAttackTimeout);
              this.endAttackTimeout = null;
          }

          this.sharkStatus = SharkStatus.Fleeing;
          this.setSharkStatus();
      }
  }

