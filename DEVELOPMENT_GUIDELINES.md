# Rebar Framework 开发准则

## 核心原则

### 🎯 **最少代码原则**
- 必须用最少的代码来实现功能
- 尽可能修改原有的代码而不是添加新的文件/组件
- 避免重复造轮子，优先使用现有API和工具

### 🔄 **代码复用优先**
- 在添加新功能前，先检查是否可以扩展现有系统
- 优先修改现有接口而非创建新接口
- 保持向后兼容性

## 数据库操作规范

### 📊 **使用 useDatabase() API**

```typescript
import { useRebar } from '@Server/index.js';

const Rebar = useRebar();
const db = Rebar.database.useDatabase();

// 创建文档
const docId = await db.create({ name: 'test', value: 123 }, 'collection_name');

// 获取单个文档
const doc = await db.get<MyInterface & { _id: string }>({ name: 'test' }, 'collection_name');

// 获取多个文档
const docs = await db.getMany<MyInterface>({ status: 'active' }, 'collection_name');

// 获取所有文档
const allDocs = await db.getAll<MyInterface & { _id: string }>('collection_name');

// 更新文档 (必须包含 _id)
await db.update({ _id: docId, name: 'updated', value: 456 }, 'collection_name');

// 删除文档
await db.destroy(docId, 'collection_name');

// 聚合查询
const results = await db.aggregate<MyInterface & { _id: string }>('collection_name', [
    { $match: { status: 'active' } },
    { $group: { _id: '$category', count: { $sum: 1 } } }
]);
```

### 🔍 **数据库最佳实践**
- 所有数据库文档必须包含 `_id: string` 类型
- 使用 TypeScript 接口定义数据结构
- 创建集合前使用 `db.createCollection()` 确保集合存在
- 错误处理：数据库操作可能返回 `undefined`，务必检查

## 事件系统规范

### 🎮 **useEvents 使用指南**

#### WebView 中的事件处理
```typescript
import { useEvents } from '@Composables/useEvents.js';

const events = useEvents();

// 监听来自服务器或客户端的事件
events.on('event-from-server-or-client', (var1: string, var2: number) => {
    console.log(var1, var2);
});

// 发送到客户端 (可被 useWebview().on 接收)
events.emitClient('emit-to-client-event', myVariable);

// 发送到服务器 (可被 alt.onClient 接收)
events.emitServer('emit-to-server-event', myVariable);

// 服务器端 RPC 调用
const result = await events.emitServerRpc('get-something', 'hello');

// 客户端 RPC 调用
const result = await events.emitClientRpc('get-something', 'hello');
```

#### 服务器端 RPC 处理
```typescript
import * as alt from 'alt-server';

alt.onRpc('get-something', (player, arg1: string) => {
    const result = arg1 + ' world';
    return result;
});
```

#### 客户端 RPC 处理
```typescript
import { useWebview } from '@Client/webview/index.js';

const view = useWebview();

view.onRpc('get-something', (arg1: string) => {
    return arg1 + ' world';
});
```

### ⌨️ **按键监听**
```typescript
import { onMounted, onUnmounted } from 'vue';
import { useEvents } from '@Composables/useEvents.js';

const events = useEvents();

function doSomething() {
    console.log('Key pressed');
}

onMounted(() => {
    events.onKeyUp('unique-identifier', 73, doSomething); // 73 = I key
});

onUnmounted(() => {
    events.offKeyUp('unique-identifier');
});
```

## 本地存储规范

### 💾 **useLocalStorage 使用**
```typescript
import { useLocalStorage } from '@Composables/useLocalStorage';

async function example() {
    const localStorage = useLocalStorage();
    
    // 存储数据
    localStorage.set('myKey', 'someValue');

    // 获取数据
    const result = await localStorage.get('myKey');
    
    // 删除数据
    await localStorage.remove('myKey');
}
```

## 玩家数据获取

### 📊 **usePlayerStats 组合式API**
```typescript
import { usePlayerStats } from '@Composables/usePlayerStats';

const {
    armour, engineOn, fps, gear, headlights, health,
    highbeams, indicatorLights, inVehicle, inWater,
    isAiming, isFlying, isTalking, locked, lights,
    maxGear, ping, speed, stamina, street, time,
    vehicleHealth, weapon, weather, zone
} = usePlayerStats();
```

## 交互点系统

### 🎯 **创建交互点**
```typescript
import * as alt from 'alt-server';
import { useRebar } from '@Server/index.js';

const Rebar = useRebar();

const interaction = Rebar.controllers.useInteraction(
    new alt.ColshapeCylinder(x, y, z, radius, height),
    'player'
);

interaction.onEnter((player) => {
    // 玩家进入交互区域
});

interaction.onLeave((player) => {
    // 玩家离开交互区域
});

interaction.on((player) => {
    // 玩家按下交互键
});
```

## 命令系统

### 💬 **注册命令**
```typescript
import { useRebar } from '@Server/index.js';

const Rebar = useRebar();
const Messenger = Rebar.messenger.useMessenger();

// 基础命令
Messenger.commands.register({
    name: 'test',
    desc: '- 测试命令',
    callback: async (player) => {
        // 命令逻辑
    },
});

// 权限限制命令
Messenger.commands.register({
    name: 'admincommand',
    desc: '- 管理员专用命令',
    options: { accountPermissions: ['admin'] },
    callback: async (player) => {
        // 管理员命令逻辑
    },
});

// 角色权限限制命令
Messenger.commands.register({
    name: 'mechaniccommand',
    desc: '- 机械师专用命令',
    options: { permissions: ['mechanic'] },
    callback: async (player) => {
        // 机械师命令逻辑
    },
});
```

## WebView 开发规范

### 🌐 **WebView 类型**
- **Page**: 单次显示的页面 (库存、ATM、商店)
- **Overlay**: 始终显示的覆盖层 (HUD、现金显示)
- **Persistent**: 永久显示的页面 (网站水印、Logo)

### 🎨 **Vue 3 组件模板**
```vue
<script lang="ts" setup>
import { ref } from 'vue';
import { useEvents } from '@Composables/useEvents.js';

const events = useEvents();
const count = ref(0);
</script>

<template>
    <div class="flex items-center justify-center h-screen">
        <div class="bg-gray-800 text-white p-4 rounded">
            <p>Hello World!</p>
            <button @click="count++" class="bg-blue-500 px-4 py-2 rounded">
                Click Count: {{ count }}
            </button>
        </div>
    </div>
</template>
```

### 📱 **显示 WebView**
```typescript
// 服务器端显示页面
const rPlayer = Rebar.usePlayer(player);
rPlayer.webview.show('my-page-name', 'page', true);

// 客户端显示页面  
const webview = Rebar.webview.useWebview();
webview.show('my-page-name', 'overlay');
```

## RmlUi 开发规范

### 🖥️ **RmlUi 基础结构**
```xml
<rml>
    <head>
        <title>Demo</title>
        <style>
            body {
                font-family: arial;
                font-weight: bold;
                width: 100%;
                height: 100%;
            }
            
            .my-element {
                position: absolute;
                color: #FFFFFFFF;
                font-size: 20dp;
            }
        </style>
    </head>
    <body>
        <div id="container"></div>
    </body>
</rml>
```

### ⚡ **RmlUi 客户端控制**
```typescript
import * as alt from "alt-client";

// 加载字体
alt.loadRmlFont("/Client/arial.ttf", "arial", false, true);

// 创建文档
const document = new alt.RmlDocument("/Client/index.rml");
const container = document.getElementByID("container");

// 创建元素
const element = document.createElement("div");
element.id = "my-element";
element.addClass("my-class");
element.innerRML = "Hello World";

// 添加到容器
container.appendChild(element);

// 事件监听
element.on("click", (element, eventArgs) => {
    alt.log("Element clicked");
});

// 动态样式
element.style["left"] = "100px";
element.style["top"] = "200px";
```

## 开发工具

### 🛠️ **推荐扩展**
- **Vue Language Features (Volar)** - Vue 3 支持
- **Tailwind CSS IntelliSense** - CSS 类提示
- **TypeScript Importer** - 自动导入管理

### 🔍 **调试和预览**
```bash
# 预览 WebView
pnpm webview:dev

# 访问 http://localhost:5173 查看页面效果
```

## 性能优化准则

### ⚡ **最佳实践**
1. **最小化数据库查询** - 缓存常用数据，批量操作
2. **事件解绑** - 在组件卸载时清理事件监听器
3. **按需加载** - 使用动态导入加载大型组件
4. **避免频繁DOM操作** - 在RmlUi中批量更新样式
5. **合理使用计算属性** - Vue中使用computed缓存计算结果

### 🔄 **生命周期管理**
```typescript
import { onMounted, onUnmounted } from 'vue';

onMounted(() => {
    // 初始化逻辑
    events.onKeyUp('my-component', 73, handleKeyPress);
});

onUnmounted(() => {
    // 清理逻辑
    events.offKeyUp('my-component');
});
```

## 类型安全

### 📝 **TypeScript 接口定义**
```typescript
// 数据库文档接口
interface MyDocument {
    name: string;
    value: number;
    createdAt: number;
}

// 带 _id 的数据库文档
type MyDocumentWithId = MyDocument & { _id: string };

// API 函数类型
declare global {
    export interface ServerPlugin {
        ['my-plugin-api']: ReturnType<typeof useMyPluginAPI>;
    }
}
```

### 🔒 **严格的类型检查**
- 所有函数参数必须有明确类型
- 数据库操作返回值需要类型断言
- 事件参数必须声明类型
- 避免使用 `any` 类型

## 通知系统

### 📢 **Notify 通知系统**

#### 通知类型
| 类型 | 用途 |
|------|------|
| `warning` | 警告信息 |
| `admin` | 管理员消息 |
| `info` | 一般信息 |
| `error` | 错误信息 |
| `success` | 成功信息 |
| `urgent` | 紧急消息 |

#### 服务器端使用
```typescript
import { useRebar } from '@Server/index.js';

const Rebar = useRebar();
const notifyApi = await Rebar.useApi().getAsync('notify-api');

// 显示通知
notifyApi.shownotify(player, '消息内容', 'info');

// 隐藏所有通知
notifyApi.hidenotify(player);
```

#### 客户端使用
```typescript
import { useRebarClient } from '@Client/index.js';

const Rebar = useRebarClient();
const notifyApi = Rebar.useClientApi().get('notify-client-api');

// 显示通知
notifyApi.shownotify('消息内容', 'info');

// 隐藏所有通知
notifyApi.hidenotify();
```

#### 管理员命令
```bash
/shownotify "消息内容" warning
```

#### 使用示例
```typescript
// 成功操作
notifyApi.shownotify(player, '欢迎来到服务器！', 'success');

// 错误提示
notifyApi.shownotify(player, '权限不足', 'error');

// 警告信息
notifyApi.shownotify(player, '请注意安全驾驶', 'warning');
```

## 任务进度条系统

### 📊 **Mission Progress 任务进度**

#### 功能特性
- 显示任务进度条
- 支持多个任务目标
- 实时更新进度
- 支持纯文本和带进度的目标

#### 服务器端使用
```typescript
import { useRebar } from '@Server/index.js';

const Rebar = useRebar();
const missionApi = await Rebar.useApi().getAsync('progressbarformission-api');

// 显示任务进度
missionApi.showMissionProgress(player, '任务标题', [
    { text: '目标1', current: 0, total: 10 },
    { text: '目标2', current: 0, total: 5 },
    { text: '目标3' } // 纯文本目标
]);

// 更新进度
missionApi.updateMissionProgress(player, 0, 5); // 更新第一个目标

// 隐藏任务进度
missionApi.hideMissionProgress(player);
```

#### 客户端使用
```typescript
import { useRebarClient } from '@Client/index.js';

const Rebar = useRebarClient();
const missionApi = Rebar.useClientApi().get('progressbarformission-client-api');

// 显示任务进度
missionApi.showMissionProgress('任务标题', [
    { text: '收集物品', current: 3, total: 10 },
    { text: '到达目的地', current: 0, total: 1 }
]);
```

#### 任务目标数据结构
```typescript
interface MissionObjective {
    text: string;           // 目标描述
    current?: number;       // 当前进度 (可选)
    total?: number;         // 总进度 (可选)
}
```

## 按键提示系统

### ⌨️ **PromptBar 提示栏**

#### 基础功能
- 显示按键提示信息
- 自定义按键和提示文本
- 多选择互动菜单 (新功能)

#### 服务器端使用
```typescript
import { useRebar } from '@Server/index.js';

const Rebar = useRebar();
const promptApi = await Rebar.useApi().getAsync('promptbar-api');

// 显示单个提示
promptApi.showPromptBar(player, '打开商店界面', 'E');

// 隐藏提示
promptApi.hidePromptBar(player);

// 多选择互动菜单 (推荐用于复杂场景)
const options = [
    {
        key: 'E',
        text: '打开商店界面', 
        icon: 'fas fa-shopping-cart',
        action: 'openShop',
        enabled: true
    },
    {
        key: 'R',
        text: '抢劫商店',
        icon: 'fas fa-mask', 
        action: 'robShop',
        enabled: canRob,
        disabledReason: canRob ? '' : '需要手枪和子弹'
    }
];

promptApi.showMultiInteraction(player, options, (player, action) => {
    // 处理玩家的选择
    handleInteraction(player, action);
}, (player) => {
    // 取消回调 (可选)
    console.log('Player cancelled interaction');
});
```

#### 客户端使用
```typescript
import { useRebarClient } from '@Client/index.js';

const Rebar = useRebarClient();
const promptApi = Rebar.useClientApi().get('promptbar-client-api');

// 显示提示
promptApi.showPromptBar('按 H 键使用物品', 'H');

// 隐藏提示
promptApi.hidePromptBar();
```

#### 多选择互动选项结构
```typescript
interface InteractionOption {
    key: string;            // 快捷键
    text: string;           // 显示文本
    icon: string;           // FontAwesome 图标类
    action: string;         // 动作标识符
    enabled: boolean;       // 是否可用
    disabledReason?: string; // 禁用原因 (可选)
}
```

#### 使用场景建议
- **单个操作**: 使用 `showPromptBar()` 
- **多个选择**: 使用 `showMultiInteraction()`
- **复杂交互**: 商店系统 (购买/抢劫/拿钱)
- **条件判断**: 显示禁用选项及原因

## 选择对话框系统

### 🎯 **Choice API 选择对话框**

#### 功能特性
- 🎯 **简单易用**: 支持简单的字符串数组选择
- 🎨 **丰富配置**: 支持图标、颜色、描述等高级配置
- ⏰ **超时控制**: 支持设置自动关闭时间
- 🎪 **现代界面**: 符合设计规范的美观界面
- 🔄 **Promise支持**: 使用async/await语法

#### 服务器端使用
```typescript
import { useRebar } from '@Server/index.js';

const Rebar = useRebar();
const choiceApi = await Rebar.useApi().getAsync('choice-api');

// 简单选择
const result = await choiceApi.showSimpleChoice(player, '选择你的行动', [
    '前往商店',
    '返回家中',
    '探索城市'
]);

console.log('玩家选择:', result); // 输出选择结果或null(取消)

// 高级选择
const config: ChoiceConfig = {
    title: '购买物品',
    description: '选择你要购买的物品类型',
    options: [
        {
            id: 'weapon',
            text: '武器',
            value: 'weapon_category',
            icon: 'fas fa-gun',
            color: 'error'
        },
        {
            id: 'food',
            text: '食物',
            value: 'food_category',
            icon: 'fas fa-hamburger',
            color: 'success'
        },
        {
            id: 'vehicle',
            text: '载具',
            value: 'vehicle_category',
            icon: 'fas fa-car',
            color: 'primary'
        }
    ],
    allowCancel: true,
    cancelText: '不购买',
    timeout: 10000 // 10秒超时
};

const result = await choiceApi.showChoice(player, config);
```

#### 客户端使用
```typescript
import { useRebarClient } from '@Client/index.js';

const Rebar = useRebarClient();
const choiceClientApi = await Rebar.useClientApi().getAsync('choice-client-api');

// 在客户端显示选择对话框
const result = await choiceClientApi.showSimpleChoice('选择操作', ['选项1', '选项2', '选项3']);
```

#### 数据结构定义
```typescript
interface ChoiceOption {
    id: string;                    // 选项唯一ID
    text: string;                  // 显示文本
    value: any;                    // 返回值
    icon?: string;                 // FontAwesome图标类名
    color?: 'primary' | 'success' | 'warning' | 'error' | 'info'; // 颜色主题
    disabled?: boolean;            // 是否禁用
}

interface ChoiceConfig {
    title: string;                 // 标题
    description?: string;          // 描述文本
    options: ChoiceOption[];       // 选项数组
    allowCancel?: boolean;         // 是否允许取消
    cancelText?: string;           // 取消按钮文本
    timeout?: number;              // 超时时间（毫秒）
}
```

#### 使用示例

**商店购买选择**
```typescript
const shopResult = await choiceApi.showChoice(player, {
    title: '武器商店',
    description: '选择你要购买的武器',
    options: [
        { id: 'pistol', text: '手枪', value: 'weapon_pistol', icon: 'fas fa-gun', color: 'primary' },
        { id: 'rifle', text: '步枪', value: 'weapon_rifle', icon: 'fas fa-crosshairs', color: 'warning' },
        { id: 'shotgun', text: '散弹枪', value: 'weapon_shotgun', icon: 'fas fa-bullseye', color: 'error' }
    ],
    allowCancel: true,
    cancelText: '离开商店'
});

if (shopResult) {
    console.log('玩家选择购买:', shopResult);
}
```

**交通工具选择**
```typescript
const vehicleResult = await choiceApi.showSimpleChoice(player, '选择交通工具', [
    '汽车', '摩托车', '自行车', '步行'
]);

switch (vehicleResult) {
    case '汽车':
        // 生成汽车
        break;
    case '摩托车':
        // 生成摩托车
        break;
    default:
        // 玩家取消了选择
        break;
}
```

**带超时的紧急选择**
```typescript
const urgentChoice = await choiceApi.showChoice(player, {
    title: '紧急情况',
    description: '你有10秒时间做出选择！',
    options: [
        { id: 'run', text: '逃跑', value: 'escape', icon: 'fas fa-running', color: 'warning' },
        { id: 'fight', text: '战斗', value: 'combat', icon: 'fas fa-fist-raised', color: 'error' },
        { id: 'hide', text: '躲藏', value: 'stealth', icon: 'fas fa-eye-slash', color: 'info' }
    ],
    timeout: 10000,
    allowCancel: false
});

if (urgentChoice === null) {
    console.log('玩家超时未选择');
}
```

#### 测试命令
```bash
/choice-demo  # 测试Choice API功能 (需要管理员权限)
```

## 账单系统

### 💰 **Bill 账单系统**

#### 功能特性
- 创建和发送账单
- 账单支付和拒绝
- 派系提成系统
- 理发师特殊服务

#### 服务器端使用
```typescript
import { useRebar } from '@Server/index.js';
import { givebill, givebillwithoutplayer } from './server/index.js';

const Rebar = useRebar();

// 给玩家发送账单
givebill(player, target, '服务费用', 1000);

// 给玩家发送无发送者的账单
givebillwithoutplayer(target, '系统费用', 500, '政府', 'system');
```

#### 客户端使用
```typescript
import { useRebarClient } from '@Client/index.js';

const Rebar = useRebarClient();

// 开始创建账单
alt.emit('bill:start', player, target);

// 创建账单
alt.emit('bill:create', player, '服务费用', 1000, targetId);

// 支付账单
alt.emit('bill:pay', player, '服务费用', 1000, '派系名称', playerId);

// 拒绝账单
alt.emit('bill:reject', player, playerId);
```

#### 使用示例
```typescript
// 服务器端 - 发送账单
givebill(player, target, '医疗费用', 2500);

// 服务器端 - 系统账单
givebillwithoutplayer(target, '停车费', 100, '政府', 'parking');

// 客户端 - 创建账单
alt.emit('bill:start', player, target);
// 在UI中填写账单信息后
alt.emit('bill:create', player, '理发服务', 500, targetId);
```

#### 特殊功能

**理发师服务**
```typescript
// 理发师创建账单时自动设置服务标记
if (playerBusiness === '理发师' && billname.includes('理发')) {
    target.setStreamSyncedMeta('barber:service:barber:id', player.getStreamSyncedMeta('id'));
    givebillwithoutplayer(target, billname, billamount, '理发师个体户', 'barber:service');
}
```

**派系提成**
- 支持职位专属提成比例
- 自动计算税费和净收入
- 派系资金自动增加

## 确认对话框系统

### ✅ **ConfirmDialog 确认对话框**

#### 功能特性
- 显示确认对话框
- 自动超时处理
- 键盘快捷键支持
- 进度条显示

#### 服务器端使用
```typescript
import { useRebar } from '@Server/index.js';

const Rebar = useRebar();
const confirmDialog = Rebar.useApi().get('confirm-dialog')(player);

// 创建确认对话框
confirmDialog.create({
    title: '确认操作',
    message: '您确定要执行此操作吗?',
    showTime: 10000, // 10秒
    triggerNoOnTimeout: true, // 超时时自动触发拒绝
    callback: (player, result) => {
        if (result) {
            // 用户点击了确认
            console.log('用户确认了操作');
        } else {
            // 用户点击了拒绝或超时
            console.log('用户拒绝了操作');
        }
    }
});
```

#### 使用示例
```typescript
// 删除确认
confirmDialog.create({
    title: '删除确认',
    message: '您确定要删除这个物品吗?',
    showTime: 5000,
    triggerNoOnTimeout: true,
    callback: (player, result) => {
        if (result) {
            // 执行删除操作
            deleteItem(player, itemId);
        }
    }
});

// 购买确认
confirmDialog.create({
    title: '购买确认',
    message: `您确定要购买 ${itemName} 吗? 价格: $${price}`,
    showTime: 8000,
    callback: (player, result) => {
        if (result) {
            // 执行购买操作
            purchaseItem(player, itemId);
        }
    }
});
```

#### 键盘快捷键
- **Y键** - 确认操作
- **N键** - 拒绝操作

#### 配置选项
```typescript
interface ConfirmDialogOptions {
    title: string;           // 对话框标题
    message: string;         // 对话框消息
    showTime?: number;       // 显示时间(毫秒)
    triggerNoOnTimeout?: boolean; // 超时时是否自动触发拒绝
    callback: (player, result: boolean) => void; // 回调函数
}
```

## 货币系统

### 💰 **Currency 货币系统**

#### 功能特性
- 现金、银行、积分管理
- ATM存取款操作
- 银行转账功能
- 密码验证和重置
- 银行流水记录
- 短信验证码

#### 服务器端使用
```typescript
import { useRebar } from '@Server/index.js';

const Rebar = useRebar();
const currencyApi = await Rebar.useApi().getAsync('currency-api');

// 增加货币
await currencyApi.add({ player }, 'cash', 1000);
await currencyApi.add({ player }, 'bank', 5000);
await currencyApi.add({ player }, 'points', 100);

// 减少货币
await currencyApi.sub({ player }, 'cash', 500);
await currencyApi.sub({ player }, 'bank', 1000);

// 设置货币
await currencyApi.set({ player }, 'cash', 2000);

// 获取货币
const cash = await currencyApi.get({ player }, 'cash');
const bank = await currencyApi.get({ player }, 'bank');
const points = await currencyApi.get({ player }, 'points');

// 检查是否可以消费
const canAfford = await currencyApi.cancost({ player }, 1000);

// 消费货币
const success = await currencyApi.cost({ player }, 1000, '购买物品');
```

#### 银行操作
```typescript
// 存款
await currencyApi.Deposit({ player }, 1000);

// 取款
await currencyApi.Withdraw({ player }, 500);

// 转账
await currencyApi.Transfer({ player }, 1000, '*********');

// 验证银行密码
const isValid = await currencyApi.verifyBankPassword({ player }, '123456');

// 修改银行密码
const success = await currencyApi.changeBankPassword({ player }, '123456', '654321');
```

#### 验证码功能
```typescript
// 生成短信验证码
const code = await currencyApi.generateSmsVerificationCode(player);

// 验证短信验证码
const isValid = await currencyApi.verifySmsCode(player, '123456');

// 生成忘记密码验证码
const code = await currencyApi.generateForgotPasswordCode(player);

// 验证忘记密码验证码
const isValid = await currencyApi.verifyForgotPasswordCode(player, '123456');

// 使用验证码重置密码
const success = await currencyApi.resetPasswordWithCode(player, '123456', 'newpassword');
```

#### 使用示例
```typescript
// 玩家获得工资
await currencyApi.add({ player }, 'bank', 5000);
await currencyApi.addbankstatement({ player }, {
    time: new Date().toISOString(),
    type: '工资收入',
    amount: 5000,
    from: '公司'
}, '月薪发放');

// 玩家购买物品
if (await currencyApi.cancost({ player }, 1000)) {
    await currencyApi.cost({ player }, 1000, '购买武器');
    // 给予物品
    giveWeapon(player, 'weapon_pistol');
} else {
    notifyApi.shownotify(player, '余额不足', 'error');
}

// 玩家转账给其他玩家
const targetPlayer = alt.Player.all.find(p => p.getStreamSyncedMeta('id') === targetId);
if (targetPlayer) {
    const success = await currencyApi.Transfer({ player }, 1000, targetPlayer.getStreamSyncedMeta('banknumber'));
    if (success) {
        notifyApi.shownotify(player, '转账成功', 'success');
        notifyApi.shownotify(targetPlayer, '收到转账 $1000', 'success');
    }
}
```

#### 离线玩家操作
```typescript
// 使用角色ID操作离线玩家
await currencyApi.add({ characterId: 123 }, 'cash', 1000);
await currencyApi.add({ characterId: 123 }, 'bank', 5000);

// 获取离线玩家信息
const cash = await currencyApi.get({ characterId: 123 }, 'cash');
const bank = await currencyApi.get({ characterId: 123 }, 'bank');
```

---

**记住**: 这个框架强调简洁、高效和类型安全。在开发过程中始终考虑如何用最少的代码实现最大的功能价值。