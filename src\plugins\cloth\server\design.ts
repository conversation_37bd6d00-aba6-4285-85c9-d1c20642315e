import * as alt from 'alt-server';
import { useRebar } from '@Server/index.js';
import { ModClothes } from '../shared/addclothes.js';
import { PageNames } from '@Shared/webview/index.js';
import { GlobalTimerManager } from '@Plugins/animmenu/server/interval.js';

const Rebar = useRebar();
const messenger = Rebar.messenger.useMessenger();
const useInventory = await Rebar.useApi().getAsync('inventory-api');
const promptbarapi = await Rebar.useApi().getAsync('promptbar-api');
const notifyapi = await Rebar.useApi().getAsync('notify-api');
const dbItemApi = await Rebar.useApi().getAsync('dbitem-api');

const db = Rebar.database.useDatabase();

// 创建服装订单数据库集合
await db.createCollection('cloth_orders');


const clothprice = [
    { name: '棉线', price: 2 },
    { name: '亚麻线', price: 3 },
    { name: '涤纶', price: 10 },
    { name: '尼龙', price: 14 },
    { name: '腈纶', price: 19 },
]



// 服装分类定义（用于设计图类型）
const ClothingTypes = {
    'tops': '上衣',
    'legs': '裤子',
    'shoes': '鞋子',
    'under': '内衬',
    'hat': '帽子',
    'glasses': '眼镜',
    'ears': '耳环',
    'watch': '手表',
    'bracelet': '手环',
    'mask': '面具',
    'bag': '背包',
    'accessories': '饰品',
    'bodyarmor': '护甲',
    'decals': '贴纸',
    'head': '发型',
    'torso': '躯干'
};

// 汽车设计图类型定义
const CarDesignTypes = {
    'car_side': '汽车侧视图',
    'car_top': '汽车俯视图', 
    'car_front': '汽车正视图'
};

// 组件ID到服装类型的映射
const ComponentIdToType: Record<number, string> = {
    0: 'head',        // 头部/发型
    1: 'mask',        // 面具
    2: 'ears',        // 耳环
    3: 'torso',       // 躯干
    4: 'legs',        // 裤子
    5: 'bag',         // 背包
    6: 'shoes',       // 鞋子
    7: 'accessories', // 饰品
    8: 'under',       // 内衬
    9: 'bodyarmor',   // 护甲
    10: 'decals',     // 贴纸
    11: 'tops'        // 上衣
};

// 道具ID到服装类型的映射
const PropIdToType: Record<number, string> = {
    0: 'hat',         // 帽子
    1: 'glasses',     // 眼镜
    2: 'ears',        // 耳环
    6: 'watch',       // 手表
    7: 'bracelet'     // 手环
};

// 创建设计图物品
dbItemApi.createitem({
    name: '服装设计图',
    desc: '服装设计图纸，记录了特定服装类型的设计方案和完成度',
    type: '工具',
    icon: 'clothblueprint.webp',
    weight: 0,
    maxStack: 1
});

//空白设计图
dbItemApi.createitem({
    name: '空白设计图',
    desc: '空白设计图纸，可以用于设计服装',
    type: '工具',
    icon: 'blankblueprint.webp',
    weight: 0,
    maxStack: 1
});

//汽车设计图
dbItemApi.createitem({
    name: '汽车设计图',
    desc: '汽车设计图纸，记录了汽车三视图的设计方案和完成度',
    type: '工具',
    icon: 'carblueprint.webp',
    weight: 0,
    maxStack: 1
});




// 设计工厂位置
const clothindustrypos = new alt.Vector3(-1559.6571, 201.8374, 59.1318);

// 添加地图标记
Rebar.controllers.useBlipGlobal({
    uid: 'huizhi',
    text: '绘图室',
    sprite: 489,
    color: 47,
    scale: 0.8,
    shortRange: true,
    pos: clothindustrypos
});

// 添加地面标记
Rebar.controllers.useMarkerGlobal({
    uid: 'huizhi',
    type: 1,
    color: new alt.RGBA(255, 165, 0, 150),
    scale: new alt.Vector3(1.5, 1.5, 0.3),
    pos: clothindustrypos
});


// 创建设计工厂互动点
const designFactoryInteraction = Rebar.controllers.useInteraction(
    new alt.ColshapeCylinder(clothindustrypos.x, clothindustrypos.y, clothindustrypos.z, 1.5, 3),
    'player'
);


// 进入提示
designFactoryInteraction.onEnter((player) => {
    promptbarapi.showPromptBar(player, '开始绘图');
});

// 离开清除提示
designFactoryInteraction.onLeave((player) => {
    promptbarapi.hidePromptBar(player);
});

// 交互逻辑
designFactoryInteraction.on(async (player) => {
    promptbarapi.hidePromptBar(player);

    // 检查玩家是否有空白设计图
    const inventory = await useInventory.getInventory({ player: player });
    const blankBlueprint = inventory.find(item => item && item.name === '空白设计图');

    if (!blankBlueprint) {
        notifyapi.shownotify(player, '你的背包里没有空白设计图，无法进行绘制', 'error');
        return;
    }

    // 打开绘图界面
    Rebar.player.useWebview(player).show('drawingInterface', 'page', true);

    // 等待界面准备就绪
    let ready = await Rebar.player.useWebview(player).isReady('drawingInterface', 'page');
    while (!ready) {
        await alt.Utils.wait(100);
        ready = await Rebar.player.useWebview(player).isReady('drawingInterface', 'page');
    }

    // 绘图界面已准备就绪，玩家可以开始绘制
});






const clothindustrypos1 = new alt.Vector3(713.7626, -962.6241, 29.3920);
// 添加地图标记
Rebar.controllers.useBlipGlobal({
    uid: 'design_factory_cloth',
    text: '服装工厂',
    sprite: 366,
    color: 47,
    scale: 0.8,
    shortRange: true,
    pos: clothindustrypos1
});

// 添加地面标记
Rebar.controllers.useMarkerGlobal({
    uid: 'design_factory_marker_cloth',
    type: 1,
    color: new alt.RGBA(255, 165, 0, 150),
    scale: new alt.Vector3(1.5, 1.5, 0.3),
    pos: clothindustrypos1
});

// 服装工厂交互点
const clothFactoryInteraction = Rebar.controllers.useInteraction(
    new alt.ColshapeCylinder(clothindustrypos1.x, clothindustrypos1.y, clothindustrypos1.z, 1.5, 3),
    'player'
);


// 进入提示
clothFactoryInteraction.onEnter((player) => {
    promptbarapi.showPromptBar(player, '服装工厂');
});

// 离开清除提示
clothFactoryInteraction.onLeave((player) => {
    promptbarapi.hidePromptBar(player);
});

// 服装工厂交互逻辑
clothFactoryInteraction.on(async (player) => {
    promptbarapi.hidePromptBar(player);

    const character = Rebar.document.character.useCharacter(player);
    const webview = Rebar.player.useWebview(player);
    const characterData = character.get();

    if (!characterData) {
        notifyapi.shownotify(player, '无法获取角色数据', 'error');
        return;
    }

    const characterSex = characterData?.appearance.sex || 0;

    // 检查玩家是否有服装订单
    const clothOrders = await db.getMany({
        characterId: characterData.id,
        submitted: true
    }, 'cloth_orders') as ClothOrder[];

    const hasOrder = clothOrders.length > 0;

    if (hasOrder) {
        // 玩家有订单，打开材料提交界面
        webview.show('designPreview', 'page', true);

        // 等待界面准备就绪
        let isReady = await webview.isReady('designPreview', 'page');
        while (!isReady) {
            await alt.Utils.wait(100);
            isReady = await webview.isReady('designPreview', 'page');
        }

        // 发送角色性别信息和设置为订单管理模式
        webview.emit('designPreview:setCharacterSex', characterSex);
        webview.emit('designPreview:setMode', 'orderManagement');
        
        await sendClothOrderDataToClient(player);
    } else {
        // 玩家没有订单，打开设计图预览和下订单界面
        const blueprints = await getPlayerBlueprints(player);

        if (blueprints.length === 0) {
            notifyapi.shownotify(player, '你的背包里没有设计图，无法下订单', 'error');
            return;
        }

        webview.show('designPreview', 'page', true);

        let isReady = await webview.isReady('designPreview', 'page');
        while (!isReady) {
            await alt.Utils.wait(100);
            isReady = await webview.isReady('designPreview', 'page');
        }

        // 先发送角色性别，再发送设计图数据
        webview.emit('designPreview:setCharacterSex', characterSex);
        webview.emit('designPreview:setBlueprints', blueprints);
        webview.emit('designPreview:setMode', 'order'); // 设置为订单模式
    }
});

// 发送服装订单数据到客户端
async function sendClothOrderDataToClient(player: alt.Player) {
    const character = Rebar.document.character.useCharacter(player);
    const characterData = character.get();

    if (!characterData) return;

    const orders = await db.getMany({
        characterId: characterData.id,
        submitted: true
    }, 'cloth_orders') as ClothOrder[];

    if (orders.length === 0) return;

    const order = orders[0]; // 取第一个订单

    // 获取玩家背包中的材料
    const playerMaterials = await getPlayerMaterials(player);

    const orderData = {
        clothingType: order.clothingType,
        typeName: ClothingTypes[order.clothingType],
        materials: order.materials,
        materialsSubmitted: order.materialsSubmitted,
        materialCost: order.materialCost,
        playerMaterials: playerMaterials,
        createdAt: order.createdAt,
        expiresAt: order.expiresAt,
        blueprintFinish: order.blueprintData.finish,
        blueprintData: order.blueprintData, // 添加完整的蓝图数据用于预览
        baseCost: CLOTHING_MATERIAL_COSTS[order.clothingType] || 50000
    };

    Rebar.player.useWebview(player).emit('clothOrder:setData', orderData);
}

// 获取玩家背包中的材料
async function getPlayerMaterials(player: alt.Player): Promise<{ [key: string]: number }> {
    try {
        const inventory = await useInventory.getInventory({ player: player });
        const materials: { [key: string]: number } = {};

        for (const item of inventory) {
            if (item && AVAILABLE_MATERIALS.includes(item.name)) {
                materials[item.name] = (materials[item.name] || 0) + item.quantity;
            }
        }

        return materials;
    } catch (error) {
        console.error('获取玩家材料失败:', error);
        return {};
    }
}

// 处理服装订单确认
alt.onClient('clothOrder:confirm', async (player: alt.Player, blueprintSlot: number) => {
    try {
        const character = Rebar.document.character.useCharacter(player);
        const characterData = character.get();

        if (!characterData) {
            notifyapi.shownotify(player, '无法获取角色数据', 'error');
            return;
        }

        // 检查是否已有订单
        const existingOrders = await db.getMany({
            characterId: characterData.id,
            submitted: true
        }, 'cloth_orders') as ClothOrder[];

        if (existingOrders.length > 0) {
            notifyapi.shownotify(player, '你已经有一个进行中的订单', 'error');
            return;
        }

        // 获取设计图
        const inventory = await useInventory.getInventory({ player: player });
        const blueprint = inventory.find(item =>
            item && item.name === '服装设计图' && item.slot === blueprintSlot
        );

        if (!blueprint || !blueprint.customData || !validateBlueprintData(blueprint.customData)) {
            notifyapi.shownotify(player, '设计图数据无效', 'error');
            return;
        }

        const blueprintData = blueprint.customData;
        const clothingType = blueprintData.type;
        const baseCost = CLOTHING_MATERIAL_COSTS[clothingType] || 50000;

        // 生成随机材料需求
        const materials = generateRandomMaterials(baseCost, blueprintData.finish);
        
        // 计算实际材料价值（根据完成度调整后的价格）
        const priceMultiplier = Math.max(0.25, Math.min(1.75, (175 - blueprintData.finish) / 100));
        const actualMaterialCost = Math.round(baseCost * priceMultiplier);
        
        const materialsSubmitted: { [key: string]: number } = {};

        // 初始化已提交材料为0
        for (const material in materials) {
            materialsSubmitted[material] = 0;
        }

        // 创建订单
        const orderData: Omit<ClothOrder, '_id'> = {
            characterId: characterData.id,
            blueprintData: blueprintData,
            clothingType: clothingType,
            materials: materials,
            materialsSubmitted: materialsSubmitted,
            materialCost: actualMaterialCost, // 使用实际调整后的价格
            submitted: true,
            createdAt: Date.now(),
            expiresAt: Date.now() + (30 * 24 * 60 * 60 * 1000) // 30天有效期
        };

        await db.create(orderData, 'cloth_orders');

        // 消耗设计图
        await useInventory.subItem(blueprint.name, 1, { player }, blueprint.slot);

        notifyapi.shownotify(player, `${ClothingTypes[clothingType]}订单已提交！请准备材料。`, 'success');

        // 根据完成度给出定性评价，不显示具体数字
        if (blueprintData.finish >= 95) {
            notifyapi.shownotify(player, '大师级设计！材料需求将大幅降低', 'success');
        } else if (blueprintData.finish >= 85) {
            notifyapi.shownotify(player, '优秀设计！材料需求有所降低', 'success');
        } else if (blueprintData.finish > 75) {
            notifyapi.shownotify(player, '良好设计，材料需求略有降低', 'info');
        } else if (blueprintData.finish === 75) {
            notifyapi.shownotify(player, '标准设计，材料需求正常', 'info');
        } else if (blueprintData.finish >= 65) {
            notifyapi.shownotify(player, '一般设计，材料需求略有增加', 'warning');
        } else if (blueprintData.finish >= 55) {
            notifyapi.shownotify(player, '粗糙设计，材料需求有所增加', 'warning');
        } else {
            notifyapi.shownotify(player, '简陋设计，材料需求大幅增加', 'error');
        }

        // 发送订单提交确认到前端
        Rebar.player.useWebview(player).emit('clothOrder:orderSubmitted');

        // 重新发送数据
        await sendClothOrderDataToClient(player);

    } catch (error) {
        console.error('订单确认错误:', error);
        notifyapi.shownotify(player, '订单处理失败', 'error');
    }
});

// 处理取消订单
alt.onClient('clothOrder:cancel', async (player: alt.Player) => {
    try {
        const character = Rebar.document.character.useCharacter(player);
        const characterData = character.get();

        if (!characterData) {
            notifyapi.shownotify(player, '无法获取角色数据', 'error');
            return;
        }

        // 查找现有订单
        const existingOrders = await db.getMany({
            characterId: characterData.id,
            submitted: true
        }, 'cloth_orders') as ClothOrder[];

        if (existingOrders.length === 0) {
            notifyapi.shownotify(player, '没有找到进行中的订单', 'error');
            return;
        }

        const order = existingOrders[0];

        // 删除订单（不返还任何材料）
        await db.deleteDocument(order._id, 'cloth_orders');

        notifyapi.shownotify(player, '订单已取消（不返还任何材料）', 'warning');

        // 通知前端订单已取消
        Rebar.player.useWebview(player).emit('clothOrder:orderCancelled');

    } catch (error) {
        console.error('取消订单错误:', error);
        notifyapi.shownotify(player, '取消订单失败', 'error');
    }
});

// 批量提交材料
alt.onClient('clothOrder:submitAllMaterials', async (player: alt.Player, submissionList: { material: string, amount: number }[]) => {
    try {
        const character = Rebar.document.character.useCharacter(player);
        const characterData = character.get();

        if (!characterData) {
            notifyapi.shownotify(player, '无法获取角色数据', 'error');
            return;
        }

        // 获取订单
        const orders = await db.getMany({
            characterId: characterData.id,
            submitted: true
        }, 'cloth_orders') as ClothOrder[];

        if (orders.length === 0) {
            notifyapi.shownotify(player, '没有找到有效订单', 'error');
            return;
        }

        const order = orders[0];

        let totalSubmitted = 0;
        const successfulSubmissions: string[] = [];
        const failedSubmissions: string[] = [];

        // 获取玩家背包中的材料数量
        const playerMaterials = await getPlayerMaterials(player);

        for (const submission of submissionList) {
            const { material, amount } = submission;
            const required = order.materials[material];
            const submitted = order.materialsSubmitted[material] || 0;

            if (!required) {
                failedSubmissions.push(`${material}(不在需求列表中)`);
                continue;
            }

            if (submitted >= required) {
                failedSubmissions.push(`${material}(已满足需求)`);
                continue;
            }

            // 检查玩家是否有足够材料
            const playerAmount = playerMaterials[material] || 0;
            if (playerAmount < amount) {
                failedSubmissions.push(`${material}(材料不足)`);
                continue;
            }

            // 计算实际可提交的数量
            const canSubmit = Math.min(amount, required - submitted, playerAmount);
            if (canSubmit <= 0) {
                failedSubmissions.push(`${material}(无法提交)`);
                continue;
            }

            // 从玩家背包移除材料
            const [success, message] = await useInventory.subItem(material, canSubmit, { player });
            if (!success) {
                failedSubmissions.push(`${material}(${message})`);
                continue;
            }

            // 更新订单
            order.materialsSubmitted[material] = submitted + canSubmit;
            await db.update(order, 'cloth_orders');

            successfulSubmissions.push(`${material} x${canSubmit}`);
            totalSubmitted += canSubmit;
        }

        // 发送结果通知
        if (successfulSubmissions.length > 0) {
            notifyapi.shownotify(player, `成功提交: ${successfulSubmissions.join(', ')}`, 'success');
        }

        if (failedSubmissions.length > 0) {
            notifyapi.shownotify(player, `提交失败: ${failedSubmissions.join(', ')}`, 'warning');
        }

        // 检查是否完成所有材料
        let allCompleted = true;
        for (const [mat, req] of Object.entries(order.materials)) {
            if ((order.materialsSubmitted[mat] || 0) < req) {
                allCompleted = false;
                break;
            }
        }

        if (allCompleted) {
            notifyapi.shownotify(player, '所有材料已齐！可以完成订单了！', 'success');
            Rebar.player.useWebview(player).emit('clothOrder:orderComplete');
        }

        // 发送批量提交完成确认到前端
        Rebar.player.useWebview(player).emit('clothOrder:allMaterialsSubmitted');

        // 重新发送数据
        await sendClothOrderDataToClient(player);

    } catch (error) {
        console.error('批量提交材料时出错:', error);
        notifyapi.shownotify(player, '批量提交材料失败', 'error');
        Rebar.player.useWebview(player).emit('clothOrder:allMaterialsSubmitted');
    }
});

// 处理材料提交
alt.onClient('clothOrder:submitMaterial', async (player: alt.Player, materialName: string, amount: number) => {
    try {
        const character = Rebar.document.character.useCharacter(player);
        const characterData = character.get();

        if (!characterData) {
            notifyapi.shownotify(player, '无法获取角色数据', 'error');
            return;
        }

        // 获取订单
        const orders = await db.getMany({
            characterId: characterData.id,
            submitted: true
        }, 'cloth_orders') as ClothOrder[];

        if (orders.length === 0) {
            notifyapi.shownotify(player, '没有找到有效订单', 'error');
            return;
        }

        const order = orders[0];
        const required = order.materials[materialName];
        const submitted = order.materialsSubmitted[materialName] || 0;

        if (!required) {
            notifyapi.shownotify(player, '该材料不在需求列表中', 'error');
            return;
        }

        if (submitted >= required) {
            notifyapi.shownotify(player, '该材料已满足需求', 'info');
            return;
        }

        // 计算实际可提交的数量
        const canSubmit = Math.min(amount, required - submitted);

        if (canSubmit <= 0) {
            notifyapi.shownotify(player, '无法提交更多该材料', 'error');
            return;
        }

        // 从玩家背包移除材料
        const [success, message] = await useInventory.subItem(materialName, canSubmit, { player });
        if (!success) {
            notifyapi.shownotify(player, `移除材料失败: ${message}`, 'error');
            return;
        }

        // 更新订单
        order.materialsSubmitted[materialName] = submitted + canSubmit;
        await db.update(order, 'cloth_orders');

        notifyapi.shownotify(player, `已提交 ${canSubmit} 个 ${materialName}`, 'success');

        // 检查是否完成所有材料
        let allCompleted = true;
        for (const [mat, req] of Object.entries(order.materials)) {
            if ((order.materialsSubmitted[mat] || 0) < req) {
                allCompleted = false;
                break;
            }
        }

        if (allCompleted) {
            notifyapi.shownotify(player, '所有材料已齐！可以获得服装了！', 'success');
            Rebar.player.useWebview(player).emit('clothOrder:orderComplete');
        }

        // 发送材料提交确认到前端
        Rebar.player.useWebview(player).emit('clothOrder:materialSubmitted');

        // 重新发送数据
        await sendClothOrderDataToClient(player);

    } catch (error) {
        console.error('提交材料失败:', error);
        notifyapi.shownotify(player, '提交材料失败，请重试', 'error');
    }
});

// 处理完成订单
alt.onClient('clothOrder:complete', async (player: alt.Player) => {
    try {
        const character = Rebar.document.character.useCharacter(player);
        const characterData = character.get();

        if (!characterData) {
            notifyapi.shownotify(player, '无法获取角色数据', 'error');
            return;
        }

        // 获取订单
        const orders = await db.getMany({
            characterId: characterData.id,
            submitted: true
        }, 'cloth_orders') as ClothOrder[];

        if (orders.length === 0) {
            notifyapi.shownotify(player, '没有找到有效订单', 'error');
            return;
        }

        const order = orders[0];

        // 检查是否所有材料都已提交
        let allCompleted = true;
        for (const [material, required] of Object.entries(order.materials)) {
            const submitted = order.materialsSubmitted[material] || 0;
            if (submitted < required) {
                allCompleted = false;
                break;
            }
        }

        if (!allCompleted) {
            notifyapi.shownotify(player, '还有材料未完成，无法获得服装', 'error');
            return;
        }

        // 创建服装物品
        const clothingName = `${ClothingTypes[order.clothingType]}`;
        
        // 从设计图数据中构建正确的服装组件
        let clothcomponents: any = null;
        let clothname: string | undefined = undefined;
        
        if (order.blueprintData && order.blueprintData.component) {
            const component = order.blueprintData.component;
            clothcomponents = {
                id: component.id,
                dlc: component.dlc,
                drawable: component.drawable,
                texture: component.texture,
                isProp: component.isProp,
                sex: component.sex  // sex包含在clothcomponents中
            };
            clothname = `定制${clothingName}`;
        }
        
        const [success, message] = await useInventory.addItem(
            clothingName,
            1,
            { player: player },
            undefined,  // customData设为undefined
            clothcomponents,  // 传递clothcomponents（包含sex）
            clothname  // 传递clothname
        );

        if (success) {
            // 删除订单
            await db.deleteDocument(order._id!, 'cloth_orders');

            notifyapi.shownotify(player, `成功获得定制${clothingName}！`, 'success');
            Rebar.player.useWebview(player).emit('clothOrder:clothReceived');

            console.log(`玩家 ${player.name} 完成服装订单: ${order.clothingType}`);
        } else {
            notifyapi.shownotify(player, `创建服装失败: ${message || '未知错误'}`, 'error');
        }

    } catch (error) {
        console.error('完成订单失败:', error);
        notifyapi.shownotify(player, '完成订单失败，请重试', 'error');
    }
});

// 工具函数：创建设计图
export function createDesignBlueprint(
    type: string,
    finish: number = 0,
    component?: {
        dlc: string | number,
        drawable: number,
        texture: number,
        sex: number,
        id: number,
        isProp: boolean
    }
): any {
    // 检查是否为汽车设计图类型
    const isCarDesign = CarDesignTypes[type];
    const isClothingDesign = ClothingTypes[type];
    
    if (!isCarDesign && !isClothingDesign) {
        throw new Error(`未知的设计图类型: ${type}`);
    }

    const blueprintData: any = {
        type: type,
        finish: Math.max(0, Math.min(100, finish)) // 限制完成度在0-100之间
    };

    // 只有汽车设计图才添加designCategory标识，保持服装设计图向后兼容
    if (isCarDesign) {
        blueprintData.designCategory = 'car';
    }

    // 如果是服装设计图且提供了服装组件信息，则添加到设计图中
    if (isClothingDesign && component) {
        blueprintData.component = {
            dlc: component.dlc,
            drawable: component.drawable,
            texture: component.texture,
            sex: component.sex,
            id: component.id,
            isProp: component.isProp
        };
    }

    return blueprintData;
}

// 获取玩家背包中的设计图
async function getPlayerBlueprints(player: alt.Player): Promise<any[]> {
    try {
        const inventory = await useInventory.getInventory({ player: player });
        const blueprints: any[] = [];

        for (let i = 0; i < inventory.length; i++) {
            const item = inventory[i];
            if (item && item.name === '服装设计图' && item.customData && validateBlueprintData(item.customData)) {
                const typeName = ClothingTypes[item.customData.type] || '未知类型';
                const blueprintInfo = {
                    slot: item.slot,
                    type: item.customData.type,
                    typeName: typeName,
                    finish: item.customData.finish,
                    component: item.customData.component || null,
                    customname: (item as any).customname || `${typeName}设计图(${item.customData.finish}%)`
                };
                blueprints.push(blueprintInfo);
            }
            // 添加汽车设计图支持
            else if (item && item.name === '汽车设计图' && item.customData && validateCarBlueprintData(item.customData)) {
                const typeName = CarDesignTypes[item.customData.type] || '未知汽车类型';
                const blueprintInfo = {
                    slot: item.slot,
                    type: item.customData.type,
                    typeName: typeName,
                    finish: item.customData.finish,
                    designCategory: 'car',
                    customname: (item as any).customname || `${typeName}设计图(${item.customData.finish}%)`
                };
                blueprints.push(blueprintInfo);
            }
        }

        return blueprints;
    } catch (error) {
        console.error('获取玩家设计图失败:', error);
        return [];
    }
}

// 工具函数：验证汽车设计图数据
export function validateCarBlueprintData(customData: any): boolean {
    return (
        customData &&
        typeof customData === 'object' &&
        typeof customData.type === 'string' &&
        CarDesignTypes[customData.type] &&
        typeof customData.finish === 'number' &&
        customData.finish >= 0 &&
        customData.finish <= 100 &&
        customData.designCategory === 'car'
    );
}

// 工具函数：验证设计图数据
export function validateBlueprintData(customData: any): boolean {
    const baseValid = (
        customData &&
        typeof customData === 'object' &&
        typeof customData.type === 'string' &&
        ClothingTypes[customData.type] &&
        typeof customData.finish === 'number' &&
        customData.finish >= 0 &&
        customData.finish <= 100
    );

    // 如果有component数据，也需要验证
    if (customData.component) {
        const componentValid = (
            typeof customData.component === 'object' &&
            (typeof customData.component.dlc === 'string' || typeof customData.component.dlc === 'number') &&
            typeof customData.component.drawable === 'number' &&
            typeof customData.component.texture === 'number' &&
            typeof customData.component.sex === 'number' &&
            typeof customData.component.id === 'number' &&
            typeof customData.component.isProp === 'boolean'
        );
        return baseValid && componentValid;
    }

    return baseValid;
}

// 设计图预览相关事件处理
alt.onClient('designPreview:preview', (player: alt.Player, blueprintData: any) => {
    try {
        if (!blueprintData.component) {
            notifyapi.shownotify(player, '该设计图没有服装组件信息，无法预览', 'error');
            return;
        }

        const component = blueprintData.component;
        const dlcHash = component.dlc === 0 ? 0 : (typeof component.dlc === 'string' ? alt.hash(component.dlc) : component.dlc);

        // 预览服装
        if (component.isProp) {
            player.setDlcProp(dlcHash, component.id, component.drawable, component.texture);
        } else {
            player.setDlcClothes(dlcHash, component.id, component.drawable, component.texture, 0);
        }

       // console.log(`玩家 ${player.name} 预览设计图: ${blueprintData.typeName} (${blueprintData.finish}%)`);
    } catch (error) {
        console.error('设计图预览失败:', error);
        notifyapi.shownotify(player, '预览失败，请重试', 'error');
    }
});

// 恢复玩家原始外观
alt.onClient('designPreview:restore', (player: alt.Player) => {
    try {
        const playerData = Rebar.document.character.useCharacter(player).get();
        Rebar.player.useClothing(player).sync(playerData);

        // 确保躯体值正确设置
        const bodynumber = Rebar.document.character.useCharacter(player).getField('body') || 0;
        player.setClothes(3, bodynumber, 0);
    } catch (error) {
        console.error('恢复玩家外观失败:', error);
    }
});

// 管理员命令：创建设计图（用于测试）
messenger.commands.register({
    name: '/createblueprint',
    desc: '创建设计图 [类型] [完成度] [dlc] [drawable] [texture] [sex] [id] [isProp]',
    options: { permissions: ['admin'] },
    callback: async (player: alt.Player, ...args: string[]) => {
        const [type, finishStr, dlc, drawableStr, textureStr, sexStr, idStr, isPropStr] = args;

        if (!type) {
            notifyapi.shownotify(player, '请指定设计图类型', 'error');
            notifyapi.shownotify(player, `可用服装类型: ${Object.keys(ClothingTypes).join(', ')}`, 'info');
            notifyapi.shownotify(player, `可用汽车类型: ${Object.keys(CarDesignTypes).join(', ')}`, 'info');
            return;
        }

        const isCarType = CarDesignTypes[type];
        const isClothingType = ClothingTypes[type];

        if (!isCarType && !isClothingType) {
            notifyapi.shownotify(player, `未知的设计图类型: ${type}`, 'error');
            notifyapi.shownotify(player, `可用服装类型: ${Object.keys(ClothingTypes).join(', ')}`, 'info');
            notifyapi.shownotify(player, `可用汽车类型: ${Object.keys(CarDesignTypes).join(', ')}`, 'info');
            return;
        }

        const finish = parseInt(finishStr) || 0;
        if (finish < 0 || finish > 100) {
            notifyapi.shownotify(player, '完成度必须在0-100之间', 'error');
            return;
        }

        let component = undefined;

        // 只有服装类型才需要组件参数
        if (isClothingType && dlc && drawableStr && textureStr && sexStr && idStr && isPropStr) {
            component = {
                dlc: dlc === '0' ? 0 : dlc,
                drawable: parseInt(drawableStr) || 0,
                texture: parseInt(textureStr) || 0,
                sex: parseInt(sexStr) || 0,
                id: parseInt(idStr) || 0,
                isProp: isPropStr === 'true'
            };
        }

        try {
            const blueprintData = createDesignBlueprint(type, finish, component);
            
            // 根据类型选择物品名称
            const itemName = isCarType ? '汽车设计图' : '服装设计图';
            const typeName = isCarType ? CarDesignTypes[type] : ClothingTypes[type];
            
            const [success, message] = await useInventory.addItem(
                itemName,
                1,
                { player: player },
                blueprintData  // customData
            );

            if (success) {
                const componentInfo = component ? ' (含服装组件)' : '';
                notifyapi.shownotify(player, 
                    `成功创建${typeName}设计图，完成度${finish}%${componentInfo}`, 
                    'success'
                );
            } else {
                notifyapi.shownotify(player, `创建设计图失败: ${message || '未知错误'}`, 'error');
            }
        } catch (error) {
            console.error('创建设计图失败:', error);
            notifyapi.shownotify(player, '创建设计图失败', 'error');
        }
    }
});

// 管理员命令：随机创建100%完成度设计图
messenger.commands.register({
    name: '/randblueprint',
    desc: '创建一张100%完成度的设计图（排除背包）',
    options: { permissions: ['admin'] },
    callback: async (player: alt.Player) => {
        try {
            // 过滤掉背包组件（id=5的组件）
            const nonBagClothes = ModClothes.filter(item => item.id !== 5);

            if (nonBagClothes.length === 0) {
                notifyapi.shownotify(player, '没有可用的服装组件', 'error');
                return;
            }

            // 随机选择一个服装组件
            const randomIndex = Math.floor(Math.random() * nonBagClothes.length);
            const selectedCloth = nonBagClothes[randomIndex];

            // 根据组件ID和是否为道具确定服装类型
            let clothingType: string;
            if (selectedCloth.isProp) {
                clothingType = PropIdToType[selectedCloth.id];
            } else {
                clothingType = ComponentIdToType[selectedCloth.id];
            }

            if (!clothingType || !ClothingTypes[clothingType]) {
                notifyapi.shownotify(player, `未知的服装组件类型: ID=${selectedCloth.id}, isProp=${selectedCloth.isProp}`, 'error');
                return;
            }

            // 创建组件信息
            const component = {
                dlc: selectedCloth.dlc,
                drawable: selectedCloth.drawable,
                texture: selectedCloth.texture,
                sex: selectedCloth.sex,
                id: selectedCloth.id,
                isProp: selectedCloth.isProp
            };

            // 创建100%完成度的设计图
            const blueprintData = createDesignBlueprint(clothingType, 100, component);

            // 添加到管理员背包
            const [success, message] = await useInventory.addItem(
                '服装设计图',
                1,
                { player: player },
                blueprintData  // customData
            );

            if (success) {
                const sexText = selectedCloth.sex === 0 ? '女性' : '男性';
                const dlcText = selectedCloth.dlc === '0' ? '基础款式' : String(selectedCloth.dlc);

                notifyapi.shownotify(player,
                    `获得${ClothingTypes[clothingType]}设计图 (100%)`,
                    'success'
                );
                notifyapi.shownotify(player,
                    `设计款式: ${sexText} ${dlcText} ${selectedCloth.drawable}-${selectedCloth.texture}`,
                    'info'
                );

                console.log(`管理员 ${player.name} 生成了设计图: ${clothingType} (${selectedCloth.dlc}, ${selectedCloth.drawable}, ${selectedCloth.texture})`);
            } else {
                notifyapi.shownotify(player, `创建设计图失败: ${message || '未知错误'}`, 'error');
            }

        } catch (error) {
            console.error('创建设计图失败:', error);
            notifyapi.shownotify(player, '创建设计图失败，请重试', 'error');
        }
    }
});

// 管理员命令：查看玩家服装订单
messenger.commands.register({
    name: '/checkclothorder',
    desc: '查看指定玩家的服装订单 [玩家ID]',
    options: { permissions: ['admin'] },
    callback: async (player: alt.Player, playerIdStr: string) => {
        try {
            const playerId = parseInt(playerIdStr);
            if (!playerId) {
                notifyapi.shownotify(player, '请输入有效的玩家ID', 'error');
                return;
            }

            const orders = await db.getMany({
                characterId: playerId,
                submitted: true
            }, 'cloth_orders') as ClothOrder[];

            if (orders.length === 0) {
                notifyapi.shownotify(player, `玩家ID ${playerId} 没有进行中的服装订单`, 'info');
                return;
            }

            const order = orders[0];
            notifyapi.shownotify(player, `订单类型: ${ClothingTypes[order.clothingType]}`, 'info');
            notifyapi.shownotify(player, `材料价值: ¥${order.materialCost.toLocaleString()}`, 'info');

            let completedMaterials = 0;
            let totalMaterials = 0;
            for (const [material, required] of Object.entries(order.materials)) {
                const submitted = order.materialsSubmitted[material] || 0;
                totalMaterials++;
                if (submitted >= required) completedMaterials++;

                notifyapi.shownotify(player, `${material}: ${submitted}/${required}`, 'info');
            }

            notifyapi.shownotify(player, `完成度: ${completedMaterials}/${totalMaterials}`, 'info');

        } catch (error) {
            console.error('查看订单失败:', error);
            notifyapi.shownotify(player, '查看订单失败', 'error');
        }
    }
});

// 管理员命令：清除玩家服装订单
messenger.commands.register({
    name: '/clearclothorder',
    desc: '清除指定玩家的服装订单 [玩家ID]',
    options: { permissions: ['admin'] },
    callback: async (player: alt.Player, playerIdStr: string) => {
        try {
            const playerId = parseInt(playerIdStr);
            if (!playerId) {
                notifyapi.shownotify(player, '请输入有效的玩家ID', 'error');
                return;
            }

            const orders = await db.getMany({
                characterId: playerId,
                submitted: true
            }, 'cloth_orders') as ClothOrder[];

            if (orders.length === 0) {
                notifyapi.shownotify(player, `玩家ID ${playerId} 没有进行中的服装订单`, 'info');
                return;
            }

            // 删除所有订单
            for (const order of orders) {
                await db.deleteDocument(order._id!, 'cloth_orders');
            }

            notifyapi.shownotify(player, `已清除玩家ID ${playerId} 的 ${orders.length} 个服装订单`, 'success');

        } catch (error) {
            console.error('清除订单失败:', error);
            notifyapi.shownotify(player, '清除订单失败', 'error');
        }
    }
});

// 管理员命令：给予材料
messenger.commands.register({
    name: '/givematerial',
    desc: '给予材料 [材料名] [数量]',
    options: { permissions: ['admin'] },
    callback: async (player: alt.Player, materialName: string, amountStr: string = '1') => {
        try {
            if (!materialName) {
                notifyapi.shownotify(player, '请指定材料名称', 'error');
                notifyapi.shownotify(player, `可用材料: ${AVAILABLE_MATERIALS.join(', ')}`, 'info');
                return;
            }

            if (!AVAILABLE_MATERIALS.includes(materialName)) {
                notifyapi.shownotify(player, `未知的材料: ${materialName}`, 'error');
                notifyapi.shownotify(player, `可用材料: ${AVAILABLE_MATERIALS.join(', ')}`, 'info');
                return;
            }

            const amount = parseInt(amountStr) || 1;
            if (amount <= 0) {
                notifyapi.shownotify(player, '数量必须大于0', 'error');
                return;
            }

            const [success, message] = await useInventory.addItem(
                materialName,
                amount,
                { player: player }
            );

            if (success) {
                notifyapi.shownotify(player, `成功获得${amount}个${materialName}`, 'success');
            } else {
                notifyapi.shownotify(player, `给予材料失败: ${message || '未知错误'}`, 'error');
            }

        } catch (error) {
            console.error('给予材料失败:', error);
            notifyapi.shownotify(player, '操作失败', 'error');
        }
    }
});


// 服装订单接口
interface ClothOrder {
    _id?: string;
    characterId: number;
    blueprintData: any; // 设计图数据
    clothingType: string;
    materials: { [key: string]: number };
    materialsSubmitted: { [key: string]: number };
    materialCost: number; // 材料总价格
    submitted: boolean;
    createdAt: number;
    expiresAt: number; // 订单过期时间（30天）
}

// 服装类型材料价格配置
const CLOTHING_MATERIAL_COSTS = {
    'shoes': 70000,      // 鞋子 7w
    'tops': 50000,       // 上衣 5w  
    'mask': 30000,       // 面具 3w
    'legs': 60000,       // 裤子 6w
    'hat': 30000,        // 帽子 3w
    'glasses': 30000,    // 眼镜 3w
    'ears': 30000,       // 耳环 3w
    'watch': 30000,      // 手表 3w
    'bracelet': 30000,   // 手环 3w
    'bag': 40000,        // 背包 4w
    'accessories': 30000, // 饰品 3w
    'bodyarmor': 80000,  // 护甲 8w
    'decals': 20000,     // 贴纸 2w
    'under': 30000       // 内衬 3w
};

// 材料列表（用于随机生成需求）
const AVAILABLE_MATERIALS = [
    '棉线', '亚麻线', '涤纶', '尼龙', '腈纶',
];

// 生成随机材料需求
function generateRandomMaterials(baseCost: number, completionRate: number = 75): { [key: string]: number } {
    const materials: { [key: string]: number } = {};

    // 根据完成度调整材料价格
    // 设计图完成度范围50-100，以75%为基准点
    // 100%完成度 → 75%材料需求，50%完成度 → 125%材料需求
    // 公式：材料需求 = 150% - 完成度 × 0.75%
    const priceMultiplier = Math.max(0.75, Math.min(1.25, 1.5 - (completionRate * 0.0075)));
    const adjustedCost = Math.round(baseCost * priceMultiplier);

    let remainingCost = adjustedCost;

    // 首先给每种材料分配基础数量（至少1个）
    for (const materialName of AVAILABLE_MATERIALS) {
        const price = clothprice.find(p => p.name === materialName)?.price || 50;
        const baseQuantity = Math.max(1, Math.floor(adjustedCost * 0.05 / price)); // 每种材料至少占总价值的5%
        materials[materialName] = baseQuantity;
        remainingCost -= baseQuantity * price;
    }

    // 然后随机分配剩余的价值
    if (remainingCost > 0) {
        // 随机选择一些材料增加数量
        const materialNames = [...AVAILABLE_MATERIALS];
        const extraRounds = Math.floor(Math.random() * 3) + 2; // 2-4轮额外分配

        for (let round = 0; round < extraRounds && remainingCost > 0; round++) {
            // 随机选择一个材料
            const randomMaterial = materialNames[Math.floor(Math.random() * materialNames.length)];
            const price = clothprice.find(p => p.name === randomMaterial)?.price || 50;
            
            // 分配10-30%的剩余价值给这个材料
            const percentage = 0.1 + Math.random() * 0.2;
            const allocatedCost = Math.min(remainingCost, Math.floor(remainingCost * percentage));
            const extraQuantity = Math.max(1, Math.floor(allocatedCost / price));
            
            materials[randomMaterial] += extraQuantity;
            remainingCost -= extraQuantity * price;
        }

        // 如果还有剩余价值，随机分配给最便宜的材料
        if (remainingCost > 0) {
            const cheapestMaterial = AVAILABLE_MATERIALS.reduce((cheapest, current) => {
                const currentPrice = clothprice.find(p => p.name === current)?.price || 50;
                const cheapestPrice = clothprice.find(p => p.name === cheapest)?.price || 50;
                return currentPrice < cheapestPrice ? current : cheapest;
            });
            
            const price = clothprice.find(p => p.name === cheapestMaterial)?.price || 50;
            const extraQuantity = Math.floor(remainingCost / price);
            if (extraQuantity > 0) {
                materials[cheapestMaterial] += extraQuantity;
            }
        }
    }

    return materials;
}

// 处理开始绘画（选择模板时立即消耗空白设计图）
alt.onClient('cloth:startDrawing', async (player: alt.Player, data: any) => {
    try {
        const { template } = data;

        if (!template) {
           Rebar.player.useWebview(player).emit('cloth:drawingStarted', { success: false, message: '模板数据无效' });
            return;
        }

        // 检查玩家是否有空白设计图
        const inventory = await useInventory.getInventory({ player: player });
        const blankBlueprint = inventory.find(item => item && item.name === '空白设计图');

        if (!blankBlueprint) {
            Rebar.player.useWebview(player).emit('cloth:drawingStarted', { 
                success: false, 
                message: '你的背包里没有空白设计图' 
            });
            return;
        }

        // 立即消耗空白设计图
        const [removeSuccess, removeMessage] = await useInventory.subItem(
            '空白设计图',
            1,
            { player: player }
        );

        if (!removeSuccess) {
            Rebar.player.useWebview(player).emit('cloth:drawingStarted', { 
                success: false, 
                message: `消耗空白设计图失败: ${removeMessage || '未知错误'}` 
            });
            return;
        }

        // 成功消耗，允许开始绘画
        Rebar.player.useWebview(player).emit('cloth:drawingStarted', { 
            success: true, 
            message: '空白设计图已消耗，开始绘制' 
        });
        
        console.log(`玩家 ${player.name} 选择模板 ${template}，空白设计图已消耗`);

    } catch (error) {
        console.error('处理开始绘画事件时发生错误:', error);
        Rebar.player.useWebview(player).emit('cloth:drawingStarted', { 
            success: false, 
            message: '服务器错误，请重试' 
        });
    }
});

// 处理绘画设计保存
alt.onClient('cloth:saveDesign', async (player: alt.Player, designData: any) => {

    let isReady = await Rebar.player.useWebview(player).isReady('drawingInterface', 'page');
    if (isReady) {
        Rebar.player.useWebview(player).hide('drawingInterface');
    }

    try {
        const { template, completion } = designData;

        if (!template || completion < 0) {
            notifyapi.shownotify(player, '设计数据无效', 'error');
            return;
        }

        // 注意：空白设计图已经在startDrawing时消耗了，这里不再消耗

        // 检查完成度是否达到最低要求
        if (completion < 50) {
            // 完成度过低，不生成任何东西
            notifyapi.shownotify(player, '绘制失败！完成度过低，设计失败', 'error');
            notifyapi.shownotify(player, '提示：完成度至少需要达到50%才能产出有效设计', 'warning');
            return;
        }

        // 检查是否为汽车设计图模板
        const isCarTemplate = template.includes('car_');
        
        if (isCarTemplate) {
            // 处理汽车设计图
            let carType: string;
            if (template === 'car_side') {
                carType = 'car_side';
            } else if (template === 'car_top') {
                carType = 'car_top';
            } else if (template === 'car_front') {
                carType = 'car_front';
            } else {
                carType = 'car_side'; // 默认侧视图
            }

            // 创建汽车设计图数据
            const carBlueprintData = createDesignBlueprint(carType, Math.round(completion));

            // 保存汽车设计图到玩家背包
            const [success, message] = await useInventory.addItem(
                '汽车设计图',
                1,
                { player: player },
                carBlueprintData  // customData
            );

            if (success) {
                notifyapi.shownotify(player,
                    `成功完成${CarDesignTypes[carType]}设计，完成度${Math.round(completion)}%`,
                    'success'
                );
                console.log(`玩家 ${player.name} 成功创建汽车设计图: ${carType} (${completion}%)`);
            } else {
                notifyapi.shownotify(player, `保存汽车设计图失败: ${message || '未知错误'}`, 'error');
            }
            return;
        }

        // 根据模板确定服装类型和筛选条件
        let clothingType: string;
        let filterConditions: { sex: number; dlc?: string; id: number } = { sex: 1, id: 11 };

        if (template.includes('boyuppr') || template.includes('男士上衣')) {
            clothingType = 'tops';
            filterConditions = { sex: 1, dlc: 'boyuppr', id: 11 }; // 男士上衣
        } else if (template.includes('boylowr') || template.includes('男士裤子')) {
            clothingType = 'legs';
            filterConditions = { sex: 1, dlc: 'boylowr', id: 4 }; // 男士裤子
        } else if (template.includes('boyshoe') || template.includes('男士鞋子')) {
            clothingType = 'shoes';
            filterConditions = { sex: 1, dlc: 'boyshoe', id: 6 }; // 男士鞋子
        } else if (template.includes('boyneak') || template.includes('男士饰品')) {
            clothingType = 'accessories';
            filterConditions = { sex: 1, dlc: 'boyneak', id: 7 }; // 男士饰品
        } else if (template.includes('boymask') || template.includes('男士面具')) {
            clothingType = 'mask';
            filterConditions = { sex: 1, dlc: 'boymask', id: 1 }; // 男士面具
        } else {
            clothingType = 'tops'; // 默认为上衣
            filterConditions = { sex: 1, dlc: 'boyuppr', id: 11 };
        }

        // 根据筛选条件从ModClothes中精确筛选
        const clothingComponents = ModClothes.filter(item => {
            const sexMatch = item.sex === filterConditions.sex;
            const idMatch = item.id === filterConditions.id;
            const dlcMatch = filterConditions.dlc ? item.dlc === filterConditions.dlc : true;

            return sexMatch && idMatch && dlcMatch;
        });

        if (clothingComponents.length === 0) {
            notifyapi.shownotify(player, `该模板类型暂无可用组件: ${template}`, 'error');
            console.log(`警告: 模板 ${template} 没有找到匹配的服装组件`, filterConditions);
            return;
        }

        // 随机选择一个组件
        const randomComponent = clothingComponents[Math.floor(Math.random() * clothingComponents.length)];

        // 创建组件信息
        const component = {
            dlc: randomComponent.dlc,
            drawable: randomComponent.drawable,
            texture: randomComponent.texture,
            sex: randomComponent.sex,
            id: randomComponent.id,
            isProp: randomComponent.isProp
        };

        // 创建设计图数据
        const blueprintData = createDesignBlueprint(clothingType, Math.round(completion), component);

        // 保存设计图到玩家背包
        const [success, message] = await useInventory.addItem(
            '服装设计图',
            1,
            { player: player },
            blueprintData  // customData
        );

        if (success) {
            const sexText = randomComponent.sex === 0 ? '女性' : '男性';
            const dlcText = randomComponent.dlc === '0' ? '基础款式' : String(randomComponent.dlc);

            notifyapi.shownotify(player,
                `成功完成${ClothingTypes[clothingType]}设计，完成度${Math.round(completion)}%`,
                'success'
            );
            notifyapi.shownotify(player,
                `设计风格: ${sexText} ${dlcText} 款式 ${randomComponent.drawable}-${randomComponent.texture}`,
                'info'
            );
          //  console.log(`玩家 ${player.name} 成功创建手绘设计图: ${clothingType} (${completion}%) 组件: ${randomComponent.dlc}, ${randomComponent.drawable}, ${randomComponent.texture}`);
        } else {
            notifyapi.shownotify(player, `保存设计图失败: ${message || '未知错误'}`, 'error');
        }

    } catch (error) {
        console.error('保存绘画设计失败:', error);
        notifyapi.shownotify(player, '保存设计失败，请重试', 'error');
    }
});






alt.on('rebar:playerPageOpened', (player: alt.Player, page: PageNames) => {
    if (page === 'drawingInterface') {
        Rebar.player.useWorld(player).disableControls();
    }
});

alt.on('rebar:playerPageClosed', (player: alt.Player, page: PageNames) => {
    if (page === 'drawingInterface') {
        Rebar.player.useWorld(player).enableControls();
    }
});

// 订单过期检查功能
async function checkExpiredOrders() {
    try {
        const currentTime = Date.now();
        
        // 查找所有过期的订单
        const expiredOrders = await db.getMany({
            expiresAt: { $lt: currentTime },
            submitted: true
        }, 'cloth_orders') as unknown as ClothOrder[];

        for (const order of expiredOrders) {
            // 删除过期订单（不返还任何材料）
            await db.deleteDocument(order._id, 'cloth_orders');
            
            // 如果玩家在线，通知他们订单已过期
            const onlinePlayer = alt.Player.all.find(p => {
                const character = Rebar.document.character.useCharacter(p);
                const characterData = character.get();
                return characterData && characterData.id === order.characterId;
            });

            if (onlinePlayer) {
                notifyapi.shownotify(onlinePlayer, '你的服装订单已过期', 'warning');
                // 通知前端订单已取消
                Rebar.player.useWebview(onlinePlayer).emit('clothOrder:orderCancelled');
            }
        }

        if (expiredOrders.length > 0) {
            console.log(`清理了 ${expiredOrders.length} 个过期的服装订单`);
        }
    } catch (error) {
        console.error('检查过期订单时出错:', error);
    }
}

// 每60分钟检查一次过期订单
GlobalTimerManager.getInstance().addTask('cloth:checkExpiredOrders', checkExpiredOrders, 60 * 60);

// 服务器启动时立即检查一次
checkExpiredOrders();