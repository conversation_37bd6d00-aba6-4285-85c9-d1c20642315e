<template>
  <div class="admin-container">
    <div class="admin-panel">
      <!-- 页面头部 -->
      <div class="page-header">
        <div class="header-content">
          <div class="title-section">
            <div class="header-icon">
              <i class="fas fa-ship"></i>
            </div>
            <div class="title-info">
              <h1>船只商店管理</h1>
              <span class="subtitle">Ship Shop Management</span>
            </div>
          </div>
          <div class="status-indicator">
            <span class="status-badge">{{ ships.length }} 船只</span>
          </div>
        </div>
      </div>

      <div class="content-area">
        <!-- 搜索和操作区域 -->
        <div class="search-section">
          <div class="search-card">
            <div class="search-header">
              <div class="header-title">
                <i class="fas fa-search"></i>
                <h3>搜索船只</h3>
              </div>
            </div>
            <div class="search-content">
              <div class="search-box">
                <i class="fas fa-search"></i>
                <input 
                  v-model="searchTerm" 
                  placeholder="输入船只名称..." 
                  class="search-input"
                >
              </div>
              <div class="filter-section">
                <select v-model="filterType" class="filter-select">
                  <option value="">所有类型</option>
                  <option v-for="type in shipTypes" :key="type" :value="type">{{ type }}</option>
                </select>
                <select v-model="sortOrder" class="filter-select">
                  <option value="name">按名称排序</option>
                  <option value="price">按价格排序</option>
                  <option value="type">按类型排序</option>
                </select>
              </div>
            </div>
          </div>
        </div>

        <!-- 新建船只区域 -->
        <div class="add-vehicle-section">
          <div class="add-card">
            <div class="card-header">
              <div class="header-title">
                <i class="fas fa-plus-circle"></i>
                <h3>新建船只</h3>
              </div>
              <button @click="toggleAddForm" class="toggle-btn">
                <i :class="showAddForm ? 'fas fa-chevron-up' : 'fas fa-chevron-down'"></i>
              </button>
            </div>
            <div v-if="showAddForm" class="add-form">
              <div class="form-grid">
                <div class="form-group">
                  <label>船只名称</label>
                  <input v-model="newShip.name" placeholder="例如: speeder" class="form-input">
                </div>
                <div class="form-group">
                  <label>船只类型</label>
                  <select v-model="newShip.type" class="form-select">
                    <option value="">选择类型</option>
                    <option value="Ship">船只</option>
                    <option value="Boat">游艇</option>
                    <option value="Speedboat">快艇</option>
                    <option value="Yacht">豪华游艇</option>
                    <option value="Jetski">水上摩托</option>
                  </select>
                </div>
                <div class="form-group">
                  <label>价格</label>
                  <input v-model.number="newShip.price" type="number" min="0" placeholder="0" class="form-input">
                </div>
                <div class="form-group">
                  <label>最大速度</label>
                  <input v-model.number="newShip.maxSpeed" type="number" min="0" placeholder="120" class="form-input">
                </div>
                <div class="form-group">
                  <label>座位数</label>
                  <input v-model.number="newShip.seats" type="number" min="1" max="8" placeholder="4" class="form-input">
                </div>
                <div class="form-group">
                  <label>储物空间</label>
                  <input v-model.number="newShip.trunkCapacity" type="number" min="0" placeholder="10" class="form-input">
                </div>
                <div class="form-group">
                  <label>油箱容量</label>
                  <input v-model.number="newShip.fuelTank" type="number" min="0" placeholder="60" class="form-input">
                </div>
              </div>
              <div class="form-actions">
                <button @click="addShip" class="add-btn" :disabled="!canAddShip">
                  <i class="fas fa-plus"></i>
                  <span>添加船只</span>
                </button>
                <button @click="resetForm" class="reset-btn">
                  <i class="fas fa-undo"></i>
                  <span>重置表单</span>
                </button>
              </div>
            </div>
          </div>
        </div>

        <!-- 船只列表区域 -->
        <div class="vehicles-section">
          <div class="vehicles-card">
            <div class="card-header">
              <div class="header-title">
                <i class="fas fa-list"></i>
                <h3>船只列表</h3>
              </div>
              <div class="count-badge">
                {{ filteredShips.length }} / {{ ships.length }}
              </div>
            </div>
            <div class="vehicles-list">
              <div v-if="filteredShips.length === 0" class="empty-state">
                <div class="empty-icon">
                  <i class="fas fa-inbox"></i>
                </div>
                <div class="empty-text">
                  <h3>暂无船只</h3>
                  <p>{{ searchTerm ? '没有找到匹配的船只' : '暂时没有船只上架' }}</p>
                </div>
              </div>
              
              <div v-for="ship in filteredShips" :key="ship._id" class="vehicle-item">
                <div class="vehicle-info">
                  <div class="vehicle-icon">
                    <i class="fas fa-ship"></i>
                  </div>
                  <div class="vehicle-details">
                    <div class="vehicle-name">{{ ship.name }}</div>
                    <div class="vehicle-meta">
                      <span class="vehicle-type">{{ ship.type }}</span>
                      <span class="vehicle-specs">{{ ship.maxSpeed }}km/h · {{ ship.seats }}座</span>
                    </div>
                  </div>
                </div>
                
                <div class="vehicle-stats">
                  <div class="stat-item">
                    <span class="stat-label">储物</span>
                    <span class="stat-value">{{ ship.trunkCapacity }}kg</span>
                  </div>
                  <div class="stat-item">
                    <span class="stat-label">油箱</span>
                    <span class="stat-value">{{ ship.fuelTank }}L</span>
                  </div>
                </div>
                
                <div class="vehicle-price">
                  <input 
                    v-model.number="ship.price" 
                    type="number" 
                    min="0" 
                    class="price-input"
                    @keyup.enter="updateShip(ship)"
                  >
                  <span class="price-currency">元</span>
                </div>
                
                <div class="vehicle-actions">
                  <button @click="updateShip(ship)" class="action-btn save-btn">
                    <i class="fas fa-save"></i>
                    <span>保存</span>
                  </button>
                  <button @click="removeShip(ship)" class="action-btn remove-btn">
                    <i class="fas fa-trash"></i>
                    <span>删除</span>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 底部操作区域 -->
      <div class="footer-actions">
        <button @click="close" class="close-button">
          <i class="fas fa-times"></i>
          <span>关闭</span>
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { useEvents } from '@Composables/useEvents.js';

const events = useEvents();
const ships = ref<any[]>([]);
const searchTerm = ref('');
const filterType = ref('');
const sortOrder = ref('name');
const showAddForm = ref(false);

// 新建船只表单数据
const newShip = ref({
  name: '',
  type: '',
  price: 0,
  maxSpeed: 120,
  seats: 4,
  trunkCapacity: 10,
  fuelTank: 60
});

// 加载FontAwesome
const loadFontAwesome = () => {
  if (!document.querySelector('link[href*="font-awesome"]')) {
    const link = document.createElement('link');
    link.rel = 'stylesheet';
    link.href = 'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css';
    link.integrity = 'sha512-iecdLmaskl7CVkqkXNQ/ZH/XLlvWZOJyj7Yy7tcenmpD1ypASozpmT/E0iPtmFIB46ZmdtAc9eNBvH0H/ZpiBw==';
    link.crossOrigin = 'anonymous';
    link.referrerPolicy = 'no-referrer';
    document.head.appendChild(link);
  }
};

// 接收服务器发送的船只列表
events.on('adminShipShop:list', (list: any[]) => {
  ships.value = list || [];
});

// 船只类型列表
const shipTypes = computed(() => {
  const types = new Set(ships.value.map(v => v.type).filter(Boolean));
  return Array.from(types);
});

// 过滤后的船只列表
const filteredShips = computed(() => {
  let filtered = ships.value.filter(v => {
    const matchesSearch = !searchTerm.value || 
      (v.name && v.name.toLowerCase().includes(searchTerm.value.toLowerCase()));
    const matchesType = !filterType.value || v.type === filterType.value;
    return matchesSearch && matchesType;
  });

  // 排序
  filtered.sort((a, b) => {
    switch (sortOrder.value) {
      case 'price':
        return (a.price || 0) - (b.price || 0);
      case 'type':
        return (a.type || '').localeCompare(b.type || '');
      default:
        return (a.name || '').localeCompare(b.name || '');
    }
  });

  return filtered;
});

// 是否可以添加船只
const canAddShip = computed(() => {
  return newShip.value.name && 
         newShip.value.type && 
         newShip.value.price >= 0;
});

// 切换添加表单显示
const toggleAddForm = () => {
  showAddForm.value = !showAddForm.value;
};

// 重置表单
const resetForm = () => {
  newShip.value = {
    name: '',
    type: '',
    price: 0,
    maxSpeed: 120,
    seats: 4,
    trunkCapacity: 10,
    fuelTank: 60
  };
};

// 添加船只
const addShip = () => {
  if (!canAddShip.value) return;
  
  events.emitServer('adminShipShop:addVehicle', newShip.value);
  resetForm();
  showAddForm.value = false;
};

// 删除船只
const removeShip = (ship: any) => {
  if (confirm(`确定要删除船只 "${ship.name}" 吗？`)) {
    events.emitServer('adminShipShop:removeVehicle', ship._id);
  }
};

// 更新船只
const updateShip = (ship: any) => {
  if (ship.price < 0) {
    alert('价格不能为负数');
    return;
  }
  events.emitServer('adminShipShop:updateVehicle', ship._id, ship.price);
};

// 关闭页面
const close = () => {
  events.emitClient('closeAdminShipShop');
};

onMounted(() => {
  loadFontAwesome();
});
</script>

<style scoped>
/* CSS重置和基础样式 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

.admin-container {
  --primary-color: #64ffda;
  --primary-dark: #1de9b6;
  --primary-light: #4fd1c7;
  --card-bg: rgba(15, 23, 42, 0.95);
  --card-bg-light: rgba(30, 41, 59, 0.8);
  --text-primary: #ffffff;
  --text-secondary: rgba(255, 255, 255, 0.7);
  --text-muted: rgba(255, 255, 255, 0.5);
  --border-color: rgba(100, 255, 218, 0.2);
  --border-hover: rgba(100, 255, 218, 0.3);
  --shadow-sm: 0 0.2vh 0.8vh rgba(0, 0, 0, 0.2);
  --shadow-md: 0 0.4vh 1.6vh rgba(0, 0, 0, 0.3);
  --shadow-lg: 0 0.8vh 3.2vh rgba(0, 0, 0, 0.4);
  --shadow-primary: 0 0.4vh 2vh rgba(100, 255, 218, 0.3);

  display: flex;
  align-items: center;
  justify-content: center;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  overflow: hidden;
  font-family: "Inter", "PingFang SC", "Microsoft YaHei", sans-serif;
  position: fixed;
  z-index: 1000;
  background: rgba(20, 25, 40, 0.95);
}

.admin-panel {
  position: relative;
  background: var(--card-bg);
  border-radius: 1.2vh;
  box-shadow: var(--shadow-lg), 0 0 2vh rgba(100, 255, 218, 0.1);
  border: 0.1vh solid var(--border-color);
  overflow: hidden;
  width: 90vw;
  max-width: 1200px;
  max-height: 90vh;
  display: flex;
  flex-direction: column;
  animation: panelSlideIn 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 页面头部 */
.page-header {
  padding: 2vh 2.5vh 1.5vh;
  background: linear-gradient(135deg, rgba(100, 255, 218, 0.05) 0%, transparent 60%);
  border-bottom: 0.1vh solid var(--border-color);
  flex-shrink: 0;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.title-section {
  display: flex;
  align-items: center;
  gap: 12px;
  color: #ffffff;
}

.header-icon {
  font-size: 2.4vh;
  color: #0f172a;
  width: 4vh;
  height: 4vh;
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
  border: 0.1vh solid var(--border-color);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: var(--shadow-sm);
}

.title-info h1 {
  font-size: 2vh;
  font-weight: 600;
  margin: 0;
  color: var(--primary-color);
}

.subtitle {
  color: var(--text-secondary);
  font-size: 1.2vh;
  font-weight: 400;
  text-transform: uppercase;
  letter-spacing: 0.1em;
}

.status-indicator {
  display: flex;
  align-items: center;
}

.status-badge {
  background: linear-gradient(135deg, rgba(100, 255, 218, 0.15) 0%, rgba(100, 255, 218, 0.08) 100%);
  border: 0.1vh solid var(--border-color);
  border-radius: 1.5vh;
  padding: 0.4vh 1.2vh;
  color: var(--primary-color);
  font-size: 1.2vh;
  font-weight: 600;
  box-shadow: var(--shadow-sm);
}

/* 内容区域 */
.content-area {
  flex: 1;
  overflow-y: auto;
  padding: 1.5vh;
  display: flex;
  flex-direction: column;
  gap: 1.5vh;
  min-height: 0;
}

/* 卡片通用样式 */
.search-card,
.add-card,
.vehicles-card {
  background: var(--card-bg-light);
  border: 0.1vh solid var(--border-color);
  border-radius: 1.2vh;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: var(--shadow-sm);
}

.search-card:hover,
.add-card:hover,
.vehicles-card:hover {
  background: var(--card-bg);
  border-color: var(--border-hover);
  box-shadow: var(--shadow-primary);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5vh 2vh;
  background: linear-gradient(135deg, rgba(100, 255, 218, 0.05) 0%, transparent 60%);
  border-bottom: 0.1vh solid var(--border-color);
}

.search-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5vh 2vh;
  background: linear-gradient(135deg, rgba(100, 255, 218, 0.05) 0%, transparent 60%);
  border-bottom: 0.1vh solid var(--border-color);
}

.header-title {
  display: flex;
  align-items: center;
  gap: 10px;
}

.header-title i {
  color: var(--primary-color);
  font-size: 1.4vh;
}

.header-title h3 {
  font-size: 1.6vh;
  font-weight: 600;
  margin: 0;
  color: var(--primary-color);
}

.count-badge {
  background: linear-gradient(135deg, rgba(100, 255, 218, 0.15) 0%, rgba(100, 255, 218, 0.08) 100%);
  color: var(--primary-color);
  padding: 0.4vh 0.8vh;
  border-radius: 1vh;
  font-size: 1.2vh;
  font-weight: 600;
  box-shadow: var(--shadow-sm);
}

.toggle-btn {
  width: 3vh;
  height: 3vh;
  background: linear-gradient(135deg, rgba(100, 255, 218, 0.1) 0%, rgba(100, 255, 218, 0.05) 100%);
  border: 0.1vh solid var(--border-color);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--primary-color);
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: var(--shadow-sm);
}

.toggle-btn:hover {
  background: linear-gradient(135deg, rgba(100, 255, 218, 0.2) 0%, rgba(100, 255, 218, 0.1) 100%);
  border-color: var(--border-hover);
  transform: scale(1.05);
  box-shadow: var(--shadow-primary);
}

/* 搜索区域 */
.search-content {
  padding: 1.5vh;
  display: flex;
  flex-direction: column;
  gap: 1.5vh;
}

.search-box {
  position: relative;
  width: 100%;
}

.search-box i {
  position: absolute;
  left: 1.2vh;
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-secondary);
  font-size: 1.4vh;
}

.search-input {
  width: 100%;
  padding: 1.2vh 1.2vh 1.2vh 4vh;
  background: var(--card-bg-light);
  border: 0.1vh solid var(--border-color);
  border-radius: 1vh;
  color: var(--text-primary);
  font-size: 1.4vh;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.search-input:focus {
  border-color: var(--primary-color);
  background-color: var(--card-bg);
  outline: none;
  box-shadow: var(--shadow-primary);
}

.filter-section {
  display: flex;
  gap: 1.5vh;
}

.filter-select {
  flex: 1;
  padding: 1vh;
  background: var(--card-bg-light);
  border: 0.1vh solid var(--border-color);
  border-radius: 1vh;
  color: var(--text-primary);
  font-size: 1.4vh;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.filter-select:focus {
  border-color: var(--primary-color);
  outline: none;
  box-shadow: var(--shadow-primary);
}

.filter-select option {
  background: var(--card-bg);
  color: var(--text-primary);
}

/* 新建船只表单 */
.add-form {
  padding: 1.5vh;
  border-top: 0.1vh solid var(--border-color);
  animation: formSlideDown 0.3s ease;
}

.form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(20vh, 1fr));
  gap: 1.5vh;
  margin-bottom: 2vh;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5vh;
}

.form-group label {
  color: var(--text-secondary);
  font-size: 1.2vh;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.form-input,
.form-select {
  padding: 1vh;
  background: var(--card-bg-light);
  border: 0.1vh solid var(--border-color);
  border-radius: 1vh;
  color: var(--text-primary);
  font-size: 1.4vh;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.form-input:focus,
.form-select:focus {
  border-color: var(--primary-color);
  background-color: var(--card-bg);
  outline: none;
  box-shadow: var(--shadow-primary);
}

.form-select option {
  background: var(--card-bg);
  color: var(--text-primary);
}

.form-actions {
  display: flex;
  gap: 1vh;
  justify-content: flex-end;
}

.add-btn,
.reset-btn {
  display: flex;
  align-items: center;
  gap: 0.8vh;
  padding: 1vh 2vh;
  border: 0.1vh solid;
  border-radius: 1vh;
  cursor: pointer;
  font-size: 1.4vh;
  font-weight: 500;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: var(--shadow-sm);
}

.add-btn {
  background: linear-gradient(135deg, rgba(100, 255, 218, 0.1) 0%, rgba(100, 255, 218, 0.05) 100%);
  border-color: var(--border-color);
  color: var(--primary-color);
}

.add-btn:hover:not(:disabled) {
  background: linear-gradient(135deg, rgba(100, 255, 218, 0.2) 0%, rgba(100, 255, 218, 0.1) 100%);
  border-color: var(--primary-color);
  transform: translateY(-0.1vh);
  box-shadow: var(--shadow-primary);
}

.add-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.reset-btn {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.05) 0%, rgba(255, 255, 255, 0.02) 100%);
  border-color: var(--border-color);
  color: var(--text-secondary);
}

.reset-btn:hover {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
  border-color: var(--border-hover);
  transform: translateY(-0.1vh);
  box-shadow: var(--shadow-primary);
}

/* 船只列表 */
.vehicles-list {
  padding: 1.5vh;
  display: flex;
  flex-direction: column;
  gap: 1.5vh;
  max-height: 40vh;
  overflow-y: auto;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1.5vh;
  padding: 4vh;
  text-align: center;
}

.empty-icon {
  font-size: 4.8vh;
  color: rgba(100, 255, 218, 0.3);
}

.empty-text h3 {
  color: var(--text-secondary);
  font-size: 1.8vh;
  margin-bottom: 0.5vh;
}

.empty-text p {
  color: var(--text-muted);
  font-size: 1.4vh;
}

.vehicle-item {
  display: flex;
  align-items: center;
  gap: 1.5vh;
  padding: 1.5vh;
  background: var(--card-bg-light);
  border: 0.1vh solid var(--border-color);
  border-radius: 1vh;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: var(--shadow-sm);
}

.vehicle-item:hover {
  background: var(--card-bg);
  border-color: var(--border-hover);
  transform: translateY(-0.1vh);
  box-shadow: var(--shadow-primary);
}

.vehicle-info {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
}

.vehicle-icon {
  width: 4vh;
  height: 4vh;
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
  border: 0.1vh solid var(--border-color);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #0f172a;
  font-size: 1.6vh;
  box-shadow: var(--shadow-sm);
}

.vehicle-details {
  flex: 1;
}

.vehicle-name {
  font-size: 1.6vh;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 0.4vh;
}

.vehicle-meta {
  display: flex;
  gap: 1vh;
  font-size: 1.2vh;
  color: var(--text-muted);
}

.vehicle-type {
  color: var(--primary-color);
  font-weight: 500;
}

.vehicle-stats {
  display: flex;
  gap: 1.5vh;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.2vh;
}

.stat-label {
  font-size: 1vh;
  color: var(--text-muted);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.stat-value {
  font-size: 1.2vh;
  color: var(--text-primary);
  font-weight: 500;
}

.vehicle-price {
  display: flex;
  align-items: center;
  gap: 0.5vh;
}

.price-input {
  width: 10vh;
  padding: 0.8vh;
  background: var(--card-bg-light);
  border: 0.1vh solid var(--border-color);
  border-radius: 0.8vh;
  color: var(--text-primary);
  font-size: 1.4vh;
  text-align: right;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.price-input:focus {
  border-color: var(--primary-color);
  outline: none;
  box-shadow: var(--shadow-primary);
}

.price-currency {
  color: var(--text-muted);
  font-size: 1.2vh;
}

.vehicle-actions {
  display: flex;
  gap: 0.8vh;
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 0.5vh;
  padding: 0.8vh 1.2vh;
  border: 0.1vh solid;
  border-radius: 0.8vh;
  cursor: pointer;
  font-size: 1.2vh;
  font-weight: 500;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: var(--shadow-sm);
}

.save-btn {
  background: linear-gradient(135deg, rgba(100, 255, 218, 0.1) 0%, rgba(100, 255, 218, 0.05) 100%);
  border-color: var(--border-color);
  color: var(--primary-color);
}

.save-btn:hover {
  background: linear-gradient(135deg, rgba(100, 255, 218, 0.2) 0%, rgba(100, 255, 218, 0.1) 100%);
  border-color: var(--primary-color);
  transform: translateY(-0.1vh);
  box-shadow: var(--shadow-primary);
}

.remove-btn {
  background: linear-gradient(135deg, rgba(255, 68, 68, 0.1) 0%, rgba(255, 68, 68, 0.05) 100%);
  border-color: rgba(255, 68, 68, 0.3);
  color: #ff4444;
}

.remove-btn:hover {
  background: linear-gradient(135deg, rgba(255, 68, 68, 0.2) 0%, rgba(255, 68, 68, 0.1) 100%);
  border-color: rgba(255, 68, 68, 0.5);
  transform: translateY(-0.1vh);
  box-shadow: 0 0.4vh 2vh rgba(255, 68, 68, 0.3);
}

/* 底部操作区域 */
.footer-actions {
  padding: 1.5vh 2.5vh;
  border-top: 0.1vh solid var(--border-color);
  background: var(--card-bg-light);
  flex-shrink: 0;
}

.close-button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.8vh;
  width: 100%;
  padding: 1.2vh 2vh;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
  border: 0.1vh solid var(--border-color);
  border-radius: 1vh;
  color: var(--text-secondary);
  font-size: 1.4vh;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: var(--shadow-sm);
}

.close-button:hover {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.15) 0%, rgba(255, 255, 255, 0.1) 100%);
  border-color: var(--border-hover);
  transform: translateY(-0.1vh);
  box-shadow: var(--shadow-primary);
}

/* 滚动条样式 */
.content-area::-webkit-scrollbar,
.vehicles-list::-webkit-scrollbar {
  width: 0.8vh;
}

.content-area::-webkit-scrollbar-track,
.vehicles-list::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 0.4vh;
  margin: 0.4vh 0;
}

.content-area::-webkit-scrollbar-thumb,
.vehicles-list::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
  border-radius: 0.4vh;
  transition: background 0.3s ease;
}

.content-area::-webkit-scrollbar-thumb:hover,
.vehicles-list::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, var(--primary-light) 0%, var(--primary-color) 100%);
  box-shadow: var(--shadow-primary);
}

@keyframes panelSlideIn {
  from {
    opacity: 0;
    transform: translate(-50%, -50%) scale(0.9);
  }
  to {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1);
  }
}

@keyframes formSlideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style> 