# 车库载具类型限制功能 - 数据库优化版本

## 功能概述

基于之前的实现，进行了重大性能优化：**将载具类型信息存储到数据库中**，避免每次都需要生成临时载具来查询类型，大幅提升性能和稳定性。

## 优化内容

### 🚀 性能优化

#### 之前的问题
- 每次验证载具类型都需要RPC调用客户端
- 跨车库调车需要创建临时载具，消耗资源
- 大量载具时性能下降明显

#### 现在的解决方案
- **首次检测后存储到数据库**：载具类型只需检测一次
- **优先从数据库读取**：避免重复RPC调用和临时载具创建
- **智能缓存策略**：数据库中有类型信息就直接使用，没有才检测

### 🔧 核心组件

#### 1. VehicleTypeManager (`server/vehicleTypeManager.ts`)

新增的载具类型管理器，提供以下功能：

```typescript
// 核心方法
VehicleTypeManager.detectAndStoreVehicleType(player, vehicle, vehicleId)  // 检测并存储类型
VehicleTypeManager.getVehicleTypeFromDB(vehicleId)                        // 从数据库获取类型
VehicleTypeManager.detectVehicleTypeByModel(player, model, vehicleId)     // 通过模型检测类型
VehicleTypeManager.canStoreVehicleType(vehicleType, allowedTypes)         // 类型匹配检查
```

#### 2. 数据库扩展字段

在Vehicle文档中扩展了`vehicleType`字段：
```typescript
// 扩展的Vehicle类型（插件级别）
Vehicle & { vehicleType?: GarageVehicleType }
```

### 📊 性能对比

| 操作场景 | 优化前 | 优化后 |
|---------|-------|-------|
| 取车验证 | RPC调用 + 载具实例检测 | 数据库查询（首次RPC+存储） |
| 存车验证 | RPC调用 + 载具实例检测 | 数据库查询（首次RPC+存储） |
| 跨车库调车 | 创建临时载具 + RPC调用 | 数据库查询（首次临时载具+存储） |
| 重复操作 | 每次都需要检测 | 直接读取数据库 |

### 🔄 工作流程

#### 首次载具操作
1. 检查数据库中是否有`vehicleType`字段
2. 如果没有，通过RPC/临时载具检测类型
3. **将检测结果存储到数据库**
4. 执行类型验证和操作

#### 后续载具操作
1. 直接从数据库读取`vehicleType`
2. 执行类型验证和操作
3. **无需任何RPC调用或临时载具**

## 实现细节

### 1. 智能类型检测

```typescript
// 存车时：使用现有载具实例检测
const vehicleType = await VehicleTypeManager.detectAndStoreVehicleType(player, vehicle, vehicleId);

// 跨车库调车时：通过模型检测（优先数据库）
const vehicleType = await VehicleTypeManager.detectVehicleTypeByModel(player, vehicle.model, vehicle._id);
```

### 2. 数据库操作优化

```typescript
// 智能查询：优先数据库，没有才检测
static async detectAndStoreVehicleType(player, vehicle, vehicleId) {
    // 1. 先尝试从数据库获取
    let vehicleType = await this.getVehicleTypeFromDB(vehicleId);
    
    if (vehicleType) {
        return vehicleType;  // 直接返回，无需检测
    }

    // 2. 数据库没有，才进行检测
    vehicleType = await player.emitRpc('garage:getVehicleGarageType', vehicle);
    
    // 3. 存储到数据库供下次使用
    await this.saveVehicleTypeToDB(vehicleId, vehicleType);
    
    return vehicleType;
}
```

### 3. 临时载具管理优化

```typescript
// 跨车库调车的优化版本
static async detectVehicleTypeByModel(player, vehicleModel, vehicleId) {
    // 优先尝试数据库
    if (vehicleId) {
        const vehicleType = await this.getVehicleTypeFromDB(vehicleId);
        if (vehicleType) {
            return vehicleType;  // 无需创建临时载具
        }
    }

    // 只有在数据库没有时才创建临时载具
    const tempVehicle = new alt.Vehicle(vehicleModel, ...);
    try {
        const vehicleType = await player.emitRpc('garage:getVehicleGarageType', tempVehicle);
        if (vehicleId) {
            await this.saveVehicleTypeToDB(vehicleId, vehicleType);
        }
        return vehicleType;
    } finally {
        tempVehicle.destroy();  // 确保清理
    }
}
```

## 迁移功能

### 管理员命令

为现有载具补充类型信息：
```
/migrate-vehicle-types
```

该命令会：
1. 查找所有没有`vehicleType`字段的载具
2. 分批处理（默认每批10个）
3. 为每个载具检测并存储类型信息
4. 避免服务器负载过高

### 分批处理策略

```typescript
// 分批迁移避免性能问题
static async migrateExistingVehicles(player, batchSize = 10) {
    const vehiclesWithoutType = await db.getMany({ vehicleType: { $exists: false } });
    
    for (let i = 0; i < vehiclesWithoutType.length; i += batchSize) {
        const batch = vehiclesWithoutType.slice(i, i + batchSize);
        // 处理当前批次
        await new Promise(resolve => setTimeout(resolve, 100)); // 防止过载
    }
}
```

## 安全性和稳定性

### 1. 向后兼容
- 所有新功能都包含错误处理
- 如果数据库操作失败，回退到原有检测方式
- 保留了所有原有的RPC接口

### 2. 数据一致性
- 载具类型一旦检测并存储，保持一致
- 支持手动重新检测和更新
- 数据库字段为可选，不影响现有载具

### 3. 内存管理
- 临时载具确保正确销毁
- 数据库连接正确管理
- 批处理避免内存溢出

## 使用指南

### 新服务器部署
1. 直接使用优化版本
2. 载具类型会在首次操作时自动检测和存储

### 现有服务器升级
1. 部署新代码
2. 运行 `/migrate-vehicle-types` 命令
3. 等待迁移完成
4. 享受性能提升

### 性能监控
- 关注数据库查询性能
- 监控临时载具创建频率（应该很低）
- 检查迁移进度和完成状态

## 预期效果

### 🚀 性能提升
- **90%+ 操作无需RPC调用**
- **100%减少重复临时载具创建**
- **数据库查询性能远超RPC+载具检测**

### 🔧 维护性
- **一次检测，永久存储**
- **清晰的类型管理接口**
- **完整的迁移和管理工具**

### 🛡️ 稳定性
- **减少客户端-服务端通信**
- **降低载具实例相关错误**
- **提高大量载具场景的稳定性**

这个优化版本在保持所有原有功能的基础上，大幅提升了性能和稳定性，特别适合有大量载具的生产环境。