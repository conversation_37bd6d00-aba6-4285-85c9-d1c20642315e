export const addvehicles = [
    '03sierra',
    '17magotan',
    '17turbo',
    '18s560',
    '190e',
    '19yukongpe',
    '2021jettasel',
    '2022siennaxse',
    '20gladiator',
    '21durango',
    '21dura',
    '21bro',
    '22chevan',
    '22lx600f',
    '23hornet',
    'jeepranger',
    '23rngrxlt',
    '23santa',
    '23sprintfl',
    '23subur',
    '23teslapf',
    '23x5',
    '24hellecharger',
    '25rav4xsehybrid',
    '296gts',
    '307c',
    '337flatbed',
    '560sel87',
    '600sel',
    '650e',
    '745le',
    '77monaco',
    '88j60',
    'accord20',
    'adv1502023',
    'argiu',
    'aetron',
    'polswatstoc',
    'aierfa3',
    'amels200',
    'a45amg',
    'beetle',
    'bentayga17',
    'benzg55',
    'bmci',
    'bmwe38',
    '23i7M70',
    'bmwm2pak',
    'bmwm4v42021wb',
    'blfs05',
    'bronco_backwheel',
    'c300',
    'c300w206',
    'camry18',
    'cascadia2019',
    'cesc21',
    'cla250',
    'cla45sb',
    'cls63s',
    'corolla77',
    'corolla94',
    'ct624',
    'ct6v21',
    'ctsv16',
    'SNCyberTruck',
    'defender110',
    'dl_glr700',
    'c63s',
    'e400',
    'e63amg',
    'e63sf',
    'eldorado57',
    'exige12',
    'Pitbullrb',
    'fj2022',
    'fortwo17',
    'gc10',
    'gcm206sw',
    'gcma4sw2021',
    'gcmct4v2023',
    'gcmex90',
    'naglfar',
    'gonzo_a6tron',
    'grapefpiu',
    'gs350',
    'polgs350',
    'cullinan',
    'svr',
    'ngt17',
    'nissanr34',
    'gxa90',
    'hevo',
    'hiace',
    'hiacesc',
    'GODzHUMMEREV',
    'ss3rb',
    'idbuzz25',
    'ikx3cross22',
    'ikx3dbx707',
    'ikx3f15022',
    'ikx3gt3rs23',
    'ikx3mgxp21',
    'ikx3nsx22',
    'ikx3sangue23',
    'ikx3_r823',
    'ikx3_svj19',
    'ikx3_x323',
    'impala59',
    'jeep20',
    'legacy18taurus',
    'legacy16exp',
    'legacy18f150',
    'defender',
    'jieda',
    'jplbx21',
    'l7',
    'legacy2',
    'legacy10',
    'legacy9',
    'legacy7',
    'legacy4',
    'legacy13',
    'legacy1',
    'legacy15',
    'legacy14',
    'legacy21',
    'legacy19',
    'legacy16',
    'legacy17',
    'lexuslm',
    'lp780r',
    'ls500',
    'ltd85',
    'm5e34',
    'm5g60',
    'maybach62s',
    'mbhome',
    'w222s',
    'mclaev25',
    'mclart',
    'megrs09',
    'mevo9t',
    'minif150',
    'model324',
    'moxm23',
    'mulef',
    'mw1',
    'mz3c',
    'navigator',
    'ndd530',
    'nmule',
    'nznismo24',
    'ocni422spe',
    'okjltv20',
    'oycm3',
    'pcs20',
    'pcx160_2025',
    'peanut',
    'phantomlimo',
    'pika01',
    'prado18',
    'qx56',
    'r2525',
    'r32025ld',
    'rangerover2',
    'redeye',
    'rev_autentica24',
    'rr12',
    'rs6',
    'rs6c8',
    'rx7tunable',
    's1000rr',
    's65w222',
    's680pg',
    's8d4',
    '5500wrecker',
    'nsandstormmk',
    'seq23',
    'she6',
    'sjbenzml',
    'sjdodge',
    'sr650fly',
    'stelvioqv',
    'dpcsu7max',
    'svt00',
    'swatvanr2',
    't2000',
    'taco23',
    'trans_teslams',
    'taycan',
    'toyotasj',
    'tundra22',
    'tw_ar3sb',
    'tw_b330i22',
    'tw_dcsh18',
    'v60sp',
    'v250',
    '600sel',
    'ae86',
    'amggt',
    'amggt63s',
    'bmwe39',
    'z28',
    'camry70',
    'cayenne2018',
    '69charger',
    'gls600',
    'gcmgolf8r',
    'challenger',
    'i8',
    'modelx',
    'q72020',
    '718bs',
    '720s',
    '918s',
    'bmwx7',
    'camry2',
    'cayenne2',
    'e63s',
    'etron',
    'evo10',
    'f150',
    'fxxk',
    'g63',
    'ghost',
    'gt63s',
    'gtr50',
    'imola',
    'jgc',
    'models',
    'mustang2',
    'octavia18',
    'q8',
    'ram',
    'rs72',
    'urus',
    'venomscat',
    'vwgti23',
    'w223s500',
    'wulhong',
    'x3evija20',
    'x3ramtrx21',
    'yaris08',
    'yaris99',
    'sar250',
    'EMSf550ambo',
    'yzfr6',
    'zeekr009',
]