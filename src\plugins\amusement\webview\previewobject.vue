<template>
    <div class="preview-object-container" v-show="isVisible">
        <div class="preview-panel">
            <div class="preview-header">
                <h2>对象预览</h2>
            </div>

            <div class="model-selection card-section">
                <h3>选择模型</h3>
                <div class="model-list">
                    <div v-for="(model, index) in modelList" :key="index" class="model-item"
                        :class="{ 'selected': selectedModel === model.name }" @click="selectModel(model.name)">
                        <span>{{ model.displayName }}</span>
                    </div>
                </div>
            </div>

            <div class="position-controls card-section">
                <h3>位置控制</h3>
                <div class="control-group">
                    <div class="slider-control">
                        <label>X 坐标</label>
                        <input type="number" v-model.number="position.x" step="0.01" @change="updateObject">
                    </div>

                    <div class="slider-control">
                        <label>Y 坐标</label>
                        <input type="number" v-model.number="position.y" step="0.01" @change="updateObject">
                    </div>

                    <div class="slider-control">
                        <label>Z 坐标</label>
                        <input type="number" v-model.number="position.z" step="0.01" @change="updateObject">
                    </div>
                </div>
            </div>

            <div class="rotation-controls card-section">
                <h3>旋转控制</h3>
                <div class="control-group">
                    <div class="slider-control">
                        <label>X 旋转</label>
                        <input type="number" v-model.number="rotation.x" step="0.01" @change="updateObject">
                    </div>

                    <div class="slider-control">
                        <label>Y 旋转</label>
                        <input type="number" v-model.number="rotation.y" step="0.01" @change="updateObject">
                    </div>

                    <div class="slider-control">
                        <label>Z 旋转</label>
                        <input type="number" v-model.number="rotation.z" step="0.01" @change="updateObject">
                    </div>
                </div>
            </div>

             <h3>物品信息</h3>
             <div class="item-info">
                <div class="item-name">
                    <label>物品名称</label>
                    <span>{{ Objectname }}</span>

                    <label>pos</label>
                    <span>{{ Objectposition }}</span>

                    <label>rot</label>
                    <span>{{ Objectrotation }}</span>
                    
                </div>
                
             </div>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted } from 'vue';
import { useEvents } from '@Composables/useEvents';

const events = useEvents();
const isVisible = ref(false);

const Objectname = ref('');
const Objectposition = reactive({ x: 0, y: 0, z: 0 });
const Objectrotation = reactive({ x: 0, y: 0, z: 0 });

// 模型列表
const modelList = ref([
    { name: 'prop_vend_soda_01', displayName: '自动售货机' },
    { name: 'prop_bench_01a', displayName: '长椅' },
    { name: 'prop_bin_07a', displayName: '垃圾桶' },
    { name: 'prop_atm_01', displayName: 'ATM机' },
    { name: 'prop_plant_int_01a', displayName: '室内植物' },
    { name: 'prop_ferris_car_01', displayName: '摩天轮仓位' },
]);

// 选中的模型
const selectedModel = ref('');

// 位置和旋转控制
const position = reactive({ x: 0, y: 0, z: 0 });
const rotation = reactive({ x: 0, y: 0, z: 0 });

// 选择模型
function selectModel(modelName: string) {
    selectedModel.value = modelName;
    events.emitClient('previewobject', modelName);
}

// 更新对象位置和旋转
function updateObject() {
    if (!selectedModel.value) return;
    events.emitClient('changepreviewobjectposandrot', position, rotation);
}

events.on('previewobject:show', () => {
    isVisible.value = true;
    events.emitClient('previewobject:state', true)
});

events.on('previewobject:hide', () => {
    isVisible.value = false;
    events.emitClient('previewobject:state', false)
});

events.on('previewobject:infoupdate', (modelname: string, pos: {x: number, y: number, z: number}, rot: {x: number, y: number, z: number}) => {
    Objectname.value = modelname;
    position.x = pos.x;
    position.y = pos.y;
    position.z = pos.z;
    rotation.x = rot.x;
    rotation.y = rot.y;
    rotation.z = rot.z;

    Objectposition.x = pos.x;
    Objectposition.y = pos.y;
    Objectposition.z = pos.z;
    Objectrotation.x = rot.x;
    Objectrotation.y = rot.y;
    Objectrotation.z = rot.z;
});
</script>

<style scoped>
.preview-object-container {
    position: fixed;
    bottom: 20px;
    right: 20px;
    display: flex;
    justify-content: flex-end;
    align-items: flex-end;
    z-index: 1000;
}

.preview-panel {
    width: 320px;
    max-height: 80vh;
    background-color: rgba(23, 33, 43, 0.95);
    border-radius: 12px;
    border: 1px solid rgba(135, 116, 225, 0.3);
    padding: 20px;
    overflow-y: auto;
    color: #fff;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
    transition: all 0.3s ease;
}

.card-section {
    background-color: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 15px;
}

.preview-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.preview-header h2 {
    margin: 0;
    font-size: 1.5rem;
    font-weight: 500;
    background: linear-gradient(135deg, #8774e1, #a389ff);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

.close-btn {
    background: none;
    border: none;
    font-size: 1.5rem;
    color: #aaa;
    cursor: pointer;
    transition: color 0.2s;
}

.close-btn:hover {
    color: #ff5252;
}

h3 {
    font-size: 1.1rem;
    margin: 0 0 15px;
    padding-bottom: 8px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    color: rgba(255, 255, 255, 0.9);
}

.model-list {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 10px;
    margin-bottom: 10px;
}

.model-item {
    background-color: rgba(40, 40, 40, 0.5);
    border-radius: 8px;
    padding: 10px;
    text-align: center;
    cursor: pointer;
    transition: all 0.2s;
    font-size: 0.9rem;
    border: 1px solid transparent;
}

.model-item:hover {
    background-color: rgba(60, 60, 60, 0.7);
    transform: translateY(-2px);
}

.model-item.selected {
    background-color: rgba(135, 116, 225, 0.2);
    border: 1px solid rgba(135, 116, 225, 0.6);
    box-shadow: 0 0 10px rgba(135, 116, 225, 0.3);
}

.control-group {
    margin-bottom: 10px;
}

.slider-control {
    display: flex;
    align-items: center;
    margin-bottom: 12px;
}

.slider-control label {
    width: 70px;
    font-size: 0.9rem;
    color: rgba(255, 255, 255, 0.8);
}

.slider-control input[type="number"] {
    width: 100px;
    background-color: rgba(40, 40, 40, 0.7);
    border: 1px solid rgba(135, 116, 225, 0.3);
    color: #fff;
    padding: 5px;
    border-radius: 6px;
    font-size: 0.9rem;
    transition: border 0.2s;
}

.slider-control input[type="number"]:focus {
    border: 1px solid #8774e1;
    outline: none;
}

.action-buttons {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    margin-top: 20px;
}

.action-buttons button {
    padding: 8px 18px;
    border-radius: 8px;
    border: none;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;
}

.reset-btn {
    background-color: rgba(100, 100, 100, 0.4);
    color: #fff;
}

.confirm-btn {
    background-color: #8774e1;
    color: #fff;
}

.reset-btn:hover {
    background-color: rgba(120, 120, 120, 0.6);
    transform: translateY(-2px);
}

.confirm-btn:hover {
    background-color: #9885f2;
    transform: translateY(-2px);
    box-shadow: 0 4px 10px rgba(135, 116, 225, 0.3);
}
</style>
