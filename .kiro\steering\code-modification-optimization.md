# 代码修改优化规则

在进行代码开发和修改时，必须遵循以下优化原则，提高开发效率。

## 核心原则

### 修改优先于重写
- [ ] 优先使用 `strReplace` 进行局部修改，而不是 `fsWrite` 重写整个文件
- [ ] 对于大文件，只修改需要变更的部分
- [ ] 保持现有代码结构，只改变必要的内容
- [ ] 避免不必要的全文件重新生成

### 查询优先于假设
- [ ] 使用 `readFile` 或 `grepSearch` 了解现有代码结构
- [ ] 在修改前先查看相关文件内容
- [ ] 确认现有实现方式后再进行修改
- [ ] 利用 `fileSearch` 快速定位相关文件

### 禁止测试执行
- [ ] 不执行任何测试命令（npm test、jest、vitest 等）
- [ ] 不运行构建命令进行验证
- [ ] 不执行代码检查工具
- [ ] 专注于代码编写，跳过验证步骤

## 操作策略

### 文件修改流程
1. **先读取**：使用 `readFile` 了解当前文件内容
2. **定位修改点**：确定需要修改的具体位置
3. **精确替换**：使用 `strReplace` 进行最小化修改
4. **避免重写**：除非必要，不使用 `fsWrite` 重新创建文件

### 多文件操作
- 使用 `readMultipleFiles` 批量读取相关文件
- 使用 `grepSearch` 快速查找跨文件的相关代码
- 并行执行多个 `strReplace` 操作
- 避免逐个文件的重复操作

### 代码查询技巧
```
优先使用的查询方式：
1. grepSearch - 快速搜索代码模式
2. fileSearch - 根据文件名模糊查找
3. readFile - 读取具体文件内容
4. listDirectory - 了解项目结构
```

## 效率优化

### 时间节省策略
- **跳过验证**：不进行代码运行测试
- **批量操作**：一次性处理多个相似修改
- **精确定位**：使用搜索工具快速找到目标代码
- **最小修改**：只改变必要的代码行

### 避免的低效操作
- ❌ 重新生成整个文件
- ❌ 执行测试命令验证
- ❌ 运行构建工具检查
- ❌ 逐行重写现有代码
- ❌ 不必要的文件创建

### 推荐的高效操作
- ✅ 使用 strReplace 精确修改
- ✅ 批量读取相关文件
- ✅ 并行执行多个修改操作
- ✅ 利用搜索工具快速定位
- ✅ 保持现有代码结构

## 特殊环境适配

### 环境限制处理
- 不依赖外部工具验证代码正确性
- 不执行可能影响环境的命令
- 专注于代码逻辑实现
- 信任用户的环境配置

### 开发流程简化
1. **理解需求** → 2. **查询现有代码** → 3. **精确修改** → 4. **完成交付**
   
   跳过：测试、验证、构建、检查等步骤

## 实施要求

- **强制执行**：每次代码操作都必须遵循这些规则
- **效率优先**：速度比完美更重要
- **用户导向**：以用户的特殊环境需求为准
- **结果导向**：专注于代码实现，不关注运行结果

这个规则确保在特殊环境下的高效代码开发，避免不必要的时间消耗。