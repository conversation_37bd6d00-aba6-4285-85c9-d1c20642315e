<template>
  <div class="racetrack-overlay">
    <div class="racetrack-container">
      <!-- 装饰性霓虹灯效果 -->
      <div class="neon-border-top"></div>
      
      <!-- 顶部信息 -->
      <div class="header-section">
        <div class="track-logo">
          <div class="logo-icons">🏇🐎🏆</div>
          <div class="track-info">
            <span class="track-name">皇家赛马场 · Derby</span>
            <span class="track-details">
              <span class="detail-icon">🏁</span>
              1英里土质赛道 
            </span>
          </div>
        </div>
        <button @click="closeGame" class="exit-btn">
          <span class="exit-icon">🚪</span>
          <span>离开赛场</span>
        </button>
      </div>

      <!-- 游戏主区域 -->
      <div class="game-area">
        <!-- 赛道区域 -->
        <div class="track-area">
          <div class="race-header">
            <div class="race-badge">
              <span class="badge-icon">🏆</span>
  
            </div>
            <div class="race-status">
              <div class="status-light" :class="{ 'racing': gameState.isRacing, 'betting': !gameState.isRacing }"></div>
              <span class="status-text">{{ gameState.isRacing ? '比赛进行中' : '投注开放' }}</span>
              <button @click="toggleAudio" class="audio-toggle" :class="{ 'enabled': audioEnabled }">
                {{ audioEnabled ? '🔊' : '🔇' }}
              </button>
            </div>
          </div>
          
          <div class="track-surface">
            <!-- 实时解说区域 -->
            <div v-if="currentCommentary && gameState.isRacing" class="live-commentary">
              <div class="commentary-icon">📢</div>
              <div class="commentary-text">{{ currentCommentary }}</div>
            </div>
            
            <!-- 赛道标记 -->
            <div class="track-markers">
              <div class="marker start">🚩 起跑线</div>
              <div class="marker quarter">¼</div>
              <div class="marker half">½</div>
              <div class="marker three-quarter">¾</div>
              <div class="marker finish">🏁 终点</div>
            </div>
            
            <!-- 马匹赛道 -->
            <div class="racing-lanes">
              <div v-for="horse in horses" :key="horse.id" class="racing-lane">
                <div class="lane-info">
                  <div class="lane-number">{{ horse.id }}</div>
                  <div class="horse-details">
                    <div class="horse-name">
                      {{ horse.name }}
                    </div>
                    <div class="jockey-info">{{ horse.jockey }}</div>
                  </div>
                </div>
                <div class="race-track">
                  <div class="horse-runner" :style="{ left: horse.position + '%' }">
                    <div class="jockey-silk" :style="{ backgroundColor: horse.silkColors }"></div>
                    <div class="horse-icon">🏇</div>
                  </div>
                  <div class="finish-line"></div>
                </div>
                <div class="odds-display">{{ horse.odds }}倍</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 侧边信息区 -->
      <div class="info-section">
        <!-- 账户信息 -->
        <div class="account-status">
          <div class="status-header">
            <span class="header-icon">💰</span>
            <span>账户余额</span>
          </div>
          <div class="balance-info">
            <div class="balance-amount">¥{{ playerChips.toLocaleString() }}</div>
            <div class="balance-label">可用筹码</div>
          </div>
        </div>

        <!-- 投注单区域 -->
        <div v-if="selectedHorse && !gameState.isRacing" class="betting-slip-area">
          <div class="slip-header">
            <span class="header-icon">🎫</span>
            <span>投注单</span>
          </div>
          
          <div class="selected-horse-info">
            <div class="horse-badge">
              <div class="horse-number">{{ selectedHorse }}</div>
              <div class="silk-color" :style="{ backgroundColor: getHorseSilk(selectedHorse) }"></div>
            </div>
            <div class="horse-details">
              <div class="horse-name">{{ getHorseName(selectedHorse) }}</div>
              <div class="horse-odds">{{ getHorseOdds(selectedHorse) }}倍赔率</div>
            </div>
          </div>
          
          <div class="amount-selection">
            <div class="quick-amounts">
              <button v-for="amount in [100, 500, 1000, 5000]" :key="amount"
                      @click="setBetAmount(amount)"
                      class="amount-btn"
                      :class="{ active: betAmount === amount }">
                {{ amount >= 1000 ? amount/1000 + 'K' : amount }}
              </button>
            </div>
            <div class="custom-amount">
              <span class="currency">¥</span>
              <input v-model.number="betAmount" 
                     type="number" 
                     min="50" 
                     max="50000" 
                     class="amount-input"
                     placeholder="自定义金额">
            </div>
          </div>
          
          <div class="bet-summary">
            <div class="summary-row">
              <span>投注金额</span>
              <span>¥{{ betAmount.toLocaleString() }}</span>
            </div>
            <div class="summary-row payout">
              <span>预期赔付</span>
              <span>¥{{ Math.floor(betAmount * getHorseOdds(selectedHorse)).toLocaleString() }}</span>
            </div>
          </div>
          
          <button @click="placeBet" class="place-bet-btn" :disabled="!canPlaceBet">
            <span class="btn-icon">🎫</span>
            <span>确认下注</span>
          </button>
        </div>

        <!-- 未选择马匹提示 -->
        <div v-else-if="!gameState.isRacing" class="no-selection-tip">
          <div class="tip-header">
            <span class="tip-icon">👇</span>
            <span>请在下方选择马匹</span>
          </div>
          <div class="tip-content">
            选择您看好的马匹<br>查看详细投注信息
          </div>
        </div>

        <!-- 比赛进行中状态 -->
        <div v-else-if="gameState.isRacing" class="race-progress-vertical">
          <div class="progress-header">
            <span class="progress-icon">🏁</span>
            <span>比赛进行中</span>
          </div>
          <div class="countdown-display">
            <span>剩余 {{ raceCountdown }} 秒</span>
          </div>
          <div v-if="currentBet" class="current-bet">
            <div class="bet-ticket">
              <div class="ticket-info">
                <span>您的投注</span>
                <span>#{{ currentBet.horse }} {{ getHorseName(currentBet.horse) }}</span>
              </div>
              <div class="ticket-amount">¥{{ currentBet.amount.toLocaleString() }}</div>
            </div>
          </div>
        </div>

        <!-- 比赛结果 -->
        <div v-else-if="gameState.result" class="race-results-vertical">
          <div class="result-header">
            <span class="result-icon">🏆</span>
            <span>比赛结果</span>
          </div>
          
          <!-- 正常比赛结果 -->
          <div class="winner-display-vertical">
            <div class="winner-info">
              <div class="winner-badge">
                <span class="winner-number">#{{ gameState.result.winner }}</span>
                <span class="winner-title">冠军</span>
              </div>
              <div class="winner-name">{{ getHorseName(gameState.result.winner) }}</div>
            </div>
          </div>
          
          <div v-if="currentBet" class="payout-result">
            <div v-if="currentBet.horse === gameState.result.winner" class="win-result">
              <div class="result-status win">🎉 恭喜中奖！</div>
              <div class="payout-amount">+¥{{ gameState.result.winAmount.toLocaleString() }}</div>
            </div>
            <div v-else class="lose-result">
              <div class="result-status lose">很遗憾未中奖</div>
              <div class="loss-amount">-¥{{ currentBet.amount.toLocaleString() }}</div>
            </div>
          </div>
          
          <button @click="startNewGame" class="new-race-btn">
            <span class="btn-icon">🔄</span>
            <span>下一场比赛</span>
          </button>
        </div>
      </div>

      <!-- 控制区域 -->
      <div class="control-section">
        <!-- 投注阶段 -->
        <div v-if="!gameState.isRacing" class="betting-controls">
          <div class="betting-title">
            <span class="title-icon">🎯</span>
            <span>选择投注马匹</span>
          </div>
          
          <div class="horse-selection">
            <div v-for="horse in horses" :key="horse.id" 
                 class="horse-option"
                 :class="{ active: selectedHorse === horse.id }"
                 @click="selectHorse(horse.id)">
              <div class="horse-badge">
                <div class="horse-number">{{ horse.id }}</div>
                <div class="silk-color" :style="{ backgroundColor: horse.silkColors }"></div>
              </div>
              <div class="horse-info">
                <div class="horse-name">
                  {{ horse.name }}
                </div>
                <div class="jockey-name">{{ horse.jockey }}</div>
              </div>
              <div class="odds-badge">{{ horse.odds }}倍</div>
            </div>
          </div>
        </div>

        <!-- 比赛进行中提示 -->
        <div v-else-if="gameState.isRacing" class="race-status-bar">
          <div class="status-info">
            <span class="status-icon">🏁</span>
            <span>比赛进行中，剩余 {{ raceCountdown }} 秒</span>
          </div>
          <div v-if="currentBet" class="current-bet-info">
            <span>您的投注：#{{ currentBet.horse }} {{ getHorseName(currentBet.horse) }} ¥{{ currentBet.amount.toLocaleString() }}</span>
          </div>
        </div>

        <!-- 比赛结果显示 -->
        <div v-else-if="gameState.result" class="result-bar">
          <div class="result-info">
            <span class="result-icon">🏆</span>
            <span>冠军：#{{ gameState.result.winner }} {{ getHorseName(gameState.result.winner) }}</span>
          </div>
          <button @click="startNewGame" class="new-race-btn-compact">
            <span class="btn-icon">🔄</span>
            <span>下一场比赛</span>
          </button>
        </div>
      </div>

      <!-- 底部装饰 -->
      <div class="neon-border-bottom"></div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue';
import { useEvents } from '@Composables/useEvents.js';

const events = useEvents();

// 游戏状态
const gameState = ref({
  isRacing: false,
  result: null as any
});

// 赛马数据
const horses = ref([
  { id: 1, name: '雷霆闪电', odds: 2.5, position: 0, description: '速度型选手，短距离冲刺专家', silkColors: '#FF6B35', jockey: '罗德里格斯', weight: '118磅' },
  { id: 2, name: '烈焰之火', odds: 3.2, position: 0, description: '强力终结者，后程发力强劲', silkColors: '#DC143C', jockey: '史密斯', weight: '116磅' },
  { id: 3, name: '疾风骏马', odds: 4.1, position: 0, description: '稳定表现者，经验丰富的老将', silkColors: '#4169E1', jockey: '加西亚', weight: '120磅' },
  { id: 4, name: '风暴追逐者', odds: 5.5, position: 0, description: '黑马候选，巨大潜力股', silkColors: '#32CD32', jockey: '约翰逊', weight: '114磅' },
  { id: 5, name: '北极风暴', odds: 6.8, position: 0, description: '距离专家，耐力优势明显', silkColors: '#9370DB', jockey: '威廉姆斯', weight: '122磅' }
]);

// 玩家状态
const playerChips = ref(10000);
const selectedHorse = ref(null as number | null);
const betAmount = ref(100);
const currentBet = ref(null as any);
const raceCountdown = ref(0);

// 音效和解说
const audioEnabled = ref(true);
const currentCommentary = ref('');
const commentaryHistory = ref([]);
const audioSources = ref({
  hooves: null as HTMLAudioElement | null,
  gun: null as HTMLAudioElement | null,
  passenger: null as HTMLAudioElement | null
});

// 马蹄声控制
const hoovesAudio = ref(null as HTMLAudioElement | null);
const hoovesInterval = ref(null as NodeJS.Timeout | null);

// Web Audio API 音效生成器
let audioContext: AudioContext | null = null;

// 背景音乐音频对象
const backgroundMusic = ref(null as HTMLAudioElement | null);

// 初始化 Web Audio Context
const initWebAudio = () => {
  try {
    audioContext = new (window.AudioContext || (window as any).webkitAudioContext)();
  } catch (error) {
    console.warn('Web Audio API not supported:', error);
  }
};

// 生成音效的工具函数
const generateSound = {
  // 生成按键点击音效
  click: (duration: number = 0.1) => {
    if (!audioContext) return;
    
    const oscillator = audioContext.createOscillator();
    const gainNode = audioContext.createGain();
    
    oscillator.connect(gainNode);
    gainNode.connect(audioContext.destination);
    
    // 清脆的点击声
    oscillator.frequency.setValueAtTime(800, audioContext.currentTime);
    oscillator.frequency.exponentialRampToValueAtTime(400, audioContext.currentTime + duration);
    
    // 快速衰减
    gainNode.gain.setValueAtTime(0, audioContext.currentTime);
    gainNode.gain.linearRampToValueAtTime(0.2, audioContext.currentTime + 0.01);
    gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + duration);
    
    oscillator.type = 'square';
    oscillator.start(audioContext.currentTime);
    oscillator.stop(audioContext.currentTime + duration);
  },
  
  // 生成下注确认音效
  bet: (duration: number = 0.3) => {
    if (!audioContext) return;
    
    // 双音调确认声
    for (let i = 0; i < 2; i++) {
      const delay = i * 0.1;
      const oscillator = audioContext.createOscillator();
      const gainNode = audioContext.createGain();
      
      oscillator.connect(gainNode);
      gainNode.connect(audioContext.destination);
      
      oscillator.frequency.value = i === 0 ? 600 : 900;
      oscillator.type = 'sine';
      
      gainNode.gain.setValueAtTime(0, audioContext.currentTime + delay);
      gainNode.gain.linearRampToValueAtTime(0.25, audioContext.currentTime + delay + 0.05);
      gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + delay + 0.2);
      
      oscillator.start(audioContext.currentTime + delay);
      oscillator.stop(audioContext.currentTime + delay + 0.2);
    }
  },
  
  // 生成胜利音效
  win: (duration: number = 1.2) => {
    if (!audioContext) return;
    
    // 上升音阶庆祝声
    const notes = [261.63, 329.63, 392.00, 523.25]; // C-E-G-C
    
    notes.forEach((freq, index) => {
      const delay = index * 0.15;
      const oscillator = audioContext.createOscillator();
      const gainNode = audioContext.createGain();
      
      oscillator.connect(gainNode);
      gainNode.connect(audioContext.destination);
      
      oscillator.frequency.value = freq;
      oscillator.type = 'triangle';
      
      gainNode.gain.setValueAtTime(0, audioContext.currentTime + delay);
      gainNode.gain.linearRampToValueAtTime(0.3, audioContext.currentTime + delay + 0.05);
      gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + delay + 0.4);
      
      oscillator.start(audioContext.currentTime + delay);
      oscillator.stop(audioContext.currentTime + delay + 0.4);
    });
    
    // 添加闪烁效果音
    setTimeout(() => {
      for (let i = 0; i < 8; i++) {
        const delay = i * 0.05;
        const oscillator = audioContext.createOscillator();
        const gainNode = audioContext.createGain();
        
        oscillator.connect(gainNode);
        gainNode.connect(audioContext.destination);
        
        oscillator.frequency.value = 1000 + Math.random() * 500;
        oscillator.type = 'sine';
        
        gainNode.gain.setValueAtTime(0, audioContext.currentTime + delay);
        gainNode.gain.linearRampToValueAtTime(0.15, audioContext.currentTime + delay + 0.01);
        gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + delay + 0.08);
        
        oscillator.start(audioContext.currentTime + delay);
        oscillator.stop(audioContext.currentTime + delay + 0.08);
      }
    }, 600);
  },
  
  // 生成失败音效
  lose: (duration: number = 0.8) => {
    if (!audioContext) return;
    
    // 下降音调失败声
    const oscillator = audioContext.createOscillator();
    const gainNode = audioContext.createGain();
    
    oscillator.connect(gainNode);
    gainNode.connect(audioContext.destination);
    
    // 从高音下降到低音
    oscillator.frequency.setValueAtTime(400, audioContext.currentTime);
    oscillator.frequency.exponentialRampToValueAtTime(150, audioContext.currentTime + duration);
    
    // 音量包络
    gainNode.gain.setValueAtTime(0, audioContext.currentTime);
    gainNode.gain.linearRampToValueAtTime(0.2, audioContext.currentTime + 0.1);
    gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + duration);
    
    oscillator.type = 'sawtooth';
    oscillator.start(audioContext.currentTime);
    oscillator.stop(audioContext.currentTime + duration);
  },
  // 生成铃声/号角声 (赛马开始)
  raceStart: (duration: number = 0.8) => {
    if (!audioContext) return;
    
    const oscillator = audioContext.createOscillator();
    const gainNode = audioContext.createGain();
    
    oscillator.connect(gainNode);
    gainNode.connect(audioContext.destination);
    
    // 号角声频率变化
    oscillator.frequency.setValueAtTime(440, audioContext.currentTime);
    oscillator.frequency.exponentialRampToValueAtTime(660, audioContext.currentTime + duration * 0.3);
    oscillator.frequency.exponentialRampToValueAtTime(440, audioContext.currentTime + duration);
    
    // 音量包络
    gainNode.gain.setValueAtTime(0, audioContext.currentTime);
    gainNode.gain.linearRampToValueAtTime(0.3, audioContext.currentTime + 0.1);
    gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + duration);
    
    oscillator.type = 'triangle';
    oscillator.start(audioContext.currentTime);
    oscillator.stop(audioContext.currentTime + duration);
  },
  
  // 生成马蹄声 (轻量版)
  gallop: (duration: number = 0.3) => {
    if (!audioContext) return;
    
    // 创建噪声缓冲区
    const bufferSize = audioContext.sampleRate * duration;
    const buffer = audioContext.createBuffer(1, bufferSize, audioContext.sampleRate);
    const output = buffer.getChannelData(0);
    
    // 生成类似马蹄的节奏性噪声
    for (let i = 0; i < bufferSize; i++) {
      const t = i / audioContext.sampleRate;
      const envelope = Math.exp(-t * 8); // 快速衰减
      const noise = (Math.random() * 2 - 1) * envelope;
      const tick = Math.sin(t * 800) * envelope * 0.5; // 添加节奏感
      output[i] = (noise + tick) * 0.2;
    }
    
    const source = audioContext.createBufferSource();
    const gainNode = audioContext.createGain();
    
    source.buffer = buffer;
    source.connect(gainNode);
    gainNode.connect(audioContext.destination);
    gainNode.gain.value = 0.3;
    
    source.start(audioContext.currentTime);
  },
  
  // 生成终点铃声
  finish: (duration: number = 1.5) => {
    if (!audioContext) return;
    
    // 连续的铃声
    for (let i = 0; i < 3; i++) {
      const delay = i * 0.3;
      const oscillator = audioContext.createOscillator();
      const gainNode = audioContext.createGain();
      
      oscillator.connect(gainNode);
      gainNode.connect(audioContext.destination);
      
      oscillator.frequency.value = 880; // 高音铃声
      oscillator.type = 'sine';
      
      gainNode.gain.setValueAtTime(0, audioContext.currentTime + delay);
      gainNode.gain.linearRampToValueAtTime(0.4, audioContext.currentTime + delay + 0.05);
      gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + delay + 0.4);
      
      oscillator.start(audioContext.currentTime + delay);
      oscillator.stop(audioContext.currentTime + delay + 0.4);
    }
  },
  
  // 生成观众欢呼声 (噪声模拟)
  crowd: (duration: number = 2.0) => {
    if (!audioContext) return;
    
    const bufferSize = audioContext.sampleRate * duration;
    const buffer = audioContext.createBuffer(1, bufferSize, audioContext.sampleRate);
    const output = buffer.getChannelData(0);
    
    // 生成模拟欢呼的粉红噪声
    for (let i = 0; i < bufferSize; i++) {
      const t = i / audioContext.sampleRate;
      const envelope = Math.sin(t * Math.PI / duration) * 0.8 + 0.2; // 渐强渐弱
      const noise = (Math.random() * 2 - 1) * envelope;
      const rumble = Math.sin(t * 100) * 0.3 * envelope; // 低频隆隆声
      output[i] = (noise + rumble) * 0.15;
    }
    
    const source = audioContext.createBufferSource();
    const gainNode = audioContext.createGain();
    const filter = audioContext.createBiquadFilter();
    
    // 低通滤波器模拟远距离效果
    filter.type = 'lowpass';
    filter.frequency.value = 2000;
    
    source.buffer = buffer;
    source.connect(filter);
    filter.connect(gainNode);
    gainNode.connect(audioContext.destination);
    gainNode.gain.value = 0.4;
    
    source.start(audioContext.currentTime);
  }
};

// 解说词库
const commentaryTexts = {
  raceStart: [
    '比赛开始！各位骑师准备就绪！',
    '发令枪响！马匹们冲出起跑门！',
    '比赛正式开始，所有马匹齐头并进！'
  ],
  leading: [
    '号马匹目前领先！',
    '号马占据有利位置！',
    '号马冲到了前面！',
    '号马表现出色，暂时领先！'
  ],
  overtaking: [
    '号马超越了前方！',
    '号马发起冲刺！',
    '号马加速超车！',
    '号马展现强大爆发力！'
  ],
  final: [
    '进入最后冲刺阶段！',
    '马匹们正在全力以赴！',
    '距离终点线越来越近！',
    '激烈的最后争夺！'
  ],
  finish: [
    '号马率先冲过终点线，获得胜利！',
    '比赛结束！号马夺得冠军！',
    '精彩的比赛！号马成为赢家！'
  ]
};

// 计算属性
const canPlaceBet = computed(() => {
  return selectedHorse.value && betAmount.value >= 50 && betAmount.value <= playerChips.value && !gameState.value.isRacing;
});

// 方法
const selectHorse = (horseId: number) => {
  if (gameState.value.isRacing) return;
  selectedHorse.value = horseId;
  playSound('click'); // 播放选择音效
};

const setBetAmount = (amount: number) => {
  if (gameState.value.isRacing) return;
  betAmount.value = amount;
  playSound('click'); // 播放选择音效
};

const getHorseName = (horseId: number) => {
  const horse = horses.value.find(h => h.id === horseId);
  return horse ? horse.name : '';
};

const getHorseOdds = (horseId: number) => {
  const horse = horses.value.find(h => h.id === horseId);
  return horse ? horse.odds : 1;
};

const getHorseSilk = (horseId: number) => {
  const horse = horses.value.find(h => h.id === horseId);
  return horse ? horse.silkColors : '#8b4513';
};

// 背景音乐控制
const initBackgroundMusic = () => {
  try {
    backgroundMusic.value = new Audio('/sounds/jazzcasion.ogg');
    backgroundMusic.value.loop = true;
    backgroundMusic.value.volume = 0.3; // 背景音乐音量较低
    backgroundMusic.value.preload = 'auto';
  } catch (error) {
    console.warn('Background music initialization failed:', error);
  }
};

const startBackgroundMusic = () => {
  if (!audioEnabled.value || !backgroundMusic.value) return;
  
  try {
    backgroundMusic.value.play().catch(e => {
      console.warn('Background music play failed:', e);
    });
  } catch (error) {
    console.warn('Background music error:', error);
  }
};

const stopBackgroundMusic = () => {
  if (backgroundMusic.value) {
    try {
      backgroundMusic.value.pause();
      backgroundMusic.value.currentTime = 0;
    } catch (error) {
      console.warn('Stop background music error:', error);
    }
  }
};

// 音效管理方法
const initAudio = () => {
  // 初始化 Web Audio API
  initWebAudio();
  
  // 初始化背景音乐
  initBackgroundMusic();
  
  // 创建音频元素（只用于真实音频文件）
  const createAudio = (src: string, volume: number = 0.7) => {
    const audio = new Audio();
    audio.volume = volume;
    audio.preload = 'auto';
    if (src && src !== '') {
      audio.src = src;
    }
    return audio;
  };

  try {
    audioSources.value = {
      hooves: createAudio('/sounds/startinhorse.ogg', 0.5), // 真实音频
      gun: createAudio('/sounds/horsegun.ogg', 0.8), // 真实音频
      passenger: createAudio('/sounds/horsepassenger.ogg', 0.6) // 真实音频
    };
    
    // 初始化马蹄声音频
    hoovesAudio.value = createAudio('/sounds/startinhorse.ogg', 0.6);
    if (hoovesAudio.value) {
      hoovesAudio.value.loop = true;
    }
  } catch (error) {
    console.warn('Audio initialization failed:', error);
  }
};

const playSound = (soundType: keyof typeof audioSources.value | keyof typeof generateSound) => {
  if (!audioEnabled.value) return;
  
  // 对于生成的音效
  if (generateSound[soundType as keyof typeof generateSound]) {
    try {
      generateSound[soundType as keyof typeof generateSound]();
    } catch (error) {
      console.warn(`Web Audio generation failed for ${soundType}:`, error);
    }
    return;
  }
  
  // 对于真实音频文件
  const audio = audioSources.value[soundType as keyof typeof audioSources.value];
  if (audio && audio.src) {
    try {
      audio.currentTime = 0;
      audio.play().catch(e => {
        console.warn(`Audio play failed for ${soundType}:`, e);
      });
    } catch (error) {
      console.warn(`Audio error for ${soundType}:`, error);
    }
  }
};

// 开始马蹄声
const startHoovesSound = () => {
  if (!audioEnabled.value || !hoovesAudio.value) return;
  
  try {
    hoovesAudio.value.currentTime = 0;
    hoovesAudio.value.play().catch(e => {
      console.warn('Hooves sound play failed:', e);
    });
  } catch (error) {
    console.warn('Hooves sound error:', error);
  }
};

// 停止马蹄声
const stopHoovesSound = () => {
  if (hoovesAudio.value) {
    try {
      hoovesAudio.value.pause();
      hoovesAudio.value.currentTime = 0;
    } catch (error) {
      console.warn('Stop hooves sound error:', error);
    }
  }
};

const addCommentary = (text: string) => {
  currentCommentary.value = text;
  commentaryHistory.value.unshift({
    text,
    timestamp: Date.now()
  });
  
  // 保持解说历史记录最多10条
  if (commentaryHistory.value.length > 10) {
    commentaryHistory.value = commentaryHistory.value.slice(0, 10);
  }
  
  // 3秒后清除当前解说
  setTimeout(() => {
    if (currentCommentary.value === text) {
      currentCommentary.value = '';
    }
  }, 3000);
};

const getRandomCommentary = (type: keyof typeof commentaryTexts, horseNumber?: number) => {
  const texts = commentaryTexts[type];
  const randomText = texts[Math.floor(Math.random() * texts.length)];
  
  if (horseNumber && randomText.includes('号马')) {
    return randomText.replace('号马', `${horseNumber}号马`);
  }
  
  return randomText;
};

const analyzeRaceProgress = () => {
  if (!gameState.value.isRacing) return;
  
  const positions = horses.value.map((horse, index) => ({
    id: horse.id,
    position: horse.position,
    name: horse.name
  })).sort((a, b) => b.position - a.position);
  
  const leader = positions[0];
  
  // 随机解说不同的情况
  const rand = Math.random();
  
  if (leader.position > 80) {
    // 接近终点
    addCommentary(getRandomCommentary('final'));
  } else if (rand < 0.3) {
    // 领先解说
    addCommentary(getRandomCommentary('leading', leader.id));
  } else if (rand < 0.5) {
    // 超越解说
    const secondPlace = positions[1];
    if (secondPlace && leader.position - secondPlace.position < 5) {
      addCommentary(getRandomCommentary('overtaking', leader.id));
    }
  }
};

const toggleAudio = () => {
  audioEnabled.value = !audioEnabled.value;
  
  if (audioEnabled.value) {
    // 开启音效时开始播放背景音乐（如果没有比赛进行中）
    if (!gameState.value.isRacing) {
      startBackgroundMusic();
    }
  } else {
    // 关闭音效时停止背景音乐
    stopBackgroundMusic();
  }
};



const placeBet = () => {
  if (!canPlaceBet.value) return;
  
  const bet = {
    horse: selectedHorse.value,
    amount: betAmount.value
  };
  
  // 播放下注音效
  playSound('bet');
  
  // 发送下注请求到服务器
  events.emitServer('horse:placeBet', bet);
};

const startNewGame = () => {
  gameState.value.result = null;
  gameState.value.isRacing = false;
  currentBet.value = null;
  selectedHorse.value = null;
  raceCountdown.value = 0;
  resetHorsePositions();
  events.emitServer('horse:startNewGame');
};

const closeGame = () => {
  events.emitServer('horse:close');
};

const resetHorsePositions = () => {
  horses.value.forEach(horse => {
    horse.position = 0;
  });
};

// 立即重置马匹位置（用于比赛结束）
const immediateResetHorsePositions = () => {
  horses.value.forEach(horse => {
    horse.position = 0;
  });
};

// 事件监听
// 助手函数：获取马匹描述
const getHorseDescription = (horseId: number) => {
  const descriptions = {
    1: '速度型选手，短距离冲刺专家',
    2: '强力终结者，后程发力强劲',
    3: '稳定表现者，经验丰富的老将',
    4: '黑马候选，巨大潜力股',
    5: '距离专家，耐力优势明显'
  };
  return descriptions[horseId as keyof typeof descriptions] || '神秘马匹';
};

// 助手函数：获取骑手名字
const getHorseJockey = (horseId: number) => {
  const jockeys = {
    1: '罗德里格斯',
    2: '史密斯',
    3: '加西亚',
    4: '约翰逊',
    5: '威廉姆斯'
  };
  return jockeys[horseId as keyof typeof jockeys] || '神秘骑手';
};

// 助手函数：获取马匹体重
const getHorseWeight = (horseId: number) => {
  const weights = {
    1: '118磅',
    2: '116磅',
    3: '120磅',
    4: '114磅',
    5: '122磅'
  };
  return weights[horseId as keyof typeof weights] || '120磅';
};

// 助手函数：获取马匹颜色
const getHorseSilkColors = (horseId: number) => {
  const colors = {
    1: '#FF6B35',
    2: '#DC143C',
    3: '#4169E1',
    4: '#32CD32',
    5: '#9370DB'
  };
  return colors[horseId as keyof typeof colors] || '#8b4513';
};

onMounted(() => {
  // 初始化音频系统（包括Web Audio API）
  initAudio();
  
  // 开始播放背景音乐
  setTimeout(() => {
    if (audioEnabled.value && !gameState.value.isRacing) {
      startBackgroundMusic();
    }
  }, 1000);
  
  // 下注成功后的处理
  events.on('horse:betPlaced', (data: any) => {
    if (data.success) {
      currentBet.value = data.bet;
      playerChips.value = data.newChips;
      selectedHorse.value = null;
      betAmount.value = 100;
    }
  });
  
  // 比赛开始
  events.on('horse:raceStart', (data: any) => {
    gameState.value.isRacing = true;
    gameState.value.result = null;
    raceCountdown.value = 10;
    resetHorsePositions();
    
    // 停止背景音乐
    stopBackgroundMusic();
    
    // 播放发令枪音效
    playSound('gun');
    
    // 延迟播放其他音效
    setTimeout(() => {
      startHoovesSound(); // 开始马蹄声
      addCommentary(getRandomCommentary('raceStart'));
    }, 500);
    
    // 再延迟播放观众欢呼声
    setTimeout(() => {
      playSound('passenger');
    }, 1000);
    
    // 开始倒计时
    const countdownInterval = setInterval(() => {
      raceCountdown.value--;
      if (raceCountdown.value <= 0) {
        clearInterval(countdownInterval);
      }
    }, 1000);
    
    // 定期解说
    const commentaryInterval = setInterval(() => {
      if (gameState.value.isRacing) {
        analyzeRaceProgress();
      } else {
        clearInterval(commentaryInterval);
      }
    }, 3000);
  });
  
  // 接收马匹位置更新
  events.on('horse:positionUpdate', (positions: number[]) => {
    positions.forEach((position, index) => {
      if (horses.value[index]) {
        horses.value[index].position = Math.min(Math.max(position, 0), 100);
      }
    });
  });
  
  // 比赛结束
  events.on('horse:raceFinished', (result: any) => {
    gameState.value.isRacing = false;
    raceCountdown.value = 0;
    
    // 停止马蹄声
    stopHoovesSound();
    
    // 确保获胜马到达终点
    horses.value.forEach(horse => {
      if (horse.id === result.winner) {
        horse.position = 100;
      }
    });
    
    addCommentary(getRandomCommentary('finish', result.winner));
  
    
    // 延迟播放输赢音效
    setTimeout(() => {
      if (currentBet.value && currentBet.value.horse === result.winner) {
        playSound('win'); // 播放胜利音效
        addCommentary(`恭喜！您支持的${result.winner}号马获得胜利！`);
      } else if (currentBet.value) {
        playSound('lose'); // 播放失败音效
        addCommentary(`很遗憾，${result.winner}号马获胜，您的投注未中奖。`);
      }
    }, 1500);
    
    // 2秒后立即重置马匹位置到起点，并重新开始背景音乐
    setTimeout(() => {
      immediateResetHorsePositions();
      // 比赛结束后重新开始背景音乐
      if (audioEnabled.value) {
        startBackgroundMusic();
      }
    }, 2000);
    
    gameState.value.result = {
      winner: result.winner,
      winAmount: result.winAmount
    };
    
    playerChips.value = result.newChips;
  });
  
  // 接收新游戏数据（下一场比赛）
  events.on('horse:newGameData', (data: any) => {
    playerChips.value = data.chips;
    
    // 重置马匹位置
    horses.value.forEach(horse => {
      horse.position = 0;
    });
  });
  
  // 游戏数据初始化
  events.on('horse:gameData', (data: any) => {
    playerChips.value = data.chips;
    
    // 重置马匹位置
    horses.value.forEach(horse => {
      horse.position = 0;
    });
  });
});

onUnmounted(() => {
  // 清理音频资源
  stopHoovesSound();
  stopBackgroundMusic();
  
  if (hoovesInterval.value) {
    clearInterval(hoovesInterval.value);
  }
  
  // 清理所有音频对象
  Object.values(audioSources.value).forEach(audio => {
    if (audio) {
      audio.pause();
      audio.src = '';
    }
  });
  
  if (hoovesAudio.value) {
    hoovesAudio.value.pause();
    hoovesAudio.value.src = '';
  }
  
  if (backgroundMusic.value) {
    backgroundMusic.value.pause();
    backgroundMusic.value.src = '';
  }
});
</script>

<style scoped>
.racetrack-overlay {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  align-items: center;
  justify-content: center;
  font-family: 'Georgia', 'Times New Roman', serif;
  animation: slideIn 0.4s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: scale(0.95) translateY(-2vh);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

.racetrack-container {
  width: 80vw;
  height: 80vh;
  background: 
    radial-gradient(ellipse at center, #2d4a1a 0%, #1e3210 40%, #0f1a08 100%);
  border: 0.4vh solid #8b4513;
  border-radius: 1.5vh;
  box-shadow: 
    0 1.5vh 3vh rgba(0, 0, 0, 0.6),
    inset 0 0.2vh 0.5vh rgba(139, 69, 19, 0.3),
    0 0 8vh rgba(139, 69, 19, 0.15);
  display: grid;
  grid-template-columns: 2.8fr 1fr;
  grid-template-rows: 6vh 1fr 8vh;
  grid-template-areas: 
    "header header"
    "game info"
    "control control";
  gap: 0.8vh;
  padding: 1.5vh;
  color: white;
  position: relative;
  overflow: hidden;
}

/* 霓虹灯装饰边框 */
.neon-border-top,
.neon-border-bottom {
  position: absolute;
  left: 0;
  right: 0;
  height: 0.3vh;
  background: linear-gradient(90deg, 
    transparent 0%, 
    #8b4513 20%, 
    #daa520 50%, 
    #8b4513 80%, 
    transparent 100%);
  opacity: 0.8;
}

.neon-border-top {
  top: 0;
  box-shadow: 0 0.5vh 2vh rgba(139, 69, 19, 0.5);
}

.neon-border-bottom {
  bottom: 0;
  box-shadow: 0 -0.5vh 2vh rgba(139, 69, 19, 0.5);
}

/* 顶部信息栏 */
.header-section {
  grid-area: header;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.8vh 2vw;
  background: 
    linear-gradient(135deg, rgba(139, 69, 19, 0.4) 0%, rgba(160, 82, 45, 0.2) 100%),
    rgba(0, 0, 0, 0.4);
  border-radius: 0.8vh;
  border: 0.2vh solid rgba(139, 69, 19, 0.5);
  box-shadow: 
    inset 0 0.2vh 0.5vh rgba(139, 69, 19, 0.2),
    0 0.3vh 0.8vh rgba(0, 0, 0, 0.3);
}

.track-logo {
  display: flex;
  align-items: center;
  gap: 1.5vw;
}

.logo-icons {
  font-size: 3vh;
  color: #daa520;
  text-shadow: 
    0 0 1vh rgba(218, 165, 32, 0.8),
    0 0 2vh rgba(218, 165, 32, 0.4);
  letter-spacing: 0.3vw;
}

.track-info {
  display: flex;
  flex-direction: column;
  gap: 0.6vh;
}

.track-name {
  font-size: 2vh;
  font-weight: bold;
  color: #ffed4e;
  text-shadow: 
    0.1vh 0.1vh 0.3vh rgba(0, 0, 0, 0.8),
    0 0 1vh rgba(255, 237, 78, 0.5);
  letter-spacing: 0.1vw;
}

.track-details {
  font-size: 1.2vh;
  color: #f0e6d2;
  display: flex;
  align-items: center;
  gap: 0.5vw;
}

.detail-icon {
  font-size: 1.1vh;
}

.exit-btn {
  padding: 0.8vh 2vw;
  background: 
    linear-gradient(135deg, #8b4513 0%, #a0522d 50%, #8b4513 100%);
  color: #f0e6d2;
  border: 0.2vh solid #daa520;
  border-radius: 0.6vh;
  font-size: 1.3vh;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  gap: 0.6vw;
  box-shadow: 
    0 0.2vh 0.6vh rgba(0, 0, 0, 0.4),
    inset 0 0.1vh 0.3vh rgba(218, 165, 32, 0.3);
}

.exit-btn:hover {
  background: 
    linear-gradient(135deg, #a0522d 0%, #cd853f 50%, #a0522d 100%);
  transform: translateY(-0.1vh);
  box-shadow: 
    0 0.5vh 1.2vh rgba(0, 0, 0, 0.5),
    inset 0 0.1vh 0.3vh rgba(218, 165, 32, 0.4);
}

.exit-icon {
  font-size: 1.8vh;
}

/* 游戏主区域 */
.game-area {
  grid-area: game;
  display: flex;
  flex-direction: column;
  gap: 0.8vh;
  overflow-y: auto;
  max-height: 100%;
}

.track-area {
  background: 
    radial-gradient(ellipse at center, rgba(45, 74, 26, 0.3) 0%, rgba(30, 50, 16, 0.2) 100%),
    rgba(0, 0, 0, 0.3);
  border: 0.3vh solid rgba(139, 69, 19, 0.4);
  border-radius: 1vh;
  padding: 1.2vh;
  box-shadow: 
    inset 0 0.3vh 0.8vh rgba(0, 0, 0, 0.3),
    0 0.2vh 0.5vh rgba(139, 69, 19, 0.2);
  position: relative;
}

.race-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5vh;
  padding-bottom: 1vh;
  border-bottom: 0.2vh solid rgba(139, 69, 19, 0.3);
}

.race-badge {
  display: flex;
  align-items: center;
  gap: 0.8vw;
  background: rgba(0, 0, 0, 0.4);
  padding: 0.8vh 1.5vw;
  border-radius: 2vh;
  border: 0.15vh solid rgba(218, 165, 32, 0.5);
}

.badge-icon {
  font-size: 2vh;
}

.race-title {
  font-size: 1.9vh;
  font-weight: bold;
  color: #ffed4e;
  text-shadow: 0 0.1vh 0.3vh rgba(0, 0, 0, 0.8);
}

.race-status {
  display: flex;
  align-items: center;
  gap: 1vw;
  background: rgba(0, 0, 0, 0.4);
  padding: 0.8vh 1.5vw;
  border-radius: 2vh;
  border: 0.15vh solid rgba(139, 69, 19, 0.5);
}

.status-light {
  width: 1.5vh;
  height: 1.5vh;
  border-radius: 50%;
  border: 0.1vh solid #333;
}

.status-light.betting {
  background: #28a745;
  box-shadow: 0 0 1vh #28a745;
}

.status-light.racing {
  background: #dc3545;
  box-shadow: 0 0 1vh #dc3545;
}

.status-text {
  font-size: 1.4vh;
  font-weight: bold;
  color: #f8f9fa;
}

.audio-toggle {
  background: rgba(0, 0, 0, 0.6);
  border: 0.1vh solid #8b4513;
  border-radius: 0.3vh;
  color: #f8f9fa;
  padding: 0.4vh 0.8vh;
  cursor: pointer;
  font-size: 1.2vh;
  transition: all 0.2s;
  margin-left: 1vh;
}

.audio-toggle:hover {
  background: rgba(139, 69, 19, 0.4);
  border-color: #daa520;
}

.audio-toggle.enabled {
  color: #28a745;
  border-color: #28a745;
}

.track-surface {
  background: 
    linear-gradient(to bottom, #8b7355 0%, #6b5b47 50%, #4a3f35 100%);
  border: 0.3vh solid #654321;
  border-radius: 1vh;
  padding: 1.5vh;
  position: relative;
  overflow: hidden;
}

/* 随机事件通知样式 */
.event-notification {
  position: absolute;
  top: 3vh;
  left: 50%;
  transform: translateX(-50%);
  background: linear-gradient(135deg, rgba(255, 69, 0, 0.95) 0%, rgba(220, 20, 60, 0.95) 100%);
  border: 0.2vh solid #FF4500;
  border-radius: 2vh;
  padding: 1vh 2vh;
  display: flex;
  align-items: center;
  gap: 1vh;
  max-width: 85%;
  z-index: 15;
  box-shadow: 
    0 0.5vh 1.5vh rgba(255, 69, 0, 0.6),
    0 0 3vh rgba(255, 69, 0, 0.4);
  animation: eventAlert 0.6s ease-out, eventPulse 2s ease-in-out infinite 0.6s;
}

@keyframes eventAlert {
  0% {
    opacity: 0;
    transform: translateX(-50%) translateY(-2vh) scale(0.8);
  }
  100% {
    opacity: 1;
    transform: translateX(-50%) translateY(0) scale(1);
  }
}

@keyframes eventPulse {
  0%, 100% { 
    box-shadow: 
      0 0.5vh 1.5vh rgba(255, 69, 0, 0.6),
      0 0 3vh rgba(255, 69, 0, 0.4);
  }
  50% { 
    box-shadow: 
      0 0.8vh 2vh rgba(255, 69, 0, 0.8),
      0 0 4vh rgba(255, 69, 0, 0.6);
  }
}

.event-icon {
  font-size: 2vh;
  animation: iconBounce 1.5s ease-in-out infinite;
}

@keyframes iconBounce {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.2); }
}

.event-text {
  font-size: 1.6vh;
  font-weight: bold;
  color: #ffffff;
  text-shadow: 0.1vh 0.1vh 0.4vh rgba(0, 0, 0, 0.9);
  white-space: nowrap;
}

/* 特殊马匹样式 */
.special-horse {
  background: radial-gradient(ellipse at center, rgba(255, 215, 0, 0.2) 0%, rgba(255, 165, 0, 0.1) 100%);
  border: 0.2vh solid rgba(255, 215, 0, 0.5);
  box-shadow: 0 0 1vh rgba(255, 215, 0, 0.3);
}

.lane-number.special, .horse-number.special {
  background: linear-gradient(135deg, #FFD700 0%, #FFA500 100%);
  color: #000000;
  border-color: #FFD700;
  box-shadow: 0 0 1vh rgba(255, 215, 0, 0.5);
  animation: goldenGlow 2s ease-in-out infinite alternate;
}

@keyframes goldenGlow {
  from {
    box-shadow: 0 0 1vh rgba(255, 215, 0, 0.5);
  }
  to {
    box-shadow: 0 0 2vh rgba(255, 215, 0, 0.8);
  }
}

.horse-name.special {
  color: #FFD700;
  text-shadow: 
    0 0.1vh 0.3vh rgba(0, 0, 0, 0.8),
    0 0 1vh rgba(255, 215, 0, 0.6);
}

.special-badge {
  font-size: 1.2vh;
  color: #FFD700;
  text-shadow: 0 0 0.5vh rgba(255, 215, 0, 0.8);
  animation: starTwinkle 1.5s ease-in-out infinite alternate;
}

@keyframes starTwinkle {
  from { opacity: 0.7; transform: scale(1); }
  to { opacity: 1; transform: scale(1.2); }
}

.odds-display.special, .odds-badge.special {
  background: linear-gradient(135deg, #FFD700 0%, #FFA500 100%);
  color: #000000;
  box-shadow: 0 0 1vh rgba(255, 215, 0, 0.4);
}

.horse-runner.special {
  filter: drop-shadow(0 0 0.5vh rgba(255, 215, 0, 0.8));
}

.horse-runner.special .horse-icon {
  animation: specialHorseRun 1s ease-in-out infinite alternate;
}

@keyframes specialHorseRun {
  from { transform: scale(1); }
  to { transform: scale(1.1); }
}

/* 受伤马匹样式 */
.injured-horse {
  background: rgba(220, 53, 69, 0.1);
  border: 0.2vh solid rgba(220, 53, 69, 0.3);
}

.horse-name.injured {
  color: #dc3545;
  text-decoration: line-through;
  opacity: 0.7;
}

.injured-badge {
  font-size: 1vh;
  color: #dc3545;
  animation: injuredBlink 2s ease-in-out infinite;
}

@keyframes injuredBlink {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

.horse-runner.injured {
  opacity: 0.6;
  filter: grayscale(50%);
}

.horse-runner.injured .horse-icon {
  animation: injuredWobble 3s ease-in-out infinite;
}

@keyframes injuredWobble {
  0%, 100% { transform: rotate(0deg); }
  25% { transform: rotate(-2deg); }
  75% { transform: rotate(2deg); }
}

/* 马匹选择特殊样式 */
.horse-option.special {
  border-color: #FFD700;
  background: rgba(255, 215, 0, 0.1);
  box-shadow: 0 0 0.8vh rgba(255, 215, 0, 0.3);
}

.horse-option.injured {
  border-color: #dc3545;
  background: rgba(220, 53, 69, 0.1);
  opacity: 0.7;
  pointer-events: none; /* 受伤马匹不能选择 */
}

.horse-option.special:hover {
  border-color: #FFD700;
  background: rgba(255, 215, 0, 0.2);
  box-shadow: 0 0 1.2vh rgba(255, 215, 0, 0.5);
}

/* 比赛取消样式 */
.cancellation-info {
  text-align: center;
  padding: 2vh;
  background: rgba(255, 165, 0, 0.1);
  border-radius: 0.6vh;
  border: 0.15vh solid rgba(255, 165, 0, 0.3);
  width: 100%;
}

.cancel-reason {
  font-size: 1.4vh;
  color: #ff8c00;
  font-weight: bold;
  margin-bottom: 1vh;
}

.refund-info {
  font-size: 1.6vh;
  color: #28a745;
  font-weight: bold;
  text-shadow: 0 0.1vh 0.2vh rgba(0, 0, 0, 0.8);
}

/* 实时解说样式 */
.live-commentary {
  position: absolute;
  top: 0.5vh;
  left: 50%;
  transform: translateX(-50%);
  background: linear-gradient(135deg, rgba(0, 0, 0, 0.9) 0%, rgba(20, 20, 20, 0.9) 100%);
  border: 0.2vh solid #daa520;
  border-radius: 2vh;
  padding: 0.8vh 2vh;
  display: flex;
  align-items: center;
  gap: 1vh;
  max-width: 80%;
  z-index: 10;
  box-shadow: 
    0 0.4vh 1.2vh rgba(0, 0, 0, 0.6),
    0 0 2vh rgba(218, 165, 32, 0.3);
  animation: commentaryFadeIn 0.5s ease-out;
}

@keyframes commentaryFadeIn {
  from {
    opacity: 0;
    transform: translateX(-50%) translateY(-1vh);
  }
  to {
    opacity: 1;
    transform: translateX(-50%) translateY(0);
  }
}

.commentary-icon {
  font-size: 1.6vh;
  color: #daa520;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.1); }
}

.commentary-text {
  font-size: 1.4vh;
  font-weight: bold;
  color: #ffffff;
  text-shadow: 0.1vh 0.1vh 0.3vh rgba(0, 0, 0, 0.8);
  white-space: nowrap;
  animation: textGlow 2s ease-in-out infinite alternate;
}

@keyframes textGlow {
  from {
    text-shadow: 0.1vh 0.1vh 0.3vh rgba(0, 0, 0, 0.8);
  }
  to {
    text-shadow: 
      0.1vh 0.1vh 0.3vh rgba(0, 0, 0, 0.8),
      0 0 1vh rgba(255, 255, 255, 0.6);
  }
}

.track-markers {
  display: flex;
  justify-content: space-between;
  margin-bottom: 1vh;
  padding: 0 2vh;
}

.marker {
  background: rgba(0, 0, 0, 0.6);
  color: #f8f9fa;
  padding: 0.5vh 1vw;
  border-radius: 0.3vh;
  font-size: 1.2vh;
  font-weight: bold;
  border: 0.1vh solid #daa520;
}

.marker.finish {
  background: linear-gradient(135deg, #dc3545 0%, #a71e2a 100%);
}

.racing-lanes {
  display: flex;
  flex-direction: column;
  gap: 1vh;
}

.racing-lane {
  display: flex;
  align-items: center;
  gap: 1vh;
  background: rgba(139, 115, 85, 0.3);
  border-radius: 0.5vh;
  padding: 0.8vh;
  border: 0.1vh solid rgba(101, 67, 33, 0.5);
}

.lane-info {
  display: flex;
  align-items: center;
  gap: 1vh;
  min-width: 15vh;
}

.lane-number {
  background: linear-gradient(135deg, #8b4513 0%, #654321 100%);
  color: #f8f9fa;
  width: 3vh;
  height: 3vh;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 1.2vh;
  border: 0.2vh solid #daa520;
  box-shadow: 0 0.2vh 0.5vh rgba(0, 0, 0, 0.4);
}

.horse-details {
  display: flex;
  flex-direction: column;
  gap: 0.2vh;
}

.horse-name {
  font-size: 1.4vh;
  color: #f8f9fa;
  font-weight: bold;
}

.jockey-info {
  font-size: 1vh;
  color: #daa520;
}

.race-track {
  flex: 1;
  height: 4vh;
  background: linear-gradient(to right, #8b7355 0%, #a0956b 50%, #8b7355 100%);
  border-radius: 2vh;
  position: relative;
  border: 0.2vh solid #654321;
  overflow: hidden;
}

.horse-runner {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  display: flex;
  align-items: center;
  transition: left 1.5s ease-in-out;
  z-index: 2;
}

.jockey-silk {
  width: 0.8vh;
  height: 2vh;
  border-radius: 0.2vh;
  margin-right: 0.3vh;
  border: 0.1vh solid #333;
}

.horse-icon {
  font-size: 2.5vh;
  text-shadow: 0 0.1vh 0.3vh rgba(0, 0, 0, 0.8);
}

.finish-line {
  position: absolute;
  right: 0;
  top: 0;
  width: 0.3vh;
  height: 100%;
  background: linear-gradient(to bottom, #dc3545 0%, #a71e2a 100%);
  border-radius: 0.1vh;
}

.odds-display {
  background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%);
  color: #f8f9fa;
  padding: 0.8vh 1vw;
  border-radius: 0.3vh;
  font-weight: bold;
  font-size: 1.2vh;
  min-width: 6vh;
  text-align: center;
}

/* 侧边信息区 */
.info-section {
  grid-area: info;
  display: flex;
  flex-direction: column;
  gap: 0.8vh;
  overflow-y: auto;
  max-height: 100%;
}

.account-status,
.race-info-card,
.betting-tips {
  background: 
    linear-gradient(135deg, rgba(45, 74, 26, 0.3) 0%, rgba(30, 50, 16, 0.2) 100%),
    rgba(0, 0, 0, 0.4);
  border: 0.25vh solid rgba(139, 69, 19, 0.4);
  border-radius: 1vh;
  overflow: hidden;
  box-shadow: 
    inset 0 0.2vh 0.5vh rgba(0, 0, 0, 0.3),
    0 0.2vh 0.5vh rgba(139, 69, 19, 0.2);
}

.status-header,
.card-header {
  background: 
    linear-gradient(135deg, rgba(139, 69, 19, 0.25) 0%, rgba(139, 69, 19, 0.15) 100%);
  padding: 1.2vh 1.5vw;
  font-size: 1.7vh;
  font-weight: bold;
  color: #ffed4e;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.8vw;
  border-bottom: 0.2vh solid rgba(139, 69, 19, 0.3);
  text-shadow: 0 0.1vh 0.3vh rgba(0, 0, 0, 0.8);
}

.header-icon {
  font-size: 1.8vh;
}

.balance-info {
  padding: 2vh 1.5vw;
  text-align: center;
}

.balance-amount {
  font-size: 2.8vh;
  font-weight: bold;
  color: #ffd700;
  text-shadow: 
    0 0.1vh 0.3vh rgba(0, 0, 0, 0.8),
    0 0 1vh rgba(255, 215, 0, 0.6);
  margin-bottom: 0.5vh;
}

.balance-label {
  font-size: 1.2vh;
  color: #b8a47e;
  text-transform: uppercase;
  letter-spacing: 0.1vw;
}

.card-content {
  padding: 1.8vh 1.5vw;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 1vw;
  padding: 0.8vh 0;
  font-size: 1.3vh;
  color: #e0d5c7;
}

.info-icon {
  font-size: 1.5vh;
  color: #daa520;
}

/* 侧边投注单区域 */
.betting-slip-area {
  background: 
    linear-gradient(135deg, rgba(45, 74, 26, 0.3) 0%, rgba(30, 50, 16, 0.2) 100%),
    rgba(0, 0, 0, 0.4);
  border: 0.25vh solid rgba(139, 69, 19, 0.4);
  border-radius: 1vh;
  overflow: hidden;
  box-shadow: 
    inset 0 0.2vh 0.5vh rgba(0, 0, 0, 0.3),
    0 0.2vh 0.5vh rgba(139, 69, 19, 0.2);
  display: flex;
  flex-direction: column;
  flex: 1;
  gap: 1vh;
  padding: 1vh;
}

.selected-horse-info {
  display: flex;
  align-items: center;
  gap: 1vh;
  padding: 1vh;
  background: rgba(218, 165, 32, 0.1);
  border-radius: 0.6vh;
  border: 0.15vh solid rgba(218, 165, 32, 0.3);
}

.horse-details {
  display: flex;
  flex-direction: column;
  gap: 0.3vh;
  flex: 1;
}

.horse-odds {
  font-size: 1.4vh;
  color: #28a745;
  font-weight: bold;
}

/* 未选择马匹提示 */
.no-selection-tip {
  background: 
    linear-gradient(135deg, rgba(45, 74, 26, 0.3) 0%, rgba(30, 50, 16, 0.2) 100%),
    rgba(0, 0, 0, 0.4);
  border: 0.25vh solid rgba(139, 69, 19, 0.4);
  border-radius: 1vh;
  padding: 2vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1vh;
  text-align: center;
  flex: 1;
  justify-content: center;
}

.tip-header {
  display: flex;
  align-items: center;
  gap: 0.8vh;
  font-size: 2vh;
  font-weight: bold;
  color: #daa520;
}

.tip-icon {
  font-size: 2.5vh;
}

.tip-content {
  font-size: 1.5vh;
  color: #e0d5c7;
  line-height: 1.4;
}

/* 垂直布局状态显示 */
.race-progress-vertical,
.race-results-vertical {
  background: 
    linear-gradient(135deg, rgba(45, 74, 26, 0.3) 0%, rgba(30, 50, 16, 0.2) 100%),
    rgba(0, 0, 0, 0.4);
  border: 0.25vh solid rgba(139, 69, 19, 0.4);
  border-radius: 1vh;
  padding: 1.5vh;
  display: flex;
  flex-direction: column;
  gap: 1.2vh;
  align-items: center;
  text-align: center;
  flex: 1;
}

.winner-display-vertical {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1vh;
  padding: 1vh;
  background: rgba(218, 165, 32, 0.1);
  border-radius: 0.6vh;
  border: 0.15vh solid rgba(218, 165, 32, 0.3);
  width: 100%;
}

/* 控制区域 - 底部马匹选择 */
.control-section {
  grid-area: control;
  background: rgba(0, 0, 0, 0.3);
  border: 0.2vh solid rgba(139, 69, 19, 0.3);
  border-radius: 0.8vh;
  padding: 0.8vh;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: inset 0 0.2vh 0.5vh rgba(0, 0, 0, 0.2);
  overflow: hidden;
}

.betting-controls {
  display: flex;
  gap: 1.2vh;
  align-items: center;
  width: 100%;
  max-width: 120vh;
  height: 100%;
}

.betting-title {
  display: flex;
  align-items: center;
  gap: 0.5vw;
  font-size: 2vh;
  font-weight: bold;
  color: #daa520;
  text-shadow: 0 0.1vh 0.2vh rgba(0, 0, 0, 0.8);
  min-width: 15vh;
}

.title-icon {
  font-size: 2.2vh;
}

.horse-selection {
  display: flex;
  gap: 0.8vh;
  flex: 1;
  overflow-x: auto;
  padding: 0.3vh 0;
}

.horse-option {
  background: rgba(0, 0, 0, 0.3);
  border: 0.15vh solid rgba(139, 69, 19, 0.3);
  border-radius: 0.6vh;
  padding: 0.6vh 0.8vh;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  gap: 0.6vh;
  white-space: nowrap;
  min-width: 12vh;
}

.horse-option:hover {
  border-color: #daa520;
  background: rgba(139, 69, 19, 0.2);
  transform: translateY(-0.1vh);
}

.horse-option.active {
  border-color: #daa520;
  background: rgba(218, 165, 32, 0.2);
  box-shadow: 0 0 0.8vh rgba(218, 165, 32, 0.4);
}

/* 比赛状态栏 */
.race-status-bar,
.result-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  padding: 0.8vh 1.5vh;
  background: rgba(0, 0, 0, 0.4);
  border-radius: 0.6vh;
}

.status-info,
.result-info {
  display: flex;
  align-items: center;
  gap: 0.8vh;
  font-size: 1.7vh;
  font-weight: bold;
  color: #daa520;
}

.status-icon,
.result-icon {
  font-size: 2vh;
}

.current-bet-info {
  font-size: 1.4vh;
  color: #28a745;
  font-weight: bold;
}

.new-race-btn-compact {
  background: linear-gradient(135deg, #6f42c1 0%, #5a32a3 100%);
  color: #f8f9fa;
  border: none;
  padding: 0.8vh 1.5vh;
  border-radius: 0.4vh;
  cursor: pointer;
  font-weight: bold;
  font-size: 1.5vh;
  display: flex;
  align-items: center;
  gap: 0.6vh;
  transition: all 0.2s;
  box-shadow: 0 0.2vh 0.4vh rgba(0, 0, 0, 0.3);
}

.new-race-btn-compact:hover {
  background: linear-gradient(135deg, #5a32a3 0%, #4c2a85 100%);
  transform: translateY(-0.1vh);
  box-shadow: 0 0.3vh 0.8vh rgba(0, 0, 0, 0.4);
}

/* 通用马匹元素样式 */
.horse-badge {
  display: flex;
  align-items: center;
  gap: 0.3vh;
}

.horse-number {
  background: linear-gradient(135deg, #8b4513 0%, #654321 100%);
  color: #f8f9fa;
  width: 2.5vh;
  height: 2.5vh;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 1.4vh;
  border: 0.1vh solid #daa520;
}

.silk-color {
  width: 0.8vh;
  height: 1.6vh;
  border-radius: 0.2vh;
  border: 0.1vh solid #333;
}

.horse-info {
  display: flex;
  flex-direction: column;
  gap: 0.1vh;
  flex: 1;
}

.horse-name {
  font-size: 1.6vh;
  color: #f8f9fa;
  font-weight: bold;
}

.jockey-name {
  font-size: 1.2vh;
  color: #daa520;
}

.odds-badge {
  background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%);
  color: #f8f9fa;
  padding: 0.4vh 0.8vh;
  border-radius: 0.3vh;
  font-weight: bold;
  font-size: 1.3vh;
}

/* 投注单样式 */
.slip-header {
  background: 
    linear-gradient(135deg, rgba(139, 69, 19, 0.25) 0%, rgba(139, 69, 19, 0.15) 100%);
  padding: 1.2vh 1.5vw;
  font-size: 1.8vh;
  font-weight: bold;
  color: #ffed4e;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.8vw;
  border-radius: 0.6vh 0.6vh 0 0;
  text-shadow: 0 0.1vh 0.3vh rgba(0, 0, 0, 0.8);
  margin: -1vh -1vh 0 -1vh;
  margin-bottom: 1vh;
}

.slip-title {
  font-size: 1.1vh;
  font-weight: bold;
  color: #daa520;
}

.selected-info {
  font-size: 0.9vh;
  color: #f8f9fa;
}

.amount-selection {
  display: flex;
  flex-direction: column;
  gap: 0.5vh;
}

.quick-amounts {
  display: flex;
  gap: 0.3vh;
}

.amount-btn {
  background: rgba(139, 69, 19, 0.3);
  color: #f8f9fa;
  border: 0.1vh solid #8b4513;
  padding: 0.6vh 0.8vh;
  border-radius: 0.3vh;
  cursor: pointer;
  font-weight: bold;
  font-size: 1.3vh;
  transition: all 0.2s;
  flex: 1;
  text-align: center;
}

.amount-btn:hover {
  background: rgba(139, 69, 19, 0.5);
  transform: translateY(-0.1vh);
}

.amount-btn.active {
  background: linear-gradient(135deg, #daa520 0%, #b8860b 100%);
  color: #212529;
  box-shadow: 0 0 0.6vh rgba(218, 165, 32, 0.4);
}

.custom-amount {
  display: flex;
  align-items: center;
  gap: 0.3vh;
}

.currency {
  font-size: 1.4vh;
  color: #daa520;
  font-weight: bold;
}

.amount-input {
  flex: 1;
  padding: 0.6vh 1vh;
  background: rgba(0, 0, 0, 0.6);
  border: 0.1vh solid #daa520;
  border-radius: 0.3vh;
  color: #fff;
  font-size: 1.3vh;
  font-weight: bold;
  text-align: center;
}

.amount-input:focus {
  outline: none;
  border-color: #ffed4e;
  box-shadow: 0 0 0.5vh rgba(255, 237, 78, 0.3);
}

.bet-summary {
  background: rgba(40, 167, 69, 0.1);
  border-radius: 0.3vh;
  padding: 0.5vh;
  border: 0.1vh solid rgba(40, 167, 69, 0.3);
  flex-shrink: 0;
}

.summary-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.2vh 0;
  font-size: 1.3vh;
}

.summary-row.payout {
  border-top: 0.1vh solid rgba(40, 167, 69, 0.3);
  padding-top: 0.3vh;
  font-weight: bold;
  color: #28a745;
}

.place-bet-btn {
  width: 100%;
  background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%);
  color: #f8f9fa;
  border: none;
  padding: 1vh;
  border-radius: 0.4vh;
  cursor: pointer;
  font-weight: bold;
  font-size: 1.6vh;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.6vh;
  transition: all 0.2s;
  box-shadow: 0 0.2vh 0.4vh rgba(0, 0, 0, 0.3);
  flex-shrink: 0;
}

.place-bet-btn:hover:not(:disabled) {
  background: linear-gradient(135deg, #218838 0%, #155724 100%);
  transform: translateY(-0.1vh);
  box-shadow: 0 0.3vh 0.8vh rgba(0, 0, 0, 0.4);
}

.place-bet-btn:disabled {
  background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.btn-icon {
  font-size: 1.8vh;
}

/* 状态显示共用样式 */
.progress-header,
.result-header {
  font-size: 1.6vh;
  font-weight: bold;
  color: #daa520;
  display: flex;
  align-items: center;
  gap: 0.6vh;
}

.progress-icon,
.result-icon {
  font-size: 1.8vh;
}

.countdown-display {
  font-size: 1.8vh;
  font-weight: bold;
  color: #28a745;
  text-shadow: 0 0 0.5vh rgba(40, 167, 69, 0.5);
}

.current-bet,
.bet-ticket {
  background: rgba(218, 165, 32, 0.1);
  border: 0.15vh solid rgba(218, 165, 32, 0.3);
  border-radius: 0.6vh;
  padding: 1vh;
  text-align: center;
  width: 100%;
}

.ticket-info {
  display: flex;
  flex-direction: column;
  gap: 0.4vh;
  font-size: 1.1vh;
}

.ticket-amount {
  font-size: 1.4vh;
  font-weight: bold;
  color: #daa520;
}

.winner-badge {
  display: flex;
  align-items: center;
  gap: 0.8vh;
  background: linear-gradient(135deg, #daa520 0%, #b8860b 100%);
  color: #212529;
  padding: 0.8vh;
  border-radius: 0.6vh;
  font-weight: bold;
  box-shadow: 0 0.3vh 0.8vh rgba(218, 165, 32, 0.3);
}

.winner-number {
  font-size: 1.4vh;
}

.winner-title {
  font-size: 1.1vh;
}

.winner-name {
  font-size: 1.3vh;
  font-weight: bold;
  color: #ffed4e;
}

.payout-result {
  width: 100%;
  text-align: center;
  padding: 1vh;
  border-radius: 0.6vh;
}

.win-result {
  background: rgba(40, 167, 69, 0.2);
  border: 0.15vh solid #28a745;
}

.lose-result {
  background: rgba(220, 53, 69, 0.2);
  border: 0.15vh solid #dc3545;
}

.result-status {
  font-size: 1.2vh;
  font-weight: bold;
  margin-bottom: 0.5vh;
}

.result-status.win {
  color: #28a745;
}

.result-status.lose {
  color: #dc3545;
}

.payout-amount {
  font-size: 1.4vh;
  font-weight: bold;
  color: #28a745;
}

.loss-amount {
  font-size: 1.4vh;
  font-weight: bold;
  color: #dc3545;
}

.new-race-btn {
  background: linear-gradient(135deg, #6f42c1 0%, #5a32a3 100%);
  color: #f8f9fa;
  border: none;
  padding: 0.8vh 1.5vh;
  border-radius: 0.4vh;
  cursor: pointer;
  font-weight: bold;
  font-size: 1.2vh;
  display: flex;
  align-items: center;
  gap: 0.5vh;
  transition: all 0.2s;
  box-shadow: 0 0.2vh 0.4vh rgba(0, 0, 0, 0.3);
}

.new-race-btn:hover {
  background: linear-gradient(135deg, #5a32a3 0%, #4c2a85 100%);
  transform: translateY(-0.1vh);
  box-shadow: 0 0.3vh 0.8vh rgba(0, 0, 0, 0.4);
}



/* 比赛进度和结果 - 紧凑设计 */
.race-progress,
.race-results {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1.5vh;
  width: 100%;
  height: 100%;
  padding: 0.5vh;
}

.progress-header,
.result-header {
  display: flex;
  align-items: center;
  gap: 0.6vw;
  font-size: 1.4vh;
  font-weight: bold;
  color: #daa520;
  text-shadow: 0 0.1vh 0.2vh rgba(0, 0, 0, 0.8);
}

.progress-icon,
.result-icon {
  font-size: 1.6vh;
}

.countdown-display {
  background: linear-gradient(135deg, #dc3545 0%, #a71e2a 100%);
  color: #f8f9fa;
  padding: 0.8vh 2vw;
  border-radius: 0.6vh;
  font-size: 1.6vh;
  font-weight: bold;
  box-shadow: 0 0.3vh 0.6vh rgba(0, 0, 0, 0.4);
  text-shadow: 0 0.1vh 0.2vh rgba(0, 0, 0, 0.8);
}

.current-bet {
  background: rgba(0, 0, 0, 0.4);
  border: 0.15vh solid #daa520;
  border-radius: 0.6vh;
  padding: 0.8vh 1.2vh;
  max-width: 30vh;
}

.bet-ticket {
  display: flex;
  align-items: center;
  gap: 1vh;
  text-align: center;
}

.ticket-info {
  display: flex;
  flex-direction: column;
  gap: 0.2vh;
  font-size: 1vh;
}

.ticket-amount {
  font-size: 1.3vh;
  font-weight: bold;
  color: #28a745;
}

.winner-display {
  display: flex;
  align-items: center;
  gap: 1.5vh;
  padding: 0.8vh 1.5vh;
  background: rgba(218, 165, 32, 0.2);
  border-radius: 0.6vh;
  border: 0.15vh solid rgba(218, 165, 32, 0.4);
}

.winner-info {
  display: flex;
  align-items: center;
  gap: 1vh;
}

.winner-badge {
  display: flex;
  align-items: center;
  gap: 0.6vh;
  background: linear-gradient(135deg, #daa520 0%, #b8860b 100%);
  color: #212529;
  padding: 0.6vh 1.2vh;
  border-radius: 1vh;
  font-weight: bold;
  box-shadow: 0 0.2vh 0.5vh rgba(0, 0, 0, 0.4);
}

.winner-number {
  font-size: 1.3vh;
}

.winner-title {
  font-size: 1vh;
  text-transform: uppercase;
}

.winner-name {
  font-size: 1.5vh;
  font-weight: bold;
  color: #ffed4e;
  text-shadow: 0 0.1vh 0.2vh rgba(0, 0, 0, 0.8);
}

.payout-result {
  background: rgba(0, 0, 0, 0.4);
  border-radius: 0.6vh;
  padding: 0.8vh 1.2vh;
  text-align: center;
  border: 0.15vh solid rgba(139, 69, 19, 0.3);
  min-width: 20vh;
}

.win-result,
.lose-result {
  display: flex;
  flex-direction: column;
  gap: 0.4vh;
}

.result-status.win {
  font-size: 1.3vh;
  font-weight: bold;
  color: #28a745;
  text-shadow: 0 0.1vh 0.2vh rgba(0, 0, 0, 0.8);
}

.result-status.lose {
  font-size: 1.2vh;
  font-weight: bold;
  color: #dc3545;
  text-shadow: 0 0.1vh 0.2vh rgba(0, 0, 0, 0.8);
}

.payout-amount {
  font-size: 1.8vh;
  font-weight: bold;
  color: #28a745;
  text-shadow: 0 0.1vh 0.2vh rgba(0, 0, 0, 0.8);
}

.loss-amount {
  font-size: 1.5vh;
  font-weight: bold;
  color: #dc3545;
  text-shadow: 0 0.1vh 0.2vh rgba(0, 0, 0, 0.8);
}

.new-race-btn {
  background: linear-gradient(135deg, #8b4513 0%, #654321 100%);
  color: #f8f9fa;
  border: none;
  padding: 0.8vh 2vh;
  border-radius: 0.6vh;
  cursor: pointer;
  font-weight: bold;
  font-size: 1.2vh;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5vh;
  transition: all 0.2s;
  box-shadow: 0 0.2vh 0.5vh rgba(0, 0, 0, 0.4);
}

.new-race-btn:hover {
  background: linear-gradient(135deg, #a0522d 0%, #8b4513 100%);
  transform: translateY(-0.1vh);
  box-shadow: 0 0.3vh 0.8vh rgba(0, 0, 0, 0.6);
}























</style> 