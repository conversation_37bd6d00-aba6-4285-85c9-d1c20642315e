import * as alt from 'alt-server';
import { useRebar } from '@Server/index.js';
import { Character } from '@Shared/types/character.js';
import { CollectionNames } from '@Server/document/shared.js';
import { PageNames } from '@Shared/webview/index.js';
import { taxforgoverment } from '@Plugins/faction/server/index.js';
import { GlobalTimerManager } from '@Plugins/animmenu/server/interval.js';
import { elevatorSystems, FloorData, ElevatorSystem } from '../shared/config.js';
import { MarkerType } from '@Shared/types/marker.js';


const Rebar = useRebar();
const db = Rebar.database.useDatabase();

const messenger = Rebar.messenger.useMessenger();
const promptbarapi = await Rebar.useApi().getAsync('promptbar-api')

// 存储每个电梯的玩家列表 elevatorId -> Set<Player>
const elevatorPlayers = new Map<string, Set<alt.Player>>();
// 存储每个电梯的操作者 elevatorId -> Player
const elevatorOperators = new Map<string, alt.Player>();

// 自动处理所有电梯系统
elevatorSystems.forEach((elevatorSystem) => {
    // 为该电梯系统的每个楼层创建交互点
    elevatorSystem.floors.forEach((currentFloor, floorIndex) => {
        // 为每个电梯创建唯一ID
        const elevatorId = `${elevatorSystem.id}_${floorIndex}`;
        
        const ELEVATOR = Rebar.controllers.useInteraction(
            new alt.ColshapeCylinder(currentFloor.pos.x, currentFloor.pos.y, currentFloor.pos.z , 2, 2),
            'player',
            currentFloor.dimension ? currentFloor.dimension : 0
        );

        ELEVATOR.onEnter((player) => {
            // 初始化电梯玩家集合
            if (!elevatorPlayers.has(elevatorId)) {
                elevatorPlayers.set(elevatorId, new Set());
            }
            
            // 添加玩家到电梯
            elevatorPlayers.get(elevatorId)!.add(player);
            
            // 检查是否已有操作者
            const currentOperator = elevatorOperators.get(elevatorId);
            if (!currentOperator || !currentOperator.valid) {
                // 没有操作者或操作者已断线，设置当前玩家为操作者
                elevatorOperators.set(elevatorId, player);
                promptbarapi.showPromptBar(player, `乘坐电梯 (当前: ${currentFloor.name}) - 你是操作者`);
            } else {
                // 已有操作者，显示等待信息
                promptbarapi.showPromptBar(player, `乘坐电梯 (当前: ${currentFloor.name}) - 请等待其他玩家操作`);
            }
        });

        ELEVATOR.onLeave((player) => {
            promptbarapi.hidePromptBar(player);
            
            // 从电梯移除玩家
            const playersInElevator = elevatorPlayers.get(elevatorId);
            if (playersInElevator) {
                playersInElevator.delete(player);
                
                // 如果离开的是操作者，选择新的操作者
                if (elevatorOperators.get(elevatorId) === player) {
                    elevatorOperators.delete(elevatorId);
                    
                    // 从剩余玩家中选择新的操作者
                    const remainingPlayers = Array.from(playersInElevator);
                    if (remainingPlayers.length > 0) {
                        const newOperator = remainingPlayers[0];
                        elevatorOperators.set(elevatorId, newOperator);
                        promptbarapi.showPromptBar(newOperator, `乘坐电梯 (当前: ${currentFloor.name}) - 你是操作者`);
                        
                        // 更新其他玩家的提示
                        remainingPlayers.slice(1).forEach(p => {
                            promptbarapi.showPromptBar(p, `乘坐电梯 (当前: ${currentFloor.name}) - 请等待其他玩家操作`);
                        });
                    }
                }
                
                // 如果电梯已空，清理数据
                if (playersInElevator.size === 0) {
                    elevatorPlayers.delete(elevatorId);
                }
            }
        });

        ELEVATOR.on(async (player) => {
            // 只有操作者才能打开界面
            if (elevatorOperators.get(elevatorId) !== player) {
                return;
            }
            
            Rebar.player.useWebview(player).show('elevator', 'page', true);
            let isReady = await Rebar.player.useWebview(player).isReady('elevator', 'page')
            while (!isReady) {
                await alt.Utils.wait(100);
                isReady = await Rebar.player.useWebview(player).isReady('elevator', 'page')
            }
            
            // 自动生成该电梯系统中可前往的其他楼层（排除当前楼层）
            const availableFloors = elevatorSystem.floors.filter((_, index) => index !== floorIndex);
            
            // 发送数据到前端，包含电梯ID
            Rebar.player.useWebview(player).emit('Elevator:inventoryUpdate', {
                floors: availableFloors,
                currentFloor: currentFloor.name,
                systemId: elevatorSystem.id,
                elevatorId: elevatorId
            });
            player.setStreamSyncedMeta('do', `正在操作电梯 (${currentFloor.name})`)
        });

        // 为每个楼层创建可视标记
        Rebar.controllers.useMarkerGlobal({
            pos: new alt.Vector3(currentFloor.pos.x, currentFloor.pos.y, currentFloor.pos.z ),
            color: new alt.RGBA(255, 215, 0, 150),
            scale: new alt.Vector3(2, 2, 0.2),
            type: MarkerType.CYLINDER,
            dimension: currentFloor.dimension ? currentFloor.dimension : 0
        });
    });
});

console.log(`🛗 电梯系统初始化完成！加载了 ${elevatorSystems.length} 个电梯系统，共 ${elevatorSystems.reduce((total, system) => total + system.floors.length, 0)} 个楼层交互点。`);

alt.onClient('Elevator:goToFloor', (player: alt.Player, pos: alt.Vector3, elevatorId?: string) => {
    // 如果没有传递elevatorId，使用旧的单人移动逻辑
    if (!elevatorId) {
        Rebar.player.useWorld(player).setScreenFade(0);
        player.pos = pos;
        alt.setTimeout(() => {
            Rebar.player.useWorld(player).clearScreenFade(1000);
        }, 500);
        return;
    }
    
    // 获取该电梯中的所有玩家
    const playersInElevator = elevatorPlayers.get(elevatorId);
    if (!playersInElevator) {
        return;
    }
    
    // 移动所有在电梯中的玩家
    playersInElevator.forEach(elevatorPlayer => {
        if (elevatorPlayer.valid) {
            Rebar.player.useWorld(elevatorPlayer).setScreenFade(0);
            elevatorPlayer.pos = pos;
            alt.setTimeout(() => {
                if (elevatorPlayer.valid) {
                    Rebar.player.useWorld(elevatorPlayer).clearScreenFade(1000);
                }
            }, 500);
        }
    });
    
    // 清理该电梯的玩家数据，因为他们已经移动到新楼层
    elevatorPlayers.delete(elevatorId);
    elevatorOperators.delete(elevatorId);
})

alt.onClient('Elevator:closePage', (player: alt.Player) => {
    Rebar.player.useWebview(player).hide('elevator');
})

alt.on('rebar:playerPageOpened', (player: alt.Player, page: PageNames) => {
    if (page == 'elevator') {
        Rebar.player.useWorld(player).disableControls()
    }
})

alt.on('rebar:playerPageClosed', (player: alt.Player, page: PageNames) => {
    if (page == 'elevator') {
        Rebar.player.useWorld(player).enableControls()
        player.deleteStreamSyncedMeta('do')
    }
})

// 玩家断线时清理数据
alt.on('real:playerDisconnect', (player: alt.Player) => {
    // 清理所有电梯中的该玩家数据
    elevatorPlayers.forEach((players, elevatorId) => {
        players.delete(player);
        
        // 如果离开的是操作者，选择新的操作者
        if (elevatorOperators.get(elevatorId) === player) {
            elevatorOperators.delete(elevatorId);
            
            const remainingPlayers = Array.from(players);
            if (remainingPlayers.length > 0) {
                elevatorOperators.set(elevatorId, remainingPlayers[0]);
            }
        }
        
        // 如果电梯已空，清理数据
        if (players.size === 0) {
            elevatorPlayers.delete(elevatorId);
        }
    });
});

