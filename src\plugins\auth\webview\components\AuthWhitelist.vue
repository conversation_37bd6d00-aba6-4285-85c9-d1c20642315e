<script lang="ts" setup>
import { ref, computed, onMounted, watch } from 'vue';
import { useEvents } from '../../../../../webview/composables/useEvents.js';
import { AuthEvents } from '../../shared/authEvents.js';
import { useTranslate } from '@Shared/translate.js';
import * as Tone from 'tone';

const { t } = useTranslate('en');

const events = useEvents();
const props = defineProps<{
  soundActions?: {
    playHoverSound: () => void;
    playLoginSuccessSound: () => void;
    playErrorSound: () => void;
    playNotificationSound: () => void;
  };
  accountId?: string; // 直接从父组件接收accountId
}>();

const emits = defineEmits<{
  returnToLogin: [];
}>();

const currentQuestionIndex = ref(0);
const answers = ref<string[]>([]);
const isSubmitting = ref(false);
const showResults = ref(false);
const isApproved = ref(false);
const rejectionReason = ref('');
const showRejectionInfo = ref(false);
const whitelistStatus = ref('none'); // 'none', 'rejected', 'pending', 'approved'
const rejectionMessage = ref('');
const accountErrorMessage = ref(''); // 添加账户错误消息
const showAccountError = ref(false); // 显示账户错误状态

// 添加自定义状态提示相关的响应式变量
const showStatusModal = ref(false);
const statusModalType = ref<'error' | 'warning' | 'info'>('error');
const statusModalTitle = ref('');
const statusModalMessage = ref('');

// 确认模态框状态
const showConfirmModal = ref(false);
const confirmTitle = ref('');
const confirmMessage = ref('');
const confirmCallback = ref(null);

// 添加分片传送相关状态
const isUsingChunkedTransfer = ref(false);
const chunkProgress = ref(0);
const totalChunks = ref(0);

// 硬核RP白名单问题 - 参考国外优秀服务器
const whitelistQuestions = [
  {
    id: 1,
    question: "什么是RDM（Random Death Match）？请详细解释并举例说明。",
    type: "textarea",
    placeholder: "请详细描述RDM的定义、为什么被禁止，以及具体的违规例子...",
    keywords: ["随机", "死亡", "匹配", "角色扮演", "铺垫", "互动", "暴力", "禁止"],
    category: "基础规则理解"
  },
  {
    id: 2,
    question: "什么是元游戏（Metagaming）？请说明为什么在硬核RP中被严格禁止。",
    type: "textarea",
    placeholder: "解释元游戏的概念，举例说明违规行为，以及对RP环境的影响...",
    keywords: ["元游戏", "游戏外", "信息", "直播", "论坛", "外部", "角色", "知识"],
    category: "基础规则理解"
  },
  {
    id: 3,
    question: "当你的角色面临持枪威胁时，应该如何反应？请解释Fear RP（恐惧RP）的概念。",
    type: "textarea",
    placeholder: "描述Fear RP的重要性，角色应该如何反应，以及违反Fear RP的后果...",
    keywords: ["恐惧", "生命", "价值", "威胁", "配合", "现实", "求生", "本能"],
    category: "角色扮演理念"
  },
  {
    id: 4,
    question: "什么是NLR（New Life Rule）？当你的角色死亡重生后，应该遵循什么规则？",
    type: "textarea",
    placeholder: "解释NLR的含义，重生后的记忆规则，以及相关的行为限制...",
    keywords: ["新生命", "规则", "死亡", "重生", "记忆", "遗忘", "恩怨", "地点"],
    category: "基础规则理解"
  },
  {
    id: 5,
    question: "在硬核RP中，如何正确地进行犯罪活动？请说明抢劫的基本流程和注意事项。",
    type: "textarea",
    placeholder: "描述犯罪RP的要求，抢劫前的准备，与受害者的互动方式...",
    keywords: ["犯罪", "抢劫", "互动", "角色扮演", "铺垫", "合理", "过程", "注意事项"],
    category: "情景处理能力"
  },
  {
    id: 6,
    question: "如果在RP过程中遇到其他玩家违规（如RDM、元游戏等），你应该如何处理？",
    type: "textarea",
    placeholder: "说明正确的举报流程，是否应该在游戏内处理，以及如何保护RP环境...",
    keywords: ["违规", "举报", "流程", "游戏外", "OOC", "完成", "场景", "证据"],
    category: "社区规范理解"
  },
  {
    id: 7,
    question: "什么是Powergaming？请举例说明哪些行为属于Powergaming。",
    type: "textarea",
    placeholder: "解释Powergaming的定义，列举具体的违规例子，说明为什么被禁止...",
    keywords: ["强力游戏", "强制", "控制", "超能力", "机制", "阻挡", "不公平", "优势"],
    category: "基础规则理解"
  },
  {
    id: 8,
    question: "在帮派RP中，如何正确处理帮派冲突？请说明帮派战争的基本规则。",
    type: "textarea",
    placeholder: "描述帮派冲突的升级过程，战争规则，人数限制等...",
    keywords: ["帮派", "冲突", "战争", "规则", "人数", "限制", "会议", "解决"],
    category: "情景处理能力"
  },
  {
    id: 9,
    question: "请描述一个你认为高质量的RP场景，说明为什么这个场景体现了优秀的角色扮演。（这是一道写作题，不是简答题）",
    type: "textarea",
    placeholder: "详细描述一个RP场景，分析其中的优秀元素，说明学习价值...",
    keywords: ["高质量", "场景", "角色扮演", "互动", "创意", "沉浸", "体验", "优秀"],
    category: "RP理解深度"
  },
  {
    id: 10,
    question: "请介绍您在其他RP服务器的游戏经历，包括参与过哪些服务器、扮演过什么角色、有什么印象深刻的RP体验？如果没有经验，请说明您对硬核RP的期待。",
    type: "textarea",
    placeholder: "详细描述您的RP经历，包括服务器名称、角色背景、参与的活动等，或者说明您对硬核RP的理解和期待...",
    keywords: ["经历", "服务器", "角色", "体验", "期待", "背景", "活动", "印象"],
    category: "个人经历与期待"
  },
  {
    id: 11,
    question: "我们的服务器理念是\"游玩体验高于扮演，但同时基于扮演\"。请谈谈您对这句话的理解，以及您是否认同这个理念？",
    type: "textarea",
    placeholder: "解释您对这个理念的理解，说明游戏体验与角色扮演的关系，表达您的看法和认同程度...",
    keywords: ["游玩体验", "扮演", "理念", "理解", "认同", "平衡", "关系", "看法"],
    category: "服务器理念认知"
  },
  {
    id: 12,
    question: "在您看来，角色扮演游戏的核心魅力是什么？为什么人们愿意花费大量时间和精力在虚拟角色上？请结合您的理解谈谈RP的意义。",
    type: "textarea",
    placeholder: "从心理需求、社交价值、创造性表达等角度分析RP的魅力和意义...",
    keywords: ["核心魅力", "虚拟角色", "时间投入", "意义", "价值", "需求", "表达", "社交"],
    category: "RP本质理解"
  }
];

// 音效系统
const whitelistSounds = {
  questionChange: new Tone.Synth({
    oscillator: { type: 'sine' },
    envelope: { attack: 0.01, decay: 0.1, sustain: 0, release: 0.2 },
    volume: -28
  }).toDestination(),
  
  typing: new Tone.Synth({
    oscillator: { type: 'triangle' },
    envelope: { attack: 0.005, decay: 0.05, sustain: 0, release: 0.1 },
    volume: -35
  }).toDestination(),
  
  validation: new Tone.Synth({
    oscillator: { type: 'square' },
    envelope: { attack: 0.01, decay: 0.1, sustain: 0, release: 0.2 },
    volume: -30
  }).toDestination()
};

const playQuestionChangeSound = () => {
  Tone.start();
  whitelistSounds.questionChange.triggerAttackRelease('C5', 0.1);
};

const playTypingSound = () => {
  Tone.start();
  whitelistSounds.typing.triggerAttackRelease('A4', 0.05);
};

const playValidationSound = (isValid: boolean) => {
  Tone.start();
  if (isValid) {
    whitelistSounds.validation.triggerAttackRelease('G5', 0.1);
  } else {
    whitelistSounds.validation.triggerAttackRelease('D4', 0.1);
  }
};

// 添加数据大小检测和分片传送工具函数
const DATA_SIZE_LIMIT = 400 * 1024; // 400KB 安全限制
const CHUNK_SIZE = 300 * 1024; // 每个分片300KB

// 计算数据大小（以字节为单位）
function calculateDataSize(data: any): number {
  const jsonString = JSON.stringify(data);
  return new TextEncoder().encode(jsonString).length;
}

// 分片数据
function chunkData(data: any, chunkSizeInBytes: number): string[] {
  const jsonString = JSON.stringify(data);
  const chunks: string[] = [];
  
  // 按字符数分片，而不是字节数，以避免UTF-8编码问题
  const averageCharSize = new TextEncoder().encode(jsonString).length / jsonString.length;
  const chunkSizeInChars = Math.floor(chunkSizeInBytes / averageCharSize);
  
  for (let i = 0; i < jsonString.length; i += chunkSizeInChars) {
    chunks.push(jsonString.slice(i, i + chunkSizeInChars));
  }
  
  return chunks;
}

// 显示状态提示的函数
const showStatusAlert = (type: 'error' | 'warning' | 'info', title: string, message: string) => {
  statusModalType.value = type;
  statusModalTitle.value = title;
  statusModalMessage.value = message;
  showStatusModal.value = true;
  
  // 播放对应的音效
  if (type === 'error') {
    props.soundActions?.playErrorSound();
  } else {
    props.soundActions?.playNotificationSound();
  }
};

// 关闭状态提示
const closeStatusModal = () => {
  showStatusModal.value = false;
  statusModalType.value = 'error';
  statusModalTitle.value = '';
  statusModalMessage.value = '';
};

// 确认对话框相关函数
const showConfirmDialog = (title, message, callback) => {
  confirmTitle.value = title;
  confirmMessage.value = message;
  confirmCallback.value = callback;
  showConfirmModal.value = true;
};

const handleConfirm = () => {
  if (confirmCallback.value) {
    confirmCallback.value();
  }
  closeConfirmModal();
};

const closeConfirmModal = () => {
  showConfirmModal.value = false;
  confirmTitle.value = '';
  confirmMessage.value = '';
  confirmCallback.value = null;
};

// 分片传送数据
async function sendChunkedData(applicationData: any): Promise<void> {
  try {
    isUsingChunkedTransfer.value = true;
    chunkProgress.value = 0;
    
    const chunks = chunkData(applicationData, CHUNK_SIZE);
    totalChunks.value = chunks.length;
    
    console.log(`开始分片传送，总共 ${chunks.length} 个分片`);
    
    // 生成传输ID
    const transferId = Date.now().toString() + '-' + Math.random().toString(36).substr(2, 9);
    
    // 发送开始信号
    events.emitServer(AuthEvents.toServer.whitelistChunkStart, {
      transferId,
      totalChunks: chunks.length,
      accountId: applicationData.accountId
    });
    
    // 等待服务器确认开始
    await new Promise(resolve => setTimeout(resolve, 100));
    
    // 发送每个分片
    for (let i = 0; i < chunks.length; i++) {
      console.log(`发送第 ${i + 1}/${chunks.length} 个分片`);
      
      events.emitServer(AuthEvents.toServer.whitelistChunk, {
        transferId,
        chunkIndex: i,
        totalChunks: chunks.length,
        data: chunks[i]
      });
      
      chunkProgress.value = ((i + 1) / chunks.length) * 100;
      
      // 分片之间添加小延迟，避免服务器过载
      if (i < chunks.length - 1) {
        await new Promise(resolve => setTimeout(resolve, 50));
      }
    }
    
    // 发送结束信号
    events.emitServer(AuthEvents.toServer.whitelistChunkEnd, {
      transferId,
      totalChunks: chunks.length
    });
    
    console.log('分片传送完成');
    
  } catch (error) {
    console.error('分片传送失败:', error);
    props.soundActions?.playErrorSound();
    showStatusAlert('error', '数据传送失败', '请稍后重试。');
    isSubmitting.value = false;
    isUsingChunkedTransfer.value = false;
    throw error;
  }
}

const currentQuestion = computed(() => whitelistQuestions[currentQuestionIndex.value]);
const progress = computed(() => ((currentQuestionIndex.value + 1) / whitelistQuestions.length) * 100);
const isLastQuestion = computed(() => currentQuestionIndex.value === whitelistQuestions.length - 1);
const canProceed = computed(() => {
  const answer = answers.value[currentQuestionIndex.value] || '';
  return answer.trim().length >= 50;
});

const updateAnswer = (value: string) => {
  answers.value[currentQuestionIndex.value] = value;
  playTypingSound();
};

const handleTextareaInput = (event: Event) => {
  const target = event.target as HTMLTextAreaElement;
  updateAnswer(target.value);
};

const nextQuestion = () => {
  if (!canProceed.value) {
    playValidationSound(false);
    props.soundActions?.playErrorSound();
    return;
  }
  
  if (currentQuestionIndex.value < whitelistQuestions.length - 1) {
    currentQuestionIndex.value++;
    playQuestionChangeSound();
    props.soundActions?.playNotificationSound();
  }
};

const previousQuestion = () => {
  if (currentQuestionIndex.value > 0) {
    currentQuestionIndex.value--;
    playQuestionChangeSound();
  }
};

const submitApplication = async () => {
  // 检查当前问题是否满足要求
  if (!canProceed.value) {
    playValidationSound(false);
    props.soundActions?.playErrorSound();
    return;
  }
  
  // 检查是否有账户ID
  if (!props.accountId) {
    playValidationSound(false);
    props.soundActions?.playErrorSound();
    accountErrorMessage.value = '缺少账户信息，请重新登录后再申请白名单。';
    showAccountError.value = true;
    return;
  }
  
  // 检查所有问题是否都已回答且满足50字符要求
  let hasIncompleteAnswers = false;
  const incompleteQuestions = [];
  
  for (let i = 0; i < whitelistQuestions.length; i++) {
    const answer = answers.value[i] || '';
    if (answer.trim().length < 50) {
      hasIncompleteAnswers = true;
      incompleteQuestions.push(i + 1);
    }
  }
  
  if (hasIncompleteAnswers) {
    playValidationSound(false);
    props.soundActions?.playErrorSound();
    showStatusAlert('error', '未完成的问题', `请完成所有问题的回答（至少50个字符）。未完成的问题：第 ${incompleteQuestions.join(', ')} 题`);
    return;
  }
  
  isSubmitting.value = true;
  
  // 构建提交数据
  const applicationData = {
    answers: whitelistQuestions.map((question, index) => ({
      questionId: question.id,
      question: question.question,
      answer: answers.value[index] || '',
      category: question.category
    })),
    submittedAt: new Date().toISOString(),
    accountId: props.accountId
  };
  
  console.log('提交白名单申请数据:', applicationData);
  
  try {
    // 检测数据大小
    const dataSize = calculateDataSize(applicationData);
    console.log(`白名单申请数据大小: ${(dataSize / 1024).toFixed(2)} KB`);
    
    if (dataSize > DATA_SIZE_LIMIT) {
      console.log('数据大小超过限制，使用分片传送');
      await sendChunkedData(applicationData);
    } else {
      console.log('数据大小正常，使用普通传送');
      isUsingChunkedTransfer.value = false;
      // 发送到服务器进行审核
      events.emitServer(AuthEvents.toServer.submitWhitelist, applicationData);
      console.log('白名单申请已发送到服务器');
    }
    
    if (!isUsingChunkedTransfer.value) {
      props.soundActions?.playLoginSuccessSound();
    }
  } catch (error) {
    console.error('提交白名单申请失败:', error);
    props.soundActions?.playErrorSound();
    showStatusAlert('error', '提交失败', '请稍后重试。');
  } finally {
    if (!isUsingChunkedTransfer.value) {
      isSubmitting.value = false;
    }
  }
};

const handleWhitelistResult = (result: { approved: boolean; reason?: string }) => {
  console.log('收到白名单结果:', result);
  
  // 重置分片传送状态
  isSubmitting.value = false;
  isUsingChunkedTransfer.value = false;
  chunkProgress.value = 0;
  totalChunks.value = 0;
  
  showResults.value = true;
  isApproved.value = result.approved;
  
  if (result.approved) {
    // 申请已通过，直接进入游戏
    console.log('白名单申请已通过');
    props.soundActions?.playLoginSuccessSound();
    window.dispatchEvent(new CustomEvent('auth:whitelist:approved'));
  } else {
    // 申请提交成功但等待审核，或者其他消息
    rejectionReason.value = result.reason || '未知原因';
    
    // 检查是否是账户识别问题
    if (result.reason?.includes('无法确定您的账户身份') || result.reason?.includes('重新登录')) {
      // 如果是账户识别问题，提供返回登录的选项
      console.log('账户识别问题，需要重新登录');
      props.soundActions?.playErrorSound();
      
      // 显示特殊的错误处理界面
      setTimeout(() => {
        showConfirmDialog('账户识别问题', '检测到账户识别问题，是否返回登录界面重新登录？', () => {
          window.dispatchEvent(new CustomEvent('auth:return:login'));
        });
      }, 2000);
    } else {
      console.log('白名单申请状态:', result.reason);
      props.soundActions?.playNotificationSound();
    }
  }
};

const handleApproved = () => {
  // 发射事件通知父组件白名单已通过
  window.dispatchEvent(new CustomEvent('auth:whitelist:approved'));
};

const restartApplication = () => {
  currentQuestionIndex.value = 0;
  answers.value = [];
  showResults.value = false;
  isApproved.value = false;
  rejectionReason.value = '';
};

// 监控accountId变化
watch(() => props.accountId, (newAccountId, oldAccountId) => {
  console.log('[AuthWhitelist.vue] accountId变化:', { 
    old: String(oldAccountId), 
    new: String(newAccountId),
    oldType: typeof oldAccountId,
    newType: typeof newAccountId
  });
}, { immediate: true });

onMounted(async () => {
  await Tone.start();
  events.on(AuthEvents.fromServer.whitelistResult, handleWhitelistResult);
  
  // 监听分片接收确认
  events.on(AuthEvents.fromServer.whitelistChunkReceived, (data: { success: boolean; message?: string }) => {
    if (data.success) {
      console.log('分片传送完成，服务器确认接收');
      props.soundActions?.playLoginSuccessSound();
    } else {
      console.error('分片传送失败:', data.message);
      props.soundActions?.playErrorSound();
      showStatusAlert('error', '数据传送失败', data.message || '未知错误');
    }
    
    isSubmitting.value = false;
    isUsingChunkedTransfer.value = false;
    chunkProgress.value = 0;
    totalChunks.value = 0;
  });
  
  console.log('白名单组件挂载，接收到的accountId:', props.accountId);
});
</script>

<template>
  <div class="whitelist-container">
    <!-- 自定义状态提示模态框 -->
    <div v-if="showStatusModal" class="status-modal-overlay">
      <div class="overlay-background" @click="closeStatusModal"></div>
      <div class="status-modal-panel" :class="statusModalType">
        <div class="status-modal-header">
          <div class="status-icon">
            <i v-if="statusModalType === 'error'" class="fas fa-exclamation-circle"></i>
            <i v-else-if="statusModalType === 'warning'" class="fas fa-exclamation-triangle"></i>
            <i v-else class="fas fa-info-circle"></i>
          </div>
          <h3>{{ statusModalTitle }}</h3>
          <button class="status-close-button" @click="closeStatusModal">
            <i class="fas fa-times"></i>
          </button>
        </div>
        <div class="status-modal-content">
          <p>{{ statusModalMessage }}</p>
        </div>
        <div class="status-modal-actions">
          <button class="status-confirm-button" @click="handleConfirm">
            <i class="fas fa-check"></i>
            <span>确定</span>
          </button>
        </div>
      </div>
    </div>

    <!-- 确认对话框 -->
    <div v-if="showConfirmModal" class="confirm-modal-overlay">
      <div class="overlay-background" @click="closeConfirmModal"></div>
      <div class="confirm-modal-panel">
        <div class="confirm-modal-header">
          <div class="confirm-icon">
            <i class="fas fa-question-circle"></i>
          </div>
          <h3>{{ confirmTitle }}</h3>
          <button class="confirm-close-button" @click="closeConfirmModal">
            <i class="fas fa-times"></i>
          </button>
        </div>
        <div class="confirm-modal-content">
          <p>{{ confirmMessage }}</p>
        </div>
        <div class="confirm-modal-actions">
          <button class="confirm-cancel-button" @click="closeConfirmModal">
            <i class="fas fa-times"></i>
            <span>取消</span>
          </button>
          <button class="confirm-ok-button" @click="handleConfirm">
            <i class="fas fa-check"></i>
            <span>确定</span>
          </button>
        </div>
      </div>
    </div>

    <!-- 拒绝信息显示 -->
    <div v-if="showRejectionInfo" class="rejection-info-section">
      <div class="rejection-card">
        <div class="rejection-icon">
          <i class="fas fa-exclamation-triangle"></i>
        </div>
        <div class="rejection-content">
          <h3>白名单申请被拒绝</h3>
          <p class="rejection-main-message">{{ rejectionMessage }}</p>
          <div v-if="rejectionReason" class="rejection-reason-detail">
            <h4>拒绝原因：</h4>
            <p>{{ rejectionReason }}</p>
          </div>
          <p class="rejection-guide">请仔细阅读拒绝原因，重新填写申请表格。</p>
          <button 
            class="dismiss-rejection-btn" 
            @click="showRejectionInfo = false"
            @mouseenter="props.soundActions?.playHoverSound"
          >
            <i class="fas fa-check"></i>
            <span>我已了解，重新申请</span>
          </button>
        </div>
      </div>
    </div>
    
    <!-- 账户错误信息显示 -->
    <div v-if="showAccountError" class="account-error-section">
      <div class="account-error-card">
        <div class="account-error-icon">
          <i class="fas fa-user-times"></i>
        </div>
        <div class="account-error-content">
          <h3>账户识别问题</h3>
          <p class="account-error-message">{{ accountErrorMessage }}</p>
          <div class="account-error-detail">
            <h4>可能的原因：</h4>
            <ul>
              <li>登录会话已过期或异常</li>
              <li>网络连接不稳定导致数据丢失</li>
              <li>系统暂时无法识别您的账户信息</li>
            </ul>
            <h4>建议解决方案：</h4>
            <ol>
              <li>退出当前界面，重新登录您的账户</li>
              <li>确保网络连接稳定</li>
              <li>登录成功后再次尝试提交白名单申请</li>
            </ol>
          </div>
          <div class="account-error-actions">
            <button 
              class="return-login-btn" 
              @click="emits('returnToLogin')"
              @mouseenter="props.soundActions?.playHoverSound"
            >
              <i class="fas fa-sign-in-alt"></i>
              <span>返回登录</span>
            </button>
            <button 
              class="dismiss-error-btn" 
              @click="showAccountError = false"
              @mouseenter="props.soundActions?.playHoverSound"
            >
              <i class="fas fa-times"></i>
              <span>继续尝试</span>
            </button>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 结果显示 -->
    <div v-else-if="showResults" class="results-section">
      <div v-if="isApproved" class="success-result">
        <div class="result-icon">
          <i class="fas fa-check-circle"></i>
        </div>
        <h3>白名单申请通过！</h3>
        <p>恭喜您通过了白名单审核，现在可以进入服务器开始游戏了。</p>
        <button class="continue-button" @click="handleApproved">
          <i class="fas fa-play"></i>
          <span>进入服务器</span>
        </button>
      </div>
      
      <div v-else class="pending-result">
        <div class="result-icon">
          <i v-if="rejectionReason.includes('无法确定您的账户身份') || rejectionReason.includes('重新登录')" class="fas fa-user-times"></i>
          <i v-else class="fas fa-clock"></i>
        </div>
        <h3 v-if="rejectionReason.includes('无法确定您的账户身份') || rejectionReason.includes('重新登录')">账户识别问题</h3>
        <h3 v-else>申请已提交</h3>
        <p class="submission-message">{{ rejectionReason }}</p>
        
        <!-- 账户识别错误的特殊处理 -->
        <div v-if="rejectionReason.includes('无法确定您的账户身份') || rejectionReason.includes('重新登录')" class="account-error-box">
          <div class="error-icon">
            <i class="fas fa-exclamation-triangle"></i>
          </div>
          <div class="error-info">
            <h4>🔍 账户识别问题</h4>
            <p>系统无法准确识别您的账户信息，这可能是因为：</p>
            <ul>
              <li>您使用的是较老的账户，缺少部分身份信息</li>
              <li>当前会话状态异常</li>
              <li>网络连接不稳定</li>
            </ul>
            <p><strong>建议解决方案：</strong></p>
            <ol>
              <li>退出当前界面，重新登录您的账户</li>
              <li>确保使用正确的邮箱和密码</li>
              <li>登录成功后再次尝试提交白名单申请</li>
            </ol>
          </div>
        </div>
        
        <!-- 正常的邮件通知信息 -->
        <div v-else class="email-notification-box">
          <div class="email-icon">
            <i class="fas fa-envelope-open"></i>
          </div>
          <div class="email-info">
            <h4>📧 邮件通知已启用</h4>
            <p>审核结果将自动发送至您的注册邮箱，请注意查收。同时请留意垃圾邮件文件夹。</p>
          </div>
        </div>
        
        <div class="action-buttons">
          <button v-if="rejectionReason.includes('无法确定您的账户身份') || rejectionReason.includes('重新登录')" 
                  class="login-button" 
                  @click="emits('returnToLogin')"
                  @mouseenter="props.soundActions?.playHoverSound">
            <i class="fas fa-sign-in-alt"></i>
            <span>返回登录</span>
          </button>
          <button v-else class="retry-button" @click="restartApplication">
            <i class="fas fa-redo"></i>
            <span>重新申请</span>
          </button>
        </div>
      </div>
    </div>
    
    <!-- 问题页面 -->
    <div v-else class="question-section">
      <div class="whitelist-header">
        <h2 class="whitelist-title">硬核RP白名单申请</h2>
        <p class="whitelist-subtitle">请认真回答以下问题，展示您对硬核角色扮演的理解</p>
      </div>
      
      <!-- 进度条 -->
      <div class="progress-section">
        <div class="progress-bar">
          <div class="progress-fill" :style="{ width: progress + '%' }"></div>
        </div>
        <div class="progress-text">
          问题 {{ currentQuestionIndex + 1 }} / {{ whitelistQuestions.length }}
          ({{ Math.round(progress) }}%)
        </div>
      </div>
      
      <!-- 分片传送进度显示 -->
      <div v-if="isUsingChunkedTransfer" class="chunk-progress-section">
        <div class="chunk-progress-header">
          <i class="fas fa-upload"></i>
          <h4>正在分片传送大数据...</h4>
        </div>
        <div class="chunk-progress-bar">
          <div class="chunk-progress-fill" :style="{ width: chunkProgress + '%' }"></div>
        </div>
        <div class="chunk-progress-info">
          <span>进度: {{ Math.round(chunkProgress) }}%</span>
          <span>总分片数: {{ totalChunks }}</span>
        </div>
        <div class="chunk-progress-tip">
          <i class="fas fa-info-circle"></i>
          <span>数据较大，正在分片传送以确保传输稳定性，请耐心等待...</span>
        </div>
      </div>
      
      <!-- 当前问题 -->
      <div class="question-card">
        <div class="question-header">
          <div class="question-category">{{ currentQuestion.category }}</div>
          <div class="question-number">Q{{ currentQuestion.id }}</div>
        </div>
        
        <div class="question-content">
          <h3 class="question-text">{{ currentQuestion.question }}</h3>
          
          <div class="answer-section">
            <textarea
              v-model="answers[currentQuestionIndex]"
              @input="handleTextareaInput($event)"
              @focus="props.soundActions?.playHoverSound"
              :placeholder="currentQuestion.placeholder"
              class="answer-textarea"
              rows="8"
            ></textarea>
            
            <div class="answer-info">
              <div class="char-count" :class="{ 'valid': canProceed, 'warning': (answers[currentQuestionIndex] || '').length > 0 && (answers[currentQuestionIndex] || '').length < 50 }">
                {{ (answers[currentQuestionIndex] || '').length }} / 50 字符
              </div>
              <div v-if="!canProceed" class="requirement-hint">
                <i class="fas fa-info-circle"></i>
                {{ (answers[currentQuestionIndex] || '').length === 0 ? '请填写回答内容' : `还需要 ${50 - (answers[currentQuestionIndex] || '').length} 个字符` }}
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 导航按钮 -->
      <div class="navigation-section">
        <button
          v-if="currentQuestionIndex > 0"
          class="nav-button prev-button"
          @click="previousQuestion"
          @mouseenter="props.soundActions?.playHoverSound"
        >
          <i class="fas fa-chevron-left"></i>
          <span>上一题</span>
        </button>
        
        <div class="nav-spacer"></div>
        
        <button
          v-if="!isLastQuestion"
          class="nav-button next-button"
          :class="{ 'disabled': !canProceed }"
          :disabled="!canProceed"
          @click="nextQuestion"
          @mouseenter="canProceed ? props.soundActions?.playHoverSound() : null"
        >
          <span>下一题</span>
          <i class="fas fa-chevron-right"></i>
        </button>
        
        <button
          v-else
          class="nav-button submit-button"
          :class="{ 'disabled': !canProceed || isSubmitting }"
          :disabled="!canProceed || isSubmitting"
          @click="submitApplication"
          @mouseenter="canProceed ? props.soundActions?.playHoverSound() : null"
        >
          <i v-if="isSubmitting" class="fas fa-spinner fa-spin"></i>
          <i v-else class="fas fa-paper-plane"></i>
          <span v-if="isUsingChunkedTransfer">{{ `分片传送中... ${Math.round(chunkProgress)}%` }}</span>
          <span v-else>{{ isSubmitting ? '提交中...' : '提交申请' }}</span>
        </button>
      </div>
      
      <!-- 提示信息 -->
      <div class="tips-section">
        <div class="tip-card">
          <i class="fas fa-lightbulb"></i>
          <div class="tip-content">
            <h4>回答提示</h4>
            <ul>
              <li>请用自己的话详细回答，避免复制粘贴</li>
              <li>每个问题至少需要回答50个字符</li>
              <li>结合具体例子说明您的理解</li>
              <li>展示您对硬核RP的深度认知</li>
              <li>诚实回答，我们重视真实的理解而非标准答案</li>
            </ul>
          </div>
        </div>
        
        <div class="notification-info">
          <i class="fas fa-envelope"></i>
          <div class="notification-content">
            <h4>📧 审核通知</h4>
            <p>提交申请后，请耐心等待管理员审核。审核结果将通过以下方式通知：</p>
            <ul>
              <li>📧 邮件通知（发送至注册邮箱）</li>
              <li>🔔 游戏内消息提醒</li>
              <li>💬 QQ群内通知公告</li>
            </ul>
            <p class="review-time">⏰ 预计审核时间：24小时内</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.whitelist-container {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  max-height: 100%;
  margin: 0;
  padding: 1.5vh;
  box-sizing: border-box;
  overflow-y: auto;
}

.whitelist-header {
  text-align: center;
  margin-bottom: 3vh;
  flex-shrink: 0;
}

.whitelist-title {
  font-size: 2.8vh;
  font-weight: 600;
  color: #ffffff;
  margin-bottom: 0.8vh;
}

.whitelist-subtitle {
  font-size: 1.6vh;
  color: rgba(255, 255, 255, 0.7);
  margin: 0;
}

.progress-section {
  margin-bottom: 3vh;
  flex-shrink: 0;
}

.progress-bar {
  width: 100%;
  height: 1vh;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 0.5vh;
  overflow: hidden;
  margin-bottom: 1vh;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #64ffda 0%, #4fd1c7 100%);
  border-radius: 0.5vh;
  transition: width 0.3s ease;
}

.progress-text {
  text-align: center;
  font-size: 1.4vh;
  color: rgba(255, 255, 255, 0.8);
  font-weight: 500;
}

.question-card {
  background: linear-gradient(135deg, rgba(26, 26, 46, 0.9) 0%, rgba(16, 16, 32, 0.9) 100%);
  border: 0.1vh solid rgba(100, 255, 218, 0.3);
  border-radius: 1.2vh;
  padding: 2.5vh;
  margin-bottom: 2vh;
  box-shadow: 0 0.8vh 3.2vh rgba(0, 0, 0, 0.3);
  flex-shrink: 0;
  min-height: 35vh;
}

.question-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2.5vh;
}

.question-category {
  background: rgba(100, 255, 218, 0.2);
  color: #64ffda;
  padding: 0.8vh 1.5vh;
  border-radius: 2vh;
  font-size: 1.2vh;
  font-weight: 600;
}

.question-number {
  background: rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.8);
  padding: 0.8vh 1.5vh;
  border-radius: 2vh;
  font-size: 1.2vh;
  font-weight: 600;
}

.question-text {
  font-size: 1.8vh;
  font-weight: 600;
  color: #ffffff;
  line-height: 1.6;
  margin-bottom: 2.5vh;
}

.answer-textarea {
  width: 100%;
  min-height: 18vh;
  padding: 2vh;
  border: 0.1vh solid rgba(255, 255, 255, 0.2);
  border-radius: 1vh;
  background: rgba(0, 0, 0, 0.3);
  color: #ffffff;
  font-size: 1.3vh;
  line-height: 1.6;
  resize: vertical;
  transition: all 0.3s ease;
  font-family: inherit;
  box-sizing: border-box;
}

.answer-textarea:focus {
  outline: none;
  border-color: #64ffda;
  box-shadow: 0 0 0 0.2vh rgba(100, 255, 218, 0.2);
}

.answer-textarea::placeholder {
  color: rgba(255, 255, 255, 0.5);
}

.answer-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 1.5vh;
}

.char-count {
  font-size: 1.2vh;
  color: rgba(255, 255, 255, 0.6);
  transition: color 0.3s ease;
}

.char-count.valid {
  color: #64ffda;
}

.char-count.warning {
  color: #ff9800;
}

.requirement-hint {
  display: flex;
  align-items: center;
  gap: 0.8vh;
  font-size: 1.2vh;
  color: #ff9800;
}

.navigation-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2.5vh;
  flex-shrink: 0;
}

.nav-spacer {
  flex: 1;
}

.nav-button {
  display: flex;
  align-items: center;
  gap: 1vh;
  padding: 1.5vh 2.5vh;
  border: none;
  border-radius: 1vh;
  font-size: 1.3vh;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 12vh;
  justify-content: center;
}

.prev-button {
  background: rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.8);
}

.prev-button:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-0.2vh);
}

.next-button, .submit-button {
  background: linear-gradient(135deg, #64ffda 0%, #4fd1c7 100%);
  color: #1a1a2e;
}

.next-button:hover, .submit-button:hover {
  transform: translateY(-0.2vh);
  box-shadow: 0 0.8vh 2.5vh rgba(100, 255, 218, 0.4);
}

.nav-button.disabled {
  background: rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.4);
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.tips-section {
  margin-top: 2vh;
  display: flex;
  flex-direction: column;
  gap: 1.5vh;
}

.tip-card, .notification-info {
  display: flex;
  gap: 2vh;
  padding: 2vh;
  background: rgba(100, 255, 218, 0.1);
  border: 0.1vh solid rgba(100, 255, 218, 0.3);
  border-radius: 1vh;
}

.tip-card i, .notification-info i {
  color: #64ffda;
  font-size: 2vh;
  margin-top: 0.2vh;
  flex-shrink: 0;
}

.tip-content h4, .notification-content h4 {
  color: #64ffda;
  font-size: 1.6vh;
  margin-bottom: 1vh;
}

.tip-content ul, .notification-content ul {
  margin: 1vh 0;
  padding-left: 2.5vh;
  color: rgba(255, 255, 255, 0.8);
}

.tip-content li, .notification-content li {
  margin-bottom: 0.6vh;
  font-size: 1.3vh;
}

.notification-content p {
  color: rgba(255, 255, 255, 0.8);
  font-size: 1.3vh;
  line-height: 1.5;
  margin: 1vh 0;
}

.review-time {
  color: #ffa726 !important;
  font-weight: 500;
  margin-top: 1.5vh !important;
}

.results-section {
  text-align: center;
  padding: 4vh 2vh;
  min-height: 50vh;
  display: flex;
  align-items: center;
  justify-content: center;
}

.success-result, .pending-result {
  max-width: 50vh;
  margin: 0 auto;
  min-width: 400px;
}

.result-icon {
  font-size: 6vh;
  margin-bottom: 2.5vh;
}

.success-result h3 {
  color: #4caf50;
  font-size: 2.8vh;
  margin-bottom: 2vh;
}

.pending-result h3 {
  color: #f44336;
  font-size: 2.8vh;
  margin-bottom: 2vh;
}

.success-result p, .pending-result p {
  color: rgba(255, 255, 255, 0.8);
  font-size: 1.6vh;
  line-height: 1.6;
  margin-bottom: 1.5vh;
}

.submission-message {
  background: rgba(244, 67, 54, 0.1);
  border: 0.1vh solid rgba(244, 67, 54, 0.3);
  border-radius: 1vh;
  padding: 2vh;
  margin: 2.5vh 0;
  text-align: left;
  font-size: 1.4vh;
}

.email-notification-box {
  background: rgba(100, 255, 218, 0.1);
  border: 0.1vh solid rgba(100, 255, 218, 0.3);
  border-radius: 1vh;
  padding: 2vh;
  margin: 2.5vh 0;
  display: flex;
  align-items: flex-start;
  gap: 2vh;
}

.email-icon {
  flex-shrink: 0;
}

.email-icon i {
  font-size: 3vh;
  color: #64ffda;
}

.email-info {
  flex: 1;
}

.email-info h4 {
  color: #64ffda;
  font-size: 1.6vh;
  margin-bottom: 1vh;
}

.email-info p {
  color: rgba(255, 255, 255, 0.8);
  font-size: 1.3vh;
  line-height: 1.5;
  margin: 1vh 0;
}

.action-buttons {
  margin-top: 2.5vh;
}

.continue-button, .retry-button, .login-button {
  display: inline-flex;
  align-items: center;
  gap: 1.2vh;
  padding: 2vh 4vh;
  border: none;
  border-radius: 1vh;
  font-size: 1.6vh;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 15vh;
  justify-content: center;
}

.continue-button {
  background: linear-gradient(135deg, #4caf50 0%, #45a049 100%);
  color: white;
}

.retry-button {
  background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%);
  color: white;
}

.login-button {
  background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
  color: white;
}

.continue-button:hover, .retry-button:hover, .login-button:hover {
  transform: translateY(-0.3vh);
  box-shadow: 0 0.8vh 2.5vh rgba(0, 0, 0, 0.3);
}

.rejection-info-section {
  margin-bottom: 3vh;
}

.rejection-card {
  background: rgba(244, 67, 54, 0.1);
  border: 0.1vh solid rgba(244, 67, 54, 0.3);
  border-radius: 1.5vh;
  padding: 3vh;
  display: flex;
  gap: 2.5vh;
  animation: rejectionSlideIn 0.5s ease-out;
  min-height: 20vh;
}

.rejection-icon {
  flex-shrink: 0;
}

.rejection-icon i {
  font-size: 3vh;
  color: #f44336;
}

.rejection-content {
  flex: 1;
}

.rejection-content h3 {
  color: #f44336;
  font-size: 2vh;
  margin-bottom: 1.5vh;
  font-weight: 600;
}

.rejection-main-message {
  color: rgba(255, 255, 255, 0.9);
  font-size: 1.6vh;
  margin-bottom: 2vh;
  line-height: 1.5;
}

.rejection-reason-detail {
  background: rgba(244, 67, 54, 0.15);
  border: 0.1vh solid rgba(244, 67, 54, 0.2);
  border-radius: 1vh;
  padding: 2vh;
  margin: 2vh 0;
}

.rejection-reason-detail h4 {
  color: #f44336;
  font-size: 1.4vh;
  margin-bottom: 1vh;
  font-weight: 600;
}

.rejection-reason-detail p {
  color: rgba(255, 255, 255, 0.8);
  font-size: 1.3vh;
  margin: 0;
  line-height: 1.4;
}

.rejection-guide {
  color: rgba(255, 255, 255, 0.7);
  font-size: 1.3vh;
  margin: 2vh 0;
  font-style: italic;
}

.dismiss-rejection-btn {
  display: inline-flex;
  align-items: center;
  gap: 1vh;
  padding: 1.5vh 2.5vh;
  background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%);
  color: white;
  border: none;
  border-radius: 1vh;
  font-size: 1.3vh;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.dismiss-rejection-btn:hover {
  transform: translateY(-0.3vh);
  box-shadow: 0 0.8vh 2.5vh rgba(255, 152, 0, 0.4);
}

.account-error-box {
  background: rgba(255, 193, 7, 0.1);
  border: 0.1vh solid rgba(255, 193, 7, 0.3);
  border-radius: 1vh;
  padding: 2vh;
  margin: 2.5vh 0;
  display: flex;
  align-items: flex-start;
  gap: 2vh;
}

.error-icon {
  flex-shrink: 0;
}

.error-icon i {
  font-size: 3vh;
  color: #ffc107;
}

.error-info {
  flex: 1;
}

.error-info h4 {
  color: #ffc107;
  font-size: 1.6vh;
  margin-bottom: 1vh;
}

.error-info p {
  color: rgba(255, 255, 255, 0.8);
  font-size: 1.3vh;
  line-height: 1.5;
  margin: 1vh 0;
}

.error-info ul, .error-info ol {
  margin: 1vh 0;
  padding-left: 2.5vh;
  color: rgba(255, 255, 255, 0.8);
}

.error-info li {
  margin-bottom: 0.6vh;
  font-size: 1.3vh;
}

.account-error-section {
  margin-bottom: 3vh;
}

.account-error-card {
  background: rgba(255, 193, 7, 0.1);
  border: 0.1vh solid rgba(255, 193, 7, 0.3);
  border-radius: 1.5vh;
  padding: 3vh;
  display: flex;
  gap: 2.5vh;
  animation: rejectionSlideIn 0.5s ease-out;
  min-height: 25vh;
}

.account-error-icon {
  flex-shrink: 0;
}

.account-error-icon i {
  font-size: 3vh;
  color: #ffc107;
}

.account-error-content {
  flex: 1;
}

.account-error-content h3 {
  color: #ffc107;
  font-size: 2vh;
  margin-bottom: 1.5vh;
  font-weight: 600;
}

.account-error-message {
  color: rgba(255, 255, 255, 0.9);
  font-size: 1.6vh;
  margin-bottom: 2vh;
  line-height: 1.5;
}

.account-error-detail {
  background: rgba(255, 193, 7, 0.15);
  border: 0.1vh solid rgba(255, 193, 7, 0.2);
  border-radius: 1vh;
  padding: 2vh;
  margin: 2vh 0;
}

.account-error-detail h4 {
  color: #ffc107;
  font-size: 1.4vh;
  margin-bottom: 1vh;
  font-weight: 600;
}

.account-error-detail ul {
  margin: 1vh 0;
  padding-left: 2.5vh;
  color: rgba(255, 255, 255, 0.8);
}

.account-error-detail li {
  margin-bottom: 0.6vh;
  font-size: 1.3vh;
}

.account-error-actions {
  margin-top: 2.5vh;
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 1.5vh;
}

.return-login-btn, .dismiss-error-btn {
  display: inline-flex;
  align-items: center;
  gap: 1vh;
  padding: 1.5vh 2.5vh;
  border: none;
  border-radius: 1vh;
  font-size: 1.3vh;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  flex: 1;
  justify-content: center;
}

.return-login-btn {
  background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
  color: white;
}

.return-login-btn:hover {
  transform: translateY(-0.3vh);
  box-shadow: 0 0.8vh 2.5vh rgba(0, 123, 255, 0.4);
}

.dismiss-error-btn {
  background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%);
  color: white;
}

.dismiss-error-btn:hover {
  transform: translateY(-0.3vh);
  box-shadow: 0 0.8vh 2.5vh rgba(255, 152, 0, 0.4);
}

/* 美化滚动条 */
.whitelist-container::-webkit-scrollbar {
  width: 0.6vw;
}

.whitelist-container::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 0.4vh;
  margin: 0.4vh 0;
}

.whitelist-container::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #64ffda 0%, #4fd1c7 100%);
  border-radius: 0.4vh;
  transition: background 0.3s ease;
}

.whitelist-container::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #4fd1c7 0%, #64ffda 100%);
  box-shadow: 0 0.2vh 0.8vh rgba(100, 255, 218, 0.3);
}

.whitelist-container::-webkit-scrollbar-thumb:active {
  background: linear-gradient(135deg, #3fb8af 0%, #4fd1c7 100%);
}

/* 为textarea也添加美化滚动条 */
.answer-textarea::-webkit-scrollbar {
  width: 0.45vw;
}

.answer-textarea::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 0.3vh;
}

.answer-textarea::-webkit-scrollbar-thumb {
  background: rgba(100, 255, 218, 0.6);
  border-radius: 0.3vh;
  transition: background 0.3s ease;
}

.answer-textarea::-webkit-scrollbar-thumb:hover {
  background: rgba(100, 255, 218, 0.8);
}

/* Firefox滚动条样式 */
.whitelist-container {
  scrollbar-width: thin;
  scrollbar-color: #64ffda rgba(255, 255, 255, 0.1);
}

.answer-textarea {
  scrollbar-width: thin;
  scrollbar-color: rgba(100, 255, 218, 0.6) rgba(255, 255, 255, 0.1);
}

/* 分片传送进度样式 */
.chunk-progress-section {
  background: linear-gradient(135deg, rgba(100, 255, 218, 0.1) 0%, rgba(79, 209, 199, 0.1) 100%);
  border: 0.1vh solid rgba(100, 255, 218, 0.3);
  border-radius: 1.5vh;
  padding: 2.5vh;
  margin: 2.5vh 0;
  animation: chunkProgressSlideIn 0.3s ease-out;
}

.chunk-progress-header {
  display: flex;
  align-items: center;
  gap: 1.5vh;
  margin-bottom: 2vh;
}

.chunk-progress-header i {
  color: #64ffda;
  font-size: 2vh;
  animation: spin 2s linear infinite;
}

.chunk-progress-header h4 {
  color: #64ffda;
  font-size: 1.6vh;
  margin: 0;
  font-weight: 600;
}

.chunk-progress-bar {
  width: 100%;
  height: 1.5vh;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 0.8vh;
  overflow: hidden;
  margin-bottom: 1.5vh;
  position: relative;
}

.chunk-progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #64ffda 0%, #4fd1c7 50%, #64ffda 100%);
  border-radius: 0.8vh;
  transition: width 0.3s ease;
  position: relative;
  overflow: hidden;
}

.chunk-progress-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.2vh;
  color: rgba(255, 255, 255, 0.8);
  font-size: 1vw;
  font-weight: 500;
}

.chunk-progress-tip {
  display: flex;
  align-items: center;
  gap: 0.8vw;
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.9vw;
  padding: 0.8vh 1.2vw;
  background: rgba(100, 255, 218, 0.05);
  border-radius: 0.6vh;
  border: 0.1vw solid rgba(100, 255, 218, 0.1);
}

.chunk-progress-tip i {
  color: #64ffda;
  font-size: 1vw;
}

/* 自定义状态提示模态框样式 */
.status-modal-overlay {
  position: fixed;
  inset: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10001;
  animation: fadeIn 0.3s ease;
}

.overlay-background {
  position: absolute;
  inset: 0;
  background: rgba(0, 0, 0, 0.7);
}

.status-modal-panel {
  position: relative;
  width: 90%;
  max-width: 37.5vw;
  background: rgba(20, 25, 40, 0.98);
  border-radius: 1.6vh;
  border: 0.1vw solid;
  overflow: hidden;
  box-shadow: 
    0 1vh 4vh rgba(0, 0, 0, 0.5),
    0 0 2vh rgba(100, 255, 218, 0.1);
  animation: popIn 0.3s ease;
  z-index: 1;
}

.status-modal-panel.error {
  border-color: rgba(244, 67, 54, 0.5);
  box-shadow: 
    0 1vh 4vh rgba(0, 0, 0, 0.5),
    0 0 2vh rgba(244, 67, 54, 0.2);
}

.status-modal-panel.warning {
  border-color: rgba(255, 193, 7, 0.5);
  box-shadow: 
    0 1vh 4vh rgba(0, 0, 0, 0.5),
    0 0 2vh rgba(255, 193, 7, 0.2);
}

.status-modal-panel.info {
  border-color: rgba(33, 150, 243, 0.5);
  box-shadow: 
    0 1vh 4vh rgba(0, 0, 0, 0.5),
    0 0 2vh rgba(33, 150, 243, 0.2);
}

@keyframes popIn {
  from { transform: scale(0.95); opacity: 0; }
  to { transform: scale(1); opacity: 1; }
}

.status-modal-header {
  display: flex;
  align-items: center;
  gap: 1.6vw;
  padding: 2vh 2.4vw;
  border-bottom: 0.1vw solid rgba(255, 255, 255, 0.1);
  position: relative;
}

.status-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 3.6vw;
  height: 4.8vh;
  border-radius: 50%;
  font-size: 1.8vw;
  flex-shrink: 0;
}

.status-modal-panel.error .status-icon {
  background: rgba(244, 67, 54, 0.2);
  color: #f44336;
  border: 0.15vw solid rgba(244, 67, 54, 0.3);
}

.status-modal-panel.warning .status-icon {
  background: rgba(255, 193, 7, 0.2);
  color: #ffc107;
  border: 0.15vw solid rgba(255, 193, 7, 0.3);
}

.status-modal-panel.info .status-icon {
  background: rgba(33, 150, 243, 0.2);
  color: #2196f3;
  border: 0.15vw solid rgba(33, 150, 243, 0.3);
}

.status-modal-header h3 {
  color: #fff;
  font-size: 1.5vw;
  font-weight: 600;
  margin: 0;
  flex: 1;
}

.status-close-button {
  position: absolute;
  right: 1.2vw;
  top: 50%;
  transform: translateY(-50%);
  background: rgba(255, 255, 255, 0.1);
  border: 0.1vw solid rgba(255, 255, 255, 0.2);
  width: 2.4vw;
  height: 3.2vh;
  border-radius: 50%;
  color: rgba(255, 255, 255, 0.7);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  font-size: 1vw;
}

.status-close-button:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.4);
  color: #fff;
  transform: translateY(-50%) rotate(90deg);
}

.status-modal-content {
  padding: 2vh 2.4vw;
}

.status-modal-content p {
  color: rgba(255, 255, 255, 0.9);
  font-size: 1.2vw;
  line-height: 1.6;
  margin: 0;
  word-wrap: break-word;
}

.status-modal-actions {
  padding: 1.6vh 2.4vw 2.4vh;
  display: flex;
  justify-content: flex-end;
}

.status-confirm-button {
  display: flex;
  align-items: center;
  gap: 0.8vw;
  padding: 1.2vh 2.4vw;
  border: none;
  border-radius: 0.8vh;
  font-size: 1vw;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  background: linear-gradient(135deg, #64ffda 0%, #4fd1c7 100%);
  color: #1a1a2e;
}

.status-confirm-button:hover {
  transform: translateY(-0.2vh);
  box-shadow: 0 0.6vh 2vh rgba(100, 255, 218, 0.4);
}

.status-confirm-button:active {
  transform: translateY(0);
}


/* 确认对话框样式 */
.confirm-modal-overlay {
  position: fixed;
  inset: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10002; /* 比状态模态框更高的层级 */
  animation: fadeIn 0.3s ease;
}

.confirm-modal-panel {
  position: relative;
  width: 90%;
  max-width: 33.75vw;
  background: rgba(20, 25, 40, 0.98);
  border-radius: 1.6vh;
  border: 0.1vw solid rgba(255, 193, 7, 0.5);
  overflow: hidden;
  box-shadow: 
    0 1vh 4vh rgba(0, 0, 0, 0.6),
    0 0 2vh rgba(255, 193, 7, 0.2);
  animation: popIn 0.3s ease;
  z-index: 1;
}

.confirm-modal-header {
  display: flex;
  align-items: center;
  gap: 1.6vw;
  padding: 2vh 2.4vw;
  border-bottom: 0.1vw solid rgba(255, 255, 255, 0.1);
  position: relative;
}

.confirm-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 3.6vw;
  height: 4.8vh;
  border-radius: 50%;
  background: rgba(255, 193, 7, 0.2);
  color: #ffc107;
  border: 0.15vw solid rgba(255, 193, 7, 0.3);
  font-size: 1.8vw;
  flex-shrink: 0;
}

.confirm-modal-header h3 {
  color: #fff;
  font-size: 1.5vw;
  font-weight: 600;
  margin: 0;
  flex: 1;
}

.confirm-close-button {
  position: absolute;
  right: 1.2vw;
  top: 50%;
  transform: translateY(-50%);
  background: rgba(255, 255, 255, 0.1);
  border: 0.1vw solid rgba(255, 255, 255, 0.2);
  width: 2.4vw;
  height: 3.2vh;
  border-radius: 50%;
  color: rgba(255, 255, 255, 0.7);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  font-size: 1vw;
}

.confirm-close-button:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.4);
  color: #fff;
  transform: translateY(-50%) rotate(90deg);
}

.confirm-modal-content {
  padding: 2vh 2.4vw;
}

.confirm-modal-content p {
  color: rgba(255, 255, 255, 0.9);
  font-size: 1.2vw;
  line-height: 1.6;
  margin: 0;
  text-align: center;
  word-wrap: break-word;
}

.confirm-modal-actions {
  padding: 1.6vh 2.4vw 2.4vh;
  display: flex;
  justify-content: space-between;
  gap: 1.6vw;
}

.confirm-cancel-button, .confirm-ok-button {
  display: flex;
  align-items: center;
  gap: 0.8vw;
  padding: 1.2vh 2.4vw;
  border: none;
  border-radius: 0.8vh;
  font-size: 1vw;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  flex: 1;
  justify-content: center;
}

.confirm-cancel-button {
  background: rgba(255, 255, 255, 0.1);
  border: 0.1vw solid rgba(255, 255, 255, 0.2);
  color: rgba(255, 255, 255, 0.8);
}

.confirm-cancel-button:hover {
  background: rgba(255, 255, 255, 0.15);
  border-color: rgba(255, 255, 255, 0.3);
  color: #fff;
  transform: translateY(-0.1vh);
}

.confirm-ok-button {
  background: linear-gradient(135deg, #ffc107 0%, #ff9800 100%);
  color: #1a1a2e;
  border: 0.1vw solid rgba(255, 193, 7, 0.3);
}

.confirm-ok-button:hover {
  transform: translateY(-0.2vh);
  box-shadow: 0 0.6vh 2vh rgba(255, 193, 7, 0.4);
}

.confirm-ok-button:active, .confirm-cancel-button:active {
  transform: translateY(0);
}


/* 添加缺失的动画关键帧 */
@keyframes overlayFadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes containerSlideIn {
  from { 
    opacity: 0; 
    transform: scale(0.9) translateY(-3vh); 
  }
  to { 
    opacity: 1; 
    transform: scale(1) translateY(0); 
  }
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes popIn {
  from { transform: scale(0.95); opacity: 0; }
  to { transform: scale(1); opacity: 1; }
}

@keyframes rejectionSlideIn {
  from {
    opacity: 0;
    transform: translateY(-2vh);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes chunkProgressSlideIn {
  from {
    opacity: 0;
    transform: translateY(-1vh);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes shimmer {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

@keyframes iconPulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

/* 分片进度条动画效果 */
.chunk-progress-fill::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  animation: shimmer 1.5s infinite;
}

/* Firefox滚动条样式 */
.whitelist-container {
  scrollbar-width: thin;
  scrollbar-color: #64ffda rgba(255, 255, 255, 0.1);
}

.answer-textarea {
  scrollbar-width: thin;
  scrollbar-color: rgba(100, 255, 218, 0.6) rgba(255, 255, 255, 0.1);
}

</style> 