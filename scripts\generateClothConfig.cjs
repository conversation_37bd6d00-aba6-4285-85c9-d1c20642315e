const fs = require('fs');
const path = require('path');

// 部件ID映射
const COMPONENT_IDS = {
    'boybag': 5,     // 包/手部配件
    'boyuppr': 11,   // 上衣
    'boylowr': 4,    // 下装
    'boyshoe': 6,    // 鞋子
    'boymask': 1,    // 面具
    'boyneak': 7,    // 脖子配饰
    'boyuniform': 11, // 制服(作为上衣)
    'boywatch': -1,  // 手表(道具，特殊处理)
    'boyhair': 2,    // 头发
};

// 文件前缀到组件ID的映射
const FILE_PREFIX_TO_COMPONENT = {
    'jbib': 11,    // 上衣
    'lowr': 4,     // 下装
    'feet': 6,     // 鞋子
    'hand': 5,     // 手套/包
    'berd': 1,     // 面具/胡须
    'hair': 2,     // 头发
    'accs': 7,     // 脖子配饰
    'task': 8,     // 任务配饰
    'decl': 9,     // 贴花
    'teeth': 10,   // 牙齿
};

// 道具前缀到道具ID的映射
const PROP_PREFIX_TO_ID = {
    'p_head': 0,   // 帽子
    'p_eyes': 1,   // 眼镜
    'p_ears': 2,   // 耳环
    'p_mouth': 3,  // 嘴部配饰
    'p_lhand': 4,  // 左手
    'p_rhand': 5,  // 右手
    'p_lwrist': 6, // 左手腕
    'p_rwrist': 7, // 右手腕
};

// 扫描mod文件夹
const MOD_BASE_PATH = './resources/mods/cloth';

function extractDrawableFromFilename(filename) {
    // 从文件名中提取drawable编号
    // 例如: jbib_026_u.ydd -> 26, hand_030_u.ydd -> 30, p_head_025.ydd -> 25
    let match = filename.match(/_(\d{3})_u\.ydd$/);
    if (match) {
        return parseInt(match[1], 10); // 去掉前导零
    }
    
    // 处理道具文件: p_head_025.ydd -> 25
    match = filename.match(/^p_\w+_(\d{3})\.ydd$/);
    if (match) {
        return parseInt(match[1], 10);
    }
    
    return null;
}

function extractTextureFromFilename(filename) {
    // 从纹理文件名中提取texture编号
    // 例如: jbib_diff_026_a_uni.ytd -> a=0, b=1, c=2...
    let match = filename.match(/_diff_\d{3}_([a-z])_uni\.ytd$/);
    if (match) {
        return match[1].charCodeAt(0) - 97; // a=0, b=1, c=2...
    }
    
    // 处理道具纹理: p_head_diff_025_a.ytd -> a=0
    match = filename.match(/^p_\w+_diff_\d{3}_([a-z])\.ytd$/);
    if (match) {
        return match[1].charCodeAt(0) - 97;
    }
    
    return null;
}

function scanModFolder(folderPath) {
    const items = [];
    const componentName = path.basename(folderPath);
    
    // 根据文件夹名称确定性别
    // boy* = 男性 (sex: 1), girl* = 女性 (sex: 0)
    const sex = componentName.toLowerCase().startsWith('boy') ? 1 : 0;
    
    const streamPath = path.join(folderPath, 'stream');
    if (!fs.existsSync(streamPath)) {
        console.log(`Stream folder not found: ${streamPath}`);
        return items;
    }

    // 查找meta文件并解析dlc名称
    const metaFiles = fs.readdirSync(streamPath).filter(f => f.endsWith('.meta'));
    let dlcName = componentName; // 默认值
    
    if (metaFiles.length > 0) {
        const metaPath = path.join(streamPath, metaFiles[0]);
        try {
            const xmlContent = fs.readFileSync(metaPath, 'utf8');
            const dlcMatch = xmlContent.match(/<dlcName>([^<]+)<\/dlcName>/);
            if (dlcMatch) {
                dlcName = dlcMatch[1];
            }
        } catch (error) {
            console.error(`Error reading meta file: ${error.message}`);
        }
    }

    // 查找rpf文件夹
    const rpfFolders = fs.readdirSync(streamPath).filter(f => {
        const fullPath = path.join(streamPath, f);
        return fs.statSync(fullPath).isDirectory() && f.endsWith('.rpf');
    });

    for (const rpfFolder of rpfFolders) {
        const rpfPath = path.join(streamPath, rpfFolder);
        const subFolders = fs.readdirSync(rpfPath).filter(f => {
            const fullPath = path.join(rpfPath, f);
            return fs.statSync(fullPath).isDirectory();
        });

        for (const subFolder of subFolders) {
            const clothPath = path.join(rpfPath, subFolder);
            const files = fs.readdirSync(clothPath);
            
            // 查找所有模型文件（包括服装和道具）
            const modelFiles = files.filter(f => f.endsWith('.ydd'));
            
            for (const modelFile of modelFiles) {
                const drawable = extractDrawableFromFilename(modelFile);
                if (drawable === null) continue;

                let componentId, isProp;
                
                // 判断是服装组件还是道具
                if (modelFile.startsWith('p_')) {
                    // 这是道具
                    const propPrefix = modelFile.split('_').slice(0, 2).join('_'); // p_head, p_eyes等
                    componentId = PROP_PREFIX_TO_ID[propPrefix];
                    isProp = true;
                    
                    if (componentId === undefined) {
                        console.log(`Unknown prop type: ${propPrefix} in ${modelFile}`);
                        continue;
                    }
                } else {
                    // 这是服装组件
                    const prefix = modelFile.split('_')[0]; // jbib, lowr, feet等
                    componentId = FILE_PREFIX_TO_COMPONENT[prefix];
                    isProp = false;
                    
                    if (componentId === undefined) {
                        console.log(`Unknown component type: ${prefix} in ${modelFile}`);
                        continue;
                    }
                }

                // 查找对应的纹理文件
                const prefix = modelFile.split('_')[0];
                let texturePattern;
                
                if (isProp) {
                    // 道具纹理模式: p_head_diff_025_a.ytd
                    const propPrefix = modelFile.split('_').slice(0, 2).join('_');
                    texturePattern = new RegExp(`^${propPrefix}_diff_${drawable.toString().padStart(3, '0')}_[a-z]\\.ytd$`);
                } else {
                    // 服装纹理模式: jbib_diff_026_a_uni.ytd
                    texturePattern = new RegExp(`^${prefix}_diff_${drawable.toString().padStart(3, '0')}_[a-z]_\\w+\\.ytd$`);
                }
                
                const textureFiles = files.filter(f => texturePattern.test(f));

                if (textureFiles.length === 0) {
                    // 没有纹理文件，创建一个默认的
                    items.push({
                        sex: sex,
                        dlc: dlcName,
                        texture: 0,
                        id: componentId,
                        drawable: drawable,
                        isProp: isProp,
                    });
                } else {
                    // 为每个纹理文件创建一个配置项
                    for (const textureFile of textureFiles) {
                        const texture = extractTextureFromFilename(textureFile);
                        if (texture !== null) {
                            items.push({
                                sex: sex,
                                dlc: dlcName,
                                texture: texture,
                                id: componentId,
                                drawable: drawable,
                                isProp: isProp,
                            });
                        }
                    }
                }
            }
        }
    }

    return items;
}

function generateConfig(specificFolder = null) {
    const allItems = [];
    
    let foldersToProcess;
    
    if (specificFolder) {
        // 处理特定文件夹
        const folderPath = path.join(MOD_BASE_PATH, specificFolder);
        if (!fs.existsSync(folderPath)) {
            console.error(`❌ Folder not found: ${folderPath}`);
            return;
        }
        foldersToProcess = [specificFolder];
        console.log(`Processing specific folder: ${specificFolder}`);
    } else {
        // 扫描所有mod文件夹
        foldersToProcess = fs.readdirSync(MOD_BASE_PATH).filter(f => {
            const fullPath = path.join(MOD_BASE_PATH, f);
            return fs.statSync(fullPath).isDirectory() && f.startsWith('boy');
        });
        console.log(`Found ${foldersToProcess.length} mod folders:`, foldersToProcess);
    }

    for (const folder of foldersToProcess) {
        const folderPath = path.join(MOD_BASE_PATH, folder);
        const sex = folder.toLowerCase().startsWith('boy') ? 1 : 0;
        const sexLabel = sex === 1 ? 'Male' : 'Female';
        console.log(`\nProcessing: ${folder} (${sexLabel})`);
        
        const items = scanModFolder(folderPath);
        console.log(`  Generated ${items.length} items`);
        
        // 显示找到的组件类型
        const componentTypes = {};
        for (const item of items) {
            const key = item.isProp ? `Prop ${item.id}` : `Component ${item.id}`;
            componentTypes[key] = (componentTypes[key] || 0) + 1;
        }
        
        if (Object.keys(componentTypes).length > 0) {
            console.log('  Component types found:');
            for (const [type, count] of Object.entries(componentTypes)) {
                console.log(`    ${type}: ${count} items`);
            }
        }
        
        allItems.push(...items);
    }

    if (allItems.length === 0) {
        console.log('⚠️  No items generated');
        return;
    }

    // 生成TypeScript配置文件
    const configContent = `// Auto-generated cloth configuration
// Generated on: ${new Date().toISOString()}
// Source folders: ${foldersToProcess.join(', ')}

export const ModClothes = ${JSON.stringify(allItems, null, 4)};
`;

    // 写入文件
    const outputPath = './src/plugins/cloth/shared/addclothes.ts';
    fs.writeFileSync(outputPath, configContent, 'utf8');
    
    console.log(`\n✅ Generated ${allItems.length} items total`);
    console.log(`📁 Config saved to: ${outputPath}`);
    
    // 显示统计信息
    const stats = {};
    const propStats = {};
    
    for (const item of allItems) {
        if (item.isProp) {
            const key = `Prop ${item.id}`;
            propStats[key] = (propStats[key] || 0) + 1;
        } else {
            const key = `Component ${item.id}`;
            stats[key] = (stats[key] || 0) + 1;
        }
    }
    
    console.log('\n📊 Statistics:');
    if (Object.keys(stats).length > 0) {
        console.log('  Components:');
        for (const [key, count] of Object.entries(stats)) {
            console.log(`    ${key}: ${count} items`);
        }
    }
    
    if (Object.keys(propStats).length > 0) {
        console.log('  Props:');
        for (const [key, count] of Object.entries(propStats)) {
            console.log(`    ${key}: ${count} items`);
        }
    }
}

// 运行脚本
if (require.main === module) {
    // 检查命令行参数
    const args = process.argv.slice(2);
    const specificFolder = args[0];
    
    if (specificFolder) {
        console.log(`🎯 Targeting specific folder: ${specificFolder}`);
        generateConfig(specificFolder);
    } else {
        console.log('🔍 Processing all folders');
        generateConfig();
    }
}

module.exports = { generateConfig }; 