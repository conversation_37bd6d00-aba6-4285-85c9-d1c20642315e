<template>
  <div class="notify-overlay">
    <!-- 统一通知区域 - 中央下方 -->
    <div class="notify-container">
      <transition-group name="notify" tag="div" class="notify-list">
        <div
          v-for="notification in allNotifications"
          :key="notification.id"
          :class="['notify-item', notification.category, notification.category === 'currency' ? notification.operation : notification.type]"
          @animationend="handleAnimationEnd(notification.id)"
        >
          <!-- 物品通知内容 -->
          <template v-if="notification.category === 'item'">
            <div class="quantity-display">{{ notification.type === 'gain' ? '+' : '-' }}{{ notification.quantity }}</div>
            <div class="icon-container">
              <img :src="getIconPath(notification.icon)" :alt="notification.name" class="item-icon" />
            </div>
            <div class="name-display">{{ notification.name }}</div>
          </template>

          <!-- 货币通知内容 -->
          <template v-else-if="notification.category === 'currency'">
            <div class="quantity-display" :style="{ color: notification.color }">
              {{ getCurrencyAmountPrefix(notification.operation) }}{{ formatCurrencyAmount(notification.amount) }}
            </div>
            <div class="icon-container">
              <div class="currency-icon">{{ notification.icon }}</div>
            </div>
            <div class="item-name">{{ notification.title }}</div>
          </template>
        </div>
      </transition-group>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, onBeforeUnmount, computed } from 'vue';
import { useEvents } from '@Composables/useEvents.js';
import { findIcon } from '../shared/findicon.js';

interface BaseNotification {
  id: string;
  timestamp: number;
  category: 'item' | 'currency';
}

interface ItemNotification extends BaseNotification {
  category: 'item';
  name: string;
  icon: string;
  quantity: number;
  type: 'gain' | 'lose';
}

interface CurrencyNotification extends BaseNotification {
  category: 'currency';
  type: 'cash' | 'bank' | 'points';
  operation: 'add' | 'sub' | 'set';
  amount: number;
  newAmount: number;
  icon: string;
  title: string;
  color: string;
}

type UnifiedNotification = ItemNotification | CurrencyNotification;

const event = useEvents();
const notifications = ref<UnifiedNotification[]>([]);
let notificationCounter = 0;

// 计算属性：按时间排序的所有通知（新通知在底部）
const allNotifications = computed(() => {
  return notifications.value.slice().sort((a, b) => a.timestamp - b.timestamp);
});

// 获取图标路径
const getIconPath = (iconName: string): string => {
  return findIcon(iconName);
};

// 获取货币金额前缀
const getCurrencyAmountPrefix = (operation: string): string => {
  switch (operation) {
    case 'add':
      return '+';
    case 'sub':
      return '-';
    case 'set':
      return '';
    default:
      return '';
  }
};

// 格式化货币金额显示
const formatCurrencyAmount = (amount: number): string => {
  return amount.toLocaleString();
};

// 添加物品通知
const addItemNotification = (name: string, icon: string, quantity: number, type: 'gain' | 'lose') => {
  const notification: ItemNotification = {
    id: `item-${++notificationCounter}-${Date.now()}`,
    category: 'item',
    name,
    icon,
    quantity,
    type,
    timestamp: Date.now()
  };

  notifications.value.push(notification);

  // 限制同时显示的通知数量（最多6个）
  if (notifications.value.length > 6) {
    notifications.value.shift();
  }

  // 自动移除通知（3秒后）
  setTimeout(() => {
    removeNotification(notification.id);
  }, 3000);
};

// 添加货币通知
const addCurrencyNotification = (data: {
  type: 'cash' | 'bank' | 'points';
  operation: 'add' | 'sub' | 'set';
  amount: number;
  newAmount: number;
  icon: string;
  title: string;
  color: string;
}) => {
  const notification: CurrencyNotification = {
    id: `currency-${++notificationCounter}-${Date.now()}`,
    category: 'currency',
    type: data.type,
    operation: data.operation,
    amount: data.amount,
    newAmount: data.newAmount,
    icon: data.icon,
    title: data.title,
    color: data.color,
    timestamp: Date.now()
  };

  notifications.value.push(notification);

  // 限制同时显示的通知数量（最多6个）
  if (notifications.value.length > 6) {
    notifications.value.shift();
  }

  // 自动移除通知（4秒后）
  setTimeout(() => {
    removeNotification(notification.id);
  }, 4000);
};

// 移除通知
const removeNotification = (id: string) => {
  const index = notifications.value.findIndex(item => item.id === id);
  if (index > -1) {
    notifications.value.splice(index, 1);
  }
};

// 处理动画结束
const handleAnimationEnd = (id: string) => {
  // 动画结束后可以进行清理
};

// 监听物品变化事件
event.on('inventory:itemGained', (data: { name: string; icon: string; quantity: number }) => {
  addItemNotification(data.name, data.icon, data.quantity, 'gain');
});

event.on('inventory:itemLost', (data: { name: string; icon: string; quantity: number }) => {
  addItemNotification(data.name, data.icon, data.quantity, 'lose');
});

// 监听货币变化事件
event.on('currency:notification', (data: {
  type: 'cash' | 'bank' | 'points';
  operation: 'add' | 'sub' | 'set';
  amount: number;
  newAmount: number;
  icon: string;
  title: string;
  color: string;
}) => {
  addCurrencyNotification(data);
});

onMounted(() => {
  // 组件挂载完成
  /*
  console.log('[NotifyGlobal] 统一通知系统已启动');
  
  // 添加测试数据，方便前端预览
  setTimeout(() => {
    addItemNotification('苹果', 'apple.webp', 5, 'gain');
  }, 1000);
  
  setTimeout(() => {
    addCurrencyNotification({
      type: 'cash',
      operation: 'add',
      amount: 500,
      newAmount: 1500,
      icon: '💵',
      title: '现金获得',
      color: '#00ff88'
    });
  }, 2000);
  
  setTimeout(() => {
    addItemNotification('钢锭', 'steel.webp', 10, 'gain');
  }, 3000);
  
  setTimeout(() => {
    addCurrencyNotification({
      type: 'bank',
      operation: 'sub',
      amount: 1000,
      newAmount: 9000,
      icon: '💳',
      title: '银行支出',
      color: '#ff6b6b'
    });
  }, 4000);*/
});

onBeforeUnmount(() => {
  // 清理所有通知
  notifications.value = [];
  console.log('[NotifyGlobal] 统一通知系统已关闭');
});
</script>

<style scoped>
/* 统一通知覆盖层 */
.notify-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  pointer-events: none;
  z-index: 99999;
  display: flex;
  align-items: flex-end;
  justify-content: center;
  padding-bottom: 15vh;
}

/* 通知容器 */
.notify-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: auto;
  pointer-events: none;
}

.notify-list {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.8vh;
}

/* 统一通知项样式 */
.notify-item {
  width: 5.5vw;
  height: 9vh;
  background: rgba(30, 41, 59, 0.8);
  border: 0.05vh solid rgba(255, 255, 255, 0.08);
  border-radius: 0.6vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.25s ease;
  position: relative;
  overflow: hidden;
  pointer-events: none;
  box-shadow: 0 0.4vh 0.8vh rgba(0, 0, 0, 0.2), 0 0 0.4vh rgba(100, 255, 218, 0.05);
}

/* 物品通知类型样式 */
.notify-item.item.gain {
  border-color: rgba(34, 197, 94, 0.6);
}

.notify-item.item.lose {
  border-color: rgba(239, 68, 68, 0.6);
}

/* 货币通知类型样式 - 与物品通知背景一致 */
.notify-item.currency.add {
  border-color: rgba(34, 197, 94, 0.6);
}

.notify-item.currency.sub {
  border-color: rgba(239, 68, 68, 0.6);
}

.notify-item.currency.set {
  border-color: rgba(116, 192, 252, 0.6);
}

/* 数量/金额显示 */
.quantity-display {
  position: absolute;
  top: 0.5vh;
  right: 0.1vw;
  font-size: 1vh;
  font-weight: 600;
  color: #ffffff;
  background: rgba(0, 0, 0, 0.7);
  padding: 0.2vh 0.5vw;
  border-radius: 0.3vh;
  pointer-events: none;
  z-index: 10;
}

/* 物品数量颜色 */
.notify-item.item.gain .quantity-display {
  color: rgba(34, 197, 94, 0.9);
}

.notify-item.item.lose .quantity-display {
  color: rgba(239, 68, 68, 0.9);
}

/* 图标容器 */
.icon-container {
  display: flex;
  align-items: center;
  justify-content: center;
  flex: 1;
  padding: 0.5vh 0.5vw;
  z-index: 1;
  pointer-events: none;
}

/* 物品图标 */
.item-icon {
  width: 6.5vh;
  height: 6.5vh;
  object-fit: contain;
  filter: drop-shadow(0 0.2vh 0.5vh rgba(0, 0, 0, 0.4));
  transition: transform 0.2s ease;
  pointer-events: none;
}

/* 货币图标 */
.currency-icon {
  font-size: 4vh;
  filter: drop-shadow(0 0.2vh 0.5vh rgba(0, 0, 0, 0.4));
  transition: transform 0.2s ease;
  pointer-events: none;
}

/* 统一名称显示样式 */
.name-display,
.item-name {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 0.6vh 0.4vw;
  font-size: 0.9vh;
  font-weight: 400;
  color: rgba(255, 255, 255, 0.9);
  text-align: center;
  background: linear-gradient(to top, rgba(0, 0, 0, 0.8), rgba(0, 0, 0, 0.4));
  border-top: 0.1vh solid rgba(100, 255, 218, 0.15);
  line-height: 1.3;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  letter-spacing: 0.02em;
  transition: all 0.3s ease;
  pointer-events: none;
  z-index: 10;
}

/* 悬浮效果 */
.notify-item:hover .item-icon,
.notify-item:hover .currency-icon {
  transform: scale(1.03);
}

.notify-item:hover .name-display,
.notify-item:hover .item-name {
  color: #ffffff;
  background: linear-gradient(to top, rgba(20, 25, 40, 0.9), rgba(20, 25, 40, 0.5));
  border-top-color: rgba(100, 255, 218, 0.3);
}

/* 通知动画 */
.notify-enter-active {
  animation: slideIn 0.4s cubic-bezier(0.16, 1, 0.3, 1);
}

.notify-leave-active {
  animation: fadeOut 0.3s ease-in;
}

@keyframes slideIn {
  0% {
    opacity: 0;
    transform: translateY(6vh) scale(0.8);
  }
  60% {
    opacity: 0.9;
    transform: translateY(-0.5vh) scale(1.02);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes fadeOut {
  0% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
  100% {
    opacity: 0;
    transform: translateY(1vh) scale(0.95);
  }
}

/* 过渡效果 */
.notify-move {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.notify-enter-active,
.notify-leave-active {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.notify-enter-from {
  opacity: 0;
  transform: translateY(3vh);
}

.notify-leave-to {
  opacity: 0;
  transform: translateY(1vh);
}
</style>