import * as alt from 'alt-server';
import { useRebar } from '@Server/index.js';
import { Character } from '@Shared/types/character.js';
import { CollectionNames } from '@Server/document/shared.js';
import { stashBlips } from '@Plugins/Stash/server/stash.js';
import { Vehicle } from '@Shared/types/index.js';
import { dbobjectdata, storages } from '@Plugins/inventory/server/index.js';
import { checkisplayerthehighestrank, checkisplayerthelowestrank, taxforgoverment } from './index.js';
import { PageNames } from '@Shared/webview/index.js';
import { getFormattedDisassemblyRecipeByItem, getFormattedRecipeByOutput, getFormattedRecipesbytype } from '@Plugins/item/server/make.js';
import { trafficuniforms } from '@Plugins/beonduty/shared/config.js';
import { addvehicles } from '@Plugins/veh/shared/addvehicle.js';
import { handleDutyLogic } from '@Plugins/beonduty/server/dutyHelper.js';

const Rebar = useRebar();
const db = Rebar.database.useDatabase();
const promptbarapi = await Rebar.useApi().getAsync('promptbar-api')
const notifyapi = await Rebar.useApi().getAsync('notify-api')
const currencyapi = await Rebar.useApi().getAsync('currency-api')

const vector = new alt.Vector3(440.0176, -646.5626, 28.5048)

Rebar.controllers.useMarkerGlobal({
    pos: new alt.Vector3({
        x: 440.0176,
        y: -646.5626,
        z: 27.5048,
    }),
    type: 'CYLINDER',
    color: new alt.RGBA(255, 215, 0, 150),
    scale: new alt.Vector3(2, 2, 0.2),
});



Rebar.controllers.useBlipGlobal({
    pos: new alt.Vector3(440.05712890625, -640.5230712890625, 28.5047607421875),
    color: 43,
    sprite: 882,
    shortRange: true,
    text: '交管局',
});


const interaction = Rebar.controllers.useInteraction(
    new alt.ColshapeCylinder(vector.x, vector.y, vector.z - 1, 2, 3),
    'player',
    0
);

interaction.onEnter((player) => {
    promptbarapi.showPromptBar(player, '载具过户');
});

interaction.onLeave((player) => {
    promptbarapi.hidePromptBar(player);
});


interaction.on(async (player) => {
    const character = Rebar.document.character.useCharacter(player);
    if (!character) return;

    const characterId = character.getField('_id');
    const playervehicles = await db.getMany<Vehicle>({ owner: characterId }, CollectionNames.Vehicles).then(vehicles => vehicles.filter(v => !v.faction));

    const factionvehicles = await db.getMany<Vehicle>({ faction: character.getField('job').faction }, CollectionNames.Vehicles).then(vehicles => vehicles.filter(v => v.faction && v.canAuction));

    Rebar.player.useWebview(player).show('vehicletransfer', 'page', true)

    let isReady = await Rebar.player.useWebview(player).isReady('vehicletransfer', 'page')
    while (!isReady) {
        await alt.Utils.wait(100); // 等待100毫秒
        isReady = await Rebar.player.useWebview(player).isReady('vehicletransfer', 'page')
    }

    Rebar.player.useWebview(player).emit('vehicletransfer:sync', playervehicles, factionvehicles)

    promptbarapi.hidePromptBar(player);
});





const housestorageexits = await db.getMany<storages>({ pos: new alt.Vector3(448.1407, -628.4703, 28.5048), dimension: 0 }, 'storage');
if (housestorageexits.length === 0) {
    const housestorage = await db.create({ name: '交管局储藏点', pos: new alt.Vector3(448.1407, -628.4703, 28.5048), capacity: 500, content: [], dimension: 0, noblip: true }, 'storage');
}



const promptapi = await Rebar.useApi().getAsync('promptbar-api')

const trafficbeondutypos = new alt.Vector3(446.6374, -643.2264, 27.5048)

Rebar.controllers.useMarkerGlobal({
    pos: trafficbeondutypos,
    type: 'CYLINDER',
    color: new alt.RGBA(255, 215, 0, 150),
    scale: new alt.Vector3(2, 2, 0.2),
});



const interaction1 = Rebar.controllers.useInteraction(
    new alt.ColshapeCylinder(trafficbeondutypos.x, trafficbeondutypos.y, trafficbeondutypos.z - 1, 2, 3),
    'player',
    0
);

interaction1.onEnter((player) => {
    const playerdata = Rebar.document.character.useCharacter(player).get()
    if (playerdata.job.faction !== '交管局') {
        return;
    }
    promptapi.showPromptBar(player, '交管局上班')
});

interaction1.onLeave((player) => {
    const playerdata = Rebar.document.character.useCharacter(player).get()
    if (playerdata.job.faction !== '交管局') {
        return;
    }
    promptapi.hidePromptBar(player)
});

interaction1.on(async (player) => {
    const playerdata = Rebar.document.character.useCharacter(player).get()

    if (playerdata.job.faction !== '交管局') {
        notifyapi.shownotify(player, '你不是交管局', 'error')
        return;
    }

    // 使用统一的上班逻辑处理函数
    await handleDutyLogic(player, '交管局');

    promptapi.hidePromptBar(player)
});











alt.onClient('transfervehicle:tosomeone', async (player: alt.Player, targetPlayerId: number, vehicleplate: string) => {

    Rebar.player.useWebview(player).hide('vehicletransfer')

    const character = Rebar.document.character.useCharacter(player);
    if (!character) {
        notifyapi.shownotify(player, '你还没有角色', 'error');
        return;
    }

    const characterId = character.getField('_id');

    const targetPlayer = alt.Player.all.find(p => p.getStreamSyncedMeta('id') === targetPlayerId);
    if (!targetPlayer) {
        notifyapi.shownotify(player, '目标玩家不在线', 'error');
        return;
    }

    const targetCharacter = Rebar.document.character.useCharacter(targetPlayer);
    if (!targetCharacter) {
        notifyapi.shownotify(player, '目标玩家没有角色', 'error');
        return;
    }

    const targetCharacterId = targetCharacter.getField('_id');

    const vehicle = await db.getMany<Vehicle>({ numberPlateText: vehicleplate }, CollectionNames.Vehicles);
    if (!vehicle) {
        notifyapi.shownotify(player, '车辆不存在', 'error');
        return;
    }

    if (vehicle.length === 0) {
        notifyapi.shownotify(player, '车辆不存在', 'error');
        return;
    }

    if (vehicle[0].owner !== characterId) {
        notifyapi.shownotify(player, '你不是车辆的主人', 'error');
        return;
    }

    if (vehicle[0].faction && !vehicle[0].canAuction) {
        notifyapi.shownotify(player, '车辆属于派系，无法过户', 'error');
        return;
    }

    const targetplayercarslimit = targetCharacter.getField('vehiclesnumberlimit')
    const targetplayercarsnumber = await db.getMany<Vehicle>({ owner: targetCharacterId }, CollectionNames.Vehicles).then(vehicles => vehicles.filter(v => !v.faction));

    if (targetplayercarsnumber.length + 1 > targetplayercarslimit) {
        notifyapi.shownotify(player, '目标玩家载具数量已达上限', 'error');
        return;
    }


    let tax = Math.floor((vehicle[0].priceinshop || 0) * 0.01);

    if (Rebar.document.character.useCharacter(targetPlayer).getField('job').faction === '交管局') {
        tax = 0;
    }

    // 检查车辆是否在addvehicles列表中
    // 将addvehicles列表中的字符串转换为hash值进行对比
    const addvehicleHashes = addvehicles.map(modelName => alt.hash(modelName));
    const vehicleModel = typeof vehicle[0].model === 'string' ? alt.hash(vehicle[0].model) : vehicle[0].model;
    const isSpecialVehicle = addvehicleHashes.includes(vehicleModel);
    let creditCost = 0;
    
    if (isSpecialVehicle) {
        creditCost = 20;
        
        // 检查玩家信用点
        const accountdata = Rebar.document.account.useAccount(player).get();
        const points = Math.floor(accountdata.points || 0);
        
        if (points < creditCost) {
            notifyapi.shownotify(player, `特殊车辆转让需要 ${creditCost} 信用点，你的信用点不足`, 'error');
            return;
        }
        
        // 扣除信用点
        const newPoints = points - creditCost;
        Rebar.document.account.useAccount(player).set('points', newPoints);
        
        notifyapi.shownotify(player, `特殊车辆转让已扣除 ${creditCost} 信用点`, 'info');
    }

    const success = currencyapi.cost({ player: player }, tax);
    if (!success) {
        notifyapi.shownotify(player, '你不够钱支付过户手续费', 'error');
        return;
    }

    await taxforgoverment(tax, '载具过户手续费');

    const vehicleId = vehicle[0]._id;

    const vehilce = alt.Vehicle.all.find(v => v.numberPlateText === vehicle[0].numberPlateText);
    if (vehilce) {
        if (vehicle[0].faction && vehicle[0].canAuction) {
            Rebar.document.vehicle.useVehicle(vehilce).setBulk({
                owner: targetCharacterId,
                faction: null,
                canAuction: false,
            })
        } else {
            Rebar.document.vehicle.useVehicle(vehilce).setBulk({
                owner: targetCharacterId,
            })
        }
    }else {
        if (vehicle[0].faction && vehicle[0].canAuction) {
            await db.update({ _id: vehicleId, faction: null, owner: targetCharacterId, canAuction: false }, CollectionNames.Vehicles);
        } else {
            await db.update({ _id: vehicleId, owner: targetCharacterId }, CollectionNames.Vehicles);
        }
    }

    notifyapi.shownotify(player, `你已将${vehicle[0].numberPlateText}过户给${targetPlayer.getStreamSyncedMeta('name')}`, 'success');
    notifyapi.shownotify(targetPlayer, `你已收到${vehicle[0].numberPlateText}`, 'success');


})



alt.on('rebar:playerPageOpened', (player: alt.Player, page: PageNames) => {
    if (page === 'vehicletransfer' || page === 'make') {
        Rebar.player.useWorld(player).disableControls();
    }
});


alt.on('rebar:playerPageClosed', (player: alt.Player, page: PageNames) => {
    if (page === 'vehicletransfer' || page === 'make') {
        Rebar.player.useWorld(player).enableControls();
    }
});














const vector2 = new alt.Vector3(499.7670, -1963.4637, 24.9663)

Rebar.controllers.useMarkerGlobal({
    pos: new alt.Vector3({
        x: vector2.x,
        y: vector2.y,
        z: vector2.z - 1,
    }),
    type: 'CYLINDER',
    color: new alt.RGBA(255, 215, 0, 150),
    scale: new alt.Vector3(3, 3, 0.2),
});


const interaction2 = Rebar.controllers.useInteraction(
    new alt.ColshapeCylinder(vector2.x, vector2.y, vector2.z - 1, 3, 3),
    'player',
    0
);

interaction2.onEnter((player) => {
    promptbarapi.showPromptBar(player, '工业制造');
});

interaction2.onLeave((player) => {
    promptbarapi.hidePromptBar(player);
});


interaction2.on(async (player) => {


    Rebar.player.useWebview(player).show('make', 'page', true);

    const useInventory = await Rebar.useApi().getAsync('inventory-api');

    const inventory = await useInventory.getInventory({ player: player });

    let isReady = await Rebar.player.useWebview(player).isReady('make', 'page')
    while (!isReady) {
        await alt.Utils.wait(100); // 等待100毫秒
        isReady = await Rebar.player.useWebview(player).isReady('make', 'page')
    }
    const formattedRecipes = await getFormattedRecipesbytype(player, 'industrial');

    Rebar.player.useWebview(player).emit('make:inventoryUpdate', inventory);

    Rebar.player.useWebview(player).emit('recipeReady', formattedRecipes);
});






const blip = Rebar.controllers.useBlipGlobal({
    color: 56,
    pos: vector2,
    shortRange: true,
    sprite: 478,
    text: `工厂`,
});






const vector3 = new alt.Vector3(501.5077, -1966.4044, 24.9832)

Rebar.controllers.useMarkerGlobal({
    pos: new alt.Vector3({
        x: vector3.x,
        y: vector3.y,
        z: vector3.z - 1,
    }),
    type: 'CYLINDER',
    color: new alt.RGBA(255, 215, 0, 150),
    scale: new alt.Vector3(3, 3, 0.2),
});


const interaction3 = Rebar.controllers.useInteraction(
    new alt.ColshapeCylinder(vector3.x, vector3.y, vector3.z - 1, 3, 3),
    'player',
    0
);

interaction3.onEnter((player) => {
    promptbarapi.showPromptBar(player, '工业制造');
});

interaction3.onLeave((player) => {
    promptbarapi.hidePromptBar(player);
});


interaction3.on(async (player) => {

    Rebar.player.useWebview(player).show('make', 'page', true);

    const useInventory = await Rebar.useApi().getAsync('inventory-api');

    const inventory = await useInventory.getInventory({ player: player });

    let isReady = await Rebar.player.useWebview(player).isReady('make', 'page')
    while (!isReady) {
        await alt.Utils.wait(100); // 等待100毫秒
        isReady = await Rebar.player.useWebview(player).isReady('make', 'page')
    }
    const formattedRecipes = await getFormattedRecipesbytype(player, 'industrial');

    Rebar.player.useWebview(player).emit('make:inventoryUpdate', inventory);

    Rebar.player.useWebview(player).emit('recipeReady', formattedRecipes);
});






const vector4 = new alt.Vector3(473.3670, -1952.2417, 24.5787)

Rebar.controllers.useMarkerGlobal({
    pos: new alt.Vector3({
        x: vector4.x,
        y: vector4.y,
        z: vector4.z - 1,
    }),
    type: 'CYLINDER',
    color: new alt.RGBA(255, 215, 0, 150),
    scale: new alt.Vector3(3, 3, 0.2),
});


const interaction4 = Rebar.controllers.useInteraction(
    new alt.ColshapeCylinder(vector4.x, vector4.y, vector4.z - 1, 3, 3),
    'player',
    0
);

interaction4.onEnter((player) => {
    promptbarapi.showPromptBar(player, '回收站');
});

interaction4.onLeave((player) => {
    promptbarapi.hidePromptBar(player);
});


interaction4.on(async (player) => {

    Rebar.player.useWebview(player).show('make', 'page', true);

    const useInventory = await Rebar.useApi().getAsync('inventory-api');

    const inventory = await useInventory.getInventory({ player: player });

    let isReady = await Rebar.player.useWebview(player).isReady('make', 'page')
    while (!isReady) {
        await alt.Utils.wait(100); // 等待100毫秒
        isReady = await Rebar.player.useWebview(player).isReady('make', 'page')
    }
    const formattedRecipes = await getFormattedRecipesbytype(player, 'recycling');

    Rebar.player.useWebview(player).emit('make:inventoryUpdate', inventory);

    Rebar.player.useWebview(player).emit('recipeReady', formattedRecipes);
});






