<template>
  <div class="rent-boat-overlay">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="title-section">
          <div class="header-icon">
            <i class="fas fa-ship"></i>
          </div>
          <div class="title-info">
            <h1>租船服务</h1>
            <span class="subtitle">Boat Rental Service</span>
          </div>
        </div>
        <div class="status-info">
          <div class="status-badge active">
            <i class="fas fa-circle"></i>
            <span>营业中</span>
          </div>
          <div class="boat-count">
            <i class="fas fa-ship"></i>
            <span>{{ boatList.length }} 艘可租</span>
          </div>
        </div>
      </div>
    </div>

    <div class="content-area">
      <!-- 已租用船只卡片 -->
      <div v-if="currentRentedBoat" class="boats-card">
        <div class="card-header">
          <div class="header-title">
            <i class="fas fa-ship"></i>
            <h3>当前租用</h3>
          </div>
          <div class="boat-count-badge">
            <span>1 艘</span>
          </div>
        </div>
        <div class="boats-content">
          <div class="boat-grid">
            <div class="boat-card rented">
              <div class="boat-header">
                <div class="boat-status status-active">
                  <i class="fas fa-circle"></i>
                  <span>使用中</span>
                </div>
                <div class="rental-badge">
                  <i class="fas fa-clock"></i>
                  <span>租用</span>
                </div>
              </div>

              <div class="boat-info">
                <div class="boat-model">{{ getBoatName(currentRentedBoat) }}</div>
                <div class="info-grid">
                  <div class="info-item">
                    <i class="fas fa-hashtag"></i>
                    <span>{{ currentRentedBoat.numberPlateText }}</span>
                  </div>
                  <div class="info-item">
                    <i class="fas fa-clock"></i>
                    <span>{{ getRemainingTime(currentRentedBoat.renttime) }}</span>
                  </div>
                </div>
              </div>

              <button @click="returnBoat" class="action-btn return">
                <i class="fas fa-undo-alt"></i>
                <span>归还船只</span>
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- 可租船只卡片 -->
      <div v-else class="boats-card">
        <div class="card-header">
          <div class="header-title">
            <i class="fas fa-list"></i>
            <h3>可租船型</h3>
          </div>
          <div class="boat-count-badge">
            <span>{{ boatList.length }} 艘</span>
          </div>
        </div>
        <div class="boats-content">
          <div v-if="boatList.length === 0" class="empty-state">
            <div class="empty-icon">
              <i class="fas fa-ship"></i>
            </div>
            <h4>暂无可租船只</h4>
            <p>请稍后再试</p>
          </div>
          <div v-else class="boat-grid">
            <div v-for="boat in boatList" :key="boat.model" class="boat-card available">
              <div class="boat-header">
                <div class="boat-status status-available">
                  <i class="fas fa-circle"></i>
                  <span>可租</span>
                </div>
                <div class="rental-badge available">
                  <i class="fas fa-tag"></i>
                  <span>3天</span>
                </div>
              </div>

              <div class="boat-info">
                <div class="boat-model">{{ boat.name }}</div>
                <div class="info-grid">
                  <div class="info-item price">
                    <i class="fas fa-dollar-sign"></i>
                    <span>¥{{ boat.price.toLocaleString() }}</span>
                  </div>
                  <div class="info-item">
                    <i class="fas fa-calendar"></i>
                    <span>3天租期</span>
                  </div>
                </div>
              </div>

              <button @click="rentBoat(boat.model)" class="action-btn rent">
                <i class="fas fa-key"></i>
                <span>租用船只</span>
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- 租船说明 -->
      <div class="info-card">
        <div class="card-header">
          <div class="header-title">
            <i class="fas fa-info-circle"></i>
            <h3>租船说明</h3>
          </div>
        </div>
        <div class="info-content">
          <div class="info-list">
            <div class="info-item-text">
              <i class="fas fa-clock"></i>
              <span>租期为3天，到期后系统将自动回收</span>
            </div>
            <div class="info-item-text">
              <i class="fas fa-wrench"></i>
              <span>船只损坏严重时需支付维修费</span>
            </div>
            <div class="info-item-text">
              <i class="fas fa-exclamation-triangle"></i>
              <span>请按时归还，避免额外费用</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { useEvents } from '@Composables/useEvents.js';

const events = useEvents();

const currentRentedBoat = ref<any>(null);
const boatList = ref<any[]>([]);

const rentBoat = (model: string) => {
  events.emitServer('rentboat', model);
};

const returnBoat = () => {
  events.emitServer('returnboat');
};

const getBoatName = (boat: any) => {
  const boatInfo = boatList.value.find(b => b.model === boat.model);
  return boatInfo ? boatInfo.name : '未知船只';
};

const getRemainingTime = (renttime: number) => {
  const now = Date.now();
  const remaining = renttime - now;

  if (remaining <= 0) {
    return '已过期';
  }

  const days = Math.floor(remaining / (24 * 60 * 60 * 1000));
  const hours = Math.floor((remaining % (24 * 60 * 60 * 1000)) / (60 * 60 * 1000));
  const minutes = Math.floor((remaining % (60 * 60 * 1000)) / (60 * 1000));

  return `${days}天${hours}小时${minutes}分钟`;
};


events.on('rentboat:sync', (boat) => {
  currentRentedBoat.value = boat;
});

events.on('rentboatlist:sync', (boats) => {
  boatList.value = boats;
});

</script>

<style scoped>
.rent-boat-overlay {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 70vw;
  height: 92vh;
  background: rgba(30, 30, 50, 0.95);
  border: 0.1vh solid rgba(100, 255, 218, 0.2);
  border-radius: 1.5vh;
  display: flex;
  flex-direction: column;
  color: #ffffff;
  font-family: "Inter", "PingFang SC", "Microsoft YaHei", sans-serif;
  overflow: hidden;
  box-shadow: 0 1vh 3vh rgba(0, 0, 0, 0.3);
}

/* 页面头部 */
.page-header {
  padding: 1.5vh 2vh;
  background: rgba(100, 255, 218, 0.05);
  border-bottom: 0.1vh solid rgba(100, 255, 218, 0.2);
  position: relative;
  overflow: hidden;
  flex-shrink: 0;
}

.page-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(100, 255, 218, 0.1), transparent);
}



.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
  z-index: 1;
}

.title-section {
  display: flex;
  align-items: center;
  gap: 1.5vh;
}

.header-icon {
  width: 5vh;
  height: 5vh;
  background: rgba(100, 255, 218, 0.15);
  border: 0.1vh solid rgba(100, 255, 218, 0.3);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #64ffda;
  font-size: 2.2vh;
}

.title-info h1 {
  font-size: 2.4vh;
  font-weight: 700;
  margin: 0 0 0.3vh 0;
  color: #ffffff;
  letter-spacing: 0.05em;
}

.subtitle {
  font-size: 1.1vh;
  color: rgba(100, 255, 218, 0.7);
  font-weight: 400;
  text-transform: uppercase;
  letter-spacing: 0.1em;
}

.status-info {
  display: flex;
  align-items: center;
  gap: 1.5vh;
}

.status-badge {
  display: flex;
  align-items: center;
  gap: 0.8vh;
  padding: 0.6vh 1.2vh;
  border-radius: 0.6vh;
  font-size: 1.1vh;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.status-badge.active {
  background: rgba(52, 199, 89, 0.2);
  color: #34c759;
  border: 0.05vh solid rgba(52, 199, 89, 0.4);
}

.status-badge i {
  font-size: 0.8vh;
}

.boat-count {
  display: flex;
  align-items: center;
  gap: 0.8vh;
  padding: 0.6vh 1.2vh;
  background: rgba(100, 255, 218, 0.1);
  border: 0.05vh solid rgba(100, 255, 218, 0.3);
  border-radius: 0.6vh;
  color: #64ffda;
  font-size: 1.1vh;
  font-weight: 600;
}


/* 内容区域 */
.content-area {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 2vh;
  padding: 1.5vh 2vh;
  overflow-y: auto;
}

/* 船只卡片容器 */
.boats-card,
.info-card {
  background: rgba(255, 255, 255, 0.03);
  border: 0.1vh solid rgba(255, 255, 255, 0.1);
  border-radius: 1.2vh;
  overflow: hidden;
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
}

.boats-card {
  flex: 1;
}

.boats-card:hover,
.info-card:hover {
  background: rgba(255, 255, 255, 0.06);
  border-color: rgba(100, 255, 218, 0.3);
  transform: translateY(-0.2vh);
  box-shadow: 0 0.5vh 1vh rgba(100, 255, 218, 0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1vh 1.5vh;
  background: rgba(100, 255, 218, 0.05);
  border-bottom: 0.1vh solid rgba(100, 255, 218, 0.1);
  flex-shrink: 0;
}

.header-title {
  display: flex;
  align-items: center;
  gap: 0.8vh;
}

.header-title i {
  color: #64ffda;
  font-size: 1.4vh;
}

.header-title h3 {
  font-size: 1.3vh;
  font-weight: 600;
  margin: 0;
  color: #64ffda;
}

.boat-count-badge {
  background: rgba(100, 255, 218, 0.15);
  color: #64ffda;
  padding: 0.4vh 0.8vh;
  border-radius: 0.4vh;
  font-size: 1vh;
  font-weight: 600;
}

.boats-content {
  flex: 1;
  padding: 1.5vh;
  overflow-y: auto;
}

/* 船只网格 */
.boat-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(20vw, 1fr));
  gap: 1.5vh;
}

/* 船只卡片 */
.boat-card {
  background: rgba(255, 255, 255, 0.02);
  border: 0.1vh solid rgba(255, 255, 255, 0.08);
  border-radius: 1vh;
  padding: 1.2vh;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.boat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(100, 255, 218, 0.1), transparent);
  transition: all 0.3s ease;
}

.boat-card:hover::before {
  left: 100%;
}

.boat-card:hover {
  background: rgba(255, 255, 255, 0.04);
  border-color: rgba(100, 255, 218, 0.2);
  transform: translateY(-0.2vh);
  box-shadow: 0 0.3vh 0.8vh rgba(100, 255, 218, 0.1);
}

.boat-card.rented {
  border-color: rgba(52, 199, 89, 0.2);
}

.boat-card.available {
  border-color: rgba(100, 255, 218, 0.2);
}

/* 船只状态头部 */
.boat-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5vh;
  position: relative;
  z-index: 2;
}

.boat-status {
  display: flex;
  align-items: center;
  gap: 0.5vh;
  padding: 0.3vh 0.8vh;
  border-radius: 0.4vh;
  font-size: 0.9vh;
  font-weight: 600;
  border: 0.05vh solid;
}

.boat-status.status-active {
  background: rgba(52, 199, 89, 0.15);
  color: #34c759;
  border-color: rgba(52, 199, 89, 0.3);
}

.boat-status.status-available {
  background: rgba(100, 255, 218, 0.15);
  color: #64ffda;
  border-color: rgba(100, 255, 218, 0.3);
}

.boat-status i {
  font-size: 0.7vh;
}

/* 租用标记 */
.rental-badge {
  display: flex;
  align-items: center;
  gap: 0.5vh;
  padding: 0.3vh 0.8vh;
  border-radius: 0.4vh;
  font-size: 0.9vh;
  font-weight: 600;
  text-transform: uppercase;
}

.rental-badge:not(.available) {
  background: rgba(255, 149, 0, 0.15);
  color: #ff9500;
  border: 0.05vh solid rgba(255, 149, 0, 0.3);
}

.rental-badge.available {
  background: rgba(100, 255, 218, 0.15);
  color: #64ffda;
  border: 0.05vh solid rgba(100, 255, 218, 0.3);
}

.rental-badge i {
  font-size: 0.7vh;
}

/* 船只信息 */
.boat-info {
  flex: 1;
  margin-bottom: 1.2vh;
  position: relative;
  z-index: 2;
}

.boat-model {
  color: #ffffff;
  font-size: 1.4vh;
  font-weight: 600;
  margin-bottom: 1vh;
  text-align: center;
}

.info-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 0.8vh;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 0.6vh;
  color: rgba(255, 255, 255, 0.7);
  font-size: 1.1vh;
  padding: 0.4vh 0.6vh;
  background: rgba(255, 255, 255, 0.02);
  border-radius: 0.4vh;
}

.info-item i {
  color: #64ffda;
  font-size: 1vh;
  flex-shrink: 0;
}

.info-item.price {
  color: #34c759;
}

.info-item.price i {
  color: #34c759;
}

/* 操作按钮 */
.action-btn {
  width: 100%;
  height: 4vh;
  border-radius: 0.8vh;
  font-size: 1.2vh;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.8vh;
  border: 0.1vh solid;
  position: relative;
  overflow: hidden;
  font-family: inherit;
  z-index: 2;
}

.action-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transition: all 0.3s ease;
}

.action-btn:hover::before {
  left: 100%;
}

.action-btn.rent {
  background: rgba(52, 199, 89, 0.1);
  border-color: rgba(52, 199, 89, 0.3);
  color: #34c759;
}

.action-btn.rent:hover {
  background: rgba(52, 199, 89, 0.2);
  border-color: rgba(52, 199, 89, 0.5);
  transform: translateY(-0.1vh);
  box-shadow: 0 0.3vh 0.8vh rgba(52, 199, 89, 0.2);
}

.action-btn.return {
  background: rgba(255, 149, 0, 0.1);
  border-color: rgba(255, 149, 0, 0.3);
  color: #ff9500;
}

.action-btn.return:hover {
  background: rgba(255, 149, 0, 0.2);
  border-color: rgba(255, 149, 0, 0.5);
  transform: translateY(-0.1vh);
  box-shadow: 0 0.3vh 0.8vh rgba(255, 149, 0, 0.2);
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  padding: 4vh 2vh;
  color: rgba(255, 255, 255, 0.6);
  min-height: 20vh;
}

.empty-icon {
  width: 6vh;
  height: 6vh;
  background: rgba(100, 255, 218, 0.1);
  border: 0.1vh solid rgba(100, 255, 218, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #64ffda;
  font-size: 2.5vh;
  margin-bottom: 1.5vh;
}

.empty-state h4 {
  font-size: 1.6vh;
  color: #ffffff;
  margin: 0 0 0.8vh 0;
  font-weight: 600;
}

.empty-state p {
  font-size: 1.2vh;
  margin: 0;
  line-height: 1.5;
}

/* 说明信息 */
.info-content {
  padding: 1.5vh;
}

.info-list {
  display: flex;
  flex-direction: column;
  gap: 1.2vh;
}

.info-item-text {
  display: flex;
  align-items: center;
  gap: 1vh;
  color: rgba(255, 255, 255, 0.8);
  font-size: 1.2vh;
  padding: 0.8vh 1vh;
  background: rgba(255, 255, 255, 0.02);
  border-radius: 0.6vh;
  border: 0.05vh solid rgba(255, 255, 255, 0.08);
}

.info-item-text i {
  color: #64ffda;
  font-size: 1.1vh;
  flex-shrink: 0;
}

/* 滚动条样式 */
.content-area::-webkit-scrollbar,
.boats-content::-webkit-scrollbar {
  width: 0.4vh;
}

.content-area::-webkit-scrollbar-track,
.boats-content::-webkit-scrollbar-track {
  background: rgba(100, 255, 218, 0.05);
  border-radius: 0.2vh;
}

.content-area::-webkit-scrollbar-thumb,
.boats-content::-webkit-scrollbar-thumb {
  background: rgba(100, 255, 218, 0.3);
  border-radius: 0.2vh;
  transition: all 0.3s ease;
}

.content-area::-webkit-scrollbar-thumb:hover,
.boats-content::-webkit-scrollbar-thumb:hover {
  background: rgba(100, 255, 218, 0.5);
}


</style>