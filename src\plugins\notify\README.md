# Notify 通知系统

## 通知类型

| 类型 | 用途 |
|------|------|
| `warning` | 警告信息 |
| `admin` | 管理员消息 |
| `info` | 一般信息 |
| `error` | 错误信息 |
| `success` | 成功信息 |
| `urgent` | 紧急消息 |

## 服务器端使用

```typescript
import { useRebar } from '@Server/index.js';

const Rebar = useRebar();
const notifyApi = await Rebar.useApi().getAsync('notify-api');

// 显示通知
notifyApi.shownotify(player, '消息内容', 'info');

// 隐藏所有通知
notifyApi.hidenotify(player);
```

## 客户端使用

```typescript
import { useRebarClient } from '@Client/index.js';

const Rebar = useRebarClient();
const notifyApi = Rebar.useClientApi().get('notify-client-api');

// 显示通知
notifyApi.shownotify('消息内容', 'info');

// 隐藏所有通知
notifyApi.hidenotify();
```

## 管理员命令

```bash
/shownotify "消息内容" warning
```

## 使用示例

```typescript
// 服务器端
notifyApi.shownotify(player, '欢迎来到服务器！', 'success');
notifyApi.shownotify(player, '您已经死亡', 'warning');
notifyApi.shownotify(player, '操作失败', 'error');

// 客户端
notifyApi.shownotify('按键提示', 'info');
notifyApi.shownotify('进入车辆', 'success');
``` 