import * as alt from "alt-client";
import { useRebarClient } from '@Client/index.js';
import { ChoiceConfig, ChoiceOption } from '../shared/config.js';

const Rebar = useRebarClient();
const webview = Rebar.webview.useWebview();

declare global {
    export interface ClientPlugin {
        ['choice-client-api']: ReturnType<typeof useChoiceClientApi>;
    }
}

function useChoiceClientApi() {
    /**
     * 客户端显示选择对话框
     * @param config 选择配置
     * @returns Promise<any> 返回选择的值
     */
    function showChoice(config: ChoiceConfig): Promise<any> {
        webview.show('choicebar', 'page', true);

        return new Promise((resolve) => {
            webview.on('choice:select', (value: any) => {
                resolve(value);
                webview.hide('choicebar');
            });
            
            webview.emit('choice:show', config);
        });
    }

    /**
     * 简化版显示选择对话框
     * @param title 标题
     * @param options 选项数组
     * @returns Promise<any> 返回选择的值
     */
    function showSimpleChoice(title: string, options: string[] | ChoiceOption[]): Promise<any> {
        const choiceOptions: ChoiceOption[] = options.map((option, index) => {
            if (typeof option === 'string') {
                return {
                    id: index.toString(),
                    text: option,
                    value: option
                };
            }
            return option;
        });

        const config: ChoiceConfig = {
            title,
            options: choiceOptions,
            allowCancel: true,
            cancelText: '取消'
        };

        return showChoice(config);
    }

    return {
        showChoice,
        showSimpleChoice
    };
}

const choiceClientApi = useChoiceClientApi();
Rebar.useClientApi().register('choice-client-api', choiceClientApi);

// 处理服务端到客户端的选择事件
alt.onServer('choice:select', (value: any) => {
    alt.emitServerRaw('choice:select', value);
});


