<template>
  <div class="game-container">
    <div class="game-title">
      <h1>高级保险箱</h1>
      <div class="subtitle">精密机械锁</div>
    </div>
    
    <div class="lock-frame">
      <div class="lock-container">
        <div class="lock-decoration left"></div>
        <div 
          v-for="(pin, index) in pins"
          :key="index"
          class="lock-pin"
        >
          <div class="pin-container">
            <div class="pin-number">{{ index + 1 }}</div>
            <div class="pin-shaft">
              <div 
                class="pin" 
                :style="{ 
                  transform: `translateY(${-pin.position}px)`,
                  backgroundColor: getPinColor(pin.position, targetPositions[index])
                }"
              >
                <div class="pin-groove"></div>
              </div>
              <div class="pin-guide"></div>
            </div>
            <div class="pin-marks">
              <div v-for="n in 5" :key="n" class="mark"></div>
            </div>
          </div>
          <input 
            type="range" 
            min="0" 
            max="100" 
            :value="pin.position" 
            @input="(e) => updatePinStatus(index, e)"
            class="pin-control"
            :disabled="isSuccess"
          >
          <div class="position-display">{{ pin.position }}%</div>
        </div>
        <div class="lock-decoration right"></div>
      </div>
      
      <div class="lock-base">
        <div class="lock-indicator" :class="{ 'success': isSuccess }">
          <div class="indicator-light"></div>
        </div>
      </div>
    </div>
  
    <div class="control-panel">
      <div class="panel-section">
        <div class="attempts-display">
          <span class="attempts-label">尝试次数</span>
          <span class="attempts-value">{{ attempts }}</span>
        </div>
        
        <button 
          class="control-btn try-btn" 
          @click="tryUnlock"
          :disabled="isSuccess"
        >
          确认开锁
        </button>
      </div>
  
      <div v-if="message" :class="['status-message', messageType]">
        {{ message }}
      </div>
  
      <button 
        v-if="isSuccess" 
        class="control-btn reset-btn" 
        @click="resetGame"
      >
        重置锁具
      </button>
    </div>
  </div>
  </template>
  
  <script setup lang="ts">
  import { ref, computed, onMounted } from 'vue'
  import { useEvents } from '@Composables/useEvents.js';
  
  const events = useEvents();
  
  interface PinState {
  position: number
  isCorrect: boolean
  }
  
  const PIN_COUNT = 6
  const TOLERANCE = 3
  
  const pins = ref<PinState[]>(Array(PIN_COUNT).fill({ position: 0, isCorrect: false }))
  const targetPositions = ref<number[]>([])
  const attempts = ref(0)
  const message = ref('')
  const messageType = ref('')
  const isSuccess = ref(false)
  
  const generateTargetPositions = (): void => {
  targetPositions.value = Array.from(
    { length: PIN_COUNT }, 
    () => Math.floor(Math.random() * 90) + 5
  )
  }
  
  const getPinColor = (position: number, target: number): string => {
  const diff = Math.abs(position - target)
  if (diff <= TOLERANCE) return '#ffd700'
  if (diff <= TOLERANCE * 2) return '#c17d11'
  return '#666'
  }
  
  const updatePinStatus = (index: number, event: Event): void => {
  const position = Number((event.target as HTMLInputElement).value)
  const isCorrect = Math.abs(position - targetPositions.value[index]) <= TOLERANCE
  pins.value[index] = { position, isCorrect }
  }
  
  const tryUnlock = (): void => {
  if (isSuccess.value) return
  
  attempts.value++
  const allCorrect = pins.value.every(pin => pin.isCorrect)
  
  if (allCorrect) {
    message.value = '锁已解开'
    messageType.value = 'success'
    isSuccess.value = true
    resetPinPositions()
    
    // 发送解锁成功事件
    events.emitServer('getmetal:unlockSuccess');
  } else {
    message.value = '机械结构未对准'
    messageType.value = 'hint'
  }
  }
  
  const resetPinPositions = (): void => {
  setTimeout(() => {
    pins.value = pins.value.map(() => ({ position: 0, isCorrect: false }))
  }, 1000)
  }
  
  const resetGame = (): void => {
  isSuccess.value = false
  setTimeout(() => {
    pins.value = Array(PIN_COUNT).fill({ position: 0, isCorrect: false })
    attempts.value = 0
    message.value = ''
    messageType.value = ''
    generateTargetPositions()
  }, 1200)
  }
  
  onMounted(() => {
  generateTargetPositions()
  })
  </script>
  
  <style scoped>
  .game-container {
  max-width: 900px;
  margin: 0 auto;
  padding: 40px 20px;
  background: linear-gradient(to bottom, #1a1a1a, #2d2d2d);
  min-height: 100vh;
  color: #e0e0e0;
  }
  
  .game-title {
  text-align: center;
  margin-bottom: 40px;
  }
  
  .game-title h1 {
  font-size: 2.5em;
  margin: 0;
  background: linear-gradient(45deg, #ffd700, #ffa500);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  text-shadow: 0 2px 4px rgba(0,0,0,0.3);
  }
  
  .subtitle {
  color: #888;
  font-size: 1.2em;
  margin-top: 8px;
  }
  
  .lock-frame {
  background: #232323;
  padding: 30px;
  border-radius: 20px;
  box-shadow: 0 0 30px rgba(0,0,0,0.5);
  margin-bottom: 40px;
  }
  
  .lock-container {
  display: flex;
  justify-content: center;
  align-items: center;
  background: linear-gradient(to bottom, #2a2a2a, #1a1a1a);
  padding: 30px;
  border-radius: 15px;
  position: relative;
  }
  
  .lock-decoration {
  width: 20px;
  height: 100%;
  position: absolute;
  background: linear-gradient(to right, #333, #222);
  }
  
  .lock-decoration.left {
  left: 0;
  border-radius: 10px 0 0 10px;
  }
  
  .lock-decoration.right {
  right: 0;
  border-radius: 0 10px 10px 0;
  }
  
  .lock-base {
  background: #2a2a2a;
  height: 40px;
  margin-top: 20px;
  border-radius: 0 0 15px 15px;
  display: flex;
  justify-content: center;
  align-items: center;
  }
  
  .lock-indicator {
  width: 60px;
  height: 20px;
  background: #333;
  border-radius: 10px;
  display: flex;
  justify-content: center;
  align-items: center;
  }
  
  .indicator-light {
  width: 12px;
  height: 12px;
  background: #ff3333;
  border-radius: 50%;
  transition: all 0.3s ease;
  box-shadow: 0 0 10px #ff3333;
  }
  
  .lock-indicator.success .indicator-light {
  background: #33ff33;
  box-shadow: 0 0 10px #33ff33;
  }
  
  .control-panel {
  background: rgba(255,255,255,0.05);
  padding: 20px;
  border-radius: 15px;
  text-align: center;
  }
  
  .panel-section {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 30px;
  margin-bottom: 20px;
  }
  
  .attempts-display {
  background: #2a2a2a;
  padding: 15px 25px;
  border-radius: 10px;
  display: flex;
  flex-direction: column;
  align-items: center;
  }
  
  .attempts-label {
  font-size: 0.9em;
  color: #888;
  margin-bottom: 5px;
  }
  
  .attempts-value {
  font-size: 1.8em;
  font-weight: bold;
  color: #ffd700;
  }
  
  .control-btn {
  font-size: 1.1em;
  padding: 12px 30px;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 1px;
  }
  
  .try-btn {
  background: linear-gradient(45deg, #4CAF50, #45a049);
  color: white;
  }
  
  .try-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(76,175,80,0.3);
  }
  
  .try-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  }
  
  .reset-btn {
  background: linear-gradient(45deg, #f44336, #d32f2f);
  color: white;
  margin-top: 20px;
  }
  
  .reset-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(244,67,54,0.3);
  }
  
  .status-message {
  margin: 20px 0;
  padding: 15px;
  border-radius: 8px;
  font-weight: 500;
  }
  
  .success {
  background: rgba(76,175,80,0.2);
  color: #81c784;
  border: 1px solid #4CAF50;
  }
  
  .hint {
  background: rgba(33,150,243,0.2);
  color: #64b5f6;
  border: 1px solid #2196F3;
  }
  
  .lock-pin {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 80px;
  margin: 0 15px;
  }
  
  .pin-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 20px;
  }
  
  .pin-number {
  color: #888;
  font-size: 0.8em;
  margin-bottom: 5px;
  }
  
  .pin-shaft {
  height: 150px;
  width: 24px;
  background: linear-gradient(to right, #1a1a1a, #333, #1a1a1a);
  border-radius: 4px;
  position: relative;
  overflow: hidden;
  box-shadow: inset 0 0 10px rgba(0,0,0,0.5);
  }
  
  .pin {
  width: 20px;
  height: 50px;
  background-color: #666;
  position: absolute;
  left: 2px;
  bottom: 0;
  transition: transform 0.3s ease, background-color 0.3s ease;
  border-radius: 2px;
  box-shadow: 0 0 5px rgba(0,0,0,0.3);
  }
  
  .pin-groove {
  position: absolute;
  width: 100%;
  height: 2px;
  background: rgba(0,0,0,0.3);
  top: 50%;
  transform: translateY(-50%);
  }
  
  .pin-guide {
  position: absolute;
  width: 100%;
  height: 2px;
  background: linear-gradient(to right, transparent, #ffa500, transparent);
  top: 50%;
  transform: translateY(-50%);
  opacity: 0.5;
  }
  
  .pin-marks {
  width: 100%;
  height: 100px;
  position: absolute;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  padding: 10px 0;
  }
  
  .mark {
  width: 4px;
  height: 1px;
  background: rgba(255,255,255,0.2);
  margin-left: 28px;
  }
  
  .pin-control {
  width: 150px;
  transform: rotate(-90deg);
  margin: 50px 0;
  }
  
  .position-display {
  font-size: 0.8em;
  color: #888;
  margin-top: -30px;
  }
  
  input[type="range"] {
  -webkit-appearance: none;
  appearance: none;
  background: linear-gradient(to right, #2a2a2a, #3a3a3a);
  height: 6px;
  border-radius: 3px;
  cursor: pointer;
  box-shadow: inset 0 0 5px rgba(0,0,0,0.3);
  }
  
  input[type="range"]::-webkit-slider-thumb {
  -webkit-appearance: none;
  width: 20px;
  height: 20px;
  background: linear-gradient(45deg, #ffd700, #ffa500);
  border-radius: 50%;
  cursor: pointer;
  box-shadow: 0 0 10px rgba(0,0,0,0.3);
  }
  
  input[type="range"]:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  }
  
  input[type="range"]:disabled::-webkit-slider-thumb {
  background: #666;
  }
  </style>