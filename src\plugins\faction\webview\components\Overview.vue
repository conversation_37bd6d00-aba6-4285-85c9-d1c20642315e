<template>
  <div class="overview-container">
    <!-- 主要卡片：派系简介和统计数据 -->
    <div class="main-card">
      <div class="faction-intro">
        <div class="faction-header">
          <div class="faction-logo">
            <span>{{ factionData?.name?.charAt(0) || '派' }}</span>
          </div>
          <div class="faction-title">
            <h2>{{ factionData?.name || '派系' }}</h2>
            <div class="faction-type">{{ factionData?.type || '未知类型' }}</div>
          </div>
        </div>
        <div class="faction-description">
          <p>{{ factionData?.description || '暂无派系简介信息。' }}</p>
        </div>
        <div class="faction-dates">
          <div class="date-item">
            <span class="date-label">成立日期</span>
            <span class="date-value">{{ new Date(factionData?.foundedDate).toLocaleString() }}</span>
          </div>
          <div class="date-item">
            <span class="date-label">您的入职日期</span>
            <span class="date-value">{{ new Date(job?.joinDate).toLocaleString() }}</span>
          </div>
        </div>
      </div>

      <div class="stats-grid">
        <div class="stat-item">
          <div class="stat-icon members">
            <svg viewBox="0 0 24 24">
              <path d="M16,13C15.71,13 15.38,13 15.03,13.05C16.19,13.89 17,15 17,16.5V19H23V16.5C23,14.17 18.33,13 16,13M8,13C5.67,13 1,14.17 1,16.5V19H15V16.5C15,14.17 10.33,13 8,13M8,11A3,3 0 0,0 11,8A3,3 0 0,0 8,5A3,3 0 0,0 5,8A3,3 0 0,0 8,11M16,11A3,3 0 0,0 19,8A3,3 0 0,0 16,5A3,3 0 0,0 13,8A3,3 0 0,0 16,11Z" />
            </svg>
          </div>
          <div class="stat-content">
            <h3>成员数量</h3>
            <p>{{ factionData?.members?.length || 0 }}</p>
          </div>
        </div>

        <div class="stat-item">
          <div class="stat-icon funds">
            <svg viewBox="0 0 24 24">
              <path d="M5,6H23V18H5V6M14,9A3,3 0 0,1 17,12A3,3 0 0,1 14,15A3,3 0 0,1 11,12A3,3 0 0,1 14,9M9,8A2,2 0 0,1 7,10V14A2,2 0 0,1 9,16H19A2,2 0 0,1 21,14V10A2,2 0 0,1 19,8H9M1,10H3V20H19V22H1V10Z" />
            </svg>
          </div>
          <div class="stat-content">
            <h3>派系资金</h3>
            <p>${{ formatFunds(factionData?.funds || 0) }}</p>
          </div>
        </div>

        <div class="stat-item">
          <div class="stat-icon position">
            <svg viewBox="0 0 24 24">
              <path d="M12,15C7.58,15 4,16.79 4,19V21H20V19C20,16.79 16.42,15 12,15M8,9A4,4 0 0,0 12,13A4,4 0 0,0 16,9M11.5,2C11.2,2 11,2.21 11,2.5V5.5H10V3C10,3 7.75,3.86 7.75,6.75C7.75,6.75 7,6.89 7,8H17C16.95,6.89 16.25,6.75 16.25,6.75C16.25,3.86 14,3 14,3V5.5H13V2.5C13,2.21 12.81,2 12.5,2H11.5Z" />
            </svg>
          </div>
          <div class="stat-content">
            <h3>您的职位</h3>
            <p>{{ job?.name || '未知' }}</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 公告区域 -->
    <div class="announcements-card" v-if="factionData?.announcements?.length">
      <div class="card-header">
        <svg viewBox="0 0 24 24">
          <path d="M12,8H4A2,2 0 0,0 2,10V14A2,2 0 0,0 4,16H5V20A1,1 0 0,0 6,21H8A1,1 0 0,0 9,20V16H12L17,20V4L12,8M21.5,12C21.5,13.71 20.54,15.26 19,16V8C20.53,8.75 21.5,10.3 21.5,12Z" />
        </svg>
        <h2>最新公告</h2>
      </div>
      
      <div class="announcements-list">
        <div class="announcement-item" v-for="announcement in sortedAnnouncements" :key="announcement.id">
          <div class="announcement-content">
            <p>{{ announcement.content }}</p>
            <div class="announcement-meta">
              <span class="author">{{ announcement.author }}</span>
              <span class="date">{{ new Date(announcement.date).toLocaleString() }}</span>
            </div>
          </div>
          <button 
            v-if="hasPermission('manage_members') || permissions.includes('admin')"
            @click="deleteAnnouncement(announcement.id)" 
            class="delete-btn"
            title="删除公告">
            <svg viewBox="0 0 24 24">
              <path d="M19,4H15.5L14.5,3H9.5L8.5,4H5V6H19M6,19A2,2 0 0,0 8,21H16A2,2 0 0,0 18,19V7H6V19Z" />
            </svg>
          </button>
        </div>
      </div>
    </div>

    <!-- 操作按钮卡片 -->
    <div class="actions-card">
      <div v-if="hasPermission('dissolve_faction') || permissions.includes('admin')" class="admin-actions">
        <button @click="leaveFaction" class="action-btn leave-btn">
          <svg viewBox="0 0 24 24">
            <path d="M17,17.25V14H10V10H17V6.75L22.25,12L17,17.25M13,2A2,2 0 0,1 15,4V8H13V4H4V20H13V16H15V20A2,2 0 0,1 13,22H4A2,2 0 0,1 2,20V4A2,2 0 0,1 4,2H13Z" />
          </svg>
          <span>离开派系</span>
        </button>
        <button @click="openDissolveModal" class="action-btn dissolve-btn">
          <svg viewBox="0 0 24 24">
            <path d="M12,2C17.53,2 22,6.47 22,12C22,17.53 17.53,22 12,22C6.47,22 2,17.53 2,12C2,6.47 6.47,2 12,2M15.59,7L12,10.59L8.41,7L7,8.41L10.59,12L7,15.59L8.41,17L12,13.41L15.59,17L17,15.59L13.41,12L17,8.41L15.59,7Z" />
          </svg>
          <span>解散派系</span>
        </button>
      </div>
      <div v-else class="member-actions">
        <button @click="openResignModal" class="action-btn resign-btn">
          <svg viewBox="0 0 24 24">
            <path d="M17,17.25V14H10V10H17V6.75L22.25,12L17,17.25M13,2A2,2 0 0,1 15,4V8H13V4H4V20H13V16H15V20A2,2 0 0,1 13,22H4A2,2 0 0,1 2,20V4A2,2 0 0,1 4,2H13Z" />
          </svg>
          <span>申请离职</span>
        </button>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, PropType } from 'vue';

const props = defineProps({
  factionData: {
    type: Object,
    required: true
  },
  job: {
    type: Object,
    required: true
  },
  permissions: {
    type: Array,
    required: true
  },
  hasPermission: {
    type: Function,
    required: true
  },
  formatFunds: {
    type: Function,
    required: true
  },
  deleteAnnouncement: {
    type: Function,
    required: true
  },
  openDissolveModal: {
    type: Function as PropType<(event: MouseEvent) => void>,
    required: true
  },
  leaveFaction: {
    type: Function as PropType<(event: MouseEvent) => void>,
    required: true
  },
  openResignModal: {
    type: Function as PropType<(event: MouseEvent) => void>,
    required: true
  }
});

// 对公告进行排序，最新的在前面
const sortedAnnouncements = computed(() => {
  if (!props.factionData?.announcements?.length) return [];
  return [...props.factionData.announcements].sort((a, b) => b.date - a.date);
});
</script>

<style scoped>
.overview-container {
  display: grid;
  gap: 2vh;
  grid-template-columns: 2fr 1fr;
  grid-template-rows: auto auto;
}




/* 主卡片样式 */
.main-card {
  background: rgba(34, 42, 53, 0.7);
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.1);
  grid-column: 1;
  grid-row: 1 / 3;
}

.faction-intro {
  padding: 2.5vh;
}

.faction-header {
  display: flex;
  align-items: center;
  margin-bottom: 2vh;
}

.faction-logo {
  width: 6vh;
  height: 6vh;
  border-radius: 50%;
  background: linear-gradient(135deg, #8774e1, #6856b9);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 2vh;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.faction-logo span {
  font-size: 3vh;
  font-weight: 600;
  color: white;
}

.faction-title h2 {
  font-size: 2.2vh;
  font-weight: 600;
  color: #e2e8f0;
  margin-bottom: 0.5vh;
}

.faction-type {
  display: inline-block;
  padding: 0.3vh 1vh;
  background: rgba(135, 116, 225, 0.2);
  color: #a995f4;
  border-radius: 4px;
  font-size: 1.2vh;
  font-weight: 500;
}

.faction-description {
  margin-bottom: 2vh;
  padding-bottom: 2vh;
  border-bottom: 1px solid rgba(255, 255, 255, 0.08);
}

.faction-description p {
  font-size: 1.4vh;
  line-height: 1.6;
  color: #cbd5e0;
}

.faction-dates {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1.5vh;
}

.date-item {
  display: flex;
  flex-direction: column;
}

.date-label {
  font-size: 1.2vh;
  color: #a0aec0;
  margin-bottom: 0.5vh;
}

.date-value {
  font-size: 1.3vh;
  color: #e2e8f0;
  font-weight: 500;
}

/* 统计数据 */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  background-color: rgba(26, 32, 44, 0.5);
  border-top: 1px solid rgba(255, 255, 255, 0.08);
}

.stat-item {
  display: flex;
  align-items: center;
  padding: 2vh;
  position: relative;
}

.stat-item:not(:last-child)::after {
  content: '';
  position: absolute;
  right: 0;
  top: 20%;
  bottom: 20%;
  width: 1px;
  background-color: rgba(255, 255, 255, 0.08);
}

.stat-icon {
  width: 4vh;
  height: 4vh;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 1.5vh;
}

.stat-icon svg {
  width: 2.2vh;
  height: 2.2vh;
  fill: white;
}

.stat-icon.members {
  background: linear-gradient(135deg, #4c1d95, #5b21b6);
}

.stat-icon.funds {
  background: linear-gradient(135deg, #065f46, #047857);
}

.stat-icon.position {
  background: linear-gradient(135deg, #9f580a, #b45309);
}

.stat-content h3 {
  font-size: 1.2vh;
  font-weight: 500;
  color: #a0aec0;
  margin-bottom: 0.4vh;
}

.stat-content p {
  font-size: 1.6vh;
  font-weight: 600;
  color: #e2e8f0;
}

/* 公告卡片 */
.announcements-card {
  background: rgba(34, 42, 53, 0.7);
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.1);
  grid-column: 2;
  grid-row: 1;
}

.card-header {
  display: flex;
  align-items: center;
  padding: 1.8vh 2vh;
  background: rgba(26, 32, 44, 0.5);
  border-bottom: 1px solid rgba(255, 255, 255, 0.08);
}

.card-header svg {
  width: 2vh;
  height: 2vh;
  margin-right: 1vh;
  fill: #a995f4;
}

.card-header h2 {
  font-size: 1.6vh;
  font-weight: 600;
  color: #e2e8f0;
}

.announcements-list {
  max-height: 35vh;
  overflow-y: auto;
  padding: 0.5vh 0;
}

.announcements-list::-webkit-scrollbar {
  width: 6px;
}

.announcements-list::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 3px;
}

.announcements-list::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 3px;
}

.announcements-list::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.3);
}

.announcement-item {
  padding: 1.8vh 2vh;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  border-bottom: 1px solid rgba(255, 255, 255, 0.08);
}

.announcement-item:last-child {
  border-bottom: none;
}

.announcement-content {
  flex: 1;
}

.announcement-content p {
  font-size: 1.4vh;
  line-height: 1.5;
  color: #e2e8f0;
  margin-bottom: 1vh;
}

.announcement-meta {
  display: flex;
  justify-content: space-between;
  font-size: 1.2vh;
}

.announcement-meta .author {
  color: #a995f4;
  font-weight: 500;
}

.announcement-meta .date {
  color: #a0aec0;
}

.delete-btn {
  background: transparent;
  border: none;
  width: 3vh;
  height: 3vh;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s;
  cursor: pointer;
  margin-left: 1vh;
  padding: 0;
}

.delete-btn:hover {
  background: rgba(239, 68, 68, 0.2);
}

.delete-btn svg {
  width: 1.8vh;
  height: 1.8vh;
  fill: #fc8181;
}

/* 操作按钮 */
.actions-card {
  background: rgba(34, 42, 53, 0.7);
  border-radius: 12px;
  padding: 2vh;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.1);
  grid-column: 2;
  grid-row: 2;
}

.admin-actions, .member-actions {
  display: flex;
  justify-content: center;
  gap: 2vh;
}

.action-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 1.2vh 2vh;
  border-radius: 8px;
  font-size: 1.4vh;
  font-weight: 500;
  cursor: pointer;
  border: none;
  transition: all 0.2s;
}

.action-btn svg {
  width: 1.8vh;
  height: 1.8vh;
  margin-right: 0.8vh;
}

.leave-btn {
  background: rgba(135, 116, 225, 0.2);
  color: #a995f4;
}

.leave-btn svg {
  fill: #a995f4;
}

.leave-btn:hover {
  background: rgba(135, 116, 225, 0.3);
}

.dissolve-btn {
  background: rgba(229, 62, 62, 0.2);
  color: #fc8181;
}

.dissolve-btn svg {
  fill: #fc8181;
}

.dissolve-btn:hover {
  background: rgba(229, 62, 62, 0.3);
}

.resign-btn {
  background: rgba(135, 116, 225, 0.2);
  color: #a995f4;
}

.resign-btn svg {
  fill: #a995f4;
}

.resign-btn:hover {
  background: rgba(135, 116, 225, 0.3);
}


</style>