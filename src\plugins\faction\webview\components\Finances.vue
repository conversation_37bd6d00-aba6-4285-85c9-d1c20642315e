<template>
  <div class="finances-container">
    <!-- 财务总览卡片 -->
    <div class="overview-card">
      <div class="card-header">
        <svg viewBox="0 0 24 24">
          <path
            d="M11.8,10.9C9.53,10.31 8.8,9.7 8.8,8.75C8.8,7.66 9.81,6.9 11.5,6.9C13.28,6.9 13.94,7.75 14,9H16.21C16.14,7.28 15.09,5.7 13,5.19V3H10V5.16C8.06,5.58 6.5,6.84 6.5,8.77C6.5,11.08 8.41,12.23 11.2,12.9C13.7,13.5 14.2,14.38 14.2,15.31C14.2,16 13.71,17.1 11.5,17.1C9.44,17.1 8.63,16.18 8.5,15H6.32C6.44,17.19 8.08,18.42 10,18.83V21H13V18.85C14.95,18.5 16.5,17.35 16.5,15.3C16.5,12.46 14.07,11.5 11.8,10.9Z" />
        </svg>
        <h2>资金统计</h2>
      </div>

      <div class="current-funds">
        <div class="funds-amount">
          <span class="currency">$</span>
          <span class="value">{{ formatFunds(factionData?.funds || 0) }}</span>
        </div>
        <div class="funds-label">派系总资金</div>
      </div>

      <div class="funds-stats">
        <div class="stat-item">
          <div class="stat-label">全局提成比率</div>
          <div class="stat-value">{{ (factionData?.billtax * 100).toFixed(2) }}%</div>
        </div>
      </div>
    </div>

    <!-- 资金操作卡片 -->
    <div v-if="hasPermission('manage_funds') || permissions.includes('admin')" class="operations-card">
      <div class="card-header">
        <svg viewBox="0 0 24 24">
          <path d="M3,5H9V11H3V5M5,7V9H7V7H5M11,7H21V9H11V7M11,15H21V17H11V15M5,13V15H7V13H5M3,13H9V19H3V13Z" />
        </svg>
        <h2>资金操作</h2>
      </div>

      <div class="operations-form">
        <div class="input-group">
          <label for="fund-amount">操作金额</label>
          <div class="amount-input">
            <span class="currency-icon">$</span>
            <input 
              id="fund-amount" 
              v-model.number="fundAmount" 
              type="number" 
              min="0" 
              :max="Number.MAX_SAFE_INTEGER"
              step="1"
              placeholder="请输入金额"
              @input="validateFundAmount"
            >
          </div>
        </div>

        <div class="actions-row">
          <button @click="depositFunds" class="action-btn deposit-btn" :disabled="isOperating || !fundAmount || fundAmount <= 0">
            <svg viewBox="0 0 24 24">
              <path d="M19,13H13V19H11V13H5V11H11V5H13V11H19V13Z" />
            </svg>
            <span>{{ isOperating ? '处理中...' : '存入资金' }}</span>
          </button>
          <button @click="withdrawFunds" class="action-btn withdraw-btn" :disabled="isOperating || !fundAmount || fundAmount <= 0 || fundAmount > (factionData?.funds || 0)">
            <svg viewBox="0 0 24 24">
              <path d="M19,13H5V11H19V13Z" />
            </svg>
            <span>{{ isOperating ? '处理中...' : '提取资金' }}</span>
          </button>
        </div>
      </div>
    </div>

    <!-- 职位提成设置卡片 -->
    <div v-if="hasPermission('manage_funds') || permissions.includes('admin')" class="job-commission-card">
      <div class="card-header">
        <svg viewBox="0 0 24 24">
          <path d="M16,17V19H2V17S2,13 9,13 16,17 16,17M12.5,7.5A3.5,3.5 0 0,1 9,11A3.5,3.5 0 0,1 5.5,7.5A3.5,3.5 0 0,1 9,4A3.5,3.5 0 0,1 12.5,7.5M15.94,13A5.32,5.32 0 0,1 18,17V19H22V17S22,13.37 15.94,13Z" />
        </svg>
        <h2>职位提成设置</h2>
      </div>

      <div class="job-commission-form">
        <div class="info-text">
          <p>为每个职位设置专属的提成比例，独立于全局提成比例。</p>
        </div>

        <div class="job-commission-list">
          <div v-for="job in sortedJobs" :key="job.name" class="job-commission-item">
            <div class="job-info">
              <div class="job-name">{{ job.name }}</div>
              <div class="job-rank">等级 {{ job.rank }}</div>
            </div>
            <div class="commission-controls">
              <div class="commission-slider">
                <input 
                  type="range" 
                  :value="getJobCommissionValue(job.name)" 
                  min="0" 
                  max="1" 
                  step="0.01" 
                  @input="updateJobCommissionSlider(job.name, $event)"
                  class="range-slider"
                >
                <div class="slider-value">{{ (getJobCommissionValue(job.name) * 100).toFixed(2) }}%</div>
              </div>
              <button 
                @click="saveJobCommission(job.name, getJobCommissionValue(job.name))" 
                class="save-btn"
                :disabled="isOperating || !hasJobCommissionChanged(job.name) || getJobCommissionValue(job.name) < 0 || getJobCommissionValue(job.name) > 1"
              >
                <svg viewBox="0 0 24 24">
                  <path d="M21,7L9,19L3.5,13.5L4.91,12.09L9,16.17L19.59,5.59L21,7Z" />
                </svg>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 全局提成设置卡片 -->
    <div v-if="hasPermission('manage_funds') || permissions.includes('admin')" class="settings-card">
      <div class="card-header">
        <svg viewBox="0 0 24 24">
          <path
            d="M12,15.5A3.5,3.5 0 0,1 8.5,12A3.5,3.5 0 0,1 12,8.5A3.5,3.5 0 0,1 15.5,12A3.5,3.5 0 0,1 12,15.5M19.43,12.97C19.47,12.65 19.5,12.33 19.5,12C19.5,11.67 19.47,11.34 19.43,11L21.54,9.37C21.73,9.22 21.78,8.95 21.66,8.73L19.66,5.27C19.54,5.05 19.27,4.96 19.05,5.05L16.56,6.05C16.04,5.66 15.5,5.32 14.87,5.07L14.5,2.42C14.46,2.18 14.25,2 14,2H10C9.75,2 9.54,2.18 9.5,2.42L9.13,5.07C8.5,5.32 7.96,5.66 7.44,6.05L4.95,5.05C4.73,4.96 4.46,5.05 4.34,5.27L2.34,8.73C2.21,8.95 2.27,9.22 2.46,9.37L4.57,11C4.53,11.34 4.5,11.67 4.5,12C4.5,12.33 4.53,12.65 4.57,12.97L2.46,14.63C2.27,14.78 2.21,15.05 2.34,15.27L4.34,18.73C4.46,18.95 4.73,19.03 4.95,18.95L7.44,17.94C7.96,18.34 8.5,18.68 9.13,18.93L9.5,21.58C9.54,21.82 9.75,22 10,22H14C14.25,22 14.46,21.82 14.5,21.58L14.87,18.93C15.5,18.67 16.04,18.34 16.56,17.94L19.05,18.95C19.27,19.03 19.54,18.95 19.66,18.73L21.66,15.27C21.78,15.05 21.73,14.78 21.54,14.63L19.43,12.97Z" />
        </svg>
        <h2>全局提成设置</h2>
      </div>

      <div class="settings-form">
        <div class="info-text">
          <p>全局提成比率决定了从派系相关业务中抽取的资金百分比。</p>
          <p>当前比率：<span class="highlight">{{ (factionData?.billtax * 100).toFixed(2) }}%</span></p>
        </div>

        <div class="slider-container">
          <input type="range" v-model.number="newBilltax" min="0" max="1" step="0.01" class="range-slider">
          <div class="slider-value">{{ (newBilltax * 100).toFixed(2) }}%</div>
        </div>

        <button @click="updateBilltax" class="update-btn" :disabled="isOperating || newBilltax === factionData?.billtax || newBilltax < 0 || newBilltax > 1">
          <svg viewBox="0 0 24 24">
            <path d="M21,7L9,19L3.5,13.5L4.91,12.09L9,16.17L19.59,5.59L21,7Z" />
          </svg>
          <span>{{ isOperating ? '处理中...' : '更新全局提成比率' }}</span>
        </button>
      </div>
    </div>

    <!-- 交易历史记录卡片 -->
    <div class="card history-card">
      <div class="card-header">
        <h3>交易历史</h3>
      </div>
      <div class="history-list" v-if="sortedTransactions.length > 0">
        <div v-for="(item, index) in sortedTransactions" :key="index" class="transaction-item">
          <div :class="['transaction-icon', item.type === 'deposit' ? 'deposit' : 'withdrawal']">
            <svg v-if="item.type === 'deposit'" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
              <path d="M3.5 18.49l6-6.01 4 4L22 6.92l-1.41-1.41-7.09 7.97-4-4L2 16.99z" />
            </svg>
            <svg v-else xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
              <path d="M20 12l-1.41-1.41L13 16.17V4h-2v12.17l-5.58-5.59L4 12l8 8 8-8z" />
            </svg>
          </div>
          <div class="transaction-details">
            <div class="transaction-reason">{{ item.reason }}</div>
            <div class="transaction-date">{{ formatDate(item.date) }}</div>
          </div>
          <div :class="['transaction-amount', item.type === 'deposit' ? 'deposit' : 'withdrawal']">
            {{ item.type === 'deposit' ? '+' : '-' }}{{ formatFunds(item.amount) }}$
          </div>
        </div>
      </div>
      <div v-else class="empty-history">
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
          <path
            d="M19 3h-4.18C14.4 1.84 13.3 1 12 1c-1.3 0-2.4.84-2.82 2H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-7 0c.55 0 1 .45 1 1s-.45 1-1 1-1-.45-1-1 .45-1 1-1zm2 14H7v-2h7v2zm3-4H7v-2h10v2zm0-4H7V7h10v2z" />
        </svg>
        <p>暂无交易记录</p>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, PropType, computed } from 'vue';

const props = defineProps({
  factionData: {
    type: Object,
    required: true
  },
  permissions: {
    type: Array,
    required: true
  },
  hasPermission: {
    type: Function,
    required: true
  },
  withdrawFundsAction: {
    type: Function as PropType<(amount: number) => Promise<boolean>>,
    required: true
  },
  depositFundsAction: {
    type: Function as PropType<(amount: number) => Promise<boolean>>,
    required: true
  },
  updateBilltaxAction: {
    type: Function as PropType<(rate: number) => Promise<boolean>>,
    required: true
  },
  updateJobCommissionAction: {
    type: Function as PropType<(jobName: string, commission: number) => Promise<boolean>>,
    required: true
  }
});

const fundAmount = ref(0);
const newBilltax = ref(props.factionData?.billtax || 0);

// 职位提成比例的临时状态
const tempJobCommissions = ref<Record<string, number>>({});

// 添加操作状态管理
const isOperating = ref(false);

// 计算属性：按照等级排序的职位
const sortedJobs = computed(() => {
  if (!props.factionData?.jobs?.length) return [];
  return [...props.factionData.jobs].sort((a, b) => a.rank - b.rank);
});

// 计算属性：按照时间排序的交易记录
const sortedTransactions = computed(() => {
  if (!props.factionData?.fundsHistory?.length) return [];
  return [...props.factionData.fundsHistory].sort((a, b) => b.date - a.date);
});

// 获取职位当前显示的提成比例（优先显示临时值）
function getJobCommissionValue(jobName: string): number {
  const tempValue = tempJobCommissions.value[jobName];
  if (tempValue !== undefined) {
    return tempValue;
  }
  const job = props.factionData?.jobs?.find(j => j.name === jobName);
  return job?.commission || 0;
}

function formatFunds(amount: number) {
  return amount.toLocaleString();
}

// 格式化日期函数
function formatDate(date: number): string {
  return new Date(date).toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  });
}

// 输入验证函数
function validateAmount(amount: number): boolean {
  return typeof amount === 'number' && 
         !isNaN(amount) && 
         amount > 0 && 
         amount <= Number.MAX_SAFE_INTEGER &&
         Number.isFinite(amount);
}

function validateBilltax(rate: number): boolean {
  return typeof rate === 'number' && 
         !isNaN(rate) && 
         rate >= 0 && 
         rate <= 1 &&
         Number.isFinite(rate);
}

function validateCommission(commission: number): boolean {
  return typeof commission === 'number' && 
         !isNaN(commission) && 
         commission >= 0 && 
         commission <= 1 &&
         Number.isFinite(commission);
}

async function withdrawFunds() {
  if (isOperating.value) {
    console.warn('操作正在进行中，请等待');
    return;
  }

  const amount = Number(fundAmount.value);
  if (!validateAmount(amount)) {
    console.error('无效的金额');
    return;
  }

  if (amount > (props.factionData?.funds || 0)) {
    console.error('金额超过可用资金');
    return;
  }

  isOperating.value = true;
  try {
    const success = await props.withdrawFundsAction(amount);
    if (success) {
      fundAmount.value = 0;
    }
  } catch (error) {
    console.error('提取资金失败:', error);
  } finally {
    isOperating.value = false;
  }
}

async function depositFunds() {
  if (isOperating.value) {
    console.warn('操作正在进行中，请等待');
    return;
  }

  const amount = Number(fundAmount.value);
  if (!validateAmount(amount)) {
    console.error('无效的金额');
    return;
  }

  isOperating.value = true;
  try {
    const success = await props.depositFundsAction(amount);
    if (success) {
      fundAmount.value = 0;
    }
  } catch (error) {
    console.error('存入资金失败:', error);
  } finally {
    isOperating.value = false;
  }
}

async function updateBilltax() {
  if (isOperating.value) {
    console.warn('操作正在进行中，请等待');
    return;
  }

  if (!validateBilltax(newBilltax.value)) {
    console.error('无效的提成比率');
    return;
  }

  if (newBilltax.value === props.factionData?.billtax) {
    console.warn('提成比率没有变化');
    return;
  }

  isOperating.value = true;
  try {
    await props.updateBilltaxAction(newBilltax.value);
  } catch (error) {
    console.error('更新提成比率失败:', error);
  } finally {
    isOperating.value = false;
  }
}

// 更新职位提成比例滑块
function updateJobCommissionSlider(jobName: string, event: Event) {
  const target = event.target as HTMLInputElement;
  const value = parseFloat(target.value);
  
  if (!validateCommission(value)) {
    console.error('无效的提成比例');
    return;
  }
  
  tempJobCommissions.value[jobName] = value;
}

// 检查职位提成比例是否有变化
function hasJobCommissionChanged(jobName: string): boolean {
  const originalCommission = props.factionData?.jobs?.find(j => j.name === jobName)?.commission || 0;
  const tempCommission = tempJobCommissions.value[jobName];
  return tempCommission !== undefined && tempCommission !== originalCommission;
}

// 验证资金输入
function validateFundAmount(event: Event) {
  const target = event.target as HTMLInputElement;
  let value = parseFloat(target.value);
  
  if (isNaN(value) || value < 0) {
    fundAmount.value = 0;
    return;
  }
  
  if (value > Number.MAX_SAFE_INTEGER) {
    fundAmount.value = Number.MAX_SAFE_INTEGER;
    return;
  }
  
  fundAmount.value = Math.floor(value); // 确保为整数
}

// 保存职位提成比例
async function saveJobCommission(jobName: string, commission: number) {
  if (isOperating.value) {
    console.warn('操作正在进行中，请等待');
    return;
  }

  const finalCommission = tempJobCommissions.value[jobName] ?? commission;
  
  if (!validateCommission(finalCommission)) {
    console.error('无效的提成比例');
    return;
  }

  isOperating.value = true;
  try {
    const success = await props.updateJobCommissionAction(jobName, finalCommission);
    if (success) {
      // 清除临时状态
      delete tempJobCommissions.value[jobName];
    }
  } catch (error) {
    console.error('保存职位提成比例失败:', error);
  } finally {
    isOperating.value = false;
  }
}
</script>

<style scoped>
.finances-container {
  display: grid;
  gap: 2vh;
  grid-template-columns: 1fr 1fr;
  grid-template-rows: auto minmax(auto, 50vh);
  align-items: start;
}

/* 卡片通用样式 */
.overview-card,
.operations-card,
.settings-card,
.history-card {
  background: rgba(34, 42, 53, 0.7);
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.card-header {
  display: flex;
  align-items: center;
  padding: 1.8vh 2vh;
  background: rgba(26, 32, 44, 0.5);
  border-bottom: 1px solid rgba(255, 255, 255, 0.08);
}

.card-header svg {
  width: 2vh;
  height: 2vh;
  margin-right: 1vh;
  fill: #a995f4;
}

.card-header h2 {
  font-size: 1.6vh;
  font-weight: 600;
  color: #e2e8f0;
}

/* 资金总览卡片样式 */
.current-funds {
  padding: 3vh 2vh;
  text-align: center;
}

.funds-amount {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 1vh;
}

.funds-amount .currency {
  font-size: 2.5vh;
  font-weight: 600;
  color: #68d391;
  margin-right: 0.5vh;
}

.funds-amount .value {
  font-size: 4vh;
  font-weight: 700;
  color: #e2e8f0;
  letter-spacing: -0.05em;
}

.funds-label {
  font-size: 1.4vh;
  color: #a0aec0;
}

.funds-stats {
  display: flex;
  justify-content: space-between;
  padding: 2vh;
  background: rgba(26, 32, 44, 0.3);
}

.stat-item {
  text-align: center;
  flex: 1;
}

.stat-label {
  font-size: 1.2vh;
  color: #a0aec0;
  margin-bottom: 0.5vh;
}

.stat-value {
  font-size: 1.8vh;
  font-weight: 600;
  color: #a995f4;
}

/* 资金操作卡片样式 */
.operations-form {
  padding: 2vh;
}

.input-group {
  margin-bottom: 2vh;
}

.input-group label {
  display: block;
  font-size: 1.2vh;
  color: #a0aec0;
  margin-bottom: 0.8vh;
}

.amount-input {
  position: relative;
}

.currency-icon {
  position: absolute;
  left: 1.5vh;
  top: 50%;
  transform: translateY(-50%);
  color: #a0aec0;
  font-weight: 600;
}

.amount-input input {
  width: 100%;
  height: 4.5vh;
  padding: 0 1.5vh 0 3.5vh;
  background: rgba(26, 32, 44, 0.6);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 6px;
  color: #e2e8f0;
  font-size: 1.6vh;
}

.amount-input input:focus {
  outline: none;
  border-color: rgba(135, 116, 225, 0.5);
  box-shadow: 0 0 0 1px rgba(135, 116, 225, 0.2);
}

.actions-row {
  display: flex;
  gap: 1.5vh;
}

.action-btn {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 1.2vh 0;
  border-radius: 8px;
  font-size: 1.4vh;
  font-weight: 500;
  cursor: pointer;
  border: none;
  transition: all 0.2s;
}

.action-btn svg {
  width: 1.8vh;
  height: 1.8vh;
  margin-right: 0.8vh;
}

.deposit-btn {
  background: rgba(72, 187, 120, 0.2);
  color: #68d391;
}

.deposit-btn svg {
  fill: #68d391;
}

.deposit-btn:hover:not(:disabled) {
  background: rgba(72, 187, 120, 0.3);
}

.withdraw-btn {
  background: rgba(237, 137, 54, 0.2);
  color: #f6ad55;
}

.withdraw-btn svg {
  fill: #f6ad55;
}

.withdraw-btn:hover:not(:disabled) {
  background: rgba(237, 137, 54, 0.3);
}

.action-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

/* 职位提成设置卡片样式 */
.job-commission-card {
  display: flex;
  flex-direction: column;
  height: auto;
  max-height: 48vh;
}

.job-commission-form {
  padding: 2vh;
  overflow-y: auto;
  max-height: 35vh;
}

.info-text {
  margin-bottom: 2vh;
  padding: 1.5vh;
  background: rgba(26, 32, 44, 0.3);
  border-radius: 8px;
  font-size: 1.3vh;
  color: #cbd5e0;
}

.info-text p {
  margin-bottom: 0.8vh;
}

.info-text p:last-child {
  margin-bottom: 0;
}

.job-commission-list {
  margin-bottom: 2vh;
}

.job-commission-item {
  display: flex;
  align-items: center;
  padding: 1.5vh;
  margin-bottom: 1vh;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  transition: all 0.2s ease;
}

.job-commission-item:hover {
  background: rgba(255, 255, 255, 0.1);
  transform: translateY(-2px);
}

.job-info {
  flex: 0 0 20%;
  display: flex;
  flex-direction: column;
  margin-right: 2vh;
}

.job-name {
  font-weight: 500;
  margin-bottom: 0.5vh;
  color: #fff;
  font-size: 1.4vh;
}

.job-rank {
  font-size: 1.2vh;
  color: rgba(255, 255, 255, 0.6);
}

.commission-controls {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 1.5vh;
}

.commission-slider {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 1.5vh;
}

.commission-slider .range-slider {
  flex: 1;
  height: 0.5vh;
  -webkit-appearance: none;
  appearance: none;
  background: rgba(26, 32, 44, 0.5);
  border-radius: 1vh;
  outline: none;
}

.commission-slider .range-slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 2vh;
  height: 2vh;
  border-radius: 50%;
  background: #a995f4;
  cursor: pointer;
  transition: all 0.2s;
}

.commission-slider .range-slider::-webkit-slider-thumb:hover {
  background: #8774e1;
  transform: scale(1.2);
}

.commission-slider .slider-value {
  min-width: 6vh;
  text-align: center;
  font-size: 1.4vh;
  font-weight: 600;
  color: #a995f4;
}

.save-btn {
  width: 4vh;
  height: 4vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
  background: rgba(135, 116, 225, 0.2);
  color: #a995f4;
  border-radius: 50%;
  border: none;
  cursor: pointer;
  transition: all 0.2s;
}

.save-btn svg {
  width: 2vh;
  height: 2vh;
  fill: #a995f4;
}

.save-btn:hover:not(:disabled) {
  background: rgba(135, 116, 225, 0.3);
}

.save-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* 全局提成设置卡片样式 */
.settings-card {
  display: flex;
  flex-direction: column;
  height: auto;
  max-height: 48vh;
}

.settings-form {
  padding: 2vh;
  overflow-y: auto;
  max-height: 35vh;
}

.info-text {
  margin-bottom: 2vh;
  padding: 1.5vh;
  background: rgba(26, 32, 44, 0.3);
  border-radius: 8px;
  font-size: 1.3vh;
  color: #cbd5e0;
}

.info-text p {
  margin-bottom: 0.8vh;
}

.info-text p:last-child {
  margin-bottom: 0;
}

.highlight {
  color: #a995f4;
  font-weight: 600;
}

.slider-container {
  display: flex;
  align-items: center;
  margin-bottom: 2vh;
}

.range-slider {
  flex: 1;
  height: 0.5vh;
  -webkit-appearance: none;
  appearance: none;
  background: rgba(26, 32, 44, 0.5);
  border-radius: 1vh;
  outline: none;
}

.range-slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 2vh;
  height: 2vh;
  border-radius: 50%;
  background: #a995f4;
  cursor: pointer;
  transition: all 0.2s;
}

.range-slider::-webkit-slider-thumb:hover {
  background: #8774e1;
  transform: scale(1.2);
}

.slider-value {
  min-width: 5vh;
  text-align: right;
  margin-left: 1.5vh;
  font-size: 1.4vh;
  font-weight: 600;
  color: #a995f4;
}

.update-btn {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 1.2vh 0;
  background: rgba(135, 116, 225, 0.2);
  color: #a995f4;
  border-radius: 8px;
  font-size: 1.4vh;
  font-weight: 500;
  cursor: pointer;
  border: none;
  transition: all 0.2s;
}

.update-btn svg {
  width: 1.8vh;
  height: 1.8vh;
  margin-right: 0.8vh;
  fill: #a995f4;
}

.update-btn:hover:not(:disabled) {
  background: rgba(135, 116, 225, 0.3);
}

.update-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* 交易历史卡片样式 */
.history-card {
  display: flex;
  flex-direction: column;
  height: auto;
  max-height: 35vh;
  margin-top: 1rem;
  overflow: hidden;
}

.history-card .card-header {
  display: flex;
  align-items: center;
  padding: 0.75rem 1rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.history-card .card-header h3 {
  margin: 0;
  font-size: 1.1rem;
  font-weight: 600;
  color: #fff;
}

.history-list {
  flex: 1;
  overflow-y: auto;
  max-height: 35vh;
  padding: 0.5rem;
}

.history-list::-webkit-scrollbar {
  width: 6px;
}

.history-list::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 3px;
}

.history-list::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 3px;
}

.history-list::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.3);
}

.transaction-item {
  display: flex;
  align-items: center;
  padding: 0.75rem;
  margin-bottom: 0.5rem;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  transition: all 0.2s ease;
}

.transaction-item:hover {
  background: rgba(255, 255, 255, 0.1);
  transform: translateY(-2px);
}

.transaction-icon {
  width: 36px;
  height: 36px;
  min-width: 36px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
}

.transaction-icon.deposit {
  background-color: rgba(46, 213, 115, 0.15);
}

.transaction-icon.withdrawal {
  background-color: rgba(255, 71, 87, 0.15);
}

.transaction-icon svg {
  width: 20px;
  height: 20px;
  fill: currentColor;
}

.transaction-icon.deposit svg {
  color: #2ed573;
}

.transaction-icon.withdrawal svg {
  color: #ff4757;
}

.transaction-details {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.transaction-reason {
  font-weight: 500;
  margin-bottom: 4px;
  color: #fff;
}

.transaction-date {
  font-size: 0.8rem;
  color: rgba(255, 255, 255, 0.6);
}

.transaction-amount {
  font-weight: 700;
  margin-left: auto;
}

.transaction-amount.deposit {
  color: #2ed573;
}

.transaction-amount.withdrawal {
  color: #ff4757;
}

.empty-history {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  height: 100%;
  color: rgba(255, 255, 255, 0.5);
}

.empty-history svg {
  width: 48px;
  height: 48px;
  margin-bottom: 1rem;
  fill: rgba(255, 255, 255, 0.3);
}

.empty-history p {
  font-size: 0.9rem;
  margin: 0;
}
</style>