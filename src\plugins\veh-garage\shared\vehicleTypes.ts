/**
 * 车辆类型配置
 * 用于定义GTA V原生载具类别到车库类型的映射关系
 */

// 车库支持的载具类型
export enum GarageVehicleType {
    CAR = 'CAR',       // 汽车（包括轿车、SUV、跑车等）
    SHIP = 'SHIP',     // 船只
    PLANE = 'PLANE',   // 飞机
}

// GTA V载具类别到车库类型的映射
// 根据native.getVehicleClass()的返回值进行映射
export const VEHICLE_CLASS_TO_GARAGE_TYPE: Record<number, GarageVehicleType> = {
    0: GarageVehicleType.CAR,    // Compacts 紧凑型车
    1: GarageVehicleType.CAR,    // Sedans 轿车
    2: GarageVehicleType.CAR,    // SUVs 越野车
    3: GarageVehicleType.CAR,    // Coupes 双门跑车
    4: GarageVehicleType.CAR,    // Muscle 肌肉车
    5: GarageVehicleType.CAR,    // Sports Classics 经典跑车
    6: GarageVehicleType.CAR,    // Sports 运动车
    7: GarageVehicleType.CAR,    // Super 超级跑车
    8: GarageVehicleType.CAR,   // Motorcycles 摩托车
    9: GarageVehicleType.CAR,    // Off-road 越野车
    10: GarageVehicleType.CAR,   // Industrial 工业车辆
    11: GarageVehicleType.CAR,   // Utility 实用车辆
    12: GarageVehicleType.CAR,   // Vans 货车
    13: GarageVehicleType.CAR,  // Cycles 自行车
    14: GarageVehicleType.SHIP,  // Boats 船只
    15: GarageVehicleType.PLANE,  // Helicopters 直升机
    16: GarageVehicleType.PLANE, // Planes 飞机
    17: GarageVehicleType.CAR,   // Service 服务车辆
    18: GarageVehicleType.CAR,   // Emergency 紧急车辆
    19: GarageVehicleType.CAR,   // Military 军用车辆
    20: GarageVehicleType.CAR,   // Commercial 商用车辆
};

// 车库类型的中文名称映射（用于显示）
export const GARAGE_TYPE_NAMES: Record<GarageVehicleType, string> = {
    [GarageVehicleType.CAR]: '汽车',
    [GarageVehicleType.SHIP]: '船只',
    [GarageVehicleType.PLANE]: '飞机',
};

/**
 * 根据载具类别获取对应的车库类型
 * @param vehicleClass GTA V载具类别 (0-21)
 * @returns 车库类型
 */
export function getGarageTypeFromVehicleClass(vehicleClass: number): GarageVehicleType {
    return VEHICLE_CLASS_TO_GARAGE_TYPE[vehicleClass] || GarageVehicleType.CAR;
}

/**
 * 检查载具类型是否可以存入指定车库类型列表
 * @param vehicleClass GTA V载具类别
 * @param allowedTypes 车库支持的类型列表
 * @returns 是否可以存入
 */
export function canStoreVehicleInGarage(vehicleClass: number, allowedTypes: string[]): boolean {
    const vehicleGarageType = getGarageTypeFromVehicleClass(vehicleClass);
    return allowedTypes.includes(vehicleGarageType);
}

/**
 * 获取车库类型的中文名称
 * @param garageType 车库类型
 * @returns 中文名称
 */
export function getGarageTypeName(garageType: GarageVehicleType): string {
    return GARAGE_TYPE_NAMES[garageType] || '未知';
}