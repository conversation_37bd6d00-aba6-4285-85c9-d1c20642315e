import * as alt from 'alt-server';
import { NPC, Conversation } from '../shared/types.js';
import { conversationManager } from './conversation.js';

export async function createNPC(
    model: string,
    name: string,
    position: alt.Vector3,
    rotation: number,
    conversations?: Conversation[]
): Promise<NPC> {
    if (!conversations) {
        conversations = [
            {
                id: 'start',
                text: `你好，我是${name}。有什么可以帮到你的吗？`,
                options: [
                    {
                        text: '没事，谢谢',
                        endConversation: true
                    },
                    {
                        text: '告诉我关于你自己',
                        nextId: 'about_me'
                    }
                ]
            },
            {
                id: 'about_me',
                text: `我的名字是${name}。`,
                options: [
                    {
                        text: '明白了，再见',
                        endConversation: true
                    }
                ]
            }
        ];
    }

    const ped = new alt.Ped(model, position, new alt.Vector3(0, 0, rotation));
    ped.setStreamSyncedMeta('makeStupid', true);
    ped.setStreamSyncedMeta('invincible', true);
    ped.frozen = true;
    ped.setStreamSyncedMeta('conversationnpc', name);

    const npc: NPC = {
        model,
        name,
        pos: position,
        rotz: rotation,
        conversations,
        ped: ped
    };

    conversationManager.addNPC(npc);
    return npc;
}

export const npclist = conversationManager.getNPCList();