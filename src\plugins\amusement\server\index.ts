import * as alt from 'alt-server';
import { useRebar } from '@Server/index.js';
import { MarkerType } from 'alt-shared';
import { GlobalTimerManager } from '@Plugins/animmenu/server/interval.js';


const Rebar = useRebar();
const notifyapi = await Rebar.useApi().getAsync('notify-api')
const promptbarapi = await Rebar.useApi().getAsync('promptbar-api')





// 摩天轮圆心坐标
const wheelCenter = new alt.Vector3(-1663.970703125, -1126.7391357421875, 30.7066707611084);


const ferriswheelpos = [
    new alt.Vector3(-1663.9707, -1126.7391, 15.4067),
    new alt.Vector3(-1663.9707, -1115.8852, 19.5444),
    new alt.Vector3(-1663.9707, -1110.8805, 30.7067),
    new alt.Vector3(-1663.9707, -1115.8852, 41.8690),
    new alt.Vector3(-1663.9707, -1126.7391, 46.0067),
    new alt.Vector3(-1663.9707, -1137.5930, 41.8690),
    new alt.Vector3(-1663.9707, -1142.5977, 30.7067),
    new alt.Vector3(-1663.9707, -1137.5930, 19.5444)
]


const wheel1 = new alt.Object('prop_ld_ferris_wheel', new alt.Vector3(-1663.970703125, -1126.7391357421875, 30.7066707611084), alt.Vector3.zero);
wheel1.frozen = true;
wheel1.collision = true;
wheel1.streamingDistance = 1200;

// 计算每秒需要旋转的角度（2.5分钟转一圈 = 150秒转360度）
const rotationPerSecond = 360 / 150; // 每秒2.4度

let rotx = 0; // 使用角度而不是弧度

// 维护一个Map来记录在摩天轮范围内的玩家
const playersNearWheel = new Map();

let allferriscar = []


for (let pos of ferriswheelpos) {
    // 计算相对于摩天轮圆心的偏移量
    const offsetPos = new alt.Vector3(
        pos.x - wheelCenter.x,
        pos.y - wheelCenter.y,
        pos.z - wheelCenter.z
    );

    const wheel = new alt.Object('prop_ferris_car_01', pos, alt.Vector3.zero);
    wheel.frozen = true;
    wheel.collision = true;
    wheel.streamingDistance = 1200;
    allferriscar.push(wheel);
}



// 设置每秒更新一次旋转角度
GlobalTimerManager.getInstance().addTask('ferriswheelupdate', () => {
    rotx += rotationPerSecond; // 每秒增加2.4度

    if (rotx >= 360) rotx -= 360; // 角度归零

    // 获取所有玩家
    for (let player of alt.Player.all) {
        if (!player || !player.valid) continue;

        const distance = player.pos.distanceTo(wheelCenter);
        const playerId = player.id.toString();

        // 玩家在范围内
        if (distance <= 100) {
            // 如果是第一次进入范围
            if (!playersNearWheel.has(playerId)) {
                playersNearWheel.set(playerId, true);
                // 发送初始角度更新事件，确保包含所有吊舱数据
                player.emit('ferriswheel:rotationUpdate', {
                    degreerotx: rotx, // 改为直接发送角度
                    wheel: wheel1,
                    allferriscar: allferriscar
                });
            }
        }
        // 玩家离开范围
        else if (playersNearWheel.has(playerId)) {
            // 发送停止事件
            player.emit('ferriswheel:stop');
            // 从Map中移除玩家
            playersNearWheel.delete(playerId);
        }
    }
});



const leaveferriswheelpos = new alt.Vector3(-1666.4835, -1127.0110, 13.6769)

// 每个吊舱的座位配置
const sitinferriswheel = [
    {
        pos: new alt.Vector3(0, -0.3, -2),
        rot: new alt.Vector3(0, 0, 0)
    },
    {
        pos: new alt.Vector3(0, 1.2, -2),
        rot: new alt.Vector3(0, 0, Math.PI)
    },
]

// 维护吊舱座位占用情况的Map
// 格式: Map<吊舱索引, Map<座位索引, 玩家ID>>
const ferriswheelSeats = new Map();

// 初始化所有吊舱的座位状态
for (let index = 0; index < allferriscar.length; index++) {
    const seatMap = new Map();
    seatMap.set(0, null); // 第一个座位，初始为空
    seatMap.set(1, null); // 第二个座位，初始为空
    ferriswheelSeats.set(index, seatMap);
}

// 玩家离开摩天轮的函数
function leaveWheel(player) {
    if (!player || !player.valid) return;
    
    // 检查玩家是否在摩天轮上
    const wheelData = player.getMeta('onFerrisWheel');
    if (!wheelData) return;
    
    // 清除玩家任务和附加状态
    player.clearTasks();
    player.detach();
    
    // 将玩家传送到离开点
    player.pos = leaveferriswheelpos;
    
    // 释放座位
    const { carIndex, seatIndex } = wheelData;
    if (ferriswheelSeats.has(carIndex)) {
        ferriswheelSeats.get(carIndex).set(seatIndex, null);
    }
    
    // 清除玩家元数据
    player.deleteMeta('onFerrisWheel');
    
    // 清除玩家的定时器


    const timer = player.getMeta('ferrisWheelTimerId');
    if (timer) {
        alt.clearTimeout(timer);
        player.deleteMeta('ferrisWheelTimerId');
    }
    
    notifyapi.shownotify(player, `你已离开摩天轮`, 'info');
}


// 注册按alt键离开摩天轮的按键绑定
alt.onClient('ferriswheel:leave', (player) => {
    if (player.getMeta('onFerrisWheel')) {
        leaveWheel(player);
    }
});


Rebar.controllers.useMarkerGlobal({
    pos: new alt.Vector3(-1661.5121, -1126.9978, 13.6769-1),
    color: new alt.RGBA(255, 215, 0, 150),
    scale: new alt.Vector3(1, 1, 0.2),
    type: MarkerType.MarkerCylinder,
});


const ferriswheelinteraction = Rebar.controllers.useInteraction(
    new alt.ColshapeCylinder(
        -1661.5121, -1126.9978, 13.6769-1,
        0.8, 3),
    'player',
);

ferriswheelinteraction.onEnter((player) => {
    promptbarapi.showPromptBar(player, '坐摩天轮 $50')
});

ferriswheelinteraction.onLeave((player) => {
    promptbarapi.hidePromptBar(player)
});

ferriswheelinteraction.on(async (player) => {
   
    promptbarapi.hidePromptBar(player)

    const currencyapi = await Rebar.useApi().getAsync('currency-api')

    const success = currencyapi.cancost({player:player}, 50)

    if (!success) {
        notifyapi.shownotify(player, `你没钱坐摩天轮`, 'error')
        return;
    }

    player.clearTasks()
    player.detach()

    // 找到距离玩家最近的摩天轮吊舱
    let availableSeat = null;
    let availableCar = null;
    let availableSeatIndex = -1;
    let availableCarIndex = -1;

    // 遍历所有吊舱，寻找最近且有空座位的吊舱
    for (let carIndex = 0; carIndex < allferriscar.length; carIndex++) {
        const car = allferriscar[carIndex];
        const distance = car.pos.distanceTo(player.pos);
        
        // 如果距离超过4米，跳过
        if (distance > 4) continue;
        
        const seatMap = ferriswheelSeats.get(carIndex);
        
        // 检查该吊舱是否有空座位
        for (let seatIndex = 0; seatIndex < 2; seatIndex++) {
            if (seatMap.get(seatIndex) === null) {
                availableSeat = sitinferriswheel[seatIndex];
                availableCar = car;
                availableSeatIndex = seatIndex;
                availableCarIndex = carIndex;
                break;
            }
        }
        
        if (availableSeat) break;
    }

    // 如果没有找到可用座位
    if (!availableSeat) {
        notifyapi.shownotify(player, `请稍等，附近的吊舱座位都已满`, 'warning')
        return;
    }

    // 标记座位为已占用
    ferriswheelSeats.get(availableCarIndex).set(availableSeatIndex, player.id);

    // 将玩家附加到吊舱并播放动画
    player.attachTo(
        availableCar, 
        0, 
        0, 
        availableSeat.pos, 
        availableSeat.rot, 
        false, 
        false
    );

    player.playAnimation('timetable@ron@ig_5_p3', 'ig_5_p3_base', 8.0, 8.0, -1, 9 | 4 | 1);
    
    // 设置元数据，用于后续检查玩家是否在摩天轮上w
    player.setMeta('onFerrisWheel', {
        carIndex: availableCarIndex,
        seatIndex: availableSeatIndex
    });

    const success1 = currencyapi.cost({player:player}, 50)

    if (!success1) {
        notifyapi.shownotify(player, `你没钱坐摩天轮`, 'error')
        return;
    }
    
    // 通知玩家可以按alt键离开
    notifyapi.shownotify(player, `你已坐上摩天轮，按alt键可随时离开`, 'info');
    
    // 设置5分钟后自动离开
    const timer = alt.setTimeout(() => {
        if (player && player.valid) {
            leaveWheel(player);
        }
    }, 5 * 60 * 1000); // 5分钟 = 5 * 60 * 1000毫秒
    
    // 保存定时器ID以便需要时清除
    player.setMeta('ferrisWheelTimerId', timer);
});



Rebar.messenger.useMessenger().commands.register({
    name: 'attachferriswheel',
    desc: '试验attach [x] [y] [z]',
    options: { permissions: ['admin'] },
    callback: async (player, x, y, z) => {

        player.clearTasks()
        player.detach()

        const xNum = parseFloat(parseFloat(x).toFixed(2));
        const yNum = parseFloat(parseFloat(y).toFixed(2));
        const zNum = parseFloat(parseFloat(z).toFixed(2));

        // 找到距离玩家最近的摩天轮吊舱
        const closestwheelcar = allferriscar.reduce((closest, car) => {
            const distance = car.pos.distanceTo(player.pos);
            const closestDistance = closest.pos.distanceTo(player.pos);
            // 如果当前吊舱距离玩家更近，则返回当前吊舱，否则保持原来的最近吊舱
            return distance < closestDistance ? car : closest;
        }, allferriscar[0]); // 初始值设为第一个吊舱，避免undefined错误

        player.attachTo(closestwheelcar, 0, 0, new alt.Vector3(xNum, yNum, zNum), new alt.Vector3(0, 0, 0), false, false);

        player.playAnimation('timetable@ron@ig_5_p3', 'ig_5_p3_base', 8.0, 8.0, -1, 9 | 4 | 1)

    }
});


Rebar.messenger.useMessenger().commands.register({
    name: 'sitrelative',
    desc: '相对位移坐着 [x] [y] [z]',
    options: { permissions: ['admin'] },
    callback: async (player, x, y, z) => {

        player.clearTasks()
        player.detach()

        const xOffset = parseFloat(parseFloat(x).toFixed(2));
        const yOffset = parseFloat(parseFloat(y).toFixed(2));
        const zOffset = parseFloat(parseFloat(z).toFixed(2));

        // 获取玩家当前位置
        const currentPos = player.pos;
        
        // 计算新位置（相对位移）
        const newPos = new alt.Vector3(
            currentPos.x + xOffset,
            currentPos.y + yOffset,
            currentPos.z + zOffset
        );

        // 移动玩家到新位置
        player.pos = newPos;

        // 播放坐着动画
        player.playAnimation('timetable@ron@ig_5_p3', 'ig_5_p3_base', 8.0, 8.0, -1, 9 | 4 | 1);

        // 输出新坐标到控制台
        console.log(`玩家 ${player.name} 移动到新位置: x=${newPos.x.toFixed(2)}, y=${newPos.y.toFixed(2)}, z=${newPos.z.toFixed(2)}`);

    }
});


Rebar.messenger.useMessenger().commands.register({
    name: 'detach',
    desc: '分离',
    options: { permissions: ['admin'] },
    callback: async (player) => {
        player.clearTasks()
        player.detach()
    }
});

Rebar.messenger.useMessenger().commands.register({
    name: 'adjustped',
    desc: '调整ped位置和旋转',
    options: { permissions: ['admin'] },
    callback: async (player) => {
        player.emit('adjustped:start');
        notifyapi.shownotify(player, '进入ped调整模式 - 鼠标移动位置，右键旋转，左键确认，中键取消', 'info');
    }
});